#! /bin/sh

# 0 default param
# 1  Database Server
# 2  Database User
# 3  Database Password
# 4  Pullrequest Number
# 5  GITHUB Token
# 6  CLICKHOUSE_HOST
# 7  CLICKHOUSE_PORT
# 8  CLICKHOUSE_USER
# 9  CLICKHOUSE_PASS
# 10 CLICKHOUSE_DATABASE
# 11 AUDIT_LOG_TABLE
# 12 SENTRY_DSN
# 13 EMAIL_HOST_PASSWORD

echo -n "DB_HOST=$1
DB_NAME=pull$4_stockone_neo
DB_USER=$2
DB_PASS=$3
DB_REPORTS_HOST=$1
DB_REPORTS_NAME=pull$4_stockone_neo
DB_REPORTS_USER=$2
DB_REPORTS_PASS=$3
REDIS_URL=redis://:JguvU^FP4V@neo-redis$4:6379
DEBUG=1
DB_PROFILER=1
QUERY_PROFILER_KEY=$4
DEV_MODE=1
USE_S3=TRUE
AWS_ID=********************
AWS_KEY=zhpVM/378QHIPGfIBwLeK17xwIaXtviiKfiVxziO
AWS_STORAGE_BUCKET_NAME=mylocal-stockone-bucket
AWS_S3_REGION_NAME=ap-south-1
AWS_S3_SIGNATURE_VERSION=s3v4
GITHUB_TOKEN=$5
CLICKHOUSE_HOST=$6
CLICKHOUSE_PORT=$7
CLICKHOUSE_USER=$8
CLICKHOUSE_PASS=$9
CLICKHOUSE_DATABASE=${10}
AUDIT_LOG_TABLE=${11}
SCENTRY=TRUE
SCENTRY_URL=${12}
EMAIL_HOST_PASSWORD=${13}
COVERAGE_PROCESS_START=./.coveragerc"
