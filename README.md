# Stockone Neo (WMS)  Warehouse Management System

## Code Coverage

[![codecov](https://codecov.stockone.com/gh/shipsy/stockone-neo/graph/badge.svg?token=NC0P144S5X)](https://codecov.stockone.com/gh/shipsy/stockone-neo)

## Code Coverage Graph

![Coverage Graph](https://codecov.stockone.com/gh/shipsy/stockone-neo/graphs/sunburst.svg?token=NC0P144S5X)

## Overview
This project is a Django-based Warehouse Management System designed to streamline and manage inventory processes efficiently. It offers features like inventory tracking, order processing, and real-time stock updates.

## Features
- **Inventory Management:** Track and manage stock levels, product details, and supplier information.
- **Order Processing:** Handle orders from placement to delivery, including order creation, modification, and tracking.
- **Reporting:** Generate detailed reports on inventory, sales, and order status.
- **User Management:** Manage user roles and permissions for system access control.
- **API Integration:** RESTful API for integration with other systems and services.

## Getting Started

### Prerequisites
- Docker
- Docker Compose 
- Other dependencies in `requirements.txt`

### Installation
1. Clone the repository:
   ```
   git clone https://github.com/yourusername/django-wms.git](https://github.com/shipsy/stockone-neo)
   ```
2. Navigate to the project directory:
   ```
   cd stockone-neo
   ```
3. Navigate to the project directory:
   ```
   cp .sample.env .env
   ```
4. Docker Deployment for dev
   ```
   docker-compose up -d --build
   ```

### Configuration
- Edit `.env` to modify the configuration as per your requirements.
- Configure the database settings in `.env` if you're using a database system other than SQLite.

## Usage
After starting the server, navigate to `http://localhost:8081` in your web browser to access the WMS interface.

## Contributing
Contributions are welcome! Please read our `CONTRIBUTING.md` for details on our code of conduct and the process for submitting pull requests.

## Contact
For any queries or support, please contact <NAME_EMAIL>.
