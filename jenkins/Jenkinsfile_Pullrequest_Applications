import groovy.json.JsonSlurper

def buildNumber = env.BUILD_NUMBER as int
if (buildNumber > 1) milestone(buildNumber - 1)
milestone(buildNumber)
def PULL_REQUEST = env.CHANGE_ID

def epochTimeInSeconds = System.currentTimeMillis().toInteger()

Boolean stringParser(String inputString) {
    list = ['main','Hot','Major', 'M1', 'Reliance']
    branch =  inputString.split("_")[0]
    branch_check = (branch in list)
    return branch_check
}

Boolean releaseCheck(String inputString) {
    list = ['main']
    branch =  inputString.split("_")[0]
    branch_check = (branch in list)
    return branch_check
}

String getissueID(branchName) {
    try {
        def parts = branchName.split(/[-_]/)
        def taskId = parts[1] + "-" + parts[2]
        return taskId
    } catch (Exception e) {
        return null
    }
}

Boolean checkLabel(currentLabels) {
    list = ['Open', 'in progress', 'tech review', 'product review']
    for (label in currentLabels) {
        if (label in list) {
            return true
        }
    }
    return false

}

Boolean checke2eLabel(currentLabels) {
    list = ['RunE2E']
    for (label in currentLabels) {
        if (label in list) {
            return true
        }
    }
    return false

}

Boolean checkSonarLabel(currentLabels) {
    list = ['Sonar Review']
    for (label in currentLabels) {
        if (label in list) {
            return true
        }
    }
    return false

}

Boolean checkLabelback(currentLabels) {
    list = ['tech review', 'product review']
    for (label in currentLabels) {
        if (label in list) {
            return true
        }
    }
    return false

}

String getTestBranch(release_check) {
    branch = 'Major'
    if (release_check) {
        BRANCH = 'main'
    }
    return branch
}

def getMappingValue(description, search_key) {
    def result = null
    if (!description) {
        echo "Pull Request body is empty or not set."
        list = ['API Testing','E2E Testing']
        if (search_key in list) {
            return "main"
        }
        return null
    }

    def lines = description.readLines()  // Convert string to a list of lines
    for (line in lines) {
        echo "Processing line: ${line}" 
        if (line.contains("**${search_key}**")) {
            def key = line.split("\\|")[1].trim().replace("**", "")
            def value = line.split("\\|")[2].trim()
            echo "Key: ${key}, Value: ${value}"
            result = value
            break
        }
    }
    return result
}

def getJunitResults(executionTime) {
    // Archive JUnit test results
    try {
        archiveArtifacts artifacts: 'results/*.xml', allowEmptyArchive: true
        def testResults = junit testResults: 'results/*.xml'
        def totalTests = testResults.getTotalCount()
        def totalFailures = testResults.getFailCount()
        def totalPassed = testResults.getTotalCount() - testResults.getFailCount() - testResults.getSkipCount()
        def totalSkipped = testResults.getSkipCount()
        def minutes = executionTime.toInteger() / (1000 * 60)
        def seconds = (executionTime.toInteger() % (1000 * 60)) / 1000
        formattedExecutionTime = ""
        if (minutes > 1) {
            formattedExecutionTime = "${minutes.toInteger()}m ${seconds}s"
        } else {
            formattedExecutionTime = "${seconds}s"
        }
        return [
            totalTests: totalTests,
            totalFailures: totalFailures,
            totalPassed: totalPassed,
            totalSkipped: totalSkipped,
            formattedExecutionTime: formattedExecutionTime,
            result_color: totalFailures > 0 ? '#FF0000' : '#2eb886',
        ]
    } catch (Exception e) {
        echo "Error occurred while processing test results: ${e.getMessage()}"
        return [
            totalTests: 0,
            totalFailures: 0,
            totalPassed: 0,
            totalSkipped: 0,
            formattedExecutionTime: 0,
            result_color: '#FF0000',
        ]
    }
}

Boolean runE2ETest(serviceName,  configFileName) {
    try {
        sh "docker-compose -f docker-compose.yml exec -T ${serviceName} npx playwright test --config=${configFileName}"
    } catch (e) {
        // Print the error message if the test fails
        echo "${e} failed"
    }
    return true
}


def BRANCH_CHECK = stringParser(pullRequest.base)
def RELEASE_CHECK = releaseCheck(pullRequest.base)
def LABEL_CHECK = checkLabel(pullRequest.labels)
def E2E_LABEL_CHECK = checke2eLabel(pullRequest.labels)
def SONAR_CHECK = checkSonarLabel(pullRequest.labels)
def LABEL_CHECK_BACK = checkLabelback(pullRequest.labels)
def TASK_ID = getissueID(pullRequest.headRef)
def REPO_NAME = "stockone-neo"
def FAIL_STATUS = false
def DEVREV_LINKED = false
def BACKEND_URL = "https://neo${PULL_REQUEST}.pc.stockone.com/"
def API_TEST_BRANCH = getMappingValue(pullRequest.body, 'API Testing')
def E2E_TEST_BRANCH = getMappingValue(pullRequest.body, 'E2E Testing')
def FRONTEND_URL = getMappingValue(pullRequest.body, 'Frontend')
def MOBILE_URL = getMappingValue(pullRequest.body, 'Mobile')
def PACKING_URL = getMappingValue(pullRequest.body, 'Packing')
def SONARQUBE_URL = "https://sonar.shipsy.io"
def DOCKER_IMAGE_NAME = "guvvalaganesh487/sonarqube-devrev:latest"


pipeline {
    agent none
    environment {
        NEO_CICD_MACHINE = credentials("_NEO_CICD_MACHINE")
        NEO_CICD_USER = credentials("_NEO_CICD_USER")
        NEO_CICD_DB_HOST = credentials("_NEO_CICD_DB_HOST")
        NEO_CICD_DB_USER = credentials("NEO_CICD_DB_USER")
        NEO_CICD_DB_PASS = credentials("NEO_CICD_DB_PASS")
        GIT_HUB_TOKEN = credentials("GIT_HUB_TOKEN")

        //devrev
        DEVREV_TOKEN = credentials("DEV_REV_TOKEN")

        // clickup
        CLICKUP_TOKEN = credentials("CLICKUP_TOKEN")
        CLICKUP_CUSTOM_FIELD_ID = credentials("CLICKUP_CUSTOM_FIELD_ID")
        CLICKUP_REPO_FIELD_ID = credentials("CLICKUP_REPO_FIELD_ID")
        CLICKUP_BACK_STATUS = credentials("CLICKUP_BACK_STATUS")
        CLICKUP_BACK_STATUS_FLAG = credentials("CLICKUP_BACK_STATUS_FLAG")
    
        // clickhouse
        NEO_CLCIKHOUSE_HOST = credentials("NEO_CLCIKHOUSE_HOST")
        NEO_CLICKHOUSE_PORT = credentials("NEO_CLICKHOUSE_PORT")
        NEO_CLICKHOUSE_USER = credentials("NEO_CLICKHOUSE_USER")
        NEO_CLICKHOUSE_PASS = credentials("NEO_CLICKHOUSE_PASS")

        //dast
        NEO_STACKHAWK_KEY = credentials("NEO_STACKHAWK_KEY")

        //codecov
        CODECOV_TOKEN = credentials("CODECOV_TOKEN")
        COMMIT="${env.GIT_COMMIT}"
        CODECOV_URL= "https://codecov.stockone.com"

        //sentry
        SENTRY_URL = credentials("NEO_SENTRY_URL")
        SENTRY_TOKEN = credentials("SENTRY_TOKEN")


        EMAIL_HOST_PASSWORD = credentials("EMAIL_HOST_PASSWORD")

        //e2e tests
        E2E_EXECUTIONTIME = ''

        //sonarqube
        SONARQUBE_TOKEN = credentials("SONARQUBE_TOKEN")
        NEO_SONAR_PROJECT_KEY = credentials("NEO_SONAR_PROJECT_KEY")

    }

    stages {
        stage ('Pull Request Details') {
            agent {
                label "slave1"
            }
            steps {
                script {
                    if (env.CHANGE_ID) {
                        echo "This PR is for the ${pullRequest.headRef} branch"
                        echo "The PR is over the  ${pullRequest.base} branch."
                        echo "The PR request  ${PULL_REQUEST} "
                        echo "Branch check ${BRANCH_CHECK}"
                        echo "Devrev IssueID ${TASK_ID}"
                        echo "Label Check ${LABEL_CHECK}"
                        echo "pull request body ${pullRequest.body}"
                        echo "API Testing branch ${API_TEST_BRANCH}"
                        echo "E2E Testing branch ${E2E_TEST_BRANCH}"
                        echo "FrontEnd Testing URL ${FRONTEND_URL}"
                        echo "Backend Testing URL ${BACKEND_URL}"
                        echo "Mobile Testing URL ${MOBILE_URL}"
                        echo "Packing Testing URL ${PACKING_URL}"

                        // devrev link
                        for (comment in pullRequest.comments) {
                            if ("Devrev Issue: https://app.devrev.ai/shipsy/works/${TASK_ID}" in comment.body){
                                DEVREV_LINKED=true
                            }
                        }

                        if (DEVREV_LINKED) {
                            echo "already linked"
                        } else {
                            pullRequest.comment("Devrev Issue: https://app.devrev.ai/shipsy/works/${TASK_ID}")
                        }
                    }
                }
            }
        }

        stage('Deployment') {
            agent {
                label "slave1"
            }
            when {
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK
                } 
            }
            steps{
                script {
                    if (env.CHANGE_ID) {
                        echo "This PR is for the ${pullRequest.headRef} branch"
                        echo "The PR is over the  ${pullRequest.base} branch."
                        echo "The PR request  ${PULL_REQUEST} "
                    
                        try{
                            // preparing for deployment
                            sh "sudo sh ./scripts/pullrequest_deploy.sh \
                                ${NEO_CICD_MACHINE} ${NEO_CICD_USER} \
                                ${NEO_CICD_DB_HOST} ${NEO_CICD_DB_USER} ${NEO_CICD_DB_PASS} \
                                ${PULL_REQUEST} ${GIT_HUB_TOKEN} \
                                ${NEO_CLCIKHOUSE_HOST} ${NEO_CLICKHOUSE_PORT} ${NEO_CLICKHOUSE_USER} ${NEO_CLICKHOUSE_PASS} ${SENTRY_URL} ${EMAIL_HOST_PASSWORD}"

                            //building pullrequest application
                            sh "ssh ${NEO_CICD_USER}@${NEO_CICD_MACHINE} \
                                'cd pull${PULL_REQUEST} ; \
                                export PULL=${PULL_REQUEST} ; \
                                docker-compose config ; \
                                docker-compose up --build -d; \
                                docker-compose exec -T api python manage.py initial_creation; \
                                docker-compose exec -T api python manage.py clear_redis' \
                            "

                            // //building pullrequest angular ui
                            // sh "ssh ${NEO_CICD_USER}@${NEO_CICD_MACHINE} \
                            //     'cd pull${PULL_REQUEST}/stockone-neo-angular ; \
                            //     docker-compose -f pullrequest.yml config ; \
                            //     docker-compose -f pullrequest.yml up --build -d' \
                            // "

                            //updating pullquest deployment url back to github
                            pullRequest.comment("Deployed URL https://neo${PULL_REQUEST}.pc.stockone.com")

                            //updating pullrequest deployment url back to clickup
                            sh "echo ${TASK_ID}"
                            if (TASK_ID!=null){
                                sh "chmod +x scripts/pullrequest_devrev.sh"
                                String pullrequestTitle = sh(script: "scripts/pullrequest_devrev.sh ${TASK_ID} ${DEVREV_TOKEN} https://neo${PULL_REQUEST}.pc.stockone.com ${PULL_REQUEST} ${GIT_HUB_TOKEN}", returnStdout: true).trim()
                                echo "pullTitle ${pullrequestTitle}"
                            }
                        } catch (e) {
                            // fail the build if an exception is thrown
                            currentBuild.result = "FAILED"
                            pullRequest.comment('DEPLOYEMENT FAILED')
                            throw e
                        } finally {
                                // Post build steps here
                                /* Success or failure, always run post build steps */
                                // send email
                                // publish test results etc etc
                        }  
                    }   
                }
            }
        }

        stage('Testing') {
            parallel {
                stage('PyTest') {
                    when {
                        beforeAgent true
                        expression {
                            // Execute the stage if the pull request has 'review' label
                            return BRANCH_CHECK && LABEL_CHECK 
                        }
                    }
                    agent {
                        label "slave1"
                    }
                    steps {
                        script {
                            echo "Pytest"
                            try {
                                def PYTEST_RESULTS = sh (
                                    script: "ssh ${NEO_CICD_USER}@${NEO_CICD_MACHINE} 'cd pull${PULL_REQUEST} ; export PULL=${PULL_REQUEST} ; docker-compose exec -T api pytest --reuse-db --cov --cov-report=xml -q --disable-warnings --junit-prefix=stockone-wms.wms --junitxml=pytest_report.xml -n 4'",
                                    returnStdout: true
                                )
                                echo "Pytest Results: ${PYTEST_RESULTS}"
                                pullRequest.comment("PyTest Results ${PYTEST_RESULTS}")
                            } catch (Exception e) {
                                FAIL_STATUS=true
                                echo "Pytest failed"
                            }
                        }
                    }
                }
                stage('ApiTest') {
                    when {
                        beforeAgent true
                        expression {
                            return BRANCH_CHECK && LABEL_CHECK
                        }
                    }
                    agent {
                        label "slave1"
                    }
                    steps {
                        script {
                            sh 'pwd'
                            sh "mkdir -p stockone-neo-postman"
                            dir('stockone-neo-postman'){
                                git branch: "${API_TEST_BRANCH}" , credentialsId: 'RAPID-CICD', url: 'https://github.com/shipsy/stockone-neo-postman.git'
                                sh 'pwd'
                                sh 'ls -lhrt'
                                sh "sed -i 's|https://neo-backend.stockone.com|https://neo${PULL_REQUEST}.pc.stockone.com|g' inbound_pr_env.json"
                                sh "sed -i 's|https://neo-backend.stockone.com|https://neo${PULL_REQUEST}.pc.stockone.com|g' outbound_pr_env.json"
                                sh "sed -i 's|https://neo-backend.stockone.com|https://neo${PULL_REQUEST}.pc.stockone.com|g' production_and_inventory_pr_env.json"
                                sh '''
                                        node --version
                                        npm --version
                                        newman --version
                                    '''
                                parallel (
                                    "Inbound" : {
                                        try {
                                            sh 'newman run "./PostmanCollections/inbound.json" --reporters=cli,htmlextra --working-dir . --reporter-htmlextra-export "inbound.html" -e inbound_pr_env.json --silent'
                                        } catch (e) {
                                            FAIL_STATUS=true
                                            echo "inbound failed"
                                        }
                                    },
                                    "Outbound" : {
                                        try {
                                            sh 'newman run "./PostmanCollections/outbound.json" --reporters=cli,htmlextra --reporter-htmlextra-export "outbound.html" -e outbound_pr_env.json --silent'
                                        } catch (e) {
                                            FAIL_STATUS=true
                                            echo "outbound failed"
                                        }
                                    },
                                    "Inventory" : {
                                        try {
                                            sh 'newman run "./PostmanCollections/production_and_inventory.json" --reporters=cli,htmlextra --reporter-htmlextra-export "production_inventory.html" -e production_and_inventory_pr_env.json --silent'
                                        } catch (e) {
                                            FAIL_STATUS=true
                                            echo "Inventory failed"
                                        }
                                    }
                                )

                                sh '''
                                npm install
                                node upload_file.js inbound.html outbound.html production_inventory.html
                                '''
                                sh "chmod +x publish_results.sh"
                                def testresult = sh script: "./publish_results.sh", returnStdout: true
                                pullRequest.comment("${testresult}")
                            }
                        }
                    }
                }
                stage('E2ETest') {
                    when {
                        beforeAgent true
                        expression {
                            return BRANCH_CHECK && LABEL_CHECK && pullRequest.body && E2E_LABEL_CHECK
                        }
                    }
                    agent {
                        label "e2e-node"
                    }
                    steps {
                        script {
                            sh 'pwd'
                            sh "mkdir -p neo-e2e-${PULL_REQUEST}-${buildNumber}"
                            dir("neo-e2e-${PULL_REQUEST}-${buildNumber}"){
                                try{
                                    git branch: "${E2E_TEST_BRANCH}" , credentialsId: 'RapidCI', url: 'https://github.com/shipsy/stockone-neo-playwright.git'
                                    sh 'pwd'
                                    sh 'ls -lhrt'

                                    //setting up the env
                                    sh "sed -i 's|WEB_URL=.*|WEB_URL=${FRONTEND_URL}|' .env"
                                    sh "sed -i 's|BACKEND_URL=.*|BACKEND_URL=${BACKEND_URL}|' .env"
                                    sh "sed -i 's|MOBILE_URL=.*|MOBILE_URL=${MOBILE_URL}|' .env"
                                    sh "sed -i 's|PACKING_URL=.*|PACKING_URL=${PACKING_URL}|' .env"
                                    sh "echo WORKERS=3 >> .env"

                                    sh "docker-compose up --build -d"
                                    sh "docker-compose -f docker-compose.yml exec -T neo-e2e npm install --save-dev @playwright/test"
                                    sh "docker-compose -f docker-compose.yml exec -T neo-e2e npx playwright install chromium"
                                    echo "Setup"

                                    def startTime = new Date().getTime()
                                    runE2ETest("neo-e2e",  "auth.config.ts")
                                    parallel (
                                        "Retail" : {
                                            runE2ETest("neo-e2e",  "retail.config.ts")
                                        },
                                        "Pharma" : {
                                            runE2ETest("neo-e2e",  "pharma.config.ts")
                                        },
                                        "Pharma Retail" : {
                                            runE2ETest("neo-e2e",  "pharmaRetail.config.ts")
                                        },
                                        "Pharmacy" : {
                                            runE2ETest("neo-e2e",  "pharmacy.config.ts")
                                        }
                                    )
                                    sh "docker-compose -f docker-compose.yml exec -T neo-e2e npm run prepare-report"
                                    def testresult = sh script: "cat tests/utils/test-report-summary.html", returnStdout: true
                                    pullRequest.comment("${testresult}")
                                }
                                catch (e) {
                                    echo "E2E Test failed"
                                    FAIL_STATUS=true
                                    throw e
                                }
                                finally {
                                    sh "docker-compose -f docker-compose.yml down"
                                }
                            }
                        }
                    }
                }
            }
        }

        stage('Coverage') {
            when {
                beforeAgent true
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK 
                }
            }
            agent {
                label "slave1"
            }
            steps {
                script {
                    echo "Coverage"
                    //restarting pullrequest application
                    sh "ssh ${NEO_CICD_USER}@${NEO_CICD_MACHINE} \
                        'cd pull${PULL_REQUEST} ; \
                        export PULL=${PULL_REQUEST} ; \
                        docker-compose config ; \
                        docker-compose down; \
                        docker-compose up --build -d; \
                        sleep 10; \
                        docker-compose exec -T api coverage combine; \
                        docker-compose exec -T api coverage xml -o combined-coverage.xml' \
                    "
                    echo "Coverage Combined"
                }
            }
        }

        stage('Publish Pytest Results') {
            when {
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK
                } 
            }
            agent {
                label "slave1"
            }
            steps {
                script{
                    sh "scp ${NEO_CICD_USER}@${NEO_CICD_MACHINE}:/home/<USER>/pull${PULL_REQUEST}/stockone-wms/wms/pytest_report.xml ./stockone-wms/wms/"
                    junit 'stockone-wms/wms/pytest_report.xml'
                }
               
            }
        }

        stage('SonarQube Analysis') {
            agent {
                label "slave1"
            }
            steps{
                script {
                    def scannerHome = tool 'sonarqube';

                    if (BRANCH_CHECK && LABEL_CHECK) {
                        // get the coverage report from pytest to this path
                        sh "scp ${NEO_CICD_USER}@${NEO_CICD_MACHINE}:/home/<USER>/pull${PULL_REQUEST}/stockone-wms/wms/coverage.xml ./stockone-wms/wms/"
                        // sh "scp ${NEO_CICD_USER}@${NEO_CICD_MACHINE}:/home/<USER>/pull${PULL_REQUEST}/stockone-wms/wms/pytest_report.xml ./stockone-wms/wms/"
                        sh "sed -i 's/\\/application/stockone-wms\\/wms/g' ./stockone-wms/wms/coverage.xml"

                    } else {
                        echo 'No coverage report'
                    }

                    if (SONAR_CHECK) {
                        sh "ls -lart ./*"
                        sh "pwd"
                        withSonarQubeEnv() {
                            sh "${scannerHome}/bin/sonar-scanner \
                            -Dsonar.sources=. \
                            -Dsonar.scm.provider=git \
                            -Dsonar.python.version=3.8 \
                            -Dsonar.pullrequest.key=${PULL_REQUEST}\
                            -Dsonar.pullrequest.branch=${pullRequest.headRef}\
                            -Dsonar.pullrequest.base=${pullRequest.base}"
                        }
                    }

                    if (['Major', 'Hot', 'main', 'M1', 'Reliance'].contains(pullRequest.headRef)) {
                        // Branch-based analysis using pullRequest.headRef directly as branch name
                        withSonarQubeEnv() {
                            sh "${scannerHome}/bin/sonar-scanner \
                            -Dsonar.sources=. \
                            -Dsonar.scm.provider=git \
                            -Dsonar.python.version=3.8 \
                            -Dsonar.sourceEncoding=UTF-8 \
                            -Dsonar.branch.name=${pullRequest.headRef}"
                        }
                    }
                }
            }
        }

        stage('DevRev Updation') {
            agent {
                label "slave1"
            }
            steps {
                script {
                    try {
                        // Run the Docker container with necessary arguments
                        sh """
                        docker run --rm \
                            ${DOCKER_IMAGE_NAME} \
                            --sonarqube_url ${SONARQUBE_URL} \
                            --sonar_token ${SONARQUBE_TOKEN} \
                            --devrev_token ${DEVREV_TOKEN} \
                            --project_key ${NEO_SONAR_PROJECT_KEY} \
                            --pr_number ${PULL_REQUEST} \
                            --devrev_issue_id ${TASK_ID}
                        """
                    } catch (Exception e) {
                        echo "Error during DevRev updation: ${e.message}"
                    }
                }
            }
        }
        
        stage("CodeCov"){
            agent {
                label "slave1"
            }
            when {
                beforeAgent true
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK 
                }
            }
            steps{
                script {
                    // get the coverage report from pytest to this path
                    sh "scp ${NEO_CICD_USER}@${NEO_CICD_MACHINE}:/home/<USER>/pull${PULL_REQUEST}/stockone-wms/wms/combined-coverage.xml ./stockone-wms/wms/"
                    sh "sed -i 's/\\/application/stockone-wms\\/wms/g' ./stockone-wms/wms/combined-coverage.xml"
                    sh "curl -Os https://uploader.codecov.io/latest/linux/codecov"
                    sh "chmod +x codecov ./codecov"
                    sh "./codecov --verbose --url=${CODECOV_URL} --token ${CODECOV_TOKEN} --file stockone-wms/wms/combined-coverage.xml "
                }
            }
        }

        stage('Sentry Checks') {
            agent {
                label "slave1"
            }
            when {
                beforeAgent true
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK
                }
            }
            steps {
                script {
                    echo "Sentry Checks"
                    sh "chmod +x scripts/pullrequest_sentry.sh"
                    def sentryresult = sh script: "scripts/pullrequest_sentry.sh ${SENTRY_TOKEN} ${PULL_REQUEST}", returnStdout: true
                    pullRequest.comment("${sentryresult}")
                }
            }
        }

        stage('stackhawk') {
            when {
                beforeAgent true
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK & RELEASE_CHECK
                }
            }
            agent {
                label "slave1"
            }
            steps {
                script {
                    sh 'pwd'
                    sh "mkdir -p neo-stackhawk"
                     dir('neo-stackhawk'){
                            git branch: 'main' , credentialsId: 'RapidCI', url: 'https://github.com/shipsy/neo-stackhawk.git'
                            sh 'pwd'
                            sh 'ls -lhrt'
                            
                            //preparing for dast
                            sh "chmod +x auth_login.sh"
                            sh "sudo sh auth_login.sh \
                                ${NEO_STACKHAWK_KEY} https://neo${PULL_REQUEST}.pc.stockone.com \
                                https://neo${PULL_REQUEST}.pc.stockone.com/o/token/"

                            dir('app'){
                                sh 'docker-compose up --build'
                            }
                        }
                    
                }
            }
        }

        stage('slack-release-request') {
            when {
                beforeAgent true
                expression {
                    // Execute the stage if the pull request has 'review' label
                    return BRANCH_CHECK && LABEL_CHECK && RELEASE_CHECK
                }
            }
            agent {
                label "slave1"
            }
            steps {
                script {
                    sh "chmod +x scripts/publish_release.sh"
                    sh "sudo sh scripts/publish_release.sh ${GIT_HUB_TOKEN} ${pullRequest.base}"
                }
            }
        }

    }
    
    post {
        failure {
            node(label: 'slave1') {
                script {
                    pullRequest.comment('BUILD FAILED')
                }
            }
        }
        always {
            node(label: 'slave1') {
                script {
                    cleanWs()
                }
            }
        }
    }
}
