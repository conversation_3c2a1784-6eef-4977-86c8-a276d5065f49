version: "3.8"

services:

  redis:
    image: "redis:alpine"
    hostname: neo-redis${PULL}
    command: redis-server --requirepass JguvU^FP4V
    healthcheck:
      test: "redis-cli -h localhost -a JguvU^FP4V ping"
      interval: 10s
      retries: 5
      timeout: 5s
    volumes:
      - redis_data:/data

  api:
    hostname: neo${PULL}
    build: ./stockone-wms/
    command: python -m coverage run -p --data-file=./.coverage.custom -m gunicorn --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:80 wms.asgi:application
    volumes:
      - static_volume:/application/static
      - ./stockone-wms/wms/:/application/
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'true'
    stdin_open: true
    tty: true
    healthcheck:
        test: curl --fail http://api:80/admin/ || exit 1
        interval: 30s
        retries: 30
        start_period: 20s
        timeout: 10s
    depends_on:
      redis:
        condition: service_healthy

  celery:
    build: ./stockone-wms/
    command: ['celery', '-A', 'wms.celery', 'worker', '-l', 'info', '-B', '--concurrency=4']
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    depends_on:
      api:
        condition: service_healthy
        
  flower:
    build: ./stockone-wms/
    command: ['celery', '-A', 'wms.celery', 'flower', '--url-prefix=flower']
    env_file:
      - .env
    environment:
       ENTRYPOINT_FLAG: 'false'
    depends_on:
      - redis
      - celery

volumes:
  static_volume:
  redis_data: