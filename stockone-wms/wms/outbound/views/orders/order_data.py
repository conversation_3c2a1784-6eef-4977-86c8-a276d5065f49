#package imports
import pytz
from json import loads
from functools import reduce
from operator import or_
from dateutil import parser
from datetime import datetime, timezone
from collections import defaultdict

#django imports
from django.db.models import Sum, Max, Q, F, Count
from django.http import JsonResponse

#wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger, DEFAULT_DATETIME_FORMAT, DATATABLE_DATETIME_FORMAT

#core operations imports
from core_operations.views.common.main import (
    get_decimal_value, truncate_float, get_pagination_info, get_user_time_zone,
    get_local_date_known_timezone, get_paging_data, WMSListView,
    get_multiple_misc_values, get_sla_misc_details, get_ship_status_msg
)

#outbound imports
from outbound.models import (
    OrderDetail, StagingInfo, SellerOrderSummary,
    PaymentSummary, OrderCharges, CustomerOrderSummary,
    Picklist, CustomerMaster, Order, OrderFields, StockAllocation
)
from outbound.views.orders.utils import get_order_header_status

#production imports
from production.views.bom.bom import get_combo_relation_qty_dict

#inventory imports
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

log = init_logger('logs/order_data.log')

SHIPMENT_COMPLETED = 'shipment completed'
RETURN_COMPLETED = 'returns completed'
ORDER_STATUS_MAPPING = {
    'open': {'status': 1},
    'in_progress': {'status': 0},
    'allocated': {'status': 20},
    'completed': {'status': 5},
    'cancelled': {'cancelled_quantity__gt': 0},
    SHIPMENT_COMPLETED : {'status' : 2},
    RETURN_COMPLETED : {'status' : 6}
}

class OrderDetailsMixin:
    def __init__(self, request_user, warehouse, request, request_data, extra_params = None):
        self.user = request_user
        self.warehouse = warehouse
        self.request = request
        self.request_data = request_data
        self.extra_params = extra_params or {}
        self.timezone = get_user_time_zone(self.warehouse) or 'Asia/Calcutta'
        self.is_count = False
        self.orders_count = 0

    def get_order_summary(self, is_count=False):
        '''
        Get order summary details
        based on is_count flag it return count or order details
        '''
        paging_details = {}
        order_details_list, self.errors = [], []
        status = self.extra_params.get('status') if self.extra_params.get('status', "") else self.request_data.get('status', 'all')

        self.datetime_format = DEFAULT_DATETIME_FORMAT
        if self.extra_params.get('call_type') == 'datatable':
            self.datetime_format = DATATABLE_DATETIME_FORMAT

        self.is_count = is_count
        misc_types = ['order_hold_options', 'price_application', 'stock_allocate', 'enable_order_view_v2', 'order_approval', 'suspend_cancel_orders']
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        self.pricing_at_invoice, self.enable_order_view_v2_config = False, False
        if self.misc_dict.get('price_application') == 'at_invoice_creation':
            self.pricing_at_invoice = True
        if self.misc_dict.get('enable_order_view_v2') in ['true', True] :
            self.enable_order_view_v2_config = True

        order_references_list, page_info = self.get_list_of_order_references()
        if self.errors:
            return order_details_list, paging_details, self.errors
        if self.is_count:
            return self.orders_count, paging_details, self.errors
        order_details = self.get_order_details(order_references_list, status)
        paging_details = get_paging_data(self.request, page_info, len(order_references_list))

        # Organize order details in sorted order
        for order_reference in order_references_list:
            if order_reference in order_details:
                order_details[order_reference]['skus_count'] = len(order_details[order_reference].get('skus', []))
                order_details[order_reference].pop('skus')
                order_details_list.append(order_details[order_reference])

        return order_details_list, paging_details, self.errors

    def get_list_of_order_references(self):
        """
        Retrieves a list of order references based on applied filters, pagination, and status.
        Returns:
            tuple: A tuple containing the list of order references and pagination information.
        """

        filters, or_filters, order_by, order_field = self.get_order_summary_filters()
        order_references_list = []

        #get page details
        req_page_info = self.get_pagination()
        page_info = get_pagination_info(req_page_info)
        start_index = page_info.get('start_index', 0)
        end_index = page_info.get('end_index', 10)
        status_text = self.extra_params.get('status', 'all')
        if status_text not in ORDER_STATUS_MAPPING and status_text not in ['all', 'on_hold']:
            return order_references_list, page_info
        order_references_list = []
        if self.request_data.get('status'):
            status_text = self.request_data.get('status')

        if self.enable_order_view_v2_config:
            order_references_list = self.get_list_of_order_references_beta(status_text, filters, order_by, order_field, or_filters, start_index, end_index)
        else:
            order_references_list = self.get_orders_status_filters(status_text, filters, or_filters, start_index, end_index, order_field, order_by)

        return order_references_list, page_info

    def get_orders_status_filters(self, status_text, filters, or_filters, start_index, end_index, order_field, order_by):
        """
        Retrieves a filtered list of order references based on the provided status, filters, and sorting parameters.
        Args:
            status_text (str): The status of the orders to filter (e.g., 'on_hold', 'allocated').
            filters (dict): A dictionary of filters to apply to the query.
            or_filters (Q): Additional OR conditions to apply to the query.
            start_index (int): The starting index for pagination.
            end_index (int): The ending index for pagination.
            order_field (str): The field to use for ordering.
            order_by (str): The ordering direction or field.
        Returns:
            list: A list of order references matching the specified filters and conditions.
        """
        exclude_filters = {}
        if status_text == 'on_hold':
            order_filters = {'warehouse': self.warehouse}
            if filters.get('order_reference'):
                order_filters['order_reference'] = filters.pop('order_reference')
            # Fetching orders which are on hold
            order_references_list = list(Order.objects.filter(Q(status=6) | Q(hold_date__gt=datetime.now(timezone.utc)), **order_filters).values_list('order_reference', flat=True))
            filters.pop('status', None)
            filters['order_reference__in'] = order_references_list
        elif status_text == 'allocated':
            order_filters = {'warehouse_id': self.warehouse.id, 'status': 1, 'reference_model' : 'OrderDetail'}
            if filters.get('order_reference'):
                order_filters['reference_number'] = filters.pop('order_reference')
            order_references_list = list(StockAllocation.objects.filter(**order_filters).values_list('reference_number', flat=True))
            filters['order_reference__in'] = order_references_list
            filters.pop('status')
        elif self.extra_params.get('exclude_hold_orders'):
            order_references_list = list(Order.objects.filter(Q(status=6) | Q(hold_date__gt=datetime.now(timezone.utc), warehouse=self.warehouse)).values_list('order_reference', flat=True))
            exclude_filters = {'order_reference__in': order_references_list}
        
        if self.extra_params.get('full_open'):
            order_filters = {'user': self.warehouse.id}
            if filters.get('order_reference'):
                order_filters['order_reference'] = filters.pop('order_reference')
            if filters.get('order_reference__icontains'):
                order_filters['order_reference__icontains'] = filters.pop('order_reference__icontains')
            order_references_list = list(OrderDetail.objects.filter(**order_filters).exclude(**exclude_filters).values_list('order_reference').annotate(open_qty=Sum('quantity'), total_qty=Sum('original_quantity'), total_cancelled_qty=Sum('cancelled_quantity')).filter(total_qty=F('open_qty') + F('total_cancelled_qty')).values_list('order_reference', flat=True))
            filters['order_reference__in'] = order_references_list
            filters['status'] = 1

        status = filters.get('status', '')
        if (status and status in [3]):
            filters.pop('status')
            orders_list = OrderDetail.objects.filter(or_filters, **filters).values('order_reference').annotate(distinct_status_count=Count('status', distinct=True), order_date=Max(order_field)).filter(distinct_status_count=1).values_list('order_reference', flat=True)
            orders = OrderDetail.objects.filter(status=status, order_reference__in=orders_list).values('order_reference').annotate(order_date=Max(order_field))
        else:
            orders = OrderDetail.objects.filter(or_filters, **filters).exclude(**exclude_filters).values(
                'order_reference').annotate(order_date=Max(order_field))
        if self.is_count:
            self.orders_count = orders.count()
        if 'source_order_date' not in order_by:
            orders = orders.order_by(order_by)
            order_references_list = list(orders[start_index:end_index].values_list('order_reference', flat=True))
        else:
            order_references_list = list(orders.values_list('order_reference', flat=True))
            order_by = order_by.replace('source_order_date', 'order_date')
            filters = {'order_reference__in': order_references_list, 'warehouse_id': self.warehouse.id}
            order_references_list = list(Order.objects.filter(**filters).order_by(order_by)[start_index:end_index].values_list('order_reference', flat=True))
        return order_references_list

    def get_list_of_order_references_beta(self, status_text, filters, order_by_text, order_field, or_filters, start_index, end_index):
        """
        Retrieves a list of order references based on the provided filters, status, and sorting options.
        Args:
            status_text (str): The status of the orders to filter (e.g., 'open', 'completed').
            filters (dict): Additional filters to apply to the orders.
            order_by_text (str): The field to order the results by.
            order_field (str): The field used for annotating order dates.
            or_filters (Q): OR conditions to apply to the query.
            start_index (int): The starting index for pagination.
            end_index (int): The ending index for pagination.
        Returns:
            list: A list of order references matching the specified criteria.
        """

        ORDER_STATUS_MAPPING_NEW = {
            'open': {'open_quantity__gt': 0},
            'allocated': {'allocated_quantity__gt': 0},
            'completed': {'invoiced_quantity__gt': 0},
            'cancelled': {'cancelled_quantity__gt': 0},
            'on_hold': {'status': 6}
        }
        ORDER_HEADER_LEVEL_FILTERS = {
            "order_reference": "order_reference",
            "order_type": "order_type",
            "customer_reference": "customer__customer_reference",
            "creation_date__gte": "creation_date__gte",
            "updation_date__gte": "updation_date__gte",
            "customer_name": "customer__name",
        }
        header_level_order_by = self.get_beta_order_by(order_by_text, order_field)

        order_filters = {'warehouse_id': self.warehouse.id}
        status_filters = ORDER_STATUS_MAPPING_NEW.get(status_text, {})
        order_filters.update(status_filters)

        for key,value in ORDER_HEADER_LEVEL_FILTERS.items():
            if key in filters:
                order_filters[value] = filters.get(key)
                filters.pop(key)

        for key in ["user", "status"]:
            filters.pop(key, None)

        exclude_dict = Q()
        if self.extra_params.get('exclude_hold_orders'):
            exclude_dict = Q(status=6) | Q(hold_date__gt=datetime.now(timezone.utc))
        if self.extra_params.get('full_open'):
            order_filters['quantity'] = F('open_quantity') + F('cancelled_quantity')
        not_equal_condition = Q()
        if status_text == 'in_progress':
            not_equal_condition = ~Q(invoiced_quantity= (F('quantity') - F('open_quantity') - F('cancelled_quantity')))

        if filters or or_filters:
            order_references = list(Order.objects.filter(not_equal_condition, **order_filters).exclude(exclude_dict).values_list('order_reference', flat=True))
            filters['order_reference__in'] = order_references
            filters['user'] = self.warehouse.id
            orders = OrderDetail.objects.filter(or_filters, **filters).values('order_reference').annotate(order_date=Max(order_field)).order_by(order_by_text)
            if self.is_count:self.orders_count = orders.count()
            order_references_list = list(orders[start_index:end_index].values_list('order_reference', flat=True))
        else:
            orders = Order.objects.filter(not_equal_condition, **order_filters).exclude(exclude_dict)
            if self.is_count:self.orders_count = orders.count()
            order_references_list = list(orders.order_by(header_level_order_by)[start_index:end_index].values_list('order_reference', flat=True))

        return order_references_list

    def get_beta_order_by(self, order_by_text, order_field):
        """
        Transforms the given order_by field into a corresponding database field 
        and applies sorting based on the presence of a '-' prefix.
        Args:
            order_by (str): The field to order by, optionally prefixed with '-' for descending order.
        Returns:
            str: The transformed and optionally sorted database field name.
        """
        ORDER_BY_FIELDS = {
            "customer_identifier__route__route_id": "customer__route__route_id",
            "customer_identifier__route__name": "customer__route__name",
            "customer_identifier__route__priority": "customer__route__priority",
            "customer_name": "customer__name",
            "order_reference": "order_reference",
            "order_type": "order_type",
            "expiration_date": "expiration_date",
            "hold_date": "hold_date",
            "order_date": "creation_date",
            "source_order_date": "order_date"
        }
        header_level_order_by = 'order_date'
        sort_type = 0
        if order_by_text != '-order_date':
            order_field = order_by_text
        if '-' in order_by_text:
            sort_type = 1
            if '-' in order_by_text:
                order_field = order_by_text.replace('-', '')

        if order_field in ORDER_BY_FIELDS.keys():
            header_level_order_by = ORDER_BY_FIELDS.get(order_field)
        if sort_type:
            header_level_order_by = '-%s' %header_level_order_by
        return header_level_order_by

    def get_order_summary_filters(self):
        '''
        Get orders summary frilters from request
        '''
        filters, or_filters, order_field, order_by, or_filter_list = {}, Q(), 'updation_date', '-order_date', []
        if self.extra_params.get('filters'):
            filters = self.extra_params['filters']
        else:
            filters = self.get_orders_filters_from_request()

        if self.extra_params.get('order_field'):
            order_field = self.extra_params['order_field']
        if self.extra_params.get('order_by'):
            self.extra_params['order_by'] = self.extra_params['order_by'].replace('_and_time','')
            order_by = self.extra_params['order_by']
        order_field = order_field.replace('-', '')

        if self.extra_params.get('or_filters'):
            or_filter_list = self.extra_params['or_filters']
        
        if self.misc_dict.get('suspend_cancel_orders', '') == 'true':
            if 'cancelled_quantity__gt' in filters:
                or_filter_list.append(Q(suspended_quantity__gt=0))
                or_filter_list.append(Q(cancelled_quantity__gt=0))
                filters.pop('cancelled_quantity__gt', None)

        filters['user'] = self.warehouse.id
        or_filters = reduce(or_, or_filter_list) if or_filter_list else Q()
        return filters, or_filters, order_by, order_field

    def get_orders_filters_from_request(self):
        order_summary_filters = {}

        filters_key_mapping = {
            'customer_id': 'customer_id',
            'order_reference': 'order_reference',
            'order_id': 'original_order_id',
            'customer_name': 'customer_name'
        }
        for filter_key, filter_mapped_key in filters_key_mapping.items():
            if self.request_data.get(filter_key):
                order_summary_filters[filter_mapped_key] = self.request_data[filter_key]

        status = self.request_data.get('status')
        if status and ORDER_STATUS_MAPPING.get(status):
            order_summary_filters.update(ORDER_STATUS_MAPPING[status])

        updation_at = self.request_data.get('updated_at_gte', '')
        try:
            if updation_at:
                updation_at = parser.parse(updation_at)
                updation_at = datetime.combine(updation_at , updation_at.time())

                #converting to utc
                ist_datetime = pytz.timezone(self.timezone).localize(updation_at)
                updation_at = ist_datetime.astimezone(pytz.UTC)
                order_summary_filters['updation_date__gte'] = updation_at
        except Exception:
            self.errors.append('Invalid Date Filter Format')

        return order_summary_filters

    def get_pagination(self):
        '''
        Get Pageination details
        '''
        if self.extra_params.get('pagination'):
            return self.extra_params['pagination']
        else:
            return {
                "limit": int(self.request_data.get('limit', 10)),
                "offset": int(self.request_data.get('offset', 0))
            }

    def fetch_order_status(self, status, current_status, order_status, order_reference, order_details, order_dict):
        final_status = ''
        if status != 'all':
            if status not in list(ORDER_STATUS_MAPPING.keys()):
                final_status = get_order_header_status(order_status, current_status_str=current_status)
            else:
                if status == 'open' and (order_details[order_reference]['total_quantity'] != order_details[order_reference]['cancelled_quantity'] + order_details[order_reference]['total_open_quantity']):
                    final_status = 'in_progress'
                else:
                    final_status = status
        else:
            final_status = get_order_header_status(order_status, current_status_str=current_status)
            
        if order_details[order_reference].get('on_hold') or ('manual_order_hold' in self.misc_dict.get('order_hold_options', '') and order_dict.get(order_reference, {}).get('status') == '6'):
            final_status = 'on_hold'
        if self.misc_dict.get('order_approval', '') == 'true' and order_dict.get(order_reference, {}).get('status') == '6':
            final_status = 'on_hold'
        return final_status


    def prepare_order_header_data(self, order, order_dict, sla_data, status):
        shipment_date = None
        ship_status, sla_msg = '', ''

        sla_color_key_dict = {'Red': 'bg-red-500 text-white py-1 px-2 border-round-2xl text-sm', 'Green': 'bg-green-500 text-white py-1 px-2 border-round-2xl text-sm','Yellow': 'bg-yellow-500 text-white py-1 px-2 border-round-2xl text-sm'}
        order_reference = order.get('order_reference', '')
        order['total_quantity'] = order.pop('original_quantity')
        order['total_open_quantity'] = order.pop('quantity')
        order['cancelled_quantity'] = order.pop('cancelled_quantity')
        order['order_display_key'] = order_reference or order.get('original_order_id')
        order['billing_address'] = order.get('billing_address', '') or ''
        order['shipping_address'] = order.get('shipping_address', '') or ''
        order['route_id'] = order.get('route_id', '')
        order['route_name'] = order.get('route_name', '')
        order['route_priority'] = order.get('route_priority', '')
        order['no_of_skus'] = 0
        order['unique_skus'] = set()
        order_date =  get_local_date_known_timezone(self.timezone, order.pop('creation_date'), True)
        order['order_date_and_time'] = order_date.strftime("%d %b, %Y %I:%M %p")
        order['order_date'] = order_date.strftime("%d %b, %Y")
        updation_date =  get_local_date_known_timezone(self.timezone, order.get('updation_date'))
        order['updation_date'] = updation_date
        if order.get('slot_from') :
            order['slot_from'] =  get_local_date_known_timezone(self.timezone, order.get('slot_from'), True).strftime("%d %b, %Y %I:%M %p")
        if order.get('slot_to'):
            order['slot_to'] =  get_local_date_known_timezone(self.timezone, order.get('slot_to'), True).strftime("%d %b, %Y %I:%M %p")
        try:
            shipment_date = order.pop('shipment_date', '')
            shipment_date = get_local_date_known_timezone(self.timezone, shipment_date, send_date=True)
            order['shipment_date'] = shipment_date.strftime("%d %b, %Y %I:%M %p")
        except Exception as e:
            log.info(f"Error in shipment date format {e}")
        order['sla_msg_dynamic_class'] = ''
        order['sla_msg'] = ''
        if shipment_date and sla_data:
            ship_status, sla_msg = get_ship_status_msg(shipment_date, sla_data, self.timezone)
            if status in ['open', 'in_progress', 'allocated']:
                order['sla_msg_dynamic_class'] = sla_color_key_dict.get(ship_status, '')
                order['sla_msg'] = sla_msg
        order['expiration_date'] = order['hold_date'] = ''
        order['source_order_date'] = ''
        if order_dict.get(order_reference, {}).get('expiration_date'):
            order['expiration_date'] = get_local_date_known_timezone(self.timezone, order_dict[order_reference]['expiration_date'], send_date=True).strftime(self.datetime_format)
        if order_dict.get(order_reference, {}).get('hold_date'):
            hold_date = get_local_date_known_timezone(self.timezone, order_dict[order_reference]['hold_date'], send_date=True)
            if 'automated_order_hold' in self.misc_dict.get('order_hold_options', '') and hold_date > datetime.now(pytz.timezone(self.timezone)):
                order['on_hold'] = True
            order['hold_date'] = hold_date.strftime(self.datetime_format)
        if order_dict.get(order_reference, {}).get('order_date'):
            order['source_order_date'] = get_local_date_known_timezone(self.timezone, order_dict[order_reference]['order_date'], send_date=True).strftime(self.datetime_format)

    def get_order_details(self, order_references_list, status):
        """
        Retrieves order details based on the given warehouse, order references list, and status.
        """
        order_details, order_dict, order_unique_zones = {}, {}, defaultdict(set)
        order_objects_list = list(OrderDetail.objects.filter(user=self.warehouse.id, order_reference__in=order_references_list).values(
            'order_reference', 'customer_name', 'customer_id', 'address', 'order_type', 'order_taken_by', 'cancelled_quantity', 'suspended_quantity', 'status', 'state', 'unit_price', 'id', 'sku_code', 'trip_id', 'customer_po_num',
            'original_quantity', 'quantity', 'original_order_id', 'city', 'creation_date', 'updation_date', 'shipment_date','slot_from','slot_to', billing_address=F('json_data__billing_address'),
            shipping_address=F('json_data__shipping_address'), order_taken_by=F('customerordersummary__order_taken_by'), route_id=F('customer_identifier__route__route_id'),
            route_priority=F('customer_identifier__route__priority'), customer_reference=F('customer_identifier__customer_reference'), route_name=F('customer_identifier__route__name'), zone=F('json_data__zone'), cgst_tax=F('customerordersummary__cgst_tax'), 
            sgst_tax=F('customerordersummary__sgst_tax'), igst_tax=F('customerordersummary__igst_tax')))


        order_data = list(Order.objects.filter(order_reference__in=order_references_list, warehouse=self.warehouse)
                                        .values('order_reference', 'status', 'expiration_date', 'hold_date','order_date', 'approval_status'))
        for data in order_data:
            order_dict[data['order_reference']] = data
                
        original_order_ids = {order['original_order_id'] for order in order_objects_list}
        extra_order_fields = get_order_extra_fields(original_order_ids, [self.warehouse.id])
        
        sos_details = {}
        inv_qty_dict = {}
        if status in ['completed', 'all'] and self.pricing_at_invoice:
            sos_details = dict(SellerOrderSummary.objects.filter(order__order_reference__in=order_references_list, order__user=self.warehouse.id).values_list('order_id', 'json_data__unit_price'))
        if status in ['in_progress','completed']:
            inv_qty_dict  = dict(SellerOrderSummary.objects.filter(
                            order__order_reference__in=order_references_list,
                            order__user=self.warehouse.id,
                            order_status_flag='customer_invoices').values_list(
                            'order__order_reference').annotate(inv_qty=Sum('quantity')))
        #allocation details
        allocated_dict = {}
        if self.misc_dict.get('stock_allocate') == 'true':
            filters = {'reference_model': 'OrderDetail', 'reference_number__in': order_references_list, 'status' : 1, 'warehouse_id': self.warehouse.id}
            allocated_dict = dict(StockAllocation.objects.filter(**filters).values_list('reference_number').annotate(Sum('quantity')))
        #Framing order reference and order detail key value mapping
        decimal_limit = get_decimal_value(self.warehouse.id)
        sla_data, color_name_dict, sla_time_dict = get_sla_misc_details(self.warehouse)
        for order in order_objects_list:
            order_reference = order.get('order_reference', '')
            quantity = order.get('quantity', 0) or 0
            original_quantity = order.get('original_quantity', 0) or 0
            unit_price = order.get('unit_price', 0) or 0
            order_status = order.get('status')
            sgst_tax = order.get('sgst_tax', 0) or 0
            cgst_tax = order.get('cgst_tax', 0) or 0
            igst_tax = order.get('igst_tax', 0) or 0
            cancelled_quantity = order.get('cancelled_quantity', 0) or 0
            suspended_quantity = order.get('suspended_quantity', 0) or 0
            tax_percent = 0
            valid_quantity = original_quantity - cancelled_quantity
            if self.warehouse.userprofile.state.lower() == order.get('state').lower():
                # cgst and sgst tax calculation
                tax_percent = cgst_tax + sgst_tax
            else:
                tax_percent = igst_tax
            unit_price = order.get('unit_price', 0) or 0
            if sos_details.get(order.get('id')):
                unit_price = sos_details.get(order.get('id'))
                # fetch invoiced unit_price
                
            order_value = valid_quantity * unit_price
            order_value_with_tax = order_value + (order_value * tax_percent / 100)
            sku_code = order.get('sku_code', '')
            if not order_details.get(order_reference):
                self.prepare_order_header_data(order, order_dict, sla_data, status)
                order['zone'] = str(order.pop('zone') or '')
                order_details[order_reference] = order
                order_details[order_reference]['order_id'] = order_details[order_reference].pop('original_order_id')
                order_details[order_reference]['skus'] = set([sku_code])
                order_details[order_reference]['extra_fields'] = extra_order_fields.get(order['order_id'], {})
                order_details[order_reference]['allocated_quantity'] = allocated_dict.get(order_reference, 0)
            else:
                order_details[order_reference]['total_quantity'] += original_quantity
                order_details[order_reference]['total_open_quantity'] += quantity
                order_details[order_reference]['cancelled_quantity'] += cancelled_quantity
                order_details[order_reference]['suspended_quantity'] += suspended_quantity
                order_details[order_reference]['skus'].add(sku_code)
                if order.get('zone'):
                    if not order_details[order_reference]['zone']:
                        order_details[order_reference]['zone'] = str(order['zone'])
                    elif order['zone'] not in order_unique_zones[order_reference]:                        
                        order_details[order_reference]['zone'] += (',' + str(order['zone']))
            if order.get('zone'):
                order_unique_zones[order_reference].add(order['zone'])
            order_details[order_reference]['total_quantity'] = truncate_float(float(order_details[order_reference]['total_quantity']), decimal_limit)
            order_details[order_reference]['total_open_quantity'] = truncate_float(float(order_details[order_reference]['total_open_quantity']), decimal_limit)
            order_details[order_reference]['cancelled_quantity'] = truncate_float(float(order_details[order_reference]['cancelled_quantity']), decimal_limit)
            order_details[order_reference]['suspended_quantity'] = truncate_float(float(order_details[order_reference]['suspended_quantity']), decimal_limit)
            

            current_status = order_details[order_reference].get('status')
            order_details[order_reference]['order_value'] = truncate_float(order_details.get(order_reference, {}).get('order_value', 0) + order_value_with_tax, decimal_limit)

            order_details = self.get_sku_count_based_on_status(status, order, order_details)
            order_details[order_reference]['status'] = self.fetch_order_status(status, current_status, order_status, order_reference, order_details, order_dict)
            order_details[order_reference]['approval_status'] = order_dict.get(order_reference, {}).get('approval_status', '')

        for order_reference, order in order_details.items():
            order_details[order_reference]['no_of_skus'] = len(order.pop('unique_skus',set()))
        order_details = self.change_open_qty_based_on_status(status, inv_qty_dict, order_details,decimal_limit)
        return order_details
    
    def change_open_qty_based_on_status(self,status,inv_qty_dict,order_details,decimal_limit):
        for order_reference,each_order in order_details.items():
            if status == 'completed' and order_reference in inv_qty_dict:
                    each_order['total_open_quantity'] = truncate_float(float(inv_qty_dict.get(order_reference,0)), decimal_limit)
            if status == 'in_progress':
                each_order['total_open_quantity'] = each_order['total_quantity'] - (each_order['total_open_quantity'] + each_order['cancelled_quantity'] + inv_qty_dict.get(order_reference,0))
                if each_order['total_open_quantity'] < 0 :
                    each_order['total_open_quantity'] = 0
                each_order['total_open_quantity'] = truncate_float(float(each_order['total_open_quantity']), decimal_limit)
        return order_details


    def get_sku_count_based_on_status(self, status, order_dict, order_details):
        """
        Get the count of unique SKUs based on the given status.

        Args:
            status (str): The status of the order.
            order_dict (dict): The dictionary containing order details.
            order_details (dict): The dictionary containing the aggregated order details.

        Returns:
            dict: The updated order_details dictionary with the count of unique SKUs.

        """
        STATUS_DICT = {
            'in_progress': '0',
            'cancelled': '3',
            'allocated': '20',
            'open': '1',
            'completed': '5'
        }
        status_code = STATUS_DICT.get(status, '')
        order_status = order_dict.get('status', '')
        order_reference = order_dict.get('order_reference', '')
        sku_code = order_dict.get('sku_code', '')
        if status_code:
            valid_sku_code = False
            if status_code == order_status:
                valid_sku_code = True
            elif status == 'in_progress':
                original_quantity = order_dict.get('original_quantity', 0) or 0
                cancelled_quantity = order_dict.get('cancelled_quantity', 0) or 0
                quantity = order_dict.get('quantity', 0) or 0
                if original_quantity - cancelled_quantity != quantity  and order_status != '5':
                    valid_sku_code = True
            if valid_sku_code and sku_code and sku_code not in order_details[order_reference]['unique_skus']:
                order_details[order_reference]['unique_skus'].add(sku_code)
        elif sku_code and sku_code not in order_details[order_reference]['unique_skus']:
            order_details[order_reference]['unique_skus'].add(sku_code)
        return order_details

class OrdersSummary(WMSListView):
    def get(self, *args, **kwargs):
        self.set_user_credientials()

        self.request_data = self.request.GET
        extra_params = {'call_type': 'API'}
        order_data_instance = OrderDetailsMixin(self.user, self.warehouse, self.request, self.request_data, extra_params=extra_params)
        order_details, paging_details, errors = order_data_instance.get_order_summary()
        result_data = {
            "warehouse" : self.warehouse.username,
            "data": order_details,
            "paging": paging_details
        }
        if errors:
            result_data['errors'] = errors
            return JsonResponse(result_data, status=400)
        return JsonResponse(result_data)

def get_order_payment_details(order_records):
    '''
    Get payment details
    '''
    payment_dicts = {}
    payment = order_records.values("original_order_id").distinct().annotate(invoice_amount_sum=Sum('invoice_amount'), payment_received_sum=Sum('payment_received'))
    for each_payment in payment:
        payment_status = 'Pending'
        if each_payment['invoice_amount_sum'] == each_payment['payment_received_sum']:
            payment_status = 'Paid'
        if each_payment["original_order_id"] not in payment_dicts:
            payment_dicts[each_payment["original_order_id"]] = {
                "payment_status":payment_status,
                "invoice_amount_sum": each_payment['invoice_amount_sum']
            }
        else:
            payment_dicts[each_payment["original_order_id"]]["invoice_amount_sum"] += each_payment['invoice_amount_sum']
    return payment_dicts

def get_order_unique_values(order_records):
    '''
    Get unique values from order details result data
    '''
    order_ids_list, original_order_ids_list, order_ref_list, order_updation_date_dict = [], [], [], {}
    customer_ids, sku_codes, serial_skus = set(), set(), set()
    order_status_dict, order_quantity_dict = {}, {}
    for each_order in order_records:
        order_status_dict.setdefault(each_order.order_reference,[])
        order_quantity_dict.setdefault(each_order.order_reference,0)
        order_quantity_dict[each_order.order_reference] += each_order.original_quantity
        order_status_dict[each_order.order_reference].append(each_order.status)
        if each_order.original_order_id not in original_order_ids_list:
            original_order_ids_list.append(each_order.original_order_id)
        if each_order.order_reference not in order_ref_list:
            order_ref_list.append(each_order.order_reference)
        if each_order.order_reference not in order_updation_date_dict:
            order_updation_date_dict[each_order.order_reference] = each_order.updation_date
        else:
            order_updation_date_dict[each_order.order_reference] = max(order_updation_date_dict[each_order.order_reference], each_order.updation_date)
        order_ids_list.append(each_order.id)
        customer_ids.add(each_order.customer_id)
        sku_codes.add(each_order.sku.sku_code)
        if each_order.sku.enable_serial_based:
            serial_skus.add(each_order.sku.sku_code)
    customer_ids = list(customer_ids)
    sku_codes = list(sku_codes)
    return order_ids_list, original_order_ids_list, order_ref_list, order_status_dict, order_quantity_dict, customer_ids, sku_codes, serial_skus, order_updation_date_dict

def get_payment_summary_dict(order_ids_list):
    '''
    Get payment summary
    '''
    payment_summary_dict = {}
    payment_summary = PaymentSummary.objects.select_related('order','payment_info').filter(order_id__in=order_ids_list)
    for each_payment in payment_summary:
        if each_payment.order.original_order_id not in payment_summary_dict:
            payment_info= each_payment.payment_info
            payment_summary_dict[each_payment.order.original_order_id]= loads(payment_info.aux_info)
    return payment_summary_dict

def get_order_charges(original_order_ids_list, wh_users):
    '''
    Get order charges
    '''
    order_charges_dict= {}
    charges = OrderCharges.objects.filter(order_id__in=original_order_ids_list, user__in=wh_users,
                                                 charge_name='Shipping Charge').values('charge_amount', "order_id")
    for charge in charges:
        order_charges_dict[charge["order_id"]] = charge["charge_amount"]
    return order_charges_dict

def get_order_extra_fields(original_order_ids, wh_users):
    '''
    Get order extra fields
    '''
    extra_fields_dict = defaultdict(dict)
    extra_fields = OrderFields.objects.filter(original_order_id__in=original_order_ids, user__in=wh_users).values()
    for extra_field in extra_fields:
        extra_fields_dict[extra_field['original_order_id']][extra_field['name']] = extra_field['value']
    return extra_fields_dict

def get_customer_order_summary_details(order_ids_list, wh_users):
    '''
    Get customer order summary details
    '''
    order_summary_dicts = {}
    order_summary_objs = CustomerOrderSummary.objects.filter(order_id__in=order_ids_list, order__user__in=wh_users)
    for order_obj in order_summary_objs:
        discount_amount = order_obj.discount
        order_summary_dicts[order_obj.order_id] = {"address": "", "CGST": "", "SGST": "", "IGST": "", "discount_amount": ""}
        order_summary_dicts[order_obj.order_id]["discount_amount"] = float('%.2f' % discount_amount)
        order_summary_dicts[order_obj.order_id]["mrp"] = order_obj.mrp
        consignee =order_obj.consignee
        if consignee:
            order_summary_dicts[order_obj.order_id]["address"] = consignee
        if order_obj.cgst_tax:
            order_summary_dicts[order_obj.order_id]["CGST"] = order_obj.cgst_tax
            order_summary_dicts[order_obj.order_id]["SGST"] = order_obj.sgst_tax
        elif order_obj.igst_tax:
            order_summary_dicts[order_obj.order_id]["IGST"] = order_obj.sgst_tax
    return order_summary_dicts

def get_picked_and_packed_quantity(warehouse, order_ids_list, wh_users, time_zone):
    '''
    Get picked and packed quantity
    '''
    picked_quantity_dict, picklist_generated_quantity_dict, order_picked_quantity_dict, order_picklist_generated_quantity_dict = {}, {}, {}, {}
    picked_child_details_dict, child_details, batch_details_dict = defaultdict(dict), {}, defaultdict(list)
    values_list = ['order_id', 'order__order_reference', 'status', 'sku__sku_code', 'sku__sku_desc', 'sku__sku_category', 'stock__batch_detail__batch_no', 
        'stock__batch_detail__manufactured_date', 'stock__batch_detail__expiry_date', 'stock__batch_detail__mrp', 'order__quantity',
        'picked_quantity', 'reserved_quantity', 'classification', 'order__sku__sku_code', 'order__sku_id', 'sku_id']

    picklist_data = Picklist.objects.filter(order_id__in=order_ids_list, order__user__in=wh_users).exclude(status="cancelled").exclude(picklist_quantity=0).values(*values_list)
    is_combo_present = False
    parent_sku_ids = set()
    for pick in picklist_data:
        if pick['classification'] == 'combo':
            parent_sku_ids.add(pick['order__sku_id'])
            combo_qty_key = (pick['order__order_reference'], pick['order_id'], pick['order__sku_id'])
            child_details[combo_qty_key] = pick['order__quantity']
        pick['expiry_date'] = get_local_date_known_timezone(time_zone, pick["stock__batch_detail__expiry_date"], send_date='true').strftime('%Y-%m-%d') if pick.get("stock__batch_detail__expiry_date", "") else ""
        pick['manufactured_date'] = get_local_date_known_timezone(time_zone, pick["stock__batch_detail__manufactured_date"], send_date='true').strftime('%Y-%m-%d') if pick.get("stock__batch_detail__manufactured_date", "") else ""
    
    combo_data = {}
    if is_combo_present:
        combo_data = get_combo_relation_qty_dict(warehouse, parent_sku_ids)
    for pick in picklist_data:
        if pick['status'] == 'open':
            order_picklist_generated_quantity_dict.setdefault(pick['order__order_reference'], 0)
            order_picklist_generated_quantity_dict[pick['order__order_reference']] += pick['reserved_quantity']
            picklist_generated_quantity_dict.setdefault(pick['order_id'], 0)
            picklist_generated_quantity_dict[pick['order_id']] += pick['reserved_quantity']

        order_picked_quantity_dict.setdefault(pick['order__order_reference'], 0)
        picked_quantity_dict.setdefault(pick['order_id'], 0)
        order_picked_quantity_dict[pick['order__order_reference']] += pick['picked_quantity']
        picked_quantity_dict[pick['order_id']] += pick['picked_quantity']
    
        unique_line_key = (pick['order__order_reference'], pick['order_id'], pick['order__sku_id'])
        
        if unique_line_key in child_details:
            combo_key = (pick['order__sku_id'], pick['sku_id'])
            
            child_details[unique_line_key] = min(child_details[unique_line_key], pick['picked_quantity'] / combo_data.get(combo_key, 1))
            
            if pick['sku__sku_code'] not in picked_child_details_dict[unique_line_key]:
                picked_child_details_dict[unique_line_key][pick['sku__sku_code']] = {
                    'sku_code': pick['sku__sku_code'],
                    'sku_desc': pick['sku__sku_desc'],
                    'sku_category': pick['sku__sku_category'],
                    'picked_quantity' : 0,
                    'batch' : []
                }
            
            picked_child_details_dict[unique_line_key][pick['sku__sku_code']]['picked_quantity'] += pick['picked_quantity']
            picked_child_details_dict[unique_line_key][pick['sku__sku_code']]['batch'].append({
                'batch_no': pick['stock__batch_detail__batch_no'],
                'mfg_date': pick['manufactured_date'],
                'expiry_date': pick['expiry_date'],
                'mrp': pick['stock__batch_detail__mrp'],
                'picked_quantity': pick['picked_quantity']
            })
        else:
            batch_details_dict[unique_line_key].append({
                'batch_no': pick['stock__batch_detail__batch_no'],
                'mfg_date': pick['manufactured_date'],
                'expiry_date': pick['expiry_date'],
                'mrp': pick['stock__batch_detail__mrp'],
                'picked_quantity': pick['picked_quantity']
            })

    for combo_qty_key, qty in child_details.items():
        order_picked_quantity_dict[combo_qty_key[0]] = qty
        picked_quantity_dict[combo_qty_key[1]] = qty
        
    return picked_quantity_dict, picklist_generated_quantity_dict, order_picked_quantity_dict, order_picklist_generated_quantity_dict, batch_details_dict, picked_child_details_dict

def get_invoice_details(order_ids_list, wh_users, time_zone):
    '''
    Get invoiced order_ids
    '''
    values_list = [
        "picklist__stock__batch_detail__batch_no","picklist__stock__batch_detail__manufactured_date",
        "picklist__stock__batch_detail__expiry_date","picklist__stock__supplier__supplier_id",
        "picklist__stock__supplier__name","order_id","full_invoice_number","quantity", "id", "order_status_flag"
    ]
    filters = {
        "order_id__in": order_ids_list,
        "order__user__in": wh_users,
    }
    excludes = {
        "order_status_flag" : 'cancelled'
    }
    order_wise_inv_quantity, sos_order_ids_dict = defaultdict(int), {}
    related_fields = ['picklist__stock__batch_detail','picklist__stock__supplier']

    sos_data = list(SellerOrderSummary.objects.select_related(*related_fields).filter(**filters).exclude(**excludes).values(*values_list))
    invoice_items = []
    for each_sos in sos_data:
        sos_order_ids_dict[each_sos["id"]] = each_sos["order_id"]
        if each_sos["order_status_flag"] in ['customer_invoices','delivery_challans']:
            order_wise_inv_quantity[each_sos["order_id"]] += each_sos["quantity"]
            each_sos['mfg_date'] = each_sos['picklist__stock__batch_detail__manufactured_date']
            each_sos['exp_date'] = each_sos['picklist__stock__batch_detail__expiry_date']
            invoice_items.append(each_sos)
    
    order_invoice_details = get_invoiced_batch_details(invoice_items, time_zone)
    return order_wise_inv_quantity, sos_order_ids_dict, order_invoice_details

def get_invoiced_batch_details(invoice_items, time_zone):
    '''
    Get invoiced batch details
    '''
    invoice_batch_wise_data = {}
    for each_sos in invoice_items:
        batch_no = each_sos["picklist__stock__batch_detail__batch_no"]
        unique_key = (each_sos["order_id"], each_sos["full_invoice_number"], batch_no)
        
        if unique_key not in invoice_batch_wise_data:
            mfg_date = get_local_date_known_timezone(time_zone, each_sos["mfg_date"], send_date='true').strftime('%Y-%m-%d') if each_sos.get("mfg_date", "") else ""
            exp_date = get_local_date_known_timezone(time_zone, each_sos["exp_date"], send_date='true').strftime('%Y-%m-%d') if each_sos.get("exp_date", "") else ""
            invoice_batch_wise_data[unique_key] = {
                "invoice_qty": each_sos["quantity"],
                "batch": {
                    "mfg_date": mfg_date,
                    "expiry_date": exp_date,
                    "batch_no" : batch_no,
                }
            }
        else:
            invoice_batch_wise_data[unique_key]["invoice_qty"] += each_sos["quantity"]
    
    order_invoice_wise_data = {}
    for (order_id, invoice_no, batch_no), batch_item in invoice_batch_wise_data.items():
        order_inv_key = (order_id, invoice_no)
        if order_inv_key not in order_invoice_wise_data:
            order_invoice_wise_data[order_inv_key] = {
                "invoice_quantity" : 0,
                "batch_data" : []
            }
        order_invoice_wise_data[order_inv_key]["invoice_quantity"] += batch_item["invoice_qty"]
        order_invoice_wise_data[order_inv_key]["batch_data"].append(batch_item["batch"])
    
    order_invoice_details = defaultdict(list)
    for (order_id, invoice_no), inv_data in order_invoice_wise_data.items():
        order_invoice_details[order_id].append({
            "invoice_number": invoice_no,
            "invoice_qty": inv_data["invoice_quantity"],
            "batch": inv_data["batch_data"]
        })
    
    return order_invoice_details

def get_allocated_details(warehouse, enable_sales_uom, order_ids_list):
    '''
    Get allocated details
    '''
    allocate_dict = {}
    allocated_obj = StockAllocation.objects.filter(reference_id__in=order_ids_list, reference_model='OrderDetail', warehouse=warehouse.id, status=1).values(
        'json_data__pack_uom_quantity','reference_number','quantity','stock__sku_id')

    for allocate in allocated_obj:
        order_reference = allocate.get('reference_number','') or ''
        pack_uom_quantity = allocate.get('json_data__pack_uom_quantity',0) or 0
        allocated_total = allocate.get('quantity',0) or 0
        sku_code = allocate.get('stock__sku_id','')
        if pack_uom_quantity and enable_sales_uom == 'true':
            allocated_total = allocated_total / pack_uom_quantity
        allocate_key = (order_reference,sku_code)
        if allocate_key not in allocate_dict:
            allocate_dict[allocate_key] = allocated_total
        else:
            allocate_dict[allocate_key] += allocated_total

    return allocate_dict

def get_serial_details(warehouse, sos_ids_mapping, serial_skus):
    filters = {
        'transact_id__in' : sos_ids_mapping,
        'reference_type' : 'so_picking',
        'sku_code__in' : serial_skus
    }
    serial_data = SerialNumberTransactionMixin(None, warehouse, {'filters' : filters}).get_sntd_details()
    serial_data = serial_data.get('data', [])
    order_wise_serials = defaultdict(set)
    for serial in serial_data:
        order_id = sos_ids_mapping[serial['transact_id']]
        order_wise_serials[order_id].update(serial['serial_numbers'])
    return order_wise_serials

def get_order_status(order_statuses, order_reference, order_picklist_generated_quantity_dict, order_picked_quantity_dict, order_quantity_dict):
    '''
    Get order status
    '''
    order_status = "in_progress"
    if len(order_statuses) ==1 and '1' in order_statuses and not order_picklist_generated_quantity_dict.get(order_reference, 0) and not order_picked_quantity_dict.get(order_reference, 0):
        order_status = 'open'
    elif len(order_statuses) ==1 and '5' in order_statuses:
        order_status = 'invoiced'
    elif len(order_statuses) ==1 and '3' in order_statuses:
        order_status = 'cancelled'
    elif len(order_statuses) ==1 and '7' in order_statuses:
        order_status = 'cancelled'
    elif len(order_statuses) ==1 and '2' in order_statuses:
        order_status = 'dispatched'
    elif len(order_statuses) ==1 and '20' in order_statuses:
        order_status = 'allocated'
    elif order_picked_quantity_dict.get(order_reference, 0) == order_quantity_dict.get(order_reference, 0):
        order_status = 'picked'
    elif '0' not in order_statuses and '1' not in order_statuses:
        order_status = 'invoiced'
    return order_status

def get_order_item_json_data_and_sale_uom(data, item_dict, enable_sales_uom, decimal_limit):
    '''
    Get order item json data
    '''
    if enable_sales_uom == 'true':
        pack_uom_quantity = 0
        item_dict['sales_uom_quantity'] = truncate_float(data.original_quantity, decimal_limit)
        if data.json_data is not None:
            pack_uom_quantity = data.json_data.get('pack_uom_quantity',0) or 0
            for key in ['pack_uom_quantity', 'pack_id']:
                if key in data.json_data:
                    item_dict[key] = data.json_data[key]
        if pack_uom_quantity:
            item_dict['base_uom_quantity'] = item_dict['sales_uom_quantity'] * pack_uom_quantity
            item_dict['picked_quantity'] = item_dict['picked_quantity'] / pack_uom_quantity

    if data.json_data:
        for key, val in data.json_data.items():
            if key not in list(item_dict.keys()):
                item_dict[key] = str(val)
    return item_dict

def prepare_customer_order_summary_details(data, item_dict, each_order_line, order_summary_dicts):
    '''
    Premapre customer order summary details
    '''
    shipping_address = {"address": each_order_line.address}
    if order_summary_dicts.get(data.id, {}):
        item_dict['discount_amount'] = float('%.2f' % order_summary_dicts.get(data.id, {}).get("discount_amount", 0))
        consignee = order_summary_dicts.get(data.id, {}).get("address", "")
        if consignee:
            shipping_address["address"] = consignee
        item_dict['mrp'] = order_summary_dicts.get(data.id, {}).get("mrp", 0.00) or 0.00
        if order_summary_dicts.get(data.id, {}).get("CGST", ""):
            item_dict['taxes']['CGST'] = order_summary_dicts.get(data.id, {}).get("CGST", "")
            item_dict['taxes']['SGST'] = order_summary_dicts.get(data.id, {}).get("SGST", "")
        elif order_summary_dicts.get(data.id, {}).get("IGST", ""):
            item_dict['taxes']['IGST'] = order_summary_dicts.get(data.id, {}).get("IGST", "")
    return item_dict, shipping_address

def get_order_sku_status(data, order_status, cancelled_quantity, picklist_generated_quantity_dict, \
    sku_tracking_dict, picked_quantity_dict, order_charges_dict, invoice_sku_dict, seller_skus_dict, each_order_line, allocated_qty):
    '''
    Get order sku status
    '''
    charge_amount, picked_quantity_sku, unfulfilled_quantity = 0, 0, 0
    sku_status = ''
    inv_check = False
    charge = order_charges_dict.get(data.original_order_id, "")
    sku_picked_qty = picked_quantity_dict.get(data.id, 0)
    inv_det = invoice_sku_dict.get(each_order_line.id,[])
    if inv_det:
        inv_check = True
        picked_quantity_sku = seller_skus_dict.get(data.id, 0)
    if charge:
        charge_amount = charge[0]
    if inv_check:
        order_status = 'invoiced'

    if data.status == '7':
        sku_status = 'unfulfilled'
        unfulfilled_quantity = data.original_quantity
    elif data.status in ['0','5']:
        sku_status = 'in_progress'
        #if order_status != "open":
        if order_status not in  ["open", "in_progress", "picked"]:
            if picked_quantity_sku == data.original_quantity:
                sku_status = 'fulfilled'
            elif picked_quantity_sku == 0:
                sku_status = 'unfulfilled'
                unfulfilled_quantity = data.original_quantity
            else:
                if inv_check:
                    sku_status = 'partial_fulfilled'
                    unfulfilled_quantity = data.original_quantity - picked_quantity_sku
        elif sku_picked_qty == data.original_quantity - cancelled_quantity - unfulfilled_quantity:
            sku_status = 'picked'
            picked_quantity_sku = sku_picked_qty
    elif data.status == '1':
        sku_status = 'open'
        if picklist_generated_quantity_dict.get(data.id, 0) or sku_picked_qty:
            sku_status = 'in_progress'
        elif allocated_qty:
            sku_status = 'partially_allocated'
    elif data.status == '2':
        sku_status = 'dispatched'
        if sku_tracking_dict.get(data.id, ""):
            sku_status =  sku_tracking_dict.get(data.id, "")
    elif data.status == '3':
        sku_status = 'cancelled'
    elif data.status == '20':
        sku_status = 'allocated'
    return sku_status, unfulfilled_quantity, sku_picked_qty, charge_amount

def get_customer_details(warehouse_id, customer_ids):
    '''
    Get customer details
    '''
    customer_details = {}
    customer_values = [
        'customer_id', 'customer_reference', 'customer_code', 'email_id', 'name', 'address', 'shipping_address', 
        'city', 'state', 'country', 'pincode', 'latitude', 'longitude', 'tin_number', 'pan_number', 'route',
        'customer_shelf_life', 'phone_number', 'customer_priority'
    ]
    customer_objs = list(CustomerMaster.objects.filter(user=warehouse_id, customer_id__in=customer_ids).values(*customer_values))
    for customer in customer_objs:
        customer_details[customer['customer_id']] = customer
    return customer_details

def get_order_header_data(warehouse, order_references, time_zone):

    order_header_dict = {}
    order_data = list(Order.objects.filter(order_reference__in=order_references, warehouse=warehouse)
                        .values('order_reference', 'status', 'expiration_date', 'hold_date','order_date', 'updated_by__username'))
    for data in order_data:
        order_header_dict[data['order_reference']] = {
            'status': data['status'],
            'expiration_date': '',
            'hold_date': '',
            'order_date': '',
            'updated_by' : data['updated_by__username'] or ''
        }
        if data['expiration_date']:
            order_header_dict[data['order_reference']]['expiration_date'] = get_local_date_known_timezone(time_zone, data['expiration_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT)
        if data['hold_date']:
            order_header_dict[data['order_reference']]['hold_date'] = get_local_date_known_timezone(time_zone, data['hold_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT)
        if data['order_date']:
            order_header_dict[data['order_reference']]['order_date'] = get_local_date_known_timezone(time_zone, data['order_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT)   
    return order_header_dict

def get_order_details_api(warehouse: User, original_order_ids_list, wh_users, search_parameters=None, order_reference_list=None):
    """
    Retrieves order details based on the given warehouse, order references list, and status.
    """
    log.info("Request for fetch order data from %s for order reference %s with search parameters %s"%(str(warehouse.username), str(original_order_ids_list), str(search_parameters)))
    search_parameters = search_parameters or {}
    order_reference_list = order_reference_list or []
    time_zone = get_user_time_zone(warehouse)
    decimal_limit = get_decimal_value(warehouse.id, price=True)
    final_order_data_list, locations, final_order_data_dict = [], [], {}

    misc_dict = get_multiple_misc_values(['staging_area', 'enable_sales_uom','stock_allocate', 'order_hold_options'], warehouse.id)

    stock_allocate = misc_dict.get('stock_allocate','false')
    staging_area = misc_dict.get('staging_area', 'false')
    enable_sales_uom = misc_dict.get('enable_sales_uom', 'false')
    search_parameters['user__in'] = wh_users
    if original_order_ids_list:
        search_parameters['original_order_id__in'] = original_order_ids_list
    elif order_reference_list:
        search_parameters['order_reference__in'] = order_reference_list
    else:
        search_parameters['original_order_id__in'] = []

    # Order Cancellation filter from 3P Integrations
    order_cancellation_filter = search_parameters.pop('order_cancellation_filter', '')
    if order_cancellation_filter == 'full_order_cancellation':
        order_reference_list = list(Order.objects
            .filter(warehouse_id__in=wh_users, order_reference__in=order_reference_list, status=3)
            .values_list('order_reference', flat=True)
        )
        search_parameters['order_reference__in'] = order_reference_list
    elif order_cancellation_filter == 'full_item_cancellation':
        search_parameters['status'] = 3
    elif order_cancellation_filter == 'item_cancellation':
        search_parameters['cancelled_quantity__gt'] = 0

    order_records = OrderDetail.objects.select_related('sku').filter(**search_parameters).order_by('-creation_date')

    #get order payment details
    payment_dicts = get_order_payment_details(order_records)

    #get order unique values
    order_ids_list, original_order_ids_list, order_ref_list, order_status_dict, order_quantity_dict, customer_ids, sku_codes, serial_skus, order_updation_date_dict = get_order_unique_values(order_records)

    if staging_area in ['true']:
        locations =list(StagingInfo.objects.filter(order_reference__in = order_ref_list, user=warehouse.id, status=0).values_list('location__location', flat=True))

    shipment_mapping_dict = {}
    #need to implement get shipment details

    #order header details
    order_header_dict = get_order_header_data(warehouse, order_ref_list, time_zone)

    #customer details
    customer_details = get_customer_details(warehouse.id, customer_ids)

    #get payment summary details
    payment_summary_dict = get_payment_summary_dict(order_ids_list)

    #get order charges details
    order_charges_dict = get_order_charges(original_order_ids_list, wh_users)
    
    #get order extra fields
    extra_order_fields = get_order_extra_fields(original_order_ids_list, wh_users)

    #get customer order summary details
    order_summary_dicts = get_customer_order_summary_details(order_ids_list, wh_users)

    #get picked and packed quantity
    picked_quantity_dict, picklist_generated_quantity_dict, order_picked_quantity_dict, order_picklist_generated_quantity_dict, batch_details_dict, picked_child_details_dict = get_picked_and_packed_quantity(warehouse, order_ids_list, wh_users, time_zone)

    #get invoice details
    seller_skus_dict, sos_order_ids_mapping, invoice_sku_dict = get_invoice_details(order_ids_list, wh_users, time_zone)

    allocate_dict = {}
    if stock_allocate == 'true':
        allocate_dict = get_allocated_details(warehouse, enable_sales_uom, order_ids_list)
    
    serial_data = {}
    if serial_skus:
        serial_data = get_serial_details(warehouse, sos_order_ids_mapping, serial_skus)

    for order_reference, updation_date in order_updation_date_dict.items():
        order_updation_date_dict[order_reference] = get_local_date_known_timezone(time_zone, updation_date, send_date='true').strftime(DEFAULT_DATETIME_FORMAT)
    sku_tracking_dict = {}
    for each_order_line in  order_records:
        order_reference, original_order_id, order_type = each_order_line.order_reference, each_order_line.original_order_id, each_order_line.order_type
        if str(original_order_id) not in  final_order_data_dict:
            if order_header_dict.get(order_reference, {}):
                if not order_header_dict[order_reference].get('order_date',''):
                    order_header_dict[order_reference]['order_date'] = get_local_date_known_timezone(time_zone, each_order_line.creation_date, send_date=True).strftime(DEFAULT_DATETIME_FORMAT)

            final_order_data_dict[original_order_id]= {
                "order_id": original_order_id,
                "customer_po_number": each_order_line.customer_po_num,
                "order_type": order_type,
                "items": [],
                **order_header_dict.get(order_reference, {}),
            }
            order_statuses = set(order_status_dict.get(order_reference, []))

            #get order status
            order_status = get_order_status(order_statuses, order_reference, order_picklist_generated_quantity_dict, order_picked_quantity_dict, order_quantity_dict)
            if order_header_dict.get(order_reference, {}).get('status') == '6' or ('automated_order_hold' in misc_dict.get('order_hold_options', '') and order_header_dict.get(order_reference, {}).get('hold_date') and order_header_dict.get(order_reference, {}).get('hold_date') > datetime.now(pytz.timezone(time_zone)).strftime(DEFAULT_DATETIME_FORMAT)):
                order_status = 'on_hold'

            address = each_order_line.address
            if address:
                address = address.strip()
            billing_address = {
                "name": each_order_line.customer_name,
                "email": each_order_line.email_id,
                "phone_number": each_order_line.telephone,
                "address": address,
                "city": each_order_line.city,
                "state": each_order_line.state,
                "pincode": each_order_line.pin_code,
                "country": customer_details.get(each_order_line.customer_id, {}).get('country', ''),
            }

            customer_data = customer_details.get(each_order_line.customer_id, {})
            final_order_data_dict[original_order_id].update({
                "order_status": order_status,
                "shipment_date": get_local_date_known_timezone(time_zone, each_order_line.shipment_date, send_date='true').strftime(DEFAULT_DATETIME_FORMAT),
                "order_updation_date" : order_updation_date_dict.get(order_reference, ''),
                "order_creation_date": get_local_date_known_timezone(time_zone, each_order_line.creation_date, send_date='true').strftime(DEFAULT_DATETIME_FORMAT),
                "payment_status": payment_dicts.get(original_order_id, {}).get("payment_status", ""),
                "invoice_amount": payment_dicts.get(original_order_id, {}).get("invoice_amount_sum", ""),
                "payment_info": payment_summary_dict.get(original_order_id, {}),
                "billing_address": billing_address,
                "order_reference": order_reference,
                "source": each_order_line.marketplace,
                "customer_id": each_order_line.customer_id,
                "customer_name": each_order_line.customer_name,
                "order_display_key": order_reference or original_order_id,
                "customer_reference": customer_data.get('customer_reference', ''),
                "customer" : customer_data,
                "extra_fields" : extra_order_fields.get(original_order_id, {}),
                "slot_from": get_local_date_known_timezone(time_zone, each_order_line.slot_from, send_date=True).strftime(DEFAULT_DATETIME_FORMAT) if each_order_line.slot_from else '',
                "slot_to": get_local_date_known_timezone(time_zone, each_order_line.slot_to, send_date=True).strftime(DEFAULT_DATETIME_FORMAT) if each_order_line.slot_to else '',
            }) 
            if locations:
                final_order_data_dict[original_order_id]["location"] = ', '.join(locations)
        item_dict = {}
        data = each_order_line

        cancelled_quantity = data.cancelled_quantity
        suspended_quantity = data.suspended_quantity or 0
        dispatched_quantity = shipment_mapping_dict.get(data.id, 0)
        
        allocated_total =  allocate_dict.get((order_reference, data.sku_id), 0)
        sku_status, unfulfilled_quantity, sku_picked_qty, charge_amount = get_order_sku_status(data, order_status, cancelled_quantity, picklist_generated_quantity_dict, \
            sku_tracking_dict, picked_quantity_dict, order_charges_dict, invoice_sku_dict, seller_skus_dict, each_order_line, allocated_total)

        stock_count = 0
        
        if data.stock_count:
            stock_count = data.stock_count
        
        intransit_quantity = data.json_data.get('intransit_stock', 0)
        current_stock = data.json_data.get('current_stock', 0)
        target_stock = data.json_data.get('target_stock', 0)
        zone = data.json_data.get('zone', '')
        batch = data.json_data.get('batch_number', '')
        unique_line_key = (order_reference, data.id, data.sku.id)
        item_dict = {'sku_code': data.sku.sku_code, 'sku_description': data.sku.sku_desc, 'sku_brand': data.sku.sku_brand,
                     'order_quantity': truncate_float(data.original_quantity, decimal_limit),
                     'open_quantity': truncate_float(data.quantity, decimal_limit),
                     'picked_quantity': truncate_float(sku_picked_qty,decimal_limit), 'dispatched_quantity': dispatched_quantity,
                     'cancelled_quantity': truncate_float(cancelled_quantity, decimal_limit),
                     'suspended_quantity': truncate_float(suspended_quantity, decimal_limit),
                     'unfulfilled_quantity': truncate_float(unfulfilled_quantity, decimal_limit),
                     'mrp': data.sku.mrp,'line_reference':each_order_line.line_reference,'id':each_order_line.id,
                     'status': sku_status, 'unit_price': truncate_float(data.unit_price, decimal_limit),
                     'shipment_charge': truncate_float(charge_amount, decimal_limit),
                     'discount_amount': 0,'allocated_quantity' : allocated_total,'uom':data.sku.measurement_type,
                     'invoice_amount': truncate_float(data.invoice_amount, decimal_limit),
                     'stock_count': truncate_float(stock_count, decimal_limit),
                     'invoice_details':[],
                     'remarks':data.remarks, 'serial_numbers' : list(serial_data.get(data.id, [])),
                     'taxes': {'CGST': '', 'SGST': '', 'IGST': ''},
                     'intransit_qty': truncate_float(intransit_quantity, decimal_limit),
                     'current_qty': truncate_float(current_stock, decimal_limit),
                     'max_qty': truncate_float(target_stock, decimal_limit),
                     'zone': zone, 'batch_number': batch,
                     'batch' : batch_details_dict.get(unique_line_key, []),
                     'picklist_details' : list(picked_child_details_dict.get(unique_line_key, {}).values())
                    }

        #get sale uom and order json data
        item_dict = get_order_item_json_data_and_sale_uom(data, item_dict, enable_sales_uom, decimal_limit)
        item_dict["invoice_details"]=invoice_sku_dict.get(each_order_line.id,[])
        item_dict, shipping_address = prepare_customer_order_summary_details(data, item_dict, each_order_line, order_summary_dicts)
        final_order_data_dict[original_order_id]["items"].append(item_dict)
        final_order_data_dict[original_order_id]["shipping_address"] = shipping_address

    for key, value in final_order_data_dict.items():
        final_order_data_list.append(value)

    return final_order_data_list
