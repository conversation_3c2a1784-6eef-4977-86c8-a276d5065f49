# package imports
import pandas as pd
from json import loads, dumps
from datetime import datetime
from typing import List, Dict
import copy

# django imports
from django.http import JsonResponse
from django.test.client import RequestFactory
from django.db.models import F
from django.db import transaction
from django.core.cache import cache


# wms base imports
from wms_base.wms_utils import init_logger

# core operations imports
from core.models import SKUMaster
from core_operations.views.common.main import WMSListView, get_misc_options_list, get_multiple_misc_values, get_user_attributes
from core_operations.views.common.validation import update_order_header_status, validate_attributes
from core_operations.views.integration.integration import webhook_integration_3p

#inbound imports
from inbound.models import POLocation, ASNSummary
from inbound.views.purchase_order.validation import validate_po_updation
from inbound.views.purchase_order.update_po import update_po

#lms imports
from lms.models import TaskMaster

# outbound imports
from outbound.models import (
    OrderDetail, Picklist, SellerOrderSummary, PickAndPassStrategy, Order, OrderTypeZoneMapping, OrderFields,
    CustomerMaster, StockAllocation
)
from outbound.views.orders.constants import ORDER_JSON_UPDATE_FIELDS
from inventory.models import StockDetail, SKUPackMaster
from outbound.views.orders.main import OrdersDetailsSet
from outbound.views.orders.delete_orders import DeleteOrdersMixin
from outbound.views.orders.order_hold import OrderHoldMixin
from outbound.views.invoice.helpers import get_destination_user
from inbound.models import POHeader

from .update import create_new_sku_in_existing_order

log = init_logger('logs/seller_order_view.log')


LOCK_EXPIRE = 60

class SellerOrderSet(WMSListView):

    def __init__(self):
        self.new_sku_codes = []
        self.new_items = []
        self.order_details = {}
        self.updated_order_ids = []
        self.pull_to_locate_records = []
        self.stock_records = []
        self.sos_ids = []
        self.new_extra_fields, self.update_extra_fields = [], []
        self.hold_order_refs, self.hold_order_update_fields = [], []
        self.cancelled_sku_codes = set()
        self.cancel_allocated_reference_ids = set()
        self.update_flags = {
            'allocation' : False,
            'picklist' : False
        }
    
    def get_queryset(self, args, kwargs, warehouse=None):
        return None

    def get_request_data(self):
        '''
        Get and format the request data
        '''
        errors = []
        request = self.request
        self.set_user_credientials()
        self.account_id = self.warehouse.userprofile.id
        try:
            request_data = loads(request.body)
        except Exception:
            try:
                request_data = request.POST.get("data")
            except Exception:
                errors = ['Invalid Payload']

        if not request_data:
            errors = ['Invalid Payload']
        if not errors and not isinstance(request_data, list):
            request_data = [request_data]
        return request_data, errors

    def get_order_details_from_request(self):
        '''
        Get unique order references from request data
        '''
        self.order_references, self.sku_codes = [], set()
        for order in self.request_data:
            order_reference = order.get('order_reference', '')
            if not order_reference:
                self.errors.append('Order reference is mandatory for update!')

            if order_reference and order_reference not in self.order_references:
                self.order_references.append(str(order_reference))
            for item in order.get('items', []):
                sku_code = item.get('sku', '')
                if sku_code and sku_code not in self.sku_codes:
                    self.sku_codes.add(sku_code)

    def get_order_details(self):
        '''
        Get OrderDetail data
        '''
        filter_data = {'user': self.warehouse.id,
                       'order_reference__in': self.order_references}
        order_values = ['id', 'user','order_id','original_order_id','customer_id','customer_name','email_id','address','telephone',
                        'sku_id','title','quantity','original_quantity','cancelled_quantity','invoice_amount','shipment_date','marketplace',
                        'order_code','vat_percentage','status','sku_code','city','state','pin_code','remarks','payment_mode','payment_received',
                        'creation_date','updation_date','mrp','unit_price','nw_status','order_type','order_reference','order_reference_date',
                        'promised_time','slot_from','slot_to','forward_time','estimated_dispatch_time','trip_id','sku_avg_price','sku_avg_price_rt',
                        'json_data','stock_count','is_backorder','distributor','network_work','line_reference','customer_po_num', 'account_id',
                        'customer_identifier_id',
                        ]

        order_objects = OrderDetail.objects.filter(**filter_data).select_related('sku', 'customer_identifier').only(*order_values, 'sku__sku_code', 'customer_identifier__customer_code')
        order_data_list, original_order_ids = [], set()
        for obj in order_objects:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['sku_code_'] = obj.sku.sku_code
            if obj.customer_identifier_id:
                obj_dict['customer_identifier__customer_code'] = obj.customer_identifier.customer_code
            obj_dict['object'] = obj
            order_data_list.append(obj_dict)
            original_order_ids.add(obj.original_order_id)

        self.order_detail_df = pd.DataFrame(order_data_list)
        self.order_type = ''
        if not self.order_detail_df.empty:
            self.order_type = self.order_detail_df['order_type'].iloc[0]
        self.order_extra_fields_data = {}
        self.extra_fields = set()
        if self.cancel_open_orders != 'true':
            filter_data = {'user': self.warehouse.id, 'original_order_id__in': original_order_ids}
            extra_fields_data = OrderFields.objects.filter(**filter_data)
            for obj in extra_fields_data:
                self.order_extra_fields_data.setdefault(obj.original_order_id, {})[obj.name] = obj
            existing_extra_order_fields = get_user_attributes(self.warehouse, 'create_order')
            self.extra_fields = set(existing_extra_order_fields.values_list('attribute_name', flat=True))

    def get_order_allocation_details(self):
        '''
        Get order allocation details
        '''
        filters = {'warehouse_id': self.warehouse.id, 'reference_number__in': self.order_references, 'status':1}
        order_allocation_objects = StockAllocation.objects.filter(**filters)
        order_allocation_data_list = []
        for obj in order_allocation_objects:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['object'] = obj
            order_allocation_data_list.append(obj_dict)
        self.order_allocation_df = pd.DataFrame(order_allocation_data_list)
    
    def get_order_picklist_details(self):
        '''
        Get picklist detail for which order under picking process
        '''
        self.picklist_batch_dict = {}
        filter_data = {'user_id': self.warehouse.id, 'reference_number__in': self.order_references}
        picklist_objects = Picklist.objects.select_related('stock').filter(**filter_data)
        picklist_data_list = []
        for obj in picklist_objects:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['object'] = obj
            picklist_data_list.append(obj_dict)
            self.picklist_batch_dict[obj.id] = obj.stock.batch_detail_id if obj.stock else None
        self.picklist_df = pd.DataFrame(picklist_data_list)


    def get_sos_details(self):
        '''
        Get seller order summary and order packing table details
        '''
        #filter and fetch sos data
        sos_filter = {'order__user': self.warehouse.id, 'order__order_reference__in': self.order_references}
        sos_objects = SellerOrderSummary.objects.filter(**sos_filter).select_related('order').only('id', 'order_status_flag', 'quantity', 'order_id', 'full_invoice_number', 'invoice_reference', 'updation_date', 'account_id', 'order__order_reference')
        sos_data_list = []
        for obj in sos_objects:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['order_reference'] = obj.order.order_reference
            obj_dict['object'] = obj
            sos_data_list.append(obj_dict)
        self.sos_df = pd.DataFrame(sos_data_list)

    def validate_and_prepare_updation_data(self):
        '''
        Validating and preparing data for order updation
        '''
        self.cancelled_picklist_ids, self.picklist_numbers, self.st_po_payload = [], [], []
        hold_order_validation_attributes = list(get_user_attributes(self.warehouse, 'hold_order_validation_fields').values_list('attribute_name', flat=True))
        for order in self.request_data:
            self.new_sku_codes, self.new_items, self.order_details, self.updated_order_ids, self.pull_to_locate_records, self.stock_records, self.order_extra_fields = [], [], {}, [], [], [], []
            order_reference = order.get('order_reference', '')
            replace = order.get('replace', False)
            self.remarks = order.get('remarks', '')
            self.reason_code = order.get('reason_code', '')
            self.reason = order.get('reason', '')
            self.validate_order_reference(order_reference)
            if self.errors:
                return

            items = order.get('items', [])
            self.validate_order_items(items, order_reference)
            if self.enable_sales_uom:
                items, errors = self.validate_sku_pack_data(items)
                if errors:
                    return

            self.validate_hold_order_validation_fields(hold_order_validation_attributes, order)
            st_po_payload = self.validate_st_po_updation(order_reference, order, replace_check=replace)
            if self.errors:
                return

            extra_fields = order.get('extra_fields', {})
            if extra_fields:
                original_order_id = self.order_details['original_order_id']
                self.validate_extra_fields(extra_fields, original_order_id)
            self.create_new_sku_items(order_reference, self.order_details)
            if replace:
                self.replaced_order_ref.append(order_reference)
                self.cancelled_order_items(order_reference)

            if order_reference not in self.updated_order_references:
                self.updated_order_references.append(order_reference)

            if st_po_payload:
                self.st_po_payload.append(st_po_payload)

    def prepare_st_po_payload(self, order, status='update', replace_check=False):
        '''
        Prepare st po payload
        '''        
        st_payload = {
            'po_reference': order.get('order_reference', ''),
            'supplier_id': self.warehouse.username,
            'po_type': "STOCKTRANSFER",
            'status': status,
            'update_order': False, 
            "items": []
        }
        if status in ['cancel'] or replace_check:
            st_payload['reason'] = 'SO Updated'
        if replace_check:
            st_payload['replace'] = True
        for item in order.get('items', []):
            aux_data = item.get('aux_data', {}) or {}
            item_dict = {
                "sku": item.get('sku', ''),
                "order_quantity": item.get('quantity', 0.0) or 0.0,
                "price": item.get('sale_price', 0.0) or 0.0,
                "mrp": item.get('mrp', 0.0) or 0.0,
                "remarks": item.get('remarks', ''),
                "sgst_tax": item.get('sgst_tax', 0.0) or 0.0,
                "cgst_tax": item.get('cgst_tax', 0.0) or 0.0,
                "igst_tax": item.get('igst_tax', 0.0) or 0.0,
                "aux_data": {}
            }
            if item.get('line_reference'):
                item_dict['aux_data']['line_reference'] = item.get('line_reference')
            elif aux_data.get('line_reference'):
                item_dict['aux_data']['line_reference'] = aux_data.get('line_reference')
            st_payload['items'].append(item_dict)
        return st_payload
    
    def validate_asn_generation(self, invoice_numbers, status='update'):
        '''
        Validate if ASN already generated for this order
        '''
        if status == 'update':
            return False
        
        asn_filters = {
            'source_warehouse_id': self.dest_user.id,
            'invoice_number__in': invoice_numbers,
        }
        asn_objs = ASNSummary.objects.filter(**asn_filters).exclude(status__in=[3, 8])
        if asn_objs.exists():
            return True
        return False
        

    def validate_st_po_updation(self, order_reference, order, status='update', replace_check=False):
        '''
        Validates and prepares the stock transfer purchase order (ST PO) payload for updating.

        This method checks if a stock transfer PO needs to be updated for the given order.
        It validates the order data, checks for ASN generation, and prepares the ST PO payload.

        Args:
            order_reference (str): The reference number of the order.
            order (dict): The order data containing details for updating.
            status (str, optional): The status of the update operation. Defaults to 'update'.
            replace_check (bool, optional): Flag to check if this is a replace operation. Defaults to False.

        Returns:
            dict: The prepared ST PO payload if update is required, otherwise an empty dict.

        Raises:
            None, but sets self.errors and self.status in case of validation failures.
        '''
        update_po = order.get('update_po', 'true') or 'true'
        if update_po.lower() == 'false':
            return {}
        order_df = self.order_detail_df[(self.order_detail_df['order_reference'] == str(order_reference))]
        json_data, st_payload = {}, {}
        customer_code = ''
        if not order_df.empty:
            if order_df['order_type'].iloc[0].lower() != 'stocktransfer':
                return st_payload
            json_data = order_df.iloc[0].get('json_data', {}) or {}
            customer_code = order_df.iloc[0].get('customer_identifier__customer_code', '') or ''
        is_po_created = json_data.get('create_st_po', True)
        self.dest_user = get_destination_user(customer_code)
        if not is_po_created or not self.dest_user:
            return st_payload
        if not self.sos_df.empty:
            invoice_numbers = self.sos_df[(self.sos_df['order_reference'] == str(order_reference))]['invoice_reference'].unique().tolist()
            available_asns = self.validate_asn_generation(invoice_numbers, status)
            if available_asns:
                self.status = 400
                self.errors.append('ASN already generated for this order, hence not allowing to update!')
                return st_payload
        po_obj = POHeader.objects.filter(warehouse_id=self.dest_user.id, po_reference=order_reference)
        if not po_obj.exists():
            return st_payload
        st_payload = self.prepare_st_po_payload(order, status, replace_check)
        failed_status, _ = validate_po_updation(st_payload, self.dest_user)
        if failed_status:
            self.errors.extend(failed_status)
            self.status = 400
        return st_payload

    def validate_order_reference(self, order_reference):
        '''
        Validate the order in open state or not
        '''
        order_df = self.order_detail_df[(self.order_detail_df['order_reference'] == str(order_reference))]
        if order_df.empty:
            self.errors.append('Order data not found!')
            return
        self.order_details = copy.deepcopy(order_df.iloc[0].to_dict())
        self.order_details['status'] = '1'


    def validate_hold_order_validation_fields(self, hold_order_validation_attributes, order):

        if not hold_order_validation_attributes:
            return

        mapping_fields = {
            'pincode': 'pin_code',
            'phone_number': 'telephone',
            'state': 'state',
            'city': 'city',
        }
        customer_master_fields = {
            'country': 'country',
        }

        order_reference = str(order.get('order_reference'))
        customer_details = {}
        for field, value in order.get('customer', {}).items():
            if field in hold_order_validation_attributes:
                customer_details[field] = value

        order_obj = Order.objects.filter(order_reference=order_reference, warehouse=self.warehouse).first()
        if not order_obj or order_obj.status != '6':
            return

        error_messages, _ = validate_attributes(self.warehouse, customer_details, 'hold_order_validation_fields')
        if error_messages:
            self.hold_order_refs.append(order_reference)

        customer_update = False
        for _, order_df in self.order_detail_df[self.order_detail_df['order_reference'] == order_reference].iterrows():
            for field in hold_order_validation_attributes:
                if not (field in mapping_fields or field in customer_master_fields) or not customer_details.get(field):
                    continue
                if field in customer_master_fields:
                    customer_update = True
                    continue
                key = mapping_fields[field]
                setattr(order_df['object'], key, customer_details.get(field))
                self.hold_order_update_fields.append(key)

        if customer_update:
            customer_obj = CustomerMaster.objects.filter(id=order_obj.customer_id)
            if customer_obj:
                customer_obj = customer_obj[0]
                for field, key in customer_master_fields.items():
                    setattr(customer_obj, key, customer_details.get(field))
                customer_obj.save(update_fields=list(customer_master_fields.values()))


    def validate_order_in_process(self, reference_id):
        '''
        Validate order is in under processing or not
        '''
        order_in_process = False
        if not self.picklist_df.empty and not self.picklist_df.loc[(self.picklist_df['reference_id'] == reference_id) & (self.picklist_df['status'].isin(['picked', 'cancelled'])) & (self.picklist_df['picked_quantity'] - self.picklist_df['cancelled_quantity'] > 0)].empty:
            order_in_process = True
        return order_in_process

    def order_df_confitions(self, item, line_reference, order_reference):
        '''
        Preparing order df conditions
        '''
        DATA_FRAME_NAME = 'self.order_detail_df'
        sku_code = item.get('sku_code') or item.get('sku', '')
        mrp = item.get('mrp')
        unit_price = item.get('sale_price')

        data_frame_filter = {f"{DATA_FRAME_NAME}['order_reference']": f"== '{order_reference}'"}
        if line_reference:
            data_frame_filter[f"{DATA_FRAME_NAME}['line_reference']"] =  f"== '{line_reference}'"
        elif sku_code:
            data_frame_filter[f"{DATA_FRAME_NAME}['sku_code_']"] = f"== '{sku_code}'"

            if mrp not in [None, '', 'null']:
                data_frame_filter[f"{DATA_FRAME_NAME}['mrp']"] = f"== {mrp}"
            if unit_price not in [None, '', 'null']:
                data_frame_filter[f"{DATA_FRAME_NAME}['unit_price']"] = f"== {unit_price}"

        conditions = [f"({column} {condition})" % {'column': column, 'condition': condition} for column, condition in data_frame_filter.items()]
        return conditions

    def validate_order_items(self, items, order_reference):
        '''
        Validating order item level details
        '''
        for item in items:
            sku_code = item.get('sku_code', '') or item.get('sku', '')
            json_data = item.get('aux_data', {}) or {}
            quantity = item.get('quantity', 0.0) or 0.0

            line_reference = item.get('line_reference') or json_data.get('line_reference', '')
            df_conditions = self.order_df_confitions(item, line_reference, order_reference)

            if self.order_detail_df[eval('&'.join(df_conditions))].empty:
                self.new_sku_codes.append(sku_code)
                self.new_items.append(item)
                continue

            self.validate_and_update_details(df_conditions, sku_code, quantity, json_data)

    def validate_and_update_details(self, df_conditions, sku_code, quantity, json_data):
        '''
        item detail validation and prepare the data for updation
        '''
        for index, order_df in self.order_detail_df[eval('&'.join(df_conditions))].iterrows():
            
            continue_loop = self.basic_item_validation(sku_code, quantity, order_df)
            if continue_loop:
                continue

            order_json = order_df['object'].json_data or {}
            pack_uom_quantity = order_json.get('pack_uom_quantity', 1)
            if not pack_uom_quantity:
                pack_uom_quantity = 1

            self.updated_order_ids.append(order_df['id'])
            if order_df['original_quantity'] < quantity:
                order_df['object'].quantity += quantity - order_df['object'].original_quantity
                order_df['object'].original_quantity = quantity
                if order_df['object'].original_quantity == 0:
                    order_df['object'].status = 3
                else:
                    order_df['object'].status = 1

            elif order_df['original_quantity'] > quantity:
                is_order_in_progress = self.validate_order_in_process(str(order_df['id']))
                if is_order_in_progress:
                    self.errors.append('%s - Item in progress, hence not allowing to update!'%sku_code)
                    continue
                
                removing_qty = order_df['object'].original_quantity - quantity
                min_qty = min(order_df['object'].quantity, removing_qty)
                if quantity != 0:
                    order_df['object'].original_quantity = quantity
                if quantity == 0:
                    order_df['object'].status = 3
                    order_df['object'].cancelled_quantity = min(order_df['object'].cancelled_quantity + removing_qty, order_df['object'].original_quantity)
                order_df['object'].quantity -= min_qty
                removing_qty -= min_qty
                if removing_qty:
                    # converting removing quantity to pack uom quantity
                    removing_qty *= pack_uom_quantity
                    removing_qty = self.prepare_allocation_updation_data(order_df['id'], removing_qty)
                    removing_qty = self.prepare_picklist_updation_data(order_df['id'], removing_qty)
                invoiced_quantity, allocated_quantity = 0, 0
                if not self.sos_df.empty:
                    invoiced_quantity = self.sos_df[(self.sos_df['order_status_flag'] == 'customer_invoices') & (self.sos_df['order_id'] == order_df['id'])]['quantity'].sum() or 0
                if not self.order_allocation_df.empty:
                    allocated_quantity = self.order_allocation_df[self.order_allocation_df['reference_id']==str(order_df['id'])]['quantity'].sum() or 0
                if order_df['object'].original_quantity - order_df['object'].cancelled_quantity == 0:
                    order_df['object'].status = 3
                elif order_df['object'].original_quantity - order_df['object'].cancelled_quantity == invoiced_quantity:
                    order_df['object'].status = 5
                elif order_df['object'].original_quantity - order_df['object'].cancelled_quantity == allocated_quantity:
                    order_df['object'].status = 20
                elif order_df['object'].quantity:
                    order_df['object'].status = 1
                else:
                    order_df['object'].status = 0
                if removing_qty:
                    self.errors.append('Given quantity more then order quantity!')
                    continue

            self.update_json_data(json_data, order_df)
            self.status = 200
    
    def basic_item_validation(self, sku_code, quantity, order_df, continue_loop=False):
        '''Basic item validation for order updation'''
        if order_df['sku_code_'] != sku_code:
            self.errors.append('Invalid sku code - %s'%sku_code)
            continue_loop = True

        if quantity != 0 and order_df['status'] == '3':
            self.errors.append(f'{sku_code} - Item Already Cancelled!')
            continue_loop = True
        
        return continue_loop

    def prepare_allocation_updation_data(self, reference_id, removing_qty):
        '''
        Updating order allocation quantity
        '''
        if self.order_allocation_df.empty:
            return removing_qty
        for index, order_allocation_df in self.order_allocation_df.loc[self.order_allocation_df['reference_id'] == str(reference_id)].iterrows():
            self.update_flags['allocation'] = True
            min_qty = min(order_allocation_df['object'].quantity, removing_qty)
            order_allocation_df['object'].quantity -= min_qty
            if order_allocation_df['object'].quantity == 0:
                order_allocation_df['object'].status = 3
                self.order_allocation_df.loc[self.order_allocation_df['id'] == order_allocation_df['id'], 'status'] = 3
            self.order_allocation_df.loc[self.order_allocation_df['id'] == order_allocation_df['id'], 'quantity'] = order_allocation_df['object'].quantity
            removing_qty -= min_qty
            if removing_qty <= 0:
                break
        return removing_qty

    def prepare_picklist_updation_data(self, reference_id, removing_qty):
        '''
        Updating open picklist quantity
        '''
        if self.picklist_df.empty:
            return removing_qty
        for index, pick_df in self.picklist_df.loc[(self.picklist_df['reference_id'] == str(reference_id)) & (self.picklist_df['status'] == 'open')].iterrows():
            self.update_flags['picklist'] = True
            min_qty = min(pick_df['reserved_quantity'], removing_qty)
            pick_df['object'].reserved_quantity -= min_qty
            pick_df['object'].picklist_quantity -= min_qty
            if pick_df['object'].picklist_quantity == 0 or (pick_df['object'].reserved_quantity == 0 and pick_df['object'].picked_quantity == 0):
                pick_df['object'].status = 'cancelled'
                self.cancelled_picklist_ids.append(pick_df['id'])
                self.picklist_numbers.append(pick_df['picklist_number'])
                self.picklist_df.loc[self.picklist_df['id'] == pick_df['id'], 'status'] = 'cancelled'
            self.picklist_df.loc[self.picklist_df['id'] == pick_df['id'], 'object'] = pick_df['object']
            self.picklist_df.loc[self.picklist_df['id'] == pick_df['id'], 'reserved_quantity'] = pick_df['object'].reserved_quantity
            
            removing_qty -= min_qty
            if removing_qty <= 0:
                break

        return removing_qty

    def update_json_data(self, json_data, order_df):
        '''
        Validate picklist already generated for this order or not, if not then only we are allowing for update batch details
        '''
        order_json_data = order_df['object'].json_data or {}
        if not (json_data and (self.picklist_df.empty or self.picklist_df.loc[(self.picklist_df['reference_id'] == str(order_df['id']))].empty) and (self.order_allocation_df.empty or self.order_allocation_df.loc[(self.order_allocation_df['reference_id'] == str(order_df['id']))].empty)):
            return
        for field_name in ORDER_JSON_UPDATE_FIELDS:
            if order_json_data.get(field_name) != json_data.get(field_name) and json_data.get(field_name) is not None:
                order_json_data[field_name] = json_data.get(field_name)
        order_df['object'].json_data = order_json_data

    def update_details(self):
        '''
        validating order and picklist details
        '''
        try:
            with transaction.atomic('default'):
                self.update_order_data()
                self.update_allocation_data()
                self.update_picklist_data()
                self.update_sos_data()
                self.create_po_locations()
                self.update_lms_task_status()
                self.update_pick_and_pass_tasks()
                self.update_wip_stock()
                self.update_st_po_payload()
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("Order Updation Process failed with error- %s" % str(e))
            self.errors.append('Order Updation Failed!')

    def update_st_po_payload(self):
        '''
        update the st po payload
        '''
        if self.st_po_payload:
            log.info("ST PO payload: %s" % self.st_po_payload)
            status = update_po(self.st_po_payload, [], self.dest_user, self.user.username)
            log.info("ST PO payload updated with status: %s" % status)

    def update_order_data(self):
        '''
        Update orders
        '''
        if self.order_detail_df['object'].tolist():
            update_fields = ['quantity', 'original_quantity', 'cancelled_quantity', 'status', 'mrp', 'unit_price', 'json_data', 'remarks']
            if self.hold_order_update_fields:
                update_fields.extend(self.hold_order_update_fields)
                update_fields = list(set(update_fields))
            OrderDetail.objects.bulk_update_with_rounding(self.order_detail_df['object'].tolist(), update_fields, decimal_places=self.decimal_limit)
        if self.new_extra_fields:
            OrderFields.objects.bulk_create(self.new_extra_fields)
        if self.update_extra_fields:
            OrderFields.objects.bulk_update_with_rounding(self.update_extra_fields, ['value'])
        orders_to_update = list(set(self.order_references) - set(self.hold_order_refs))
        update_order_header_status(self.warehouse, orders_to_update)
    
    def update_allocation_data(self):
        '''
        Update order allocation details
        '''
        if self.update_flags['allocation'] and not self.order_allocation_df.empty and self.order_allocation_df['object'].tolist():
            StockAllocation.objects.bulk_update_with_rounding(self.order_allocation_df['object'].tolist(), ['quantity', 'status'], decimal_places=self.decimal_limit)
        
        if self.cancel_allocated_reference_ids:
            StockAllocation.objects.filter(reference_id__in=self.cancel_allocated_reference_ids, warehouse_id=self.warehouse.id, status=1, reference_model='OrderDetail').update(status=3, updation_date=datetime.now(), updated_by_id=self.user.id)

    def update_picklist_data(self):
        '''
        Update picklist details
        '''
        if self.update_flags['picklist'] and not self.picklist_df.empty and self.picklist_df['object'].tolist():
            Picklist.objects.bulk_update_with_rounding(self.picklist_df['object'].tolist(), ['reserved_quantity', 'picklist_quantity', 'cancelled_quantity', 'status', 'json_data'], decimal_places=self.decimal_limit)

    def update_sos_data(self):
        '''
        Update sos details
        '''

        if self.sos_df.empty:
            return

        sos_update_objs = self.sos_df[self.sos_df['id'].isin(self.sos_ids)]['object'].tolist()
        if sos_update_objs:
            SellerOrderSummary.objects.bulk_update(sos_update_objs, ['order_status_flag'])

    def create_po_locations(self):
        '''
        Create pull to locate entries
        '''
        if self.pull_to_locate_records:
            POLocation.objects.bulk_create_with_rounding(self.pull_to_locate_records, batch_size=250, decimal_places=self.decimal_limit)
        
    def update_wip_stock(self):
        '''
        clears wip locations stock
        '''
        if self.stock_records:
            StockDetail.objects.bulk_update_with_rounding(self.stock_records, ['quantity'], decimal_places=self.decimal_limit)

    def update_lms_task_status(self):
        '''
        Update lms task status
        '''
        if self.cancelled_picklist_ids:
            TaskMaster.objects.filter(task_ref_id__in=self.cancelled_picklist_ids).update(status=1, updation_date=datetime.now())

    def update_pick_and_pass_tasks(self):
        """
        Update the status of pick and pass tasks based on the open subzones.
        """
        picklist_numbers = list(Picklist.objects.filter(picklist_number__in=self.picklist_numbers, user_id=self.warehouse.id, status='open').values_list('picklist_number', flat=True).distinct())
        picklist_numbers = [str(picklist_number) for picklist_number in picklist_numbers if picklist_number]
        PickAndPassStrategy.objects.filter(warehouse_id=self.warehouse.id, reference_number__in=self.picklist_numbers).exclude(reference_number__in=picklist_numbers).update(status='cancelled')

    def create_new_sku_items(self, order_reference, order_details):
        '''
        Create new sku codes
        '''
        sku_dict = {}
        if self.new_items and order_details:
            sku_details = SKUMaster.objects.filter(user=self.warehouse.id, sku_code__in=self.new_sku_codes).values()
            for sku in sku_details:
                sku_key = str(sku['sku_code'])
                if sku['sku_code'] not in sku_dict:
                    sku_dict[sku_key] = sku
            del order_details['sku_code_']
            failed_status = create_new_sku_in_existing_order(self.new_items, sku_dict, order_details, order_details.get('creation_date'), order_reference, self.warehouse, self.user, self.call_type, failed_status={})
            errors = failed_status.get('errors', []) or []
            if errors:
                self.errors.extend(errors)

    def cancel_order_details_callback(self, cancelled_order_ids, sku_codes):
        '''
        Cancel order details callback.
        '''
        integration_filters = {
            'order_references': cancelled_order_ids,
            'sku_codes' : list(sku_codes)
        }
        webhook_integration_3p(self.warehouse.id, "cancel_order", filters=integration_filters)

    def cancelled_order_items(self, order_reference):
        '''
        Cancelling missing order items for update and cancel order items
        '''
        self.stock_records = []
        self.prepare_unique_sos_details(order_reference)
        order_ids = self.order_detail_df[self.order_detail_df['order_reference'] == order_reference]['id'].tolist()
        missing_ids = set(order_ids) - set(self.updated_order_ids)
        if missing_ids:
            cancelled_order_ids = []
            for index, order_record in self.order_detail_df[self.order_detail_df['id'].isin(list(missing_ids))].iterrows():
                if self.cancel_open_orders == 'true' and order_record['object'].quantity > 0:
                    cancelled_quantity = order_record['object'].quantity
                    order_record['object'].cancelled_quantity = min(order_record['object'].cancelled_quantity + order_record['object'].quantity, order_record['object'].original_quantity)
                    invoiced_quantity, allocated_quantity = 0, 0
                    if not self.sos_df.empty:
                        invoiced_quantity = self.sos_df[(self.sos_df['order_status_flag'] == 'customer_invoices') & (self.sos_df['order_id'] == order_record['id'])]['quantity'].sum() or 0
                    if not self.order_allocation_df.empty:
                        allocated_quantity = self.order_allocation_df[self.order_allocation_df['reference_id']==str(order_record['id'])]['quantity'].sum() or 0
                    if order_record['object'].original_quantity == order_record['object'].cancelled_quantity:
                        order_record['object'].status = 3
                    elif order_record['object'].original_quantity - order_record['object'].cancelled_quantity == invoiced_quantity:
                        order_record['object'].status = 5
                    elif order_record['object'].original_quantity - order_record['object'].cancelled_quantity == allocated_quantity:
                        order_record['object'].status = 20
                    else:
                        order_record['object'].status = 0
                    if self.order_update and order_record['id'] in self.sos_quantity_details and self.sos_quantity_details[order_record['id']] + cancelled_quantity == order_record['object'].original_quantity:
                        order_record['object'].status = '5'
                elif self.cancel_open_orders == 'false':
                    order_record['object'].status = 3
                    cancelled_order_ids.append(order_record['id'])
                    cancelled_quantity = order_record['object'].original_quantity
                    order_record['object'].cancelled_quantity = order_record['object'].original_quantity
                    if self.order_update and order_record['id'] in self.sos_quantity_details and self.sos_quantity_details[order_record['id']] + cancelled_quantity == order_record['object'].original_quantity:
                        order_record['object'].status = '5'
                order_record['object'].quantity = 0
                order_record['quantity'] = 0
                order_record['object'].remarks = self.remarks
                json_data = order_record['object'].json_data or {}
                json_data.update({
                    'cancelled_by_user': self.user.username,
                    'cancelled_from': self.call_type,
                    'reason': self.reason,
                    'reason_code': self.reason_code,
                })
                self.cancelled_sku_codes.add(order_record['sku_code_'])
                order_record['object'].json_data = json_data
            if cancelled_order_ids:
                self.update_sos_and_picklist(cancelled_order_ids)
    
    def prepare_unique_sos_details(self, order_reference):
        '''Prepare unique sos details'''
        self.sos_quantity_details = {}
        if self.order_update and not self.sos_df.empty:
            for index, sos_data in self.sos_df[self.sos_df['order_reference'] == order_reference].iterrows():
                if sos_data['order_id'] not in self.sos_quantity_details:
                    self.sos_quantity_details[sos_data['order_id']] = sos_data['object'].quantity
                else:
                    self.sos_quantity_details[sos_data['order_id']] += sos_data['object'].quantity

    def update_sos_and_picklist(self, cancelled_order_ids):
        '''
        Update picklist, seller order summary cancelled quantity, status
        '''
        if not self.sos_df.empty:
            for index, sos_data in self.sos_df[self.sos_df['order_id'].isin(cancelled_order_ids)].iterrows():
                sos_data['object'].order_status_flag = 'cancelled'
                self.sos_ids.append(sos_data['id'])

        if not self.picklist_df.empty:
            for index, picklist_record in self.picklist_df[self.picklist_df['order_id'].isin(cancelled_order_ids)].iterrows():
                self.update_flags['picklist'] = True
                cancelled_quantity = picklist_record['picked_quantity'] - picklist_record['cancelled_quantity']
                picklist_record['object'].cancelled_quantity += picklist_record['reserved_quantity']
                picklist_record['object'].reserved_quantity = 0
                picklist_record['object'].status = 'cancelled'
                self.cancelled_picklist_ids.append(picklist_record['id'])
                self.picklist_df.loc[self.picklist_df['id'] == picklist_record['id'], 'object'] = picklist_record['object']
                self.picklist_numbers.append(picklist_record['picklist_number'])
                if cancelled_quantity:
                    self.prepare_cancel_pull_to_locate(picklist_record, cancelled_quantity)
        
        if not self.order_allocation_df.empty:
            self.cancel_allocated_reference_ids = set(map(str, cancelled_order_ids))

        if self.sos_ids:
            self.clear_wip_stock()

    def prepare_cancel_pull_to_locate(self, picklist_record, cancelled_quantity):
        '''
        prepare po location dict for pull to locate
        '''
        po_location_dict = {
            'picklist_id' : picklist_record['id'],
            'quantity': cancelled_quantity,
            'original_quantity':cancelled_quantity,
            'status': 1,
            'location_id': picklist_record['location_id'],
            'sku_id':picklist_record['sku_id'],
            'putaway_type':'cancelled_picklist',
            'account_id': self.account_id,
            'reference_number': picklist_record['picklist_number']
        }
        if self.picklist_batch_dict.get(picklist_record['id'],None):
            po_location_dict['batch_detail_id'] = self.picklist_batch_dict.get(picklist_record['id'])
        self.pull_to_locate_records.append(POLocation(**po_location_dict))

    def clear_wip_stock(self):
        '''
        Clear wip stock for given sos ids
        '''
        stock_filter = {
            'sku__user': self.warehouse.id,
            'receipt_number__in': self.sos_ids,
            'receipt_type__in': ['so_picking', 'so_dispense'],
            'location__zone__segregation': 'outbound_staging',
            'quantity__gt': 0
        }
        stock_objs = list(StockDetail.objects.filter(**stock_filter))
        for stock in stock_objs:
            stock.quantity = 0
            self.stock_records.append(stock)

    def get_order_allocation_picklist_sos_details(self):
        '''
        Get order detail details and picklist details for given reference
        '''
        self.get_order_details()
        if self.order_detail_df.empty:
            self.errors.append('Order Data not found!')
            return
        if self.cancel_open_orders == 'true':
            self.order_detail_df = self.order_detail_df[(self.order_detail_df['status'] == "1")]
        if self.order_detail_df.empty:
            self.errors.append('No Open Quantity to Cancel!')
            return

        self.get_order_allocation_details()
        self.get_order_picklist_details()
        self.get_sos_details()

    def get_existing_details(self):
        '''
        Get all required details for update
        '''
        self.get_order_details_from_request()
        if self.errors:
            return

        self.get_order_allocation_picklist_sos_details()
        if self.enable_sales_uom:
            self.get_sku_pack_order_type_details()

    def order_updates(self, request_data):
        '''
        Start order updation process
        '''
        self.updated_order_references, self.errors = [], []
        self.request_data = request_data
        self.cancel_open_orders = 'false'
        self.order_update = True

        try:
            self.get_existing_details()
            if self.errors:
                return {'errors': self.errors}

            self.status = 400
            self.validate_and_prepare_updation_data()
            if self.status == 200:
                self.update_details()
                if self.errors:
                    self.status = 207

            message = 'Order Updated Successfully!'
            if self.errors:
                message = ''
            if message == 'Order Updated Successfully!':
                self.status =  200
            return {
                'errors': self.errors,
                'message': message,
                'order_references': self.updated_order_references,
                'status': self.status
            }

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("Order Updation Process failed with error- %s" % str(e))
            self.errors.append('Order Updation Failed!')
            return {'errors': self.errors}

    def fetch_existing_order_references(self, order_references: list):
        """
        Fetches existing order references from the database.
        This method takes a list of order references and returns a list of those
        that exist in the database for the current warehouse.
        Args:
            order_references (list): A list of order reference strings to check.
        Returns:
            list: A list of existing order references that match the input list.
              If no order references are provided, an empty list is returned.
        """

        if not order_references:
            return []

        order_filter = {'warehouse': self.warehouse, 'order_reference__in': order_references}
        return list(Order.objects.filter(**order_filter).values_list('order_reference', flat=True))

    def prepare_create_orders_data(self, update_orders_list: List[Dict]):
        """
        Prepares data for creating new orders and updating existing orders.
        This method takes a list of orders to be updated, segregates them into
        new orders to be created and existing orders to be updated based on
        their order references.
        Args:
            update_orders_list (list): A list of dictionaries where each dictionary
                           represents an order with an 'order_reference' key.
        Returns:
            tuple: A tuple containing two lists:
               - The first list contains dictionaries of new orders to be created.
               - The second list contains dictionaries of existing orders to be updated.
        """

        update_orders_dict = {order.get('order_reference'): order for order in update_orders_list}
        order_references = list(update_orders_dict.keys())

        # Fetch existing order references
        existing_order_references = self.fetch_existing_order_references(order_references)

        create_orders_dict = {}
        for order_reference in order_references:
            if str(order_reference) not in existing_order_references:
                create_orders_dict[order_reference] = update_orders_dict.pop(order_reference)

        return list(create_orders_dict.values()), list(update_orders_dict.values())


    def post(self, *args, **kwargs):
        self.request_data, errors = self.get_request_data()
        if errors:
            return JsonResponse({'errors': errors}, status=400)

        order_request = RequestFactory
        order_request.user = self.request.user
        order_request.warehouse = self.warehouse
        order_request.method= 'POST'
        order_request.headers = self.request.headers
        order_request.META = self.request.META
        order_request.body = dumps(self.request_data)
        order = OrdersDetailsSet()
        order.request = order_request
        return order.post()

    def put(self, *args, **kwargs):

        self.request_data, errors = self.get_request_data()
        if errors:
            return JsonResponse({'errors': errors}, status=400)
        self.call_type = self.request_data[0].get('call_type', 'API')
        # iterate through orders if duplicate orders exists return error
        unique_order_references = set()
        duplicate_orders = set()
        unique_approval_order_references = []
        update_orders_list, create_orders_list, hold_orders_list, delete_orders_list = [], [], [], []
        self.replaced_order_ref = []
        self.redis_cache_keys, self.errors = [], []
        self.add_cache()
        if self.errors:
            self.delete_cache()
            return JsonResponse({'errors': self.errors}, status=400)
        
        order_approval_order_types = get_misc_options_list(['approval_order_types'], self.warehouse).get('approval_order_types', [])
        for order in self.request_data:
            order_reference = order.get('order_reference', '')
            order_type = order.get('order_type', '')
            if not order_reference:
                self.delete_cache()
                return JsonResponse({'errors': ['Order reference is mandatory for update!']}, status=400)

            if order_reference in unique_order_references:
                duplicate_orders.add(order_reference)
            else:
                unique_order_references.add(order_reference)

            if order.get('status', 'update') in  ['cancel', 'Cancel']:
                delete_orders_list.append(order)
            elif order.get('status', '').lower() in ['hold', 'release']:
                hold_orders_list.append(order)
            else:
                update_orders_list.append(order)
            
            if order_type.lower() in order_approval_order_types:
                unique_approval_order_references.append(order_reference)
        
        if unique_approval_order_references:
            unique_approval_order_references = list(set(unique_approval_order_references))
            on_hold_orders = Order.objects.filter(
                order_reference__in=unique_approval_order_references,
                warehouse=self.warehouse, status=6, approval_status='pending').values_list('order_reference', flat=True)
            if on_hold_orders:
                self.delete_cache()
                return JsonResponse({'errors': ['Order(s) %s are on hold!'%','.join(on_hold_orders)]}, status=400)

        if duplicate_orders:
            self.delete_cache()
            return JsonResponse({'errors': ['Duplicate Order References - %s'%','.join(duplicate_orders)]}, status=400)

        self.fetch_required_configurations()

        response_data = {
                'message': 'order updated successfully!',
                'order_references': [],
                'errors': []
            }

        create_orders_list, update_orders_list = self.prepare_create_orders_data(update_orders_list)
        if create_orders_list:
            # create new orders
            order_request = RequestFactory
            order_request.user = self.request.user
            order_request.warehouse = self.warehouse
            order_request.method= 'POST'
            order_request.headers = self.request.headers
            order_request.META = self.request.META
            order_request.body = dumps(create_orders_list)
            order = OrdersDetailsSet()
            order.request = order_request
            response = order.post()
            self.delete_cache()
            return response
        if update_orders_list:
            response_data = self.order_updates(update_orders_list)
            if response_data.get('errors'):
                self.delete_cache()
                return JsonResponse(response_data, status=response_data.get('status', 400))
        if hold_orders_list:
            order_hold_obj = OrderHoldMixin(self.user, self.warehouse, hold_orders_list)
            response_data = order_hold_obj.process_order_hold()
            response_data.pop('error_data', None)
            if response_data.get('errors'):
                self.delete_cache()
                return JsonResponse(response_data, status=response_data.get('status', 400))
        temp_response = {}
        if delete_orders_list:
            delete_order_obj = DeleteOrdersMixin(self.warehouse, self.request_data, self.request)
            temp_response = delete_order_obj.delete_orders()
            if temp_response.get('errors'):
                response_data['message'] = ''
                response_data['errors'].extend(temp_response.get('errors'))
                self.delete_cache()
                return JsonResponse(response_data, status=400)
        deleted_orders = temp_response.get('orders_updated', [])
        sku_codes = temp_response.get('skus_updated', set())
        if response_data.get('order_references'):
            response_data['order_references'].extend(list(deleted_orders))
        else:
            response_data['order_references'] = list(deleted_orders)
            
        if unique_approval_order_references:
            so_status = OrdersDetailsSet().order_approval_creation(
                self.request, self.warehouse, {}, unique_approval_order_references
            )
            if not so_status:
                response_data['errors'].append('Order Approval Creation Failed!')
                self.delete_cache()
                return JsonResponse(response_data, status=400)
            response_data['message'] = 'Order Approval Created Successfully!'
            
        if deleted_orders or self.replaced_order_ref:
            del_order_refs = list(set(deleted_orders) | set(self.replaced_order_ref))
            sku_codes = list(sku_codes | self.cancelled_sku_codes)
            self.cancel_order_details_callback(del_order_refs, sku_codes)
        
        self.delete_cache()
        return JsonResponse(response_data, status=200)

    def delete(self, *args, **kwargs):
        self.errors, self.updated_order_ids, self.pull_to_locate_records = [], [], []
        self.set_user_credientials()
        self.account_id = self.warehouse.userprofile.id
        self.request_data = self.request.GET
        self.cancel_open_orders = self.request_data.get('cancel_open_orders', "false")
        self.call_type = self.request_data.get('source', 'API')
        self.order_update = False
        self.reason = self.request_data.get('reason', '')
        self.reason_code = self.request_data.get('reason_id', '') if self.reason else ''
        self.remarks = self.request_data.get('remarks', '')

        order_references = self.request_data.get('order_reference', '') or ''
        if order_references:
            order_references = str(order_references)

        if not order_references:
            return JsonResponse({'errors': ['Order reference is mandatory for cancel!']}, status=400)

        self.fetch_required_configurations()

        self.order_references = [ele.strip() for ele in order_references.split(',')]
        self.get_order_allocation_picklist_sos_details()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)

        cancelled_order_reference, self.picklist_numbers, self.cancelled_picklist_ids, self.st_po_payload = [], [], [], []
        for order_reference in self.order_references:
            try:
                st_asn_payload = self.validate_st_po_updation(order_reference, self.request_data, "cancel")
                self.cancelled_order_items(order_reference)
                cancelled_order_reference.append(order_reference)
                if st_asn_payload:
                    self.st_po_payload.append(st_asn_payload)
            except Exception as e:
                import traceback
                log.debug(traceback.format_exc())
                log.info("Order Cancellation Process failed with error- %s" % str(e))
                self.errors.append('Order Cancellation Failed!')

        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        else:
            self.update_details()
        
        Order.objects.filter(
            order_reference__in=cancelled_order_reference, warehouse=self.warehouse, approval_status='pending'
        ).update(approval_status='cancelled', status=3, updation_date=datetime.now(), updated_by=self.user)
            
        if  cancelled_order_reference:
            self.cancel_order_details_callback(cancelled_order_reference, self.cancelled_sku_codes)

        return JsonResponse({'message': 'Order Cancelled Successfully!', 'order_references': cancelled_order_reference}, status=200)

    def add_cache(self):
        """
            adds cache for order references
        """
        for order in self.request_data:
            order_reference = order.get('order_reference', '')
            cache_key = f"order_update_{self.warehouse.username}_{order_reference}"
            cache_status = cache.add(cache_key, 1, timeout=LOCK_EXPIRE)
            if not cache_status:
                self.errors.append(f"Order Reference - {order_reference} updation is already in process")
                return    
            self.redis_cache_keys.append(cache_key)
    
    def delete_cache(self):
        """
            deletes the cache for order references
        """
        for key in self.redis_cache_keys:
            cache.delete(key)
        

    def fetch_required_configurations(self):
        """
        Fetches and sets the required configurations for the warehouse.
        This method retrieves miscellaneous configuration values for the warehouse,
        such as the decimal limit, and sets them as instance variables.
        Attributes:
            misc_dict (dict): A dictionary containing miscellaneous configuration values.
        Miscellaneous Types:
            - 'decimal_limit': The limit for decimal values.
        """

        misc_types = ['decimal_limit', 'enable_sales_uom']
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        self.decimal_limit= 0
        self.enable_sales_uom = (self.misc_dict.get('enable_sales_uom') or 'false') == 'true'
        decimal_limit = self.misc_dict.get('decimal_limit')
        if decimal_limit and isinstance(decimal_limit, str) and decimal_limit.isdigit():
            self.decimal_limit = int(self.misc_dict.get('decimal_limit'))

    def get_sku_pack_order_type_details(self):
        self.sku_pack_quantity_dict, self.sku_pack_dict = {}, {}
        sku_pack_filters = {
            'sku__user': self.warehouse.id,
            'sku__sku_code__in': self.sku_codes,
            'sales_uom' : 1,
            'status' : 1
        }
        sku_pack_data = SKUPackMaster.objects.filter(**sku_pack_filters).values('sku__sku_code', 'pack_quantity', 'pack_id').order_by('pack_quantity')
        for pack in sku_pack_data:
            self.sku_pack_quantity_dict[(pack['sku__sku_code'], pack['pack_quantity'])] = pack['pack_id']
            self.sku_pack_dict[pack['sku__sku_code']] = (pack['pack_quantity'], pack['pack_id'])
        if self.order_type:
            order_type_filters = {
                'user': self.warehouse.id,
                'order_type': self.order_type,
                'sales_uom' : 1
            }
            self.sales_uom_order = OrderTypeZoneMapping.objects.filter(**order_type_filters).exists()
        
    def validate_sku_pack_data(self, items):
        '''
        Validate sku pack data
        '''
        sales_uom_qty_dict, existing_skus, errors = {}, set(), []
        for index, row in self.order_detail_df.iterrows():
            sku_code = row['sku_code']
            json_data = row['json_data']
            sales_uom_qty_dict[sku_code] = (json_data.get('pack_uom_quantity',0), json_data.get('pack_id',''))
            existing_skus.add(sku_code)
            
        for item in items:
            sku_code = item.get('sku', '')
            json_data = item.get('aux_data', {}) or {}
            pack_uom_quantity = json_data.get('pack_uom_quantity', 0)
            if pack_uom_quantity and not self.sales_uom_order:
                errors.append("Sales UOM is not enabled for given Order Type")
            elif self.sales_uom_order:
                if sku_code in existing_skus and pack_uom_quantity and sales_uom_qty_dict[sku_code][0] != pack_uom_quantity:
                    errors.append("Sales UOM updation is not allowed for existing SKU - %s" % sku_code)
                elif sales_uom_qty_dict.get(sku_code) and pack_uom_quantity and sales_uom_qty_dict[sku_code][0] != pack_uom_quantity:
                    errors.append("Multiple Sales UOM Quantity is not allowed for sku - %s" % sku_code)
                pack_data = sales_uom_qty_dict.get(sku_code) or self.sku_pack_dict.get(sku_code) or {}
                if pack_uom_quantity:
                    if self.sku_pack_quantity_dict.get((sku_code, pack_uom_quantity)):
                        json_data.update({
                            'pack_uom_quantity': pack_uom_quantity,
                            'pack_id' : self.sku_pack_quantity_dict[(sku_code, pack_uom_quantity)]
                        })
                        sales_uom_qty_dict[sku_code] = (pack_uom_quantity, self.sku_pack_quantity_dict[(sku_code, pack_uom_quantity)])
                    else:
                        errors.append("Given Sales UOM Quantity is not defined")
                elif pack_data:
                    json_data.update({
                        'pack_uom_quantity' : pack_data[0],
                        'pack_id' : pack_data[1]
                    })
                    sales_uom_qty_dict[sku_code] = pack_data
        
            item['aux_data'] = copy.deepcopy(json_data)
        
        self.errors.extend(errors)
        return items, errors
    
    def validate_extra_fields(self, extra_fields, original_order_id):
        '''
        Validate extra fields
        '''
        errors = []
        extra_fields_data = self.order_extra_fields_data.get(original_order_id,{})
        for name, value in extra_fields.items():
            extra_field_obj = extra_fields_data.get(name)
            if name not in self.extra_fields:
                errors.append('Invalid Extra Field - %s'%name)
            elif not extra_field_obj:
                new_obj = OrderFields(name=name, value=value, original_order_id=original_order_id, user=self.warehouse.id, account_id=self.account_id)
                self.new_extra_fields.append(new_obj)
            elif extra_field_obj.value != value:
                extra_field_obj.value = value
                self.update_extra_fields.append(extra_field_obj)
        
        self.errors.extend(errors)
