#package imports
import traceback
from json import loads, dumps
import pandas as pd
from collections import defaultdict

#django imports
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, F, Q, Count, Value, When, Case, IntegerField
from django.utils import timezone
from django.contrib.postgres.aggregates import ArrayAgg

#wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger

#core imports
from core.models import AuthorizedBins, TempJson, EANNumbers
from core_operations.views.common.main import (
    get_misc_value, get_user_ip, WMSListView, create_default_zones, get_multiple_misc_values,
    get_local_date_known_timezone, get_misc_options_list, get_sku_ean_numbers
)
from core_operations.views.services.packing_service import PackingService


#inventory imports
from inventory.models import LocationMaster, StockDetail

#outbound imports
from outbound.models import (
    StagingInfo, OrderPackaging, Picklist, SellerOrderSummary, OrderDetail, OrderTypeZoneMapping
)
from outbound.serializers import StagingInfoSerializer
from outbound.views.packing.main import PackingView
from outbound.views.sorting.helpers import validate_suggested_sorting_locations


log = init_logger('logs/sorting.log')


class Sorting(WMSListView):

    def post(self, *args, **kwargs):
        try:
            request_data = loads(self.request.body)
        except Exception:
            return HttpResponse(dumps({'message': 'Please send proper data'}))
        log.info("Request Sorting username %s, Ip Address %s, and params are %s"% (str(self.request.user.username),str(get_user_ip(self.request)), str(request_data)))
        picklist_no_list = []

        self.set_user_credientials()
        log.info('Request params for StagingInfo Post params for ' + self.request.user.username + ' is ' + str(request_data))
        loc = None
        data_ids = []
        reuse_carton = get_misc_value('reuse_carton', self.warehouse.id)
        if isinstance(request_data, dict) and request_data.get('is_final_drop'):
            pending_cartons_to_drop = self.prepare_pending_soring_drop_cartons_details(self.warehouse, request_data)
        else:
            pending_cartons_to_drop = request_data

        picklist_no = ''
        for data in pending_cartons_to_drop:
            picklist_no = data['picklist_number']
            if picklist_no not in picklist_no_list:
                picklist_no_list.append(picklist_no)
            carton_no = data['package_reference']
            segregation_type = data['segregation_type']
            location = data['location']
            status = data['status']
            st_info = StagingInfo.objects.filter(user=self.warehouse, segregation=segregation_type, order_reference=picklist_no,
                                       status__in=[2,0])
            st_info_carton = st_info.filter(location__location=location, location__status=1)
            if st_info_carton.exists():
                loc = st_info_carton[0].location
            else:
                if st_info.exists():
                    st_info.delete()
                location_records = LocationMaster.objects.filter(zone__user=self.warehouse.id, status=1,
                                                                 zone__segregation=segregation_type,
                                                                 max_capacity__gt=F('filled_capacity')).\
                    exclude(staginginfo__status__in=[0,2]).order_by('fill_sequence')
                if location_records.exists():
                    loc = location_records[0]
            if not loc:
                return HttpResponse(dumps({"message": "Sorting Location not found"}), status=400)

            OrderPackaging.objects.filter(seller_order_summary__picklist__sku__user=self.warehouse.id, seller_order_summary__picklist__picklist_number=picklist_no,status=0, package_reference=carton_no, json_data__sorting_status="completed").update(status=1)
            AuthorizedBins.objects.filter(user_id = self.warehouse.id, bin_number = carton_no).update(status = 1)

            if not st_info_carton.exists():
                st_info_carton = StagingInfo.objects.create(user=self.warehouse, segregation=segregation_type, location=loc,
                                           carton_no=carton_no,
                                           order_reference=picklist_no,
                                           status=status)
            else:
                st_info_carton.update(status=status, carton_no=carton_no)
                st_info_carton = st_info_carton[0]
            data_ids.append(st_info_carton.id)
        if picklist_no:
            self.update_staging_details(picklist_no)

        queryset = StagingInfo.objects.filter(id__in=data_ids)
        serializer = StagingInfoSerializer(queryset, many=True)
        data_dict = {'total_records': queryset.count(), 'data': serializer.data}
        if picklist_no_list and reuse_carton == 'true':
            try:
                self.picked_cartons_updation(self.warehouse,picklist_no_list)
            except Exception:
                pass
        return HttpResponse(dumps(data_dict))

    def update_staging_details(self, picklist_no):
        pending_ob_cartons = self.get_pending_sorting_to_ob_staging_area(self.warehouse, {'picklist_number': picklist_no})
        if not pending_ob_cartons.get('data', []):
            ob_pending_staging_objs = StagingInfo.objects.filter(user=self.warehouse.id, status=2, order_reference=str(picklist_no), segregation="sorting", location__status=1)
            if ob_pending_staging_objs.exists():
                for staging_obj in ob_pending_staging_objs:
                    staging_obj.status = 1
                StagingInfo.objects.bulk_update_with_rounding(ob_pending_staging_objs, ['status'])


    def prepare_pending_soring_drop_cartons_details(self, user, request_data):
        sorting_cartons = self.get_pending_sorting_to_ob_staging_area(user, request_data)
        picklist_number = request_data.get('picklist_number')
        sorting_location = request_data.get('location', '')
        segregation_type = request_data.get('segregation_type', '')
        pending_cartons = []
        for carton in sorting_cartons:
            data_dict = {
                'location': sorting_location,
                'package_reference': carton.get('package_reference', 0),
                'picklist_number': picklist_number,
                'segregation_type': segregation_type,
                'status': 0
            }
            pending_cartons.append(data_dict)
        return pending_cartons


    def get_pending_sorting_to_ob_staging_area(self, user, request_data):
        st_info = list(OrderPackaging.objects.filter(seller_order_summary__picklist__sku__user=user.id, seller_order_summary__picklist__picklist_number=request_data["picklist_number"],
                                       status=0, package__status=0, json_data__sorting_status="completed").values("package_reference",
                                                            picklist_number=F("seller_order_summary__picklist__picklist_number")).distinct().annotate(packed_quantity=Sum('packed_quantity')))
        final_output_data ={"data": st_info,  "message": "success"}
        return final_output_data

    def picked_cartons_updation(self,user,picklist_numbers):
        picked_cartons = list(OrderPackaging.objects.filter(seller_order_summary__picklist__sku__user=user.id, seller_order_summary__picklist__picklist_number__in=picklist_numbers,status=0,packed_quantity=0, json_data__sorting_status="pending").values_list("package__id",flat=True))
        AuthorizedBins.objects.filter(id__in = picked_cartons).update(status=0)
        return True

    def get(self, *args, **kwargs):
        request_data = self.request.GET
        self.set_user_credientials()
        log.info("Request Get Sorting username %s, Ip Address %s, and params are %s"% (str(self.request.user.username),str(get_user_ip(self.request)), str(request_data)))
        pagenum = int(request_data.get('page', 1))
        limit = int(request_data.get('limit', 50))
        start_index = (pagenum-1)*limit
        end_index = pagenum*limit
        search_params = request_data
        final_output_data = {'data': {"data": [], "picking_status":"", "sorting_status":""}, 'page_info':{'total_count': 0}, "limit": limit}
        search_term=False
        picklist_status="Completed"
        if 'sorting_to_ob_staging_area' in search_params:
            res = self.get_pending_sorting_to_ob_staging_area(self.warehouse, search_params)
            return JsonResponse(res)
        if 'search_value' in search_params:
            search_term=True
            staging_objs= StagingInfo.objects.filter(Q(location__location=search_params['search_value'], location__status=1) |
                            Q(order_reference=search_params['search_value']), user_id=self.warehouse.id, status__in=[0, 2], segregation="sorting")
        elif 'picklist_number' in search_params:
            search_term=True
            staging_objs= StagingInfo.objects.filter(order_reference=search_params['picklist_number'], user_id=self.warehouse.id, status=0, segregation="sorting", location__status=1)
            if Picklist.objects.filter(sku__user=self.warehouse.id, picklist_number=search_params['picklist_number'] ,status__in=["open", "batch_picked"]).exists():
                picklist_status="Pending"
        if search_term:
            staging_list=[]
            picklist_number, location_name= "", ""
            staging_data=staging_objs.values(location_name=F("location__location"), picklist_number=F("order_reference")).distinct()
            picklist_numbers = list(staging_data.values_list('picklist_number', flat=True))
            pending_soring = OrderPackaging.objects.filter(seller_order_summary__picklist__sku__user=self.warehouse.id, seller_order_summary__picklist__picklist_number__in=picklist_numbers, status=0, json_data__sorting_status='pending', packed_quantity__gt=0)
            if staging_data.exists():
                for each_row in list(staging_data):
                    if Picklist.objects.filter(sku__user=self.warehouse.id, picklist_number=each_row["picklist_number"] ,status__in=["open", "batch_picked"]).exists():
                        picklist_status= "Pending"
                    picklist_number, location_name=each_row["picklist_number"], each_row["location_name"]
                    staging_list.append(each_row)
                    break
            if pending_soring.exists():
                final_output_data['data']["sorting_status"]= "Pending"
            else:
                final_output_data['data']["sorting_status"]= "Completed"

            if not picklist_number:
                if search_params.get('search_value'):
                    picklist_number = int(search_params['search_value'])
                if search_params.get('picklist_number'):
                    picklist_number = int(search_params['picklist_number'])

            pending_drop_cartons = self.get_pending_sorting_to_ob_staging_area(self.warehouse, {'picklist_number':picklist_number})
            if pending_drop_cartons.get('data', []):
                final_output_data['data']["ob_drop_status"]= "Pending"
            else:
                final_output_data['data']["ob_drop_status"]= "Completed"

            final_output_data["page_info"]["total_count"] = len(staging_list)
            final_output_data['data']["data"] = staging_list
            final_output_data['data']["picking_status"]= picklist_status
            final_output_data['data']["picklist_number"]= picklist_number
            final_output_data['data']["location_name"]= location_name
            log.info("Response Sorting with filter username %s, and data is %s"% (str(self.request.user.username), str(final_output_data)))
        else:
            staging_data = StagingInfo.objects.filter(user_id=self.warehouse.id, status__in=[0, 2], segregation="sorting", location__status=1).values(location_name=F("location__location"), picklist_number=F("order_reference")).distinct()
            final_output_data["page_info"]["total_count"] = staging_data.count()
            final_output_data['data'] = list(staging_data[start_index:end_index])
        final_output_data['message'] = 'success'
        return JsonResponse(final_output_data)


class SecondarySortingView(WMSListView):
    """
    Class for handling secondary sorting operations in the outbound process.

    This class provides functionality for managing the secondary sorting of items,
    including scanning LPNs and labels, retrieving sorted order details, and updating
    stock status during the sorting process.
    """

    def get(self, *args, **kwargs):
        """
        Handle GET requests for secondary sorting operations.

        This method processes different types of GET requests based on the request_type parameter:
        - scan_lpn: Retrieves details for a scanned LPN
        - scan_label: Retrieves details for a scanned label
        - sorted_order: Retrieves details for sorted orders

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: JSON response with the requested data or error messages.
        """

        self.set_user_credientials()
        self.request_data = self.request.GET.dict()
        self.sorted_order_data, self.errors = [], []
        self.final_data = {}

        self.fetch_required_configurations()

        if self.request_data.get('request_type') == 'scan_lpn':
            self.get_lpn_details()
        elif self.request_data.get('request_type') == 'scan_label':
            self.get_label_details()
        elif self.request_data.get('request_type') == 'sorted_order':
            self.get_sorted_order_details()
            return JsonResponse({'data': self.sorted_order_data})
        elif self.request_data.get('request_type') == 'lpn_details':
            self.get_pending_lpn_details()
        else:
            self.errors.append('Invalid request type')

        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)

        return JsonResponse(self.final_data)


    def fetch_required_configurations(self):
        """
        Fetch required configuration values from the warehouse settings.

        This method retrieves configuration values such as picklist label content
        and pigeon hole sorting method from the warehouse's miscellaneous settings.
        These configurations determine how the sorting process behaves.
        """
        misc_types = [
            'picklist_label_content', 'pigeon_hole_sorting', 'restrict_default_location_suggestion_for_pigeon_hole_sorting'
        ]
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        self.picklist_label_content = 'order_line_reference'
        if self.misc_dict.get('picklist_label_content'):
            self.picklist_label_content = self.misc_dict.get('picklist_label_content')
        self.pigeon_hole_sorting = self.misc_dict.get('pigeon_hole_sorting', 'location') or 'location'
        self.restrict_default_location_suggestion_for_pigeon_hole_sorting = self.misc_dict.get('restrict_default_location_suggestion_for_pigeon_hole_sorting', 'false')

        misc_option_keys = ['outbound_staging_lanes', 'label_based_picking']
        self.misc_options_dict = get_misc_options_list(misc_option_keys, self.warehouse, False)


    def get_lpn_details(self):
        """
        Retrieve details for a scanned LPN (License Plate Number).

        This method validates the LPN number, checks if it exists in the stock details,
        and verifies if it has been dropped in the staging area. If all validations pass,
        it returns the LPN number and associated picklist number.

        The method sets error messages in self.errors if any validation fails.

        Returns:
            None: The method updates self.final_data with the LPN details if successful,
                  or adds error messages to self.errors if validation fails.
        """
        lpn_number = self.request_data.get('lpn_number', '')
        if not lpn_number:
            self.errors.append('LPN Number is required')
            return

        stock_data = StockDetail.objects.filter(
            sku__user=self.warehouse.id, receipt_type='so_picking', location__zone__storage_type='pre_sorting',
            status=6, lpn_number=lpn_number, quantity__gt=0
        ).values('transact_number').annotate(picked_lpn=F('json_data__picked_lpn'),
            order_references=ArrayAgg('grn_number', distinct=True)
        ).order_by('transact_number').first()

        if not stock_data:
            self.errors.append('Invalid LPN Number for Sorting')
            return

        picklist_number = stock_data.get('transact_number')
        picked_lpn = stock_data.get('picked_lpn')
        order_references = stock_data.get('order_references', set())

        if picked_lpn:
            lpn_dropped = StagingInfo.objects.filter(user=self.warehouse, segregation='outbound_staging', picklist_number=picklist_number, carton_no=lpn_number).exists()
            if not lpn_dropped:
                self.errors.append('LPN not dropped yet')
                return

        errors = validate_suggested_sorting_locations(self.warehouse, order_references, picklist_number, lpn_number, self.misc_dict)
        if errors:
            self.errors.extend(errors)
            return

        self.final_data = {
            'lpn_number': lpn_number,
            'picklist_number': picklist_number,
        }

    def get_lpn_location_mapping_details(self, stock_data):
        """
        Retrieve location and zone mapping details for an LPN.

        This method queries the StagingInfo model to find the location, zone, and
        mapped LPN for a given stock item. It's used to determine where an item
        should be sorted to in the warehouse.

        Args:
            stock_data (dict): Dictionary containing stock details including
                              order_reference and picklist_number.

        Returns:
            tuple: A tuple containing (location, zone, lpn_number) where:
                  - location: The location where the item should be sorted to
                  - zone: The zone where the item should be sorted to
                  - lpn_number: The LPN number associated with the location
        """
        location, zone, lpn_number = '', '', ''
        self.location_lpn_mapping = {}
        #get the location mapping for the lpn from staging
        staging_filters = {
            'user_id': self.warehouse.id,
            'segregation': 'outbound_staging',
            'location__zone__storage_type': 'sorting',
            'status': 0,
            'order_reference': stock_data.get('order_reference'),
            'picklist_number': stock_data.get('picklist_number'),
        }
        staging_data = StagingInfo.objects.filter(**staging_filters).values_list('location__location', 'location__zone__zone', 'carton_no').first()
        if not staging_data:
            self.errors.append('No LPN Location Mapping found')
            return location, zone, lpn_number
        # Unpack the staging data
        location, zone, lpn_number = staging_data

        if not lpn_number and self.pigeon_hole_sorting == 'lpn':
            self.errors.append(f'LPN not mapped for Location - {location}')
            return location, zone, lpn_number

        return location, zone, lpn_number

    def get_label_details(self):
        """
        Retrieve details for a scanned label or SKU code.

        This method validates the input parameters (LPN number, picklist number,
        label or SKU code), retrieves the corresponding stock details, and prepares
        the response data including batch details, location information, and zone mapping.

        The method handles different label content types based on the warehouse configuration:
        - order_line_reference: Uses the order line reference as the label
        - picklist_id: Uses the picklist ID as the label

        Returns:
            None: The method updates self.final_data with the label details if successful,
                  or adds error messages to self.errors if validation fails.
        """
        lpn_number = self.request_data.get('lpn_number', '')
        picklist_number = self.request_data.get('picklist_number', '')
        label = self.request_data.get('label', '')
        sku_code = self.request_data.get('sku_code', '')

        mandatory_keys = ['lpn_number', 'picklist_number']
        for key in mandatory_keys:
            if not self.request_data.get(key):
                self.errors.append(f'{key} is required')
                return

        if not (label or sku_code):
            self.errors.append('Label or SKU Code is required')
            return

        sos_filters = {
            'order__user': self.warehouse.id,
            'picklist__picklist_number': picklist_number,
            'order_status_flag': 'processed_orders',
        }
        scan_type = ''
        if label:
            scan_type = 'Label'
            if self.picklist_label_content == 'order_line_reference':
                sos_filters['order__line_reference'] = label
            elif self.picklist_label_content == 'picklist_id':
                sos_filters['picklist_id'] = label
            else:
                self.errors.append('Invalid Picklist Label Content configured')
                return
        elif sku_code:
            scan_type = 'SKU Code'
            ean_objs = EANNumbers.objects.filter(ean_number=sku_code, sku__user=self.warehouse.id).values('sku__sku_code')
            if ean_objs.exists():
                sku_code = ean_objs[0]['sku__sku_code']
            sos_filters['order__sku__sku_code'] = sku_code

        sos_dict = dict(SellerOrderSummary.objects.filter(**sos_filters).values_list('id', 'order__mrp'))
        sos_ids = list(sos_dict.keys())
        stock_data = dict(StockDetail.objects
            .filter(sku__user=self.warehouse.id, receipt_type='so_picking', receipt_number__in=sos_ids,
                    lpn_number=lpn_number, quantity__gt=0, location__zone__storage_type='pre_sorting', status=6)
            .values('lpn_number', 'receipt_number', order_reference=F('grn_number'),
                    picklist_number=F('transact_number'), sku_code=F('sku__sku_code'), sku_description=F('sku__sku_desc'),
                    sku_image=F('sku__image_url'), sku_size=F('sku__sku_size'), is_scannable=F('sku__scan_picking'),
                    batch_no=F('batch_detail__batch_no'), batch_reference=F('batch_detail__batch_reference'),
                    manufactured_date=F('batch_detail__manufactured_date'), expiry_date=F('batch_detail__expiry_date'),
                    mrp=F('batch_detail__mrp'))
            .annotate(Sum('quantity'))
            .order_by('sku_code')
            .first() or {}
        )

        if not stock_data:
            self.errors.append(f'Invalid {scan_type} for Sorting')
            return

        location, zone, mapped_lpn = self.get_lpn_location_mapping_details(stock_data)

        if self.errors:
            return

        # Prepare Batch Details
        time_zone = self.request.timezone
        manufactured_date, expiry_date = '', ''
        if stock_data.get('manufactured_date'):
            manufactured_date = get_local_date_known_timezone(time_zone, stock_data['manufactured_date'])
        if stock_data.get('expiry_date'):
            expiry_date = get_local_date_known_timezone(time_zone, stock_data['expiry_date'])
        stock_data['manufactured_date'] = manufactured_date
        stock_data['expiry_date'] = expiry_date
        stock_data['batch_no'] = stock_data.get('batch_no') or ''
        stock_data['batch_reference'] = stock_data.get('batch_reference') or ''

        sku_ean_dict = get_sku_ean_numbers([stock_data['sku_code']], self.warehouse)
        stock_data['ean_numbers'] = sku_ean_dict.get(stock_data['sku_code'], [])
        stock_data['quantity'] = stock_data.pop('quantity__sum', 0)
        stock_data['location'] = location
        stock_data['zone'] = zone
        stock_data['mapped_lpn'] = ('', mapped_lpn)[self.pigeon_hole_sorting == 'lpn']
        stock_data['order_mrp'] = sos_dict.get(stock_data.get('receipt_number', ''), 0)
        self.final_data = stock_data


    def get_sorted_order_details(self):
        """
        Retrieve details of orders that have been partially or fully sorted.

        This method queries the StagingInfo and StockDetail models to find orders
        that have items in the sorting process. It calculates the total number of
        items and the number of sorted items for each order, and retrieves the
        customer reference for each order.

        The method populates self.sorted_order_data with a list of dictionaries,
        each containing:
        - order_reference: The reference number of the order
        - customer_reference: The customer reference associated with the order
        - total_count: The total number of distinct SKUs in the order
        - sorted_count: The number of distinct SKUs that have been sorted
        """
        order_reference = self.request_data.get('order_reference', '')

        staging_filters = {
            'user_id': self.warehouse.id,
            'segregation': 'outbound_staging',
            'location__zone__storage_type': 'sorting',
            'status': 0,
            'order_reference__isnull': False,
            'carton_no__isnull': False,
        }
        if order_reference:
            staging_filters['order_reference'] = order_reference

        # Fetching sorting pending order references
        filtered_order_references = list(StagingInfo.objects.filter(**staging_filters).values_list('order_reference', flat=True).distinct())

        if not filtered_order_references:
            return

        stock_filters = {
            'sku__user': self.warehouse.id,
            'receipt_type': 'so_picking',
            'location__zone__storage_type': 'pre_sorting',
            'quantity__gt': 0,
            'json_data__pigeon_hole_sorting': True,
            'grn_number__in': filtered_order_references,
        }

        stock_data = list(
            StockDetail.objects.filter(**stock_filters)
            .values(order_reference=F('grn_number'))
            .annotate(
                total_count=Count('sku_id', distinct=True),
                sorted_count=Count('sku_id', filter=Q(status=1), distinct=True),
            )
            .filter(sorted_count__gt=0)
            .values('order_reference', 'total_count', 'sorted_count')
        )

        order_references = set()
        for data in stock_data:
            order_references.add(data['order_reference'])

        # Fetching customer reference for the order references
        order_customer_mapping = dict(OrderDetail.objects
            .filter(order_reference__in=order_references, user=self.warehouse.id)
            .values_list('order_reference', 'customer_identifier__customer_reference')
        )

        for data in stock_data:
            self.sorted_order_data.append({
                'order_reference': data['order_reference'],
                'customer_reference': order_customer_mapping.get(data['order_reference'], ''),
                'total_count': data['total_count'],
                'sorted_count': data['sorted_count'],
            })

    def get_pending_lpn_details(self):
        """
        Retrieve pending details of LPN

        returns the below details of the LPN
        1. Pending orders
        2. Pending SKU
        3. Pending Count
        """
        lpn_number = self.request_data.get('lpn_number', '')
        picklist_number = self.request_data.get('picklist_number', '')
        if not lpn_number or not picklist_number:
            self.errors.append('LPN Number and Picklist Number are required')
            return
        
        stock_filters = {
            'sku__user': self.warehouse.id,
            'receipt_type': 'so_picking',
            'location__zone__storage_type': 'pre_sorting',
            'lpn_number': lpn_number,
            'quantity__gt': 0,
            'transact_number': picklist_number,
            'status': 6
        }
        sock_data = list(StockDetail.objects.filter(**stock_filters).values('grn_number', 'sku__sku_code', 'receipt_number', 'quantity'))
        if not sock_data:
            self.errors.append('No pending LPN details found')
            return
        pending_orders = set()
        pending_skus = set()
        receipt_numbers = set()
        total_quantity = 0
        for stock in sock_data:
            pending_orders.add(stock['grn_number'])
            pending_skus.add(stock['sku__sku_code'])
            receipt_numbers.add(stock['receipt_number'])
            total_quantity += stock['quantity']
        pending_count = len(receipt_numbers)
        self.final_data = {
            'lpn_number': lpn_number,
            'picklist_number': picklist_number,
            'pending_orders': list(pending_orders),
            'pending_skus': list(pending_skus),
            'pending_count': pending_count,
            'pending_quantity': total_quantity,
        }


    def post(self, *args, **kwargs):
        """
        Handle POST requests for secondary sorting operations.

        This method processes requests to update the status of sorted items,
        including validating the request data, updating stock status, and
        determining if an order or LPN has been completely sorted.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: JSON response indicating success or failure of the sorting operation.
        """
        try:
            self.request_data = loads(self.request.body)
        except Exception:
            return JsonResponse({'message': 'Invalid Payload'}, status=400)

        self.set_user_credientials()
        self.errors = []

        try:
            self.fetch_required_configurations()
            self.order_sorted, self.lpn_sorted = False, False
            self.validate_and_update_stock_status()
            if self.errors:
                return JsonResponse({'errors': self.errors}, status=400)
            return JsonResponse({'message': 'Item Sorted successfully', 'order_sorted': self.order_sorted, 'lpn_sorted': self.lpn_sorted})
        except Exception as e:
            log.debug(traceback.format_exc())
            log.error(f'Update Sorting status failed with error {e}')
            return JsonResponse({'errors': [f'Update Sorting status failed with error {e}']}, status=400)

    def get_stock_data(self):
        """
        Retrieve stock data based on the provided details.

        This method queries the StockDetail model to find stock items that match
        the criteria specified in the request data (LPN number, order reference,
        picklist number, SKU code, etc.). It then transforms the query results
        into a pandas DataFrame for easier manipulation.
        """
        self.stock_df = pd.DataFrame()
        self.all_stock_df = pd.DataFrame()
        stock_filters = {
            'sku__user': self.warehouse.id,
            'receipt_type': 'so_picking',
            'location__zone__storage_type__in': ['pre_sorting', 'sorting'],
            'grn_number': self.order_reference,
            'transact_number': self.picklist_number,
            'lpn_number': self.lpn_number,
            'sku__sku_code': self.sku_code,
            'status__in': [6, 1],
            'quantity__gt': 0,
        }
        if self.batch_no:
            stock_filters['batch_detail__batch_no'] = self.batch_no
        if self.pigeon_hole_sorting == 'lpn' and self.mapped_lpn:
            stock_filters.pop('lpn_number')
            stock_filters['lpn_number__in'] = [self.lpn_number, self.mapped_lpn]
        stock_data = list(StockDetail.objects.select_related('sku', 'batch_detail', 'location').filter(**stock_filters))

        stock_data_list = []
        sorted_stock_data_list = []
        self.existing_packing_data = {}
        for stock in stock_data:
            data_dict = stock.__dict__.copy()
            data_dict.pop('_state', None)
            data_dict['object'] = stock
            data_dict['sku_code'] = stock.sku.sku_code
            data_dict['batch_no'] = stock.batch_detail.batch_no if stock.batch_detail else ''
            data_dict['location'] = stock.location.location
            data_dict['mrp'] = stock.batch_detail.mrp if stock.batch_detail else 0
            if stock.status == 6 and stock.location.zone.storage_type == 'pre_sorting':
                stock_data_list.append(data_dict)
            else:
                sorted_stock_data_list.append(data_dict)
            if self.mapped_lpn and stock.lpn_number == self.mapped_lpn:
                packing_key = (data_dict.get('receipt_number', ''), data_dict.get('sku_code', ''), data_dict.get('batch_no', ''), data_dict.get('mrp'))
                self.existing_packing_data[packing_key] = stock.quantity
        self.stock_df = pd.DataFrame(stock_data_list)
        self.all_stock_df = pd.DataFrame(sorted_stock_data_list)


    def validate_and_update_stock_status(self):
        """
        Validate and update the stock status for sorted items.

        This method performs the following operations:
        1. Validates that all mandatory fields are present in the request data
        2. Retrieves stock data based on the provided details
        3. Validates the location and LPN mapping
        4. Updates the stock status based on the sorting method:
           - For pigeon hole sorting by LPN: Moves stock to the mapped LPN
           - For other sorting methods: Updates the stock status directly
        5. Determines if the order and LPN have been completely sorted

        The method sets error messages in self.errors if any validation fails.
        It also sets self.order_sorted and self.lpn_sorted flags to indicate
        if the order and LPN have been completely sorted.
        """
        mandatory_fields = ['lpn_number', 'order_reference', 'picklist_number', 'sku_code', 'location', 'quantity']
        for field in mandatory_fields:
            if not self.request_data.get(field):
                self.errors.append(f'{field} is required')
                return

        self.lpn_number = self.request_data.get('lpn_number')
        self.order_reference = self.request_data.get('order_reference')
        self.picklist_number = self.request_data.get('picklist_number')
        self.sku_code = self.request_data.get('sku_code')
        self.location = self.request_data.get('location')
        self.mapped_lpn = self.request_data.get('mapped_lpn', '') or ''
        self.batch_no  = self.request_data.get('batch_no', '') or ''
        self.sort_quantity = self.request_data.get('quantity', 0) or 0
        self.get_stock_data()
        if self.stock_df.empty:
            self.errors.append('No stock found for the provided details')
            return

        # get the stock objects for lpn number
        self.existing_stock_data = self.stock_df.loc[self.stock_df['lpn_number'] == self.lpn_number]['object'].to_list()
        if not self.existing_stock_data:
            self.errors.append('Item not found in Sorting')
        self.validate_location_lpn_mapping(self.location, self.mapped_lpn)

        if self.pigeon_hole_sorting == 'lpn':
            self.validate_and_update_packing_data()
            if self.errors:
                return
            # move the stock to mapped lpn
            self.move_stock_to_mapped_lpn()
        else:
            self.update_stock_status()

        self.order_sorted = not StockDetail.objects.filter(sku__user=self.warehouse.id, grn_number=self.order_reference, receipt_type='so_picking', location__zone__storage_type='pre_sorting', status=6, quantity__gt=0).exists()
        self.lpn_sorted = not StockDetail.objects.filter(sku__user=self.warehouse.id, lpn_number=self.lpn_number, receipt_type='so_picking', location__zone__storage_type='pre_sorting', status=6, quantity__gt=0).exists()

    def update_stock_status(self):
        """
        Update the status of stock items to indicate they have been sorted.

        This method sets the status of all stock items in self.stock_df to 1,
        indicating that they have been sorted, and then performs a bulk update
        to save the changes to the database.
        """
        to_delete_stock_obj_ids, new_stock_objs = [], []
        stock_objects = self.stock_df['object'].tolist()
        rem_quantity = self.sort_quantity
        for stock in stock_objects:
            sorted_qty = min(stock.quantity, rem_quantity)
            if self.all_stock_df.empty:
                existing_objs = pd.DataFrame()
            else:
                existing_objs = self.all_stock_df[(self.all_stock_df['status'] == 1) & (self.all_stock_df['receipt_type'] == stock.receipt_type) & (self.all_stock_df['receipt_number'] == stock.receipt_number) & (self.all_stock_df['lpn_number'] == stock.lpn_number) & (self.all_stock_df['location'] == stock.location.location)]
            if existing_objs.empty:
                # if no existing objects found, update the status and move on
                location_obj = LocationMaster.objects.filter(location=self.location, zone__storage_type='sorting', zone__user=self.warehouse.id, status=1).first()
                if not location_obj:
                    self.errors.append(f'Invalid Location: {self.location}')
                    return
                if stock.quantity == sorted_qty:#full sort of current stock
                    stock.status = 1
                    stock.location = location_obj
                    self.stock_df.loc[self.stock_df['id'] == stock.id, 'status'] = 1
                else:#partial sort of current stock
                    # reduce sorted stock from it and create a new object with remaining quantity
                    stock.quantity -= sorted_qty
                    self.stock_df.loc[self.stock_df['id'] == stock.id, 'quantity'] = stock.quantity
                    new_stock_obj = stock.__dict__.copy()
                    new_stock_obj.pop('_state', None)
                    new_stock_obj.pop('id', None)
                    new_stock_obj['quantity'] = sorted_qty
                    new_stock_obj['status'] = 1
                    new_stock_obj['location_id'] = location_obj.id
                    new_stock_objs.append(StockDetail(**new_stock_obj))
                rem_quantity -= sorted_qty
            else:
                # if an object already exists reduce the current stock quantity and update the existing object
                existing_obj = existing_objs.iloc[0]
                existing_obj.quantity += sorted_qty
                existing_obj['object'].quantity += sorted_qty
                rem_quantity -= sorted_qty
                if stock.quantity == sorted_qty:
                    to_delete_stock_obj_ids.append(stock.id)
                else:
                    stock.quantity -= sorted_qty
                    self.all_stock_df.loc[self.all_stock_df['id'] == stock.id, 'quantity'] = stock.quantity

        all_stock_objects = []
        if not self.all_stock_df.empty and 'object' in self.all_stock_df.columns:
            all_stock_objects = self.all_stock_df['object'].tolist()
        if all_stock_objects:
            StockDetail.objects.bulk_update(all_stock_objects, ['quantity', 'status'])
        if stock_objects:
            StockDetail.objects.bulk_update(stock_objects, ['quantity', 'status'])
        if to_delete_stock_obj_ids:
            StockDetail.objects.filter(id__in=to_delete_stock_obj_ids).delete()
        if new_stock_objs:
            StockDetail.objects.bulk_create(new_stock_objs)


    def prepare_stock_updation_payload(self):
        """
        Prepare the payload for updating stock information.

        This method creates a dictionary containing the necessary information
        for updating stock records, including picklist numbers, LPN numbers,
        and details about which LPNs were picked and packed.

        Returns:
            dict: A dictionary containing the payload for stock update operations.
        """
        payload = {}
        payload['picklist_numbers'] = self.picklist_number
        payload['request_type'] = 'update_stock'
        payload['update_type'] = 'update'
        payload['lpn_numbers'] = f"{self.lpn_number},{self.mapped_lpn}"
        payload['lpn_details'] = {
            "picked": [self.lpn_number],
            "packed": [self.mapped_lpn]
        }
        return payload

    def update_mapped_stock_quantity(self):
        """
        Updates the stock quantity for the mapped LPN.

        Args:
            stock_data: QuerySet of StockDetail objects to be updated
        """
        # self.mapped_lpn_items_dict
        # (stock.receipt_number, stock.sku_code, stock.batch_detail.batch_no, stock.batch_detail.mrp)
        for _, stock in self.mapped_stocks.iterrows():
            receipt_number = stock['receipt_number']
            sku_code = stock['sku_code']
            batch_no = stock['batch_no']
            mrp = stock['mrp']
            key = (receipt_number, sku_code, batch_no, mrp)
            packed_item = self.mapped_lpn_items_dict.get(key, {})
            quantity = packed_item.get('packed_quantity', 0)
            if quantity > 0:
                stock['object'].quantity = quantity
                stock['object'].original_quantity = quantity
                stock['object'].status = 1
                self.mapped_stock_objects.append(stock['object'])
        for stock in self.existing_stock_data:
            qty = self.stock_id_qty_map.get(stock.id, 0)
            if qty >= 0:
                stock.quantity = qty

    def move_stock_to_mapped_lpn(self):
        """
        Moves the stock from source LPN to mapped LPN.

        Args:
            stock_data: QuerySet of StockDetail objects to be moved
        """
        if self.pigeon_hole_sorting != 'lpn':
            return
        # Update the stock data with the mapped LPN
        if self.all_stock_df.empty:
            self.mapped_stocks = pd.DataFrame(columns=self.all_stock_df.columns)
        else:
            self.mapped_stocks = self.all_stock_df.loc[self.all_stock_df['lpn_number'] == self.mapped_lpn]
            # self.mapped_stocks = self.all_stock_df.loc[(self.all_stock_df['lpn_number'] == self.mapped_lpn) & (self.all_stock_df['sku_code'] == self.sku_code) & (self.all_stock_df['batch_no'] == self.batch_no) & (self.all_stock_df['transact_number'] == self.picklist_number) & (self.all_stock_df['grn_number'] == self.order_reference)]
        # self.mapped_stocks = self.all_stock_df.loc[self.all_stock_df['lpn_number'] == self.mapped_lpn] #if any entry present it status should be 1
        self.mapped_stock_objects, self.new_stock_objects = [], []
        self.packing_update_completed_stocks = {}
        if not self.mapped_stocks.empty:
            self.update_mapped_stock_quantity()
        else:
            # get sort location and move stock to sorted location
            location_obj = LocationMaster.objects.filter(location=self.location, zone__storage_type='sorting', zone__user=self.warehouse.id, status=1).first()
            if not location_obj:
                self.errors.append(f'Invalid Location: {self.location}')
                return
            for stock in self.existing_stock_data:
                # if no sorted stock found for mapped lpn create a new stock object
                current_stock_qty = self.stock_id_qty_map.get(stock.id, 0)
                if current_stock_qty <= 0:
                    stock.status = 1
                    stock.location = location_obj
                stock.quantity = current_stock_qty

                # create new stock object
                mapped_lpn_key = (
                    stock.receipt_number, 
                    stock.sku.sku_code, 
                    stock.batch_detail.batch_no if stock.batch_detail else '',
                    stock.batch_detail.mrp if stock.batch_detail else 0
                )
                if mapped_lpn_key in self.packing_update_completed_stocks:
                    continue
                packed_item = self.mapped_lpn_items_dict.get(mapped_lpn_key, {})
                packed_qty = packed_item.get('packed_quantity', 0)
                stock_data = stock.__dict__.copy()
                stock_data.pop('_state', None)
                stock_data.pop('id', None)
                obj = StockDetail(**stock_data)
                obj.lpn_number = self.mapped_lpn
                obj.original_quantity = packed_qty
                obj.quantity = packed_qty
                obj.location = location_obj
                obj.status = 1
                obj.json_data.pop('picked_lpn', None)
                self.new_stock_objects.append(obj)
                self.packing_update_completed_stocks[mapped_lpn_key] = True
        # update the stock quanity for the current lpn
        if self.mapped_stock_objects:
            StockDetail.objects.bulk_update_with_rounding(self.mapped_stock_objects, ['quantity', 'status', 'original_quantity'])
        if self.new_stock_objects:
            StockDetail.objects.bulk_create_with_rounding(self.new_stock_objects)
        if self.existing_stock_data:
            StockDetail.objects.bulk_update_with_rounding(self.existing_stock_data, ['quantity', 'status'])


    def validate_and_update_packing_data(self):
        """
        Validates and updates the packing data for the mapped LPN.

        Args:
            stock_data: QuerySet of StockDetail objects to be packed
        """
        self.packing_data, self.lpn_stock_items = {}, {}
        if self.pigeon_hole_sorting != 'lpn':
            return

        if not self.mapped_lpn:
            self.errors.append('Location mapped LPN is required')
            return

        self.request_dict = {
            "request_headers": {
                "Warehouse": self.warehouse.username,
                "Authorization": getattr(self.request, 'headers', {}).get('Authorization', '')
            },
            "request_meta": self.request.META,
        }
        self.packing_service = PackingService(self.request_dict, self.user, self.warehouse)


        # Prepare the packing data and call the packing service
        self.prepare_and_create_packing_data()
        if self.errors:
            log.info('packing data creation failed')
            return

    def open_mapped_lpn(self):
        '''
        Open the mapped LPN for packing
        '''
        lpn_payload = {
            "user": self.user.username,
            "warehouse": self.warehouse.username,
            "transaction_number": self.picklist_number,
            "transaction_type": "so_packing",
            "json_data": {
                "created_by": self.user.username
            },
            "lpn_numbers": [self.mapped_lpn],
        }
        _, errors = self.packing_service.scan_lpn(lpn_payload)
        if errors:
            self.errors.extend(errors)
            log.error(f"Failed to open mapped LPN {self.mapped_lpn}: {errors}")
            return

    def formart_date(self, date):
        """
        Formats the date to a string in YYYY-MM-DD format.

        Args:
            date: Date object to be formatted

        Returns:
            str: Formatted date string
        """
        try:
            date = date.strftime("%Y-%m-%d")
        except AttributeError:
            pass
        return date

    def prepare_and_create_packing_data(self):
        """
        Prepares the packing data for transferring stock from source LPN to mapped LPN.

        Args:
            stock_data: QuerySet of StockDetail objects to be packed
        """
        rem_quantity = self.sort_quantity
        stock_values = [
            'receipt_number', 'sku__sku_code', 'sku__sku_desc', 'batch_detail__batch_no', 'batch_detail__batch_reference', 'grn_number',
            'batch_detail__manufactured_date', 'batch_detail__expiry_date', 'batch_detail__mrp', 'quantity', 'location__location', 'transact_number', 'id'
        ]
        stock_filter = {
            'sku__user': self.warehouse.id,
            'receipt_type': 'so_picking',
            'lpn_number': self.lpn_number,
            'status': 6,
            'sku__sku_code': self.sku_code,
            'transact_number': self.picklist_number,
            'grn_number': self.order_reference,
        }
        if self.batch_no:
            stock_filter['batch_detail__batch_no'] = self.batch_no
        self.lpn_stock_items = StockDetail.objects.filter(**stock_filter).values(*stock_values)
        if not self.lpn_stock_items:
            self.errors.append('No stock found for the provided LPN number')
            return
        
        sorting_qty_map = defaultdict(float)
        for stock in self.lpn_stock_items:
            key = (stock.get('grn_number', ''), stock.get('transact_number', ''), stock.get('lpn_number', ''), stock.get('sku__sku_code', ''), stock.get('batch_detail__batch_no', ''))
            sorting_qty_map[key] += stock.get('quantity', 0)

        # Create dictionaries to store unique items by key
        self.source_lpn_items_dict = {}
        self.mapped_lpn_items_dict = {}
        self.stock_id_qty_map = {}
        for stock in self.lpn_stock_items:
            # Create a unique key for each item based on transaction_id, sku_code, batch, and mrp
            batch_no = stock.get('batch_detail__batch_no', '') or ''
            mrp = stock.get('batch_detail__mrp', 0) or 0
            transaction_id = stock.get('receipt_number')
            sku_code = stock.get('sku__sku_code', '')
            order_reference = stock.get('grn_number', '')
            picklist_number = stock.get('transact_number', '')
            location = stock.get('location__location', '')
            stock_quantity = stock.get('quantity', 0)

            unique_key = (transaction_id, sku_code, batch_no, mrp)

            sorting_key = (order_reference, picklist_number, stock.get('lpn_number', ''), sku_code, batch_no)

            # Format dates if they exist
            manufactured_date = ""
            if stock.get('batch_detail__manufactured_date'):
                manufactured_date = self.formart_date(stock.get('batch_detail__manufactured_date'))

            expiry_date = ""
            if stock.get('batch_detail__expiry_date'):
                expiry_date = self.formart_date(stock.get('batch_detail__expiry_date'))

            # Common item data for both LPNs
            item_data = {
                "transaction_id": transaction_id,
                "sku_code": sku_code,
                "sku_description": stock.get('sku__sku_desc', ''),
                "mrp": mrp,
                "unit_price": 0,
                "batch_details": {
                    "batch_number": batch_no,
                    "batch_reference": stock.get('batch_detail__batch_reference', ''),
                    "manufactured_date": manufactured_date,
                    "expiry_date": expiry_date,
                    "batch_keys": None
                },
                "json_data": {
                    "pack_uom_quantity": 1,
                    "packed_lpn": True
                }
            }

            # Source LPN item (with quantity 0)
            #move partial quantity from source lpn to mapped lpn and create new entry for the old entries
            if unique_key not in self.source_lpn_items_dict:
                source_item = item_data.copy()
                stock_qty = stock.get('quantity', 0)
                min_qty = min(stock_qty, rem_quantity)
                #partial sort
                current_stock_qty = stock_qty - min_qty
                rem_quantity -= min_qty
                self.stock_id_qty_map[stock.get('id')] = current_stock_qty if current_stock_qty >= 0 else 0
                source_item["packed_quantity"] = current_stock_qty if current_stock_qty >= 0 else 0
                self.source_lpn_items_dict[unique_key] = source_item
            else:
                stock_qty = stock.get('quantity', 0)
                min_qty = min(stock_qty, rem_quantity)
                #partial sort
                current_stock_qty = stock_qty - min_qty
                rem_quantity -= min_qty
                self.stock_id_qty_map[stock.get('id')] = current_stock_qty if current_stock_qty >= 0 else 0
                self.source_lpn_items_dict[unique_key]["packed_quantity"] += current_stock_qty if current_stock_qty >= 0 else 0

            # Mapped LPN item (with stock quantity)
            if unique_key not in self.mapped_lpn_items_dict:
                mapped_item = item_data.copy()
                mapped_item["packed_quantity"] = min_qty
                self.mapped_lpn_items_dict[unique_key] = mapped_item
            else:
                self.mapped_lpn_items_dict[unique_key]["packed_quantity"] += min_qty

        for key, item in self.mapped_lpn_items_dict.items():
            if key in self.existing_packing_data:
                item["packed_quantity"] += self.existing_packing_data[key]
        source_lpn_items = list(self.source_lpn_items_dict.values())
        mapped_lpn_items = list(self.mapped_lpn_items_dict.values())

        # Prepare packing payload
        packing_payload = {
            "user": self.user.username,
            "warehouse": self.warehouse.username,
            "transaction_number": str(self.picklist_number),
            "transaction_type": "so_packing",
            "json_data": {
                "created_by": self.user.username
            },
            "packing_details": [
                {
                    "lpn_number": self.lpn_number,
                    "weight": 0,
                    "uom": "",
                    "items": source_lpn_items,
                    "json_data": {
                        "packed_lpn": True
                    }
                },
                {
                    "lpn_number": self.mapped_lpn,
                    "weight": 0,
                    "uom": "",
                    "items": mapped_lpn_items,
                    "json_data": {
                        "packed_lpn": True
                    }
                }
            ],
            "status": "closed"
        }

        # Call packing service to create packing
        _, errors = self.packing_service.create_packing(packing_payload)

        if errors:
            self.errors.extend(errors)
            log.error(f"Failed to create packing for LPN {self.lpn_number} to {self.mapped_lpn}: {errors}")
        else:
            log.info(f"Successfully created packing for LPN {self.lpn_number} to {self.mapped_lpn}")

    def validate_location_lpn_mapping(self, location, mapped_lpn):
        """
        Validate the location and LPN mapping.

        This method checks if the provided location and LPN have a valid mapping
        in the staging information. For pigeon hole sorting by LPN, it also validates
        that a mapped LPN is provided.

        Args:
            location (str): The location to validate
            mapped_lpn (str): The mapped LPN to validate

        Returns:
            None: The method sets self.staging_objs with the matching staging records
                  and adds error messages to self.errors if validation fails.
        """
        self.staging_objs = []

        if self.pigeon_hole_sorting == 'lpn' and not mapped_lpn:
            self.errors.append('Location mapped LPN is required')
            return

        #staging filters
        staging_filters = {
            'user_id': self.warehouse.id,
            'location__location': location,
            'carton_no': self.lpn_number,
            'segregation': 'outbound_staging',
            'location__zone__storage_type': 'sorting',
            'status': 0,
            'order_reference': self.order_reference,
            'picklist_number': self.picklist_number,
        }
        if mapped_lpn:
            staging_filters['carton_no'] = mapped_lpn
        self.staging_objs = StagingInfo.objects.filter(**staging_filters)

        if not self.staging_objs.exists():
            self.errors.append('No mapping found for the provided location and LPN number.')

    def update_location_lpn_mapping(self, order_reference, picklist_number):
        """
        Update the staging information for the order reference and picklist number.

        This method updates the staging records with the provided order reference
        and picklist number. It only applies to pigeon hole sorting by LPN.

        Args:
            order_reference (str): The order reference to update
            picklist_number (str): The picklist number to update
        """
        if self.pigeon_hole_sorting != 'lpn':
            return

        staging_records_to_update = self.staging_objs.exclude(order_reference__isnull=True, picklist_number=0)
        if staging_records_to_update.exists():
            log.info(f'Updating Staging Info for Order Reference: {order_reference}, Picklist Number: {picklist_number}')
            staging_records_to_update.update(order_reference=order_reference, picklist_number=picklist_number)

    def put(self, *args, **kwargs):
        """
        Handle PUT requests for secondary sorting operations.

        This method processes requests to move sorted orders to the pre-invoice zone.
        It validates the orders and then moves them to the pre-invoice zone if they
        are valid and have been completely sorted.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: JSON response indicating success or failure of the operation.
        """
        try:
            self.request_data = loads(self.request.body)
        except Exception:
            return JsonResponse({'message': 'Invalid Payload'}, status=400)

        self.set_user_credientials()
        self.errors = []
        self.order_details, self.order_type_references = {}, {}
        self.order_types, self.pick_types = set(), set()

        try:
            self.fetch_required_configurations()
            self.validate_orders()
            if self.errors:
                return JsonResponse({'errors': self.errors}, status=400)
            self.move_orders_to_pre_invoice()
            return JsonResponse({'message': 'Orders moved to PRE_INVOICE zone '})
        except Exception as e:
            log.debug(traceback.format_exc())
            log.error(f'Move Orders to Pre Invoice failed with error {e}')
            return JsonResponse({'errors': [f'Move Orders to Pre Invoice failed with error {e}']}, status=500)


    def validate_orders(self):
        """
        Validate orders before moving them to the pre-invoice zone.

        This method performs several validations:
        1. Checks if order references are provided
        2. Verifies that the order references are valid
        3. Ensures that all items in the orders have been sorted
        4. Checks that there are no open picklists for the orders

        The method sets error messages in self.errors if any validation fails.
        """
        order_references = self.request_data.get('order_references', [])
        if not order_references:
            self.errors.append('Order References are required')
            return

        valid_order_refs = list(StockDetail.objects
            .filter(sku__user=self.warehouse.id, grn_number__in=order_references, receipt_type='so_picking',
                    location__zone__storage_type='pre_sorting', status=1, quantity__gt=0)
            .values_list('grn_number', flat=True)
            .distinct()
        )
        invalid_order_refs = set(order_references) - set(valid_order_refs)
        if invalid_order_refs:
            self.errors.append(f'Invalid Order References - {",".join(invalid_order_refs)}')
            return

        unsorted_orders = list(StockDetail.objects
            .filter(sku__user=self.warehouse.id, grn_number__in=order_references, receipt_type='so_picking',
                    location__zone__storage_type='pre_sorting', status=6, quantity__gt=0)
            .values_list('grn_number', flat=True)
            .distinct()
        )
        if unsorted_orders:
            self.errors.append(f"Orders {','.join(unsorted_orders)} are not sorted yet")
            return

        picklist_data = list(Picklist.objects
            .filter(user=self.warehouse, reference_model='OrderDetail', reference_number__in=order_references)
            .values('reference_number', 'status', 'pick_type', 'order_type', 'json_data__cluster_name')
            .distinct()
        )

        open_picklist_orders = set()
        for picklist in picklist_data:
            if picklist['status'] == 'open':
                open_picklist_orders.add(picklist['reference_number'])
            if picklist['reference_number'] not in self.order_details:
                self.order_details[picklist['reference_number']] = picklist
            self.order_types.add(picklist['order_type'])
            self.pick_types.add(picklist['pick_type'])
            if picklist['order_type'] not in self.order_type_references:
                self.order_type_references[picklist['order_type']] = set()
            self.order_type_references[picklist['order_type']].add(picklist['reference_number'])

        if open_picklist_orders:
            self.errors.append(f"Open Picklists found for Orders - {','.join(open_picklist_orders)}")
            return


    def move_orders_to_pre_invoice(self):
        """
        Move sorted orders from the sorting zone to the pre-invoice zone.

        This method performs the following operations:
        1. Retrieves the pre-invoice location, creating it if it doesn't exist
        2. Creates new stock records in the pre-invoice location
        3. Updates the quantity of the original stock records to zero
        4. Creates new staging records in the pre-invoice location
        5. Updates the status of the original staging records

        The method moves both stock and staging information to maintain
        consistency between the physical location of items and their
        representation in the system.
        """
        order_references = self.request_data.get('order_references', [])
        orders_staging_location = self.get_orders_staging_locations()

        remove_fields = ['id', '_state', 'creation_date', 'updation_date']
        stock_creation_list = []
        stock_objs = StockDetail.objects.filter(sku__user=self.warehouse.id, grn_number__in=order_references, receipt_type='so_picking', location__zone__storage_type='pre_sorting', status=1, quantity__gt=0)

        for stock_obj in stock_objs:
            stock_dict = stock_obj.__dict__.copy()
            for field in remove_fields:
                stock_dict.pop(field, None)
            stock_dict['location_id'] = orders_staging_location[stock_dict['grn_number']].id
            stock_creation_list.append(StockDetail(**stock_dict))
            stock_obj.quantity = 0

        if stock_creation_list:
            StockDetail.objects.bulk_create_with_rounding(stock_creation_list)
        if stock_objs:
            StockDetail.objects.bulk_update_with_rounding(stock_objs, ['quantity'])

        pre_inv_stage_records = []
        sorting_stage_records = StagingInfo.objects.filter(user=self.warehouse, segregation='outbound_staging', location__zone__storage_type='sorting', order_reference__in=order_references, status=0)
        for sorting_stage_record in sorting_stage_records:
            # Pre invoice staging record creation
            pre_inv_stage_dict = sorting_stage_record.__dict__.copy()
            for field in remove_fields:
                pre_inv_stage_dict.pop(field, None)
            pre_inv_stage_dict['location_id'] = orders_staging_location[pre_inv_stage_dict['order_reference']].id
            pre_inv_stage_records.append(StagingInfo(**pre_inv_stage_dict))
            # Sorting staging record updation
            sorting_stage_record.status = 1
            sorting_stage_record.updation_date = timezone.now()
        if pre_inv_stage_records:
            StagingInfo.objects.bulk_create(pre_inv_stage_records)
        if sorting_stage_records:
            StagingInfo.objects.bulk_update(sorting_stage_records, ['status', 'updation_date'])


    def get_orders_staging_locations(self):
        """
        Retrieve staging locations for orders based on their order type and pick type.

        This method determines the appropriate staging location for each order
        based on the order type, pick type, and configured priorities. It handles
        various scenarios including cluster picking and default zone creation.

        Returns:
            dict: A dictionary mapping order references to their corresponding
                  staging locations (LocationMaster objects).
        """
        orders_staging_location = {}
        no_zone_orders = []
        priority_list = ['PACKING', 'PRE_INVOICE']
        filters = {
            'user': self.warehouse,
            'order_type__in': list(self.order_types),
            'pick_type__in': list(self.pick_types) + [''],
            'status': 1,
            'receipt_type__in': priority_list,
            'zone__isnull': False,
        }

        order_type_zone_mapping = list(OrderTypeZoneMapping.objects
            .filter(**filters)
            .values('order_type', 'pick_type', 'receipt_type', 'zone__zone', 'json_data')
            .annotate(pick_type_priority=Case(
                When(pick_type__in=list(self.pick_types), then=Value(1)),
                default=Value(2),
                output_field=IntegerField()
            ))
            .order_by('order_type', 'pick_type_priority', 'priority')
        )

        # Prepare order type zone mapping dict
        order_type_dict = {}
        for order_type_zone in order_type_zone_mapping:
            order_type = order_type_zone['order_type']
            pick_type = order_type_zone['pick_type']
            receipt_type = order_type_zone['receipt_type']
            if order_type not in order_type_dict:
                order_type_dict[order_type] = {}
            if pick_type not in order_type_dict[order_type]:
                order_type_dict[order_type][pick_type] = {}
            if receipt_type not in order_type_dict[order_type][pick_type]:
                order_type_dict[order_type][pick_type][receipt_type] = {'zones': [], 'json_data': []}
            order_type_dict[order_type][pick_type][receipt_type]['zones'].append(order_type_zone['zone__zone'])
            order_type_dict[order_type][pick_type][receipt_type]['json_data'].append(order_type_zone['json_data'])

        for order_reference, order_data in self.order_details.items():
            order_type = order_data['order_type']
            pick_type = order_data['pick_type']
            cluster_name = order_data['json_data__cluster_name']
            if order_type not in order_type_dict:
                no_zone_orders.append(order_reference)
                continue
            if pick_type not in order_type_dict[order_type]:
                if '' not in order_type_dict[order_type]:
                    no_zone_orders.append(order_reference)
                    continue
                pick_type = ''
            for priority in priority_list:
                if priority not in order_type_dict[order_type][pick_type]:
                    continue
                if not order_type_dict[order_type][pick_type][priority]['zones']:
                    continue
                # Filter zones based on cluster name
                staging_zones = order_type_dict[order_type][pick_type][priority]['zones'].copy()
                for index, json_data in enumerate(order_type_dict[order_type][pick_type][priority]['json_data']):
                    if pick_type == 'cluster_picking' and cluster_name and json_data.get('cluster_name', '') and cluster_name != json_data.get('cluster_name', ''):
                        staging_zones.remove(order_type_dict[order_type][pick_type][priority]['zones'][index])
                if not staging_zones:
                    continue
                # Fetch locations based on filtered zones
                locations = LocationMaster.objects.filter(zone__user=self.warehouse.id, zone__zone__in=staging_zones, status=1).order_by('fill_sequence')
                if not locations:
                    locations = LocationMaster.objects.filter(zone__user=self.warehouse.id, zone__storage_type=priority.lower(), status=1).order_by('fill_sequence')
                if not locations:
                    locations = create_default_zones(self.warehouse, priority + '_ZONE', priority + '_DFLT', 99999, 'outbound_staging', priority.lower())
                orders_staging_location[order_reference] = locations[0]
                break
            if order_reference not in orders_staging_location:
                no_zone_orders.append(order_reference)

        # Fetch Locations for orders without any staging zone
        if no_zone_orders:
            storage_type = 'pre_invoice'
            if "PACKING_STAGING" in self.misc_options_dict.get('outbound_staging_lanes', []) or "all" in self.misc_options_dict.get('outbound_staging_lanes', []):
                storage_type = 'packing'
            locations = LocationMaster.objects.filter(zone__user=self.warehouse.id, zone__storage_type=storage_type, status=1).order_by('fill_sequence')
            if not locations:
                locations = create_default_zones(self.warehouse, storage_type.upper() + '_ZONE', storage_type.upper() + '_DFLT', 99999, 'outbound_staging', storage_type)
            for order_reference in no_zone_orders:
                orders_staging_location[order_reference] = locations[0]

        return orders_staging_location


class PrimarySortingView(WMSListView):
    """
    Class for handling primary sorting operations in the outbound process.

    This class provides functionality for managing the primary sorting of items,
    including mapping LPNs to zones and retrieving zone mapping information for LPNs.
    Primary sorting typically occurs before secondary sorting and involves initial
    organization of items into appropriate zones.
    """

    def get(self, *args, **kwargs):
        """
        Handle GET requests for primary sorting operations.

        This method retrieves mapped zones for a given LPN number, including
        information about picklist numbers, order references, and customer references.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: JSON response with the mapped zones data or error messages.
        """

        self.set_user_credientials()
        self.request_data = self.request.GET.dict()
        self.errors, self.final_data = [], []

        self.fetch_required_configurations()

        self.get_mapped_zones_for_lpn()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        return JsonResponse({'data': self.final_data})


    def post(self, *args, **kwargs):
        """
        Handle POST requests for primary sorting operations.

        This method processes requests to map LPNs to zones in the primary sorting process.
        It validates the request data and calls the map_lpns_to_zones method to perform
        the mapping operation.

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: JSON response indicating success or failure of the mapping operation.
        """
        self.set_user_credientials()

        try:
            self.request_data = loads(self.request.body)
        except Exception:
            return JsonResponse({'message': 'Invalid Payload'}, status=400)

        self.errors = []
        self.map_lpns_to_zones()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        return JsonResponse({'message': 'LPNs Mapped to Zones successfully'})


    def fetch_required_configurations(self):
        """
        Fetch required configuration values from the warehouse settings.

        This method retrieves configuration values such as sort_by from the
        warehouse's miscellaneous settings. These configurations determine
        how the primary sorting process behaves.

        The following configurations are fetched:
        - sort_by: Determines how items are sorted (default: 'order')
        """
        misc_types = ['sort_by', 'pigeon_hole_sorting', 'restrict_default_location_suggestion_for_pigeon_hole_sorting']
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        self.sort_by_config = 'order'
        if self.misc_dict.get('sort_by'):
            self.sort_by_config = self.misc_dict.get('sort_by')


    def get_mapped_zones_for_lpn(self):
        """
        Retrieve the mapped zones for a given LPN number.

        This method performs the following operations:
        1. Validates that an LPN number is provided
        2. Retrieves stock data for the LPN
        3. Checks if the LPN has been dropped in the staging area
        4. Retrieves zone data for the LPN's picklist and order references
        5. Retrieves any existing zone-LPN mappings from TempJson
        6. Prepares the final data with picklist number, order reference,
           customer reference, zone, and mapped LPN number

        The method sets error messages in self.errors if any validation fails,
        and populates self.final_data with the mapped zones information.
        """
        lpn_number = self.request_data.get('lpn_number', '')
        if not lpn_number:
            self.errors.append('LPN Number is required')
            return

        stock_data = list(StockDetail.objects
            .filter(sku__user=self.warehouse.id, receipt_type='so_picking', location__zone__storage_type='pre_sorting',
                    status=6, lpn_number=lpn_number, quantity__gt=0, json_data__primary_sorting_allowed=True)
            .values('transact_number')
            .annotate(picked_lpn=F('json_data__picked_lpn'), order_references=ArrayAgg('grn_number', distinct=True))
            .order_by('transact_number')
        )

        if not stock_data:
            self.errors.append('Invalid LPN Number for Primary Sorting')
            return

        stock_data = stock_data[0]
        picklist_number = stock_data.get('transact_number', 0)
        order_references = stock_data.get('order_references', set())

        lpn_dropped = StagingInfo.objects.filter(user=self.warehouse, segregation='outbound_staging', picklist_number=picklist_number, carton_no=lpn_number, location__zone__storage_type='pre_sorting').exists()
        if not lpn_dropped:
            self.errors.append('LPN not dropped yet')
            return

        errors = validate_suggested_sorting_locations(self.warehouse, order_references, picklist_number, lpn_number, self.misc_dict)
        if errors:
            self.errors.extend(errors)
            return

        zones_dict = {}
        zones_data = list(StagingInfo.objects
            .filter(user=self.warehouse, segregation='outbound_staging', location__zone__storage_type='sorting',
                    picklist_number=picklist_number, order_reference__in=order_references)
            .values('location__zone__zone', 'order_reference')
        )
        for data in zones_data:
            zones_dict[data['location__zone__zone']] = {
                'order_reference': data['order_reference'],
            }

        zone_lpn_mapping = {}
        destination_lpns_data = TempJson.objects.filter(warehouse=self.warehouse, model_name='PrimarySorting', model_reference=lpn_number).values_list('model_json', flat=True).first()
        if destination_lpns_data:
            destination_lpns_data = loads(destination_lpns_data)
            zone_lpn_mapping = destination_lpns_data.get('zone_lpn_mapping', {})

        for zone, zone_data in zones_dict.items():
            self.final_data.append({
                'picklist_number': picklist_number,
                'order_reference': zone_data.get('order_reference', ''),
                'customer_reference': zone_data.get('customer_reference') or '',
                'zone': zone,
                'lpn_number': zone_lpn_mapping.get(zone, ''),
            })


    def map_lpns_to_zones(self):
        """
        Map LPNs to zones in the primary sorting process.

        The zone-LPN mapping is a dictionary where the keys are zone names
        and the values are LPN numbers. This mapping is used to determine
        which LPNs should be sent to which zones during the sorting process.

        The method sets error messages in self.errors if any validation fails.
        """
        source_lpn = self.request_data.get('source_lpn', '')
        zone_lpn_mapping = self.request_data.get('zone_lpn_mapping', {})
        sorted_lpns = set()

        mandatory_keys = ['source_lpn', 'zone_lpn_mapping']
        for key in mandatory_keys:
            if not self.request_data.get(key):
                self.errors.append(f'{key} is required')
                return
            
        for zone, lpn in zone_lpn_mapping.items():
            if not (lpn and zone):
                self.errors.append(f'LPN or zone is required')
                return
            if lpn in sorted_lpns:
                self.errors.append(f'LPN {lpn} is already mapped to a zone')
                return
            sorted_lpns.add(lpn)

        temp_json_obj = TempJson.objects.filter(warehouse=self.warehouse, model_name='PrimarySorting', model_reference=source_lpn).first()
        if temp_json_obj:
            temp_json_obj.model_json = dumps({'zone_lpn_mapping': zone_lpn_mapping})
            temp_json_obj.save()
        else:
            TempJson.objects.create(
                warehouse=self.warehouse,
                model_name='PrimarySorting',
                model_reference=source_lpn,
                model_json=dumps({'zone_lpn_mapping': zone_lpn_mapping}),
                account_id=self.warehouse.userprofile.id,
            )
