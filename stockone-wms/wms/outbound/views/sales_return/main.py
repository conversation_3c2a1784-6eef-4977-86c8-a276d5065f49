#package imports
from json import loads

#django imports
from django.http import JsonResponse

#core operations imports
from core_operations.views.common.main import WMSListView, get_warehouse

#outbound imports
from .sales_return_creation import SalesReturnCreationMixin
from .sales_return_data import SalesReturnDataMixin
from .sales_return_updation import SalesReturnUpdationMixin


class SalesReturn(WMSListView):
    def post(self, *args, **kwargs):
        self.request_data, errors = self.get_request_data()
        self.request_headers = self.request.headers
        self.request_meta = {}
        if errors:
            return JsonResponse({'errors': errors}, status=400)

        response_data, errors = self.sales_return_process()
        if errors:
            return JsonResponse(response_data, status=400)

        return JsonResponse(response_data, status=200)

    def get(self, *args, **kwargs):
        self.request_data = self.request.GET
        self.set_user_credientials()

        get_sales_return = SalesReturnDataMixin(self.user, self.warehouse, self.request_data)
        final_data_dict, errors = get_sales_return.get_sales_return_data()

        if errors:
            return JsonResponse({'errors': errors}, status=400)

        return JsonResponse(final_data_dict, status=200)

    def put(self, *args, **kwargs):
        self.request_data, errors = self.get_request_data()
        if errors:
            return JsonResponse({'errors': errors}, status=400)
        
        extra_params = {
            'request_headers': dict(self.request.headers),
            'request_meta': self.request.META
        }

        if isinstance(self.request_data, dict):
            self.request_data = [self.request_data]
        response_data = SalesReturnUpdationMixin(self.user, self.warehouse, self.request_data, extra_params).update_sales_return()
        errors = response_data.get('errors', [])
        if errors:
            return JsonResponse(response_data, status=400)

        response = {
            'message': 'Sales Return Updated Successfully!',
            'errors': errors,
            'return_ids': response_data.get('return_ids', []),
        }

        return JsonResponse(response, status=200)

    def get_request_data(self):
        """
        Sample Request Payload
        POST : {
            "return_reference": "SR-0001",
            "document_type": "invoice_reference",
            "return_type": "COD",
            "customer_reference": 3,
            "items": [{
                "sku_code": "SKU01",
                "invoice_number": "INV01",
                "order_reference": "ORD1",
                "batch_reference": "BATCH01",
                "return_quantity": 10,
                "rejected_quantity": 5,
                "short_quantity": 4,
                "mrp": 50,
                “line_reference”: “12345”,
                "order_line_reference": "123456",
                "unit_price": 45,
                "currency": "INR",
                "manufactured_date": "06-10-2023",
                "expiry_date": "25-10-2023",
                "reason": "damaged",
                "taxes": {
                    "cgst": {
                        "percentage": 0
                    },
                    "igst": {
                        "percentage": 0
                    }
                }
            }]
        }
        PUT: {
            "return_reference": "SR-0001",
            "return_id": 1,
            "is_cancel": True
        }
        """
        errors = []
        request = self.request
        self.set_user_credientials()
        try:
            request_data = loads(request.body)
        except Exception:
            try:
                if request.method == 'POST':
                    request_data = request.POST.get("data")
                elif request.method == 'PUT':
                    request_data = request.PUT.get("data")
            except Exception:
                errors = ['Invalid Payload']

        if not request_data:
            errors = ['Invalid Payload']
        return request_data, errors

    def sales_return_process(self):
        '''
        sales return creation process
        '''
        if isinstance(self.request_data, dict):
            self.request_data = [self.request_data]

        extra_params = {
            'request_headers': dict(self.request_headers),
            'request_meta': self.request_meta
        }

        sales_return = SalesReturnCreationMixin(self.user, self.warehouse, self.request_data, extra_params=extra_params)
        sales_return_response_data = sales_return.sales_return_process()

        message, errors = '', sales_return_response_data.get('errors', []) or []
        if not errors:
            message = 'Sales Return Created Successfully!'
        else:
            errors = errors.get('', []) or []
    
        response_data = {
            'errors': errors,
            'message': message,
            'return_ids': sales_return_response_data.get('return_ids', []),
        }

        return response_data, errors



@get_warehouse
def get_sales_return_sku(request, warehouse):
    ''' returns sales return at sku level '''

    request_data = request.GET.copy()
    return_id = request_data.get('return_id', '')
    return_reference = request_data.get('return_reference', '')
    if not return_id and not return_reference:
        return JsonResponse({'errors': ['Return ID/Return Reference is required']}, status=400)
    
    request_data['line_level'] = True
    get_sales_return = SalesReturnDataMixin(request.user, warehouse, request_data)
    final_data_dict, errors = get_sales_return.get_sales_return_data()

    if errors:
        return JsonResponse({'errors': errors}, status=400)

    return JsonResponse(final_data_dict, status=200)
    
