#package imports
import pandas as pd
import traceback
from collections import defaultdict
from django.utils import timezone
import datetime
from django.db.models import Q, F

#wms base imports
from wms_base.models import User
from wms_base.wms_utils import init_logger
from core_operations.views.common.main import get_multiple_misc_values

#outbound import
from outbound.models import (
    SalesReturn, SalesReturnBatchLevel
)
from .constants import ERRORS

from core_operations.views.integration.integration import webhook_integration_3p
from .grn import auto_sales_return_grn

log = init_logger('logs/update_sales_return.log')


class SalesReturnUpdationMixin:
    def __init__(self, user: User, warehouse: User, request_data: list = None, extra_params: dict = None):
        self.user = user
        self.warehouse = warehouse
        self.extra_params = extra_params or {}
        self.request_data = request_data or []
        self.return_id_quantity_map, self.return_id_cancelled_quantity_map = defaultdict(int), defaultdict(int)
        self.request_type = 'cancel'

    def update_sales_return(self):
        """
        Updates the sales return data based on the request data.

        Returns a dictionary containing the following keys:
        - 'errors': A list of errors encountered during the update process.
        - 'sales_return_updation_details': A list of details of the sales return items that were updated.
        - 'return_ids': A list of return IDs that were updated.

        If any errors occur during the update process, the 'errors' key will be populated with the error messages.
        """
        self.sr_updation_details, self.errors, self.cancelled_return_ids = [], [], []

        self.validate_request_data()
        if self.errors:
            return {'errors': self.errors}

        self.fetch_sales_return_data()
        if self.errors:
            return {'errors': self.errors}

        self.validate_sales_return_data()
        if self.errors:
            return {'errors': self.errors}

        self.update_sales_return_data()
        self.sales_return_auto_grn()
        
        self.sales_return_callback()
        return {
            'errors': self.errors,
            'sales_return_updation_details': self.sr_updation_details,
            'return_ids': self.return_ids
        }
    
    def sales_return_callback(self):
        '''
        Callback for sales return
        '''
        if self.request_type == 'cancel':
            action = 'sales_return_cancellation'
        else:
            action = 'sales_return_update'
        try:
            return_ids = ",".join(self.return_ids)
            filters = {
                "return_id": return_ids,
            }
            webhook_integration_3p(self.warehouse.id, action, filters)
        except Exception as e:
            log.info(f"Error in sales return callback: {e}")
            log.debug(traceback.format_exc())


    def validate_request_data(self):
        """
        Validates the request data for sales return updation.

        This method checks if the required fields are present in the request data and
        appends any errors to the `self.errors` list.
        """
        self.return_ids, self.return_references = [], []
        self.full_cancel = False
        for request_record in self.request_data:
            return_id = request_record.get('return_id')
            return_reference = request_record.get('return_reference')

            if not (return_id or return_reference):
                self.errors.append(ERRORS['return_id_reference_mandatory'])
            
            self.full_cancel = request_record.get('full_cancel', False)

            self.return_ids.append(return_id)
            self.return_references.append(return_reference)

    def fetch_sales_return_data(self):
        """
        Fetches sales return data based on the applied filters.
        """
        self.get_sales_return_filter()

        sales_return_objects = SalesReturnBatchLevel.objects.filter(self.sales_return_filter_list, **self.sales_return_filter)

        if not sales_return_objects:
            self.sales_return_df = pd.DataFrame()
            self.errors.append(ERRORS['data_not_found'])
            return

        self.sales_return_df = pd.DataFrame(sales_return_objects.values('id', 'original_return_quantity', 'quantity', 'rejected_quantity', 'cancelled_quantity',
            'mrp', 'unit_price', 'reason', sku_code=F('sales_return_sku__sku__sku_code'), return_id=F('sales_return_sku__sales_return__return_id'), line_reference=F('sales_return_sku__line_reference'),
            return_reference=F('sales_return_sku__sales_return__return_reference'), batch_reference=F('batch_detail__batch_reference'),batch_number=F('batch_detail__batch_no')))
        sales_return_objects = list(sales_return_objects)
        for sr_obj in sales_return_objects:
            if self.full_cancel and sr_obj.original_return_quantity != sr_obj.quantity + sr_obj.cancelled_quantity:
                return_id = self.sales_return_df.loc[self.sales_return_df['id'] == sr_obj.id, 'return_id'].values[0]
                self.errors.append(ERRORS['full_cancel_not_possible'] % return_id)
                continue
            self.sales_return_df.loc[self.sales_return_df['id'] == sr_obj.id, 'object'] = sr_obj
        
        self.sales_return_df[['batch_reference', 'batch_number', 'line_reference']] = self.sales_return_df[['batch_reference', 'batch_number', 'line_reference']].fillna('')

    def get_sales_return_filter(self):
        """
        Get the filter for retrieving sales returns based on specified criteria.

        Returns:
            dict: The filter dictionary containing the criteria for retrieving sales returns.
        """

        self.sales_return_filter = {
            'sales_return_sku__sales_return__warehouse_id': self.warehouse.id,
        }

        self.sales_return_filter_list= Q()
        if self.return_ids:
            self.sales_return_filter_list |= Q(sales_return_sku__sales_return__return_id__in = self.return_ids)
        if self.return_references:
            self.sales_return_filter_list |= Q(sales_return_sku__sales_return__return_reference__in = self.return_references)

    def validate_sales_return_data(self):
        """
        Validates the sales return data for each request record.
        If the 'is_cancel' flag is True and there are no items, cancels the sales return.
        """
        for request_record in self.request_data:
            is_cancel = request_record.get('is_cancel', False)

            if is_cancel:
                self.cancel_sales_return(request_record)
            else:
                self.request_type = 'update'
                self.prepare_sales_return_updation(request_record)
    

    def prepare_sales_return_updation(self, request_record):
        """
        updates the sales return checkin in salesreturnbatchlevel table
        """
        items = request_record.get('items', [])
        return_id = request_record.get('return_id', '')
        current_time = datetime.datetime.now()
        local_time = current_time.astimezone()
        time_in_target_zone = local_time.astimezone(timezone.get_current_timezone())
        formatted_date = time_in_target_zone.strftime("%Y-%m-%d %H:%M:%S")
        for item in items:
            sku_code = item.get('sku_code', '')
            batch_reference = item.get('batch_reference', '') or ''
            batch_number = item.get('batch_number', '') or ''
            reason = item.get('reason', '')
            remarks = item.get('remarks', '')
            line_reference = item.get('line_reference', '')

            sales_return_filtered_df = self.sales_return_df[(self.sales_return_df['return_id'] == return_id) & (self.sales_return_df['sku_code'] == sku_code) & (self.sales_return_df['batch_reference'] == batch_reference) & (self.sales_return_df['batch_number'] == batch_number) & (self.sales_return_df['reason'] == reason) & (self.sales_return_df['line_reference'] == line_reference)]
            for idx, row in sales_return_filtered_df.iterrows():
                sales_return_batch_id = row['id']
                json_data = row['object'].json_data
                json_data['check_in_remarks'] = remarks
                json_data['check_in_user'] = self.user.username
                json_data['check_in_time'] = formatted_date
                self.sales_return_df.loc[self.sales_return_df['id'] == sales_return_batch_id, 'object'].json_data = json_data

    def cancel_sales_return(self, request_record: dict = {}):
        """
        Cancel a sales return by setting the quantity to 0 and updating the cancelled_quantity.

        Args:
            request_record (dict): The request record containing the return_id and return_reference.
        """
        return_id = request_record.get('return_id', '')
        return_reference = request_record.get('return_reference', '')

        unique_key = return_id or return_reference

        if return_id:
            sales_return_filtered_df = self.sales_return_df[self.sales_return_df['return_id'] == return_id]
        elif return_reference:
            sales_return_filtered_df = self.sales_return_df[self.sales_return_df['return_reference'] == return_reference]

        for index, sales_return_record in sales_return_filtered_df.iterrows():
            sales_return_record['object'].cancelled_quantity = sales_return_record['quantity']
            sales_return_record['object'].quantity = 0
            sales_return_record['object'].status = 3
            self.return_id_quantity_map[unique_key] += sales_return_record['original_return_quantity']
            self.return_id_cancelled_quantity_map[unique_key] += sales_return_record['cancelled_quantity'] + sales_return_record['quantity']


        self.cancelled_return_ids.append(return_id)

    def update_sales_return_data(self):
        """
        Updates the sales return data by calling the necessary methods.
        """
        self.update_sales_return_status()
        self.update_sales_return_batch_quantity()

    def update_sales_return_status(self):
        """
        Update the status of sales returns.

        This method updates the status of sales returns based on the cancelled_return_ids.
        It sets the status to 3 for the sales returns that match the warehouse_id and return_id in the cancelled_return_ids list.
        """

        if self.request_type == 'cancel':
            self.update_sales_return_cancel_status()
        else:
            self.update_sales_return_status_for_update()

    
    def update_sales_return_cancel_status(self):
        """
        updates sales return header status to cancelled items
        """
        cancelled_sales_returns = []
        for return_id in self.return_id_quantity_map:
            oringal_quantity = self.return_id_quantity_map[return_id]
            cancelled_quantity = self.return_id_cancelled_quantity_map.get(return_id, 0)
            if oringal_quantity == cancelled_quantity:
                cancelled_sales_returns.append(return_id)

        if cancelled_sales_returns:
            SalesReturn.objects.filter(warehouse_id=self.warehouse.id, return_id__in=cancelled_sales_returns).update(status=3)
        
    def update_sales_return_status_for_update(self):
        """
        update sales return status after check in to open status
        """
        SalesReturn.objects.filter(warehouse_id=self.warehouse.id, return_id__in=self.return_ids).update(status=1)

    def update_sales_return_batch_quantity(self):
        """
        Update the quantity and cancelled_quantity of sales return batches.

        This method bulk updates the quantity and cancelled_quantity fields of sales return batches
        based on the values in the sales_return_df DataFrame.
        """
        if self.request_type == 'cancel':
            if self.sales_return_df['object'].tolist():
                SalesReturnBatchLevel.objects.bulk_update(self.sales_return_df['object'].tolist(), ['quantity', 'cancelled_quantity', 'status'])
        else:
            if self.sales_return_df['object'].tolist():
                SalesReturnBatchLevel.objects.bulk_update(self.sales_return_df['object'].tolist(), ['json_data'])


    def sales_return_auto_grn(self):
        """
        Creates an auto GRN (Goods Receipt Note) for sales return.
        This function is triggered when sales return auto GRN is enabled and
        when a sales return is moved from 'onhold' to 'open' status.
        """
        if self.request_type == 'cancel':
            return
        
        # Get configuration for auto GRN
        configs = get_multiple_misc_values(['auto_grn_for_salesreturn'], self.warehouse.id)
        auto_sr_grn = configs.get('auto_grn_for_salesreturn', 'false')
    
        # Only proceed if auto GRN is enabled
        if auto_sr_grn != 'true':
            return
            
        # Trigger auto GRN for the updated sales returns
        auto_sales_return_grn.apply_async(args=[self.user.id, self.warehouse.id, self.return_ids, self.extra_params])