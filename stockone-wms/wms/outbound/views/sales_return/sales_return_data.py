#package imports
from dateutil import parser
from datetime import datetime, time, timedelta
from collections import defaultdict

#django import s
from django.db.models import F, Sum, Q

#wms base imports
from wms_base.models import User

#inventory imports
from inventory.models import SKUPackMaster

#outbound imports
from outbound.models import SalesReturn, SalesReturnBatchLevel, SellerOrderSummary

#inbound imports
from inbound.models import SellerPOSummary, QualityCheck

from .helpers import get_date_time, get_misc_value

from core_operations.views.common.main import (
    get_utc_time_from_user_timezone, get_multiple_misc_values, truncate_float
)

from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
from inventory.views.serial_numbers.serial_number import SerialNumberMixin


SALES_RETURN_GET_API_FIELDS = {
    "return_id": "return_id",
    "invoice_number": "salesreturnlinelevel__reference_number",
    "order_reference": "salesreturnlinelevel__order_reference",
    "customer_type": "salesreturnlinelevel__sales_return__customer__customer_type",
    "status": "status",
    "creation_from_date": "creation_date__gte",
    "creation_to_date": "creation_date__lte",
    "updation_from_date": "updation_date__gte",
    "updation_to_date": "updation_date__lte",
    "return_reference": "return_reference"
}

ITEM_LEVEL_CALCULATIONS = ['invoice_quantity', 'return_quantity', 'rejected_quantity', 'accepted_quantity', 'cancelled_quantity']

BATCH_LEVEL_CALCULATIONS = ['return_quantity', 'rejected_quantity', 'grn_quantity']

SALES_RETURN_STATUS_MAPPING = {
    0: "return_created",
    1: "return_initiated",
    2: "return_completed",
    3: "return_cancelled",
    4: "on_hold"
}

SALES_RETURN_TEXT_STATUS_MAP = {
    "return_created": 0,
    "return_initiated": 1,
    "return_completed": 2,
    "return_cancelled": 3,
    "on_hold": 4
}

class SalesReturnDataMixin:
    def __init__(self, user: User, warehouse: User, request_data: dict = None, is_integration=False):
        self.user = user
        self.warehouse = warehouse

        self.request_data = request_data or {}

        self.timezone = self.warehouse.userprofile.timezone
        
        self.is_integration = is_integration

    def get_sales_return_data(self):
        """
        Retrieves the sales return data.

        Returns:
            tuple: A tuple containing the final data and any errors encountered.
        """
        self.final_data, self.errors, self.sku_codes, self.sku_pack_details, self.return_id_serial_numbers_map = [], [], [], {}, {}
        self.final_data_dict, self.unique_sales_return_agg_dict, self.sr_batch_id_grn_quantity_map = {}, {}, defaultdict(float)
        self.unique_return_id_sku_keys = set()
        self.get_request_data()
        if self.errors:
            return self.final_data, self.errors

        self.fetch_misc_configurations()
        self.get_pagination_details()
        self.fetch_sales_return_objects()
        if self.errors:
            return self.final_data, self.errors
        if self.misc_dict.get('enable_sales_uom') == 'true':
            self.get_pack_master_details()
        
        self.final_data = self.format_sales_return_data()

        if self.is_integration:
           return self.final_data, {}
        
        self.get_page_info()
        final_data = {
            "warehouse" : self.warehouse.username,
            "data": self.final_data,
            "page_info": self.page_info
        } 

        return final_data, self.errors

    def get_request_data(self):
        """
        Retrieves and filters the request data for sales return.
        """
        self.sales_return_filter = {
            "warehouse_id": self.warehouse.id
        }
        self.q_filters = Q()
        try:
            for field, mapped_field in SALES_RETURN_GET_API_FIELDS.items():
                if field == 'status' and self.request_data.get(field) and self.request_data.get(field) not in SALES_RETURN_TEXT_STATUS_MAP.keys():
                    self.errors.append(f"Invalid Status Filters: {self.request_data.get('status')}")
                if field == 'status' and self.request_data.get(field):
                    self.sales_return_filter['status'] = SALES_RETURN_TEXT_STATUS_MAP.get(self.request_data.get(field))
                elif field == 'return_id' and self.request_data.get(field):
                    return_ids = [rid.strip() for rid in self.request_data.get(field, '').split(",") if rid.strip()]
                    if return_ids:
                        self.q_filters |= Q(return_id__in=return_ids) | Q(return_reference__in=return_ids)
                elif self.request_data.get(field):
                    if field in ["creation_from_date", "creation_to_date", "updation_from_date", "updation_to_date"]:
                        self.sales_return_filter[mapped_field] = get_utc_time_from_user_timezone(self.request_data.get(field))
                    else:
                        self.sales_return_filter[mapped_field] = self.request_data.get(field)
        except Exception:
            self.errors.append("Invalid Filters")
            import traceback
            print(traceback.format_exc())

        for request_field, request_value in self.request_data.items():
            if request_field not in ["warehouse", "page_size", "page", "line_level"] and request_field not in list(SALES_RETURN_GET_API_FIELDS.keys()) and 'Invalid Filters' not in self.errors:
                self.errors.append('Invalid Filters')


    def fetch_misc_configurations(self):
        """
        Fetches miscellaneous configurations for the warehouse.

        Sets default values:
        - 'decimal_limit_price' to 2 if not provided
        - 'decimal_limit' to 2 if not provided
        """

        misc_types = ['enable_sales_uom', 'decimal_limit_price', 'decimal_limit', 'sku_serialisation']

        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        if self.misc_dict.get('decimal_limit_price') in [None, '', 'false']:
            self.misc_dict['decimal_limit_price'] = 2
        if self.misc_dict.get('decimal_limit') in [None, '', 'false']:
            self.misc_dict['decimal_limit'] = 2
        


    def get_pagination_details(self):
        """
        Retrieves the pagination details from the request data.
        """
        try:
            self.page_size = int(self.request_data.get('page_size', 10))
        except Exception:
            self.page_size = 10
        try:
            self.page = int(self.request_data.get('page', 1))
        except Exception:
            self.page = 1

        self.start_index = (self.page - 1) * self.page_size
        self.end_index = self.page * self.page_size

    def get_page_info(self):
        """
        Retrieves the page info for the sales return data.
        """
        self.page_info = {
            "current_page": self.page,
            "page_size": self.page_size,
            "total_pages": len(self.sales_return_ids) // self.page_size + 1,
            "total_records": len(self.sales_return_ids),
        }
        
    def fetch_sales_return_objects(self):
        """
        Fetches sales return objects based on the provided filter criteria.
        """
        self.sales_return_ids = SalesReturn.objects.filter(**self.sales_return_filter).filter(self.q_filters).values_list('return_id', flat=True)
        if self.is_integration:
            return_ids = self.sales_return_ids
        else:
            return_ids = self.sales_return_ids[self.start_index:self.end_index]

        self.sales_return_objects = list(SalesReturnBatchLevel.objects.filter(sales_return_sku__sales_return__warehouse_id=self.warehouse.id, sales_return_sku__sales_return__return_id__in=return_ids).values(
            'id', 'mrp', 'unit_price', 'reason', 'rejected_quantity', 'accepted_quantity', 'json_data', 'creation_date', 'quantity', 'cancelled_quantity',
            return_type=F('sales_return_sku__sales_return__return_type'),
            document_type=F('sales_return_sku__sales_return__document_type'),
            reference_number=F('sales_return_sku__sales_return__reference_number'),
            return_reference=F('sales_return_sku__sales_return__return_reference'),
            return_id=F('sales_return_sku__sales_return__return_id'),
            return_quantity = F('original_return_quantity'),
            return_date = F('sales_return_sku__sales_return__return_date'),
            return_creation_date = F('sales_return_sku__sales_return__creation_date'),
            sr_status = F('sales_return_sku__sales_return__status'),
            credit_note_number=F('sales_return_sku__credit_note_number'),
            batch_reference=F('batch_detail__batch_reference'),
            batch_number = F('batch_detail__batch_no'),
            manufactured_date=F('batch_detail__manufactured_date'),
            expiry_date=F('batch_detail__expiry_date'),
            sku_code=F('sales_return_sku__sku__sku_code'),
            sku_desc=F('sales_return_sku__sku__sku_desc'),
            sku_url=F('sales_return_sku__sku__image_url'),
            sku_brand = F('sales_return_sku__sku__sku_brand'),
            sr_sku_json = F('sales_return_sku__json_data'),
            order_reference=F('sales_return_sku__order_reference'),
            sr_sku_status = F('sales_return_sku__status'),
            invoice_reference=F('sales_return_sku__reference_number'),
            sr_json_data = F('sales_return_sku__sales_return__json_data'),
            customer_id = F('sales_return_sku__sales_return__customer_id'),
            customer_reference=F('sales_return_sku__sales_return__customer__customer_reference'),
            customer_city=F('sales_return_sku__sales_return__customer__city'),
            customer_name=F('sales_return_sku__sales_return__customer__name'),
            customer_type=F('sales_return_sku__sales_return__customer__customer_type'),
            line_reference=F('sales_return_sku__line_reference'),
            sku_measurement_type = F('sales_return_sku__sku__measurement_type'),
            sos_id=F('sellerordersummary_id')
            ))

        if not self.sales_return_objects:
            self.errors.append('No Sales Return Found')
        
        return_ids = []
        sos_ids = []
        for sales_return_record in self.sales_return_objects:
            sku_code = sales_return_record.get('sku_code', '')
            if sku_code not in self.sku_codes:
                self.sku_codes.append(sku_code)
            return_id = sales_return_record.get('return_id', '')
            return_ids.append(return_id)
            sos_ids.append(sales_return_record.get('sos_id'))
        
        sos_values = list(SellerOrderSummary.objects.filter(id__in=sos_ids, order_status_flag__in=['customer_invoices', 'delivery_challans']).values('id', 'order__original_quantity', 'order__json_data', 'quantity', 'updation_date'))
        sos_values_map = {}
        for sos_value in sos_values:
            sos_id = sos_value['id']
            order_json_data = sos_value.get('order__json_data', {}) or {}
            sos_values_map[sos_id] = {
                'invoice_quantity': sos_value.get('quantity', 0),
                'order_quantity': sos_value.get('order__original_quantity', 0),
                'invoice_date': sos_value.get('updation_date', ''),
                'order_json_data': order_json_data
            }
        for sales_return_record in self.sales_return_objects:
            sos_id = sales_return_record.get('sos_id')
            if sos_id in sos_values_map:
                sales_return_record['invoice_quantity'] = sos_values_map[sos_id].get('invoice_quantity', 0)
                sales_return_record['order_quantity'] = sos_values_map[sos_id].get('order_quantity', 0)
                sales_return_record['order_json_data'] = sos_values_map[sos_id].get('order_json_data', {})
                sales_return_record['invoice_date'] = sos_values_map[sos_id].get('invoice_date', '')

        
        self.return_grn_data, self.sr_status_dict = self.get_grn_details(return_ids)

        if not self.errors and self.misc_dict.get('sku_serialisation') == 'true':
            self.get_serialised_sku_details(return_ids)
    

    def get_serialised_sku_details(self, return_ids):
        """
        returns serial numbers for the given sales return ids
        """
        self.return_id_serial_numbers_map = {}
        filters = {"reference_number__in": return_ids, "reference_type": "sales_return"}
        serial_number_transaction_obj = SerialNumberTransactionMixin(self.user, self.warehouse, {"filters": filters})
        serial_number_transaction_obj.get_existing_sntd()
        existing_serial_numbers = serial_number_transaction_obj.sntd_objects_list
        for serial_number_detail in existing_serial_numbers:
            return_id = serial_number_detail['reference_number']
            transact_id = serial_number_detail['transact_id']
            transact_type = serial_number_detail['transact_type']
            serial_number = serial_number_detail['serial_number']
            if return_id not in self.return_id_serial_numbers_map:
                self.return_id_serial_numbers_map[return_id] = {}
            if transact_id not in self.return_id_serial_numbers_map[return_id]:
                self.return_id_serial_numbers_map[return_id][transact_id] = {"accepted": [], "rejected": []}
            self.return_id_serial_numbers_map[return_id][transact_id][transact_type].append(serial_number)


    def get_pack_master_details(self):
        '''
        Get Pack Master details
        '''
        sku_pack_objects = list(SKUPackMaster.objects.filter(sku__user=self.warehouse.id, sku__sku_code__in=self.sku_codes, status=1, sales_uom=1).values('sku__sku_code', 'pack_quantity', 'pack_id'))

        for pack in sku_pack_objects:
            sku_code = pack['sku__sku_code']
            pack_quantity = pack['pack_quantity']
            self.sku_pack_details[(sku_code, pack_quantity)] = {'pack_quantity': pack_quantity,'pack_id': pack['pack_id']}

    def format_sales_return_data(self):
        """
        Formats the sales return data by iterating through the sales return objects
        and organizing them into a final data dictionary.

        Returns:
            list: The formatted sales return data as a list.
        """


        for sales_return_record in self.sales_return_objects:
            return_type = sales_return_record.get('return_type', '')
            return_id = sales_return_record.get('return_id', '')
            document_type = sales_return_record.get('document_type', '')
            reference_number = sales_return_record.get('reference_number', '')
            return_reference = sales_return_record.get('return_reference', '')
            json_data = sales_return_record.get('json_data', {}) or {}

            unique_sales_return_key = (return_type, return_id, document_type, reference_number, return_reference)
            item_amount = sales_return_record.get('unit_price', 0) * sales_return_record.get('return_quantity', 0)
            tax_amount = item_amount * (json_data.get('total_tax', 0) / 100)
            self.unique_sales_return_agg_dict.setdefault(unique_sales_return_key, {'total_return_value': 0})
            self.unique_sales_return_agg_dict[unique_sales_return_key]['total_return_value'] = truncate_float(
                item_amount + tax_amount + self.unique_sales_return_agg_dict[unique_sales_return_key]['total_return_value'], self.misc_dict.get('decimal_limit_price')
            )

        for sales_return_record in self.sales_return_objects:
            return_type = sales_return_record.get('return_type', '')
            return_id = sales_return_record.get('return_id', '')
            document_type = sales_return_record.get('document_type', '')
            reference_number = sales_return_record.get('reference_number', '')
            return_reference = sales_return_record.get('return_reference', '')

            unique_sales_return_key = (return_type, return_id, document_type, reference_number, return_reference)
            if not self.final_data_dict.get(unique_sales_return_key):
                self.get_header_level_details(sales_return_record, unique_sales_return_key)

            if self.request_data.get('line_level'):
                self.get_sr_item_sku_level_details(sales_return_record, unique_sales_return_key)
            else:
                self.get_sr_item_level_details(sales_return_record, unique_sales_return_key)

        return self.get_final_data_list()

    def get_header_level_details(self, sales_return_record: dict = {}, unique_sales_return_key: tuple = ()):
        """
        Retrieves the header level details for the sales return.
        """
        sr_line_json_data = sales_return_record.get('sr_sku_json', {}) or {}
        sr_status = sales_return_record.get('sr_status', '')
        if sr_status == 1 and self.sr_status_dict.get(unique_sales_return_key):
            mapped_sr_status = self.sr_status_dict[unique_sales_return_key]
        else:
            mapped_sr_status = SALES_RETURN_STATUS_MAPPING.get(sr_status, '')

        self.final_data_dict[unique_sales_return_key] = {
            "return_type": sales_return_record.get('return_type', ''),
            "return_id": sales_return_record.get('return_id', ''),
            "document_type": sales_return_record.get('document_type', ''),
            "reference_number": sales_return_record.get('reference_number', ''),
            "return_reference": sales_return_record.get('return_reference', ''),
            "return_date": get_date_time(self.timezone, sales_return_record.get('return_date', '')),
            "return_creation_date": get_date_time(self.timezone, sales_return_record.get('return_creation_date', '')),
            "order_type": sr_line_json_data.get('order_type', ''),
            "customer_id": sales_return_record.get('customer_id', ''),
            "customer_reference": sales_return_record.get('customer_reference', ''),
            "customer_city": sales_return_record.get('customer_city', ''),
            "customer_type": sales_return_record.get('customer_type', ''),
            "customer_name": sales_return_record.get('customer_name', ''),
            "status": mapped_sr_status,
            "total_return_value": self.unique_sales_return_agg_dict.get(unique_sales_return_key, {}).get('total_return_value', 0),
            "items": {}
        }

    def get_sr_item_level_details(self, sales_return_record: dict = {}, unique_sales_return_key: tuple = ()):
        """
        Retrieves the item level details for a sales return record.

        Args:
            sales_return_record (dict): The sales return record containing the item details.
            unique_sales_return_key (tuple): The unique key for the sales return record.
        """
        document_type = sales_return_record.get('document_type', '')
        return_id = sales_return_record.get('return_id', '')
        invoice_number = sales_return_record.get('invoice_reference', '')
        order_reference = sales_return_record.get('order_reference', '')
        sku_code = sales_return_record.get('sku_code', '')
        sku_measurement_type = sales_return_record.get('sku_measurement_type','')
        line_reference = sales_return_record.get('line_reference', '')
        
        unique_sr_sku_key = (document_type, return_id, invoice_number, order_reference, sku_code, line_reference)
        return_id_sku_key = (return_id, sku_code, invoice_number, order_reference)
        sr_grn_data = self.return_grn_data.get(return_id_sku_key, {})
        if sr_grn_data:
            if return_id_sku_key in self.unique_return_id_sku_keys:
                return
            self.unique_return_id_sku_keys.add(return_id_sku_key)
            self.prepare_sr_grn_item_level_data(sr_grn_data, sales_return_record, unique_sales_return_key, unique_sr_sku_key)
            return
        if not self.final_data_dict[unique_sales_return_key]['items'].get(unique_sr_sku_key):
            self.get_item_level_dict(sales_return_record, unique_sales_return_key, unique_sr_sku_key)
        else:
            self.get_item_level_calculation(sales_return_record, unique_sales_return_key, unique_sr_sku_key)

        if self.misc_dict.get('enable_sales_uom') == 'true':
            self.prepare_pack_details(sales_return_record, unique_sales_return_key, unique_sr_sku_key)

        batch_number = sales_return_record.get('batch_number', '')
        batch_reference = sales_return_record.get('batch_reference', '')
        unique_sr_batch_key = (sku_code, batch_number, batch_reference)
        if not self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'].get(unique_sr_batch_key):
            self.get_batch_details(sales_return_record, unique_sales_return_key, unique_sr_sku_key, unique_sr_batch_key)
        else:
            self.get_batch_level_calculation(sales_return_record, unique_sales_return_key, unique_sr_sku_key, unique_sr_batch_key)

    def prepare_sr_grn_item_level_data(self, sr_grn_data, sales_return_record, unique_sales_return_key, unique_sr_sku_key):
        """
        Prepare the item level data for the sales return GRN completed items.
        """
        document_type, return_id, invoice_number, order_reference, sku_code, line_reference = unique_sr_sku_key
        sku_measurement_type = sales_return_record.get('sku_measurement_type','')
        for sr_data in sr_grn_data.values():
            grn_number = sr_data.get('grn_number', '')
            unique_sr_sku_key = (document_type, return_id, invoice_number, order_reference, sku_code, line_reference, grn_number)
            self.get_item_level_dict(sales_return_record, unique_sales_return_key, unique_sr_sku_key, sr_data)
            if self.misc_dict.get('enable_sales_uom') == 'true':
                self.prepare_pack_details(sales_return_record, unique_sales_return_key, unique_sr_sku_key)
            
            sr_grn_batch_data = sr_data.get('batch_details', [])
            for grn_batch_data in sr_grn_batch_data:
                batch_number = grn_batch_data.get('batch_number', '')
                batch_reference = grn_batch_data.get('batch_reference', '')
                unique_sr_batch_key = (sku_code, batch_number, batch_reference, grn_number)
                self.get_batch_details(sales_return_record, unique_sales_return_key, unique_sr_sku_key, unique_sr_batch_key, grn_batch_data)

    def get_grn_details(self, return_ids):
        """
        Get GRN details for the sales return data.
        """
        values_list = [
            'sku__sku_code', 'grn_number', 'grn_reference', 'creation_date', 'batch_detail__batch_no', 'sales_return__reference_number',
            'batch_detail__manufactured_date', 'batch_detail__expiry_date', 'batch_detail__mrp', 'price', 'sales_return__document_type',
            'batch_detail__batch_reference', 'quantity', 'damaged_quantity', 'credit_note_number', 'sales_return__return_id', 'id',
            'sales_return__return_type', 'sales_return__return_reference', 'sales_return_batch__original_return_quantity',
            'sales_return_batch_id', 'sales_return_batch__sales_return_sku__order_reference', 'sales_return_batch__sales_return_sku__reference_number',
            'json_data', 'sales_return_batch__json_data', 'accepted_quantity'
        ]
        grn_data = list(SellerPOSummary.objects.filter(user_id=self.warehouse.id, sales_return__return_id__in=return_ids)
                        .values(*values_list))
        sps_ids = [data['id'] for data in grn_data]

        filters = {"transact_id__in": sps_ids, "reference_type": "grn"}
        serial_number_transaction_obj = SerialNumberTransactionMixin(self.user, self.warehouse, {"filters": filters})
        serial_number_transaction_obj.get_existing_sntd()

        existing_serial_numbers = serial_number_transaction_obj.sntd_objects_list
        sps_id_serial_number_map = {}
        for serial_number_detail in existing_serial_numbers:
            
            transact_id = serial_number_detail['transact_id']
            serial_number = serial_number_detail['serial_number']
            sps_id_serial_number_map.setdefault(transact_id, [])
            sps_id_serial_number_map[transact_id].append(serial_number)

        quantity_data = list(QualityCheck.objects.filter(po_location_id__seller_po_summary_id__in=sps_ids).values('po_location_id__seller_po_summary_id').annotate(total_accepted_quantity=Sum('accepted_quantity'), total_rejected_quantity=Sum('rejected_quantity')))
        grn_quantity_dict = {}
        for data in quantity_data:
            grn_quantity_dict[data['po_location_id__seller_po_summary_id']] = data
        sku_wise_grn_data, sr_grn_quantity_dict, sr_quantity_dict, sr_status_dict = defaultdict(dict), defaultdict(int), {}, {}
        for data in grn_data:
            accepted_quantity = grn_quantity_dict.get(data['id'], {}).get('total_accepted_quantity', 0) or 0
            rejected_quantity = grn_quantity_dict.get(data['id'], {}).get('total_rejected_quantity', 0) or 0
            if not accepted_quantity:
                accepted_quantity = data['accepted_quantity']
            if not rejected_quantity:
                rejected_quantity = data['damaged_quantity']
            serial_numbers = sps_id_serial_number_map.get(data['id'], [])
            batch_data = {
                "batch_number": data['batch_detail__batch_no'],
                "batch_reference": data['batch_detail__batch_reference'],
                "manufactured_date": get_date_time(self.timezone, data['batch_detail__manufactured_date']),
                "expiry_date": get_date_time(self.timezone, data['batch_detail__expiry_date']),
                "mrp": data['batch_detail__mrp'],
                "unit_price": data['price'],
                "return_quantity": data['quantity'],
                "rejected_quantity": rejected_quantity,
                "accepted_quantity": accepted_quantity,
                "grn_quantity": data['quantity'],
                "serial_numbers": serial_numbers
            }
            unique_key = (data['sales_return__return_id'], data['sku__sku_code'], data['sales_return_batch__sales_return_sku__reference_number'], data['sales_return_batch__sales_return_sku__order_reference'])
            sku_wise_grn_data[unique_key].setdefault(data['grn_number'], {})
            sku_wise_grn_data[unique_key][data['grn_number']].setdefault('batch_details', [])
            sku_wise_grn_data[unique_key][data['grn_number']].setdefault('grn_quantity', 0)
            sku_wise_grn_data[unique_key][data['grn_number']]['grn_quantity'] += data['quantity']
            sku_wise_grn_data[unique_key][data['grn_number']]['batch_details'].append(batch_data)
            sku_wise_grn_data[unique_key][data['grn_number']]['grn_number'] = data['grn_number']
            sku_wise_grn_data[unique_key][data['grn_number']]['grn_reference'] = data['grn_reference']
            sku_wise_grn_data[unique_key][data['grn_number']]['grn_date'] = get_date_time(self.timezone, data['creation_date'])
            sku_wise_grn_data[unique_key][data['grn_number']]['credit_note_number'] = data['credit_note_number']
            sku_wise_grn_data[unique_key][data['grn_number']].setdefault('accepted_quantity', 0)
            sku_wise_grn_data[unique_key][data['grn_number']].setdefault('rejected_quantity', 0)
            sku_wise_grn_data[unique_key][data['grn_number']]['accepted_quantity'] += accepted_quantity
            sku_wise_grn_data[unique_key][data['grn_number']]['rejected_quantity'] += rejected_quantity
            json_data = data.get('json_data', {}) or {}
            sku_wise_grn_data[unique_key][data['grn_number']]['created_by'] = json_data.get('created_by', '')
            sr_batch_json_data = data.get('sales_return_batch__json_data', {}) or {}
            total_tax_amount = truncate_float(data['price'] * data['quantity'] * (sr_batch_json_data.get('total_tax', 0) / 100), self.misc_dict.get('decimal_limit_price'))
            sku_wise_grn_data[unique_key][data['grn_number']].setdefault('grn_total_value', 0)
            sku_wise_grn_data[unique_key][data['grn_number']]['grn_total_value'] = truncate_float(
                (data['price'] * data['quantity']) + total_tax_amount + sku_wise_grn_data[unique_key][data['grn_number']]['grn_total_value'],
                self.misc_dict.get('decimal_limit_price')
            )
            return_type = data['sales_return__return_type']
            return_id = data['sales_return__return_id']
            document_type = data['sales_return__document_type']
            reference_number = data['sales_return__reference_number']
            return_reference = data['sales_return__return_reference']
            sales_return_batch_id = data['sales_return_batch_id']
            self.sr_batch_id_grn_quantity_map[sales_return_batch_id] += data.get('quantity', 0) or 0
            unique_sales_return_key = (return_type, return_id, document_type, reference_number, return_reference)
            unique_sales_return_batch_key = (return_type, return_id, document_type, reference_number, return_reference, sales_return_batch_id)
            sr_quantity_dict[unique_sales_return_batch_key] = (data['sales_return_batch__original_return_quantity'])
            sr_grn_quantity_dict[unique_sales_return_key] += (data['quantity'])
        sr_qty_dict = defaultdict(int)
        for key, grn_qty in sr_quantity_dict.items():
            unique_key = (key[0], key[1], key[2], key[3], key[4])
            sr_qty_dict[unique_key] += grn_qty
        for key, grn_qty in sr_grn_quantity_dict.items():
            return_qty = sr_qty_dict[key]
            if grn_qty < return_qty:
                sr_status_dict[key] = 'Partially_received'
            else:
                sr_status_dict[key] = 'return_received'
        
        return sku_wise_grn_data, sr_status_dict

    def get_item_level_calculation(self, sales_return_record: dict = {}, unique_sales_return_key: tuple = (), unique_sr_sku_key: tuple = ()):
        """
        Calculates the item level details for the sales return.
        """
        for calculation in ITEM_LEVEL_CALCULATIONS:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key][calculation] += sales_return_record.get(calculation, 0) or 0

    def get_batch_level_calculation(self, sales_return_record: dict = {}, unique_sales_return_key: tuple = (), unique_sr_sku_key: tuple = (), unique_sr_batch_key: tuple = ()):
        """
        Calculates the batch level details for the sales return.
        """
        for calculation in BATCH_LEVEL_CALCULATIONS:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key][calculation] += sales_return_record.get(calculation, 0) or 0
        
        self.prepare_serial_number_details(sales_return_record, unique_sales_return_key, unique_sr_sku_key, unique_sr_batch_key)
    
    def prepare_serial_number_details(self, sales_return_record, unique_sales_return_key, unique_sr_sku_key, unique_sr_batch_key):
        """
        Prepare the serial number details for the sales return record.
        """
        try:
            serial_numbers = self.return_id_serial_numbers_map.get(sales_return_record.get('return_id', ''), {}).get(sales_return_record.get('id', ''), {})
            if serial_numbers:
                accepted_serial_numbers = serial_numbers.get('accepted', [])
                rejected_serial_numbers = serial_numbers.get('rejected', [])
                if 'accepted' in self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key]['serial_numbers']:
                    self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key]['serial_numbers']['accepted'].extend(accepted_serial_numbers)
                else:
                    self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key]['serial_numbers']['accepted'] = accepted_serial_numbers
                if 'rejected' in self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key]['serial_numbers']:
                    self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key]['serial_numbers']['rejected'].extend(rejected_serial_numbers)
                else:
                    self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key]['serial_numbers']['rejected'] = rejected_serial_numbers
                
        except Exception as e:
            pass

    def get_item_level_dict(self, sales_return_record: dict = {}, unique_sales_return_key: tuple = (), unique_sr_sku_key: tuple = (), sr_grn_data: dict = {}):
        """
        Get the item level dictionary for a sales return record.

        Args:
            sales_return_record (dict): The sales return record.
            unique_sales_return_key (str): The unique key for the sales return.
            unique_sr_sku_key (str): The unique key for the sales return SKU.

        Returns:
            dict: The item level dictionary for the sales return record.
        """
        json_data = sales_return_record.get('json_data', {}) or {}
        sr_json_data = sales_return_record.get('sr_json_data', {}) or {}
        sr_sku_json_data = sales_return_record.get('sr_sku_json', {}) or {}
        return_quantity = sales_return_record.get('return_quantity', 0) or 0
        rejected_quantity = sales_return_record.get('rejected_quantity', 0) or 0
        order_quantity = sales_return_record.get('order_quantity', 0) or 0
        original_accepted_qty = sales_return_record.get('accepted_quantity', 0) or 0
        short_quantity = max(0, return_quantity - rejected_quantity - original_accepted_qty)
        order_json_data = sales_return_record.get('order_json_data', {}) or {}
        cancelled_quantity = sales_return_record.get('cancelled_quantity', 0) or 0

        self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key] = {
            "sku_code": sales_return_record.get('sku_code', ''),
            "sku_desc": sales_return_record.get('sku_desc', ''),
            "sku_brand": sales_return_record.get('sku_brand', ''),
            "sku_url": sales_return_record.get('sku_url', ''),
            "weight": json_data.get('weight', ''),
            "line_reference": sales_return_record.get('line_reference', ''),
            "order_line_reference": order_json_data.get('line_reference', ''),
            "taxes" : {
                "cgst": json_data.get('cgst', 0),
                "igst": json_data.get('igst', 0),
                "sgst": json_data.get('sgst', 0),
                "cess": json_data.get('cess', 0),
                "cgst_amount": json_data.get('cgst', 0) * (sales_return_record.get('unit_price', 0) / 100),
                "igst_amount": json_data.get('igst', 0) * (sales_return_record.get('unit_price', 0) / 100),
                "sgst_amount": json_data.get('sgst', 0) * (sales_return_record.get('unit_price', 0) / 100),
                "cess_amount": json_data.get('cess', 0) * (sales_return_record.get('unit_price', 0) / 100),
            },
            "order_reference": sales_return_record.get('order_reference', ''),
            "invoice_reference": sales_return_record.get('invoice_reference', ''),
            "invoice_date": get_date_time(self.timezone, sales_return_record.get('invoice_date', '')),
            "invoice_quantity": sales_return_record.get('invoice_quantity', 0) or 0,
            "order_quantity": order_quantity,
            "return_quantity": return_quantity,
            "rejected_quantity": rejected_quantity,
            "cancelled_quantity": cancelled_quantity,
            "accepted_quantity": max(0, return_quantity - rejected_quantity),
            "short_quantity": short_quantity,
            "batch_details": {},
            "aux_data": {
                "currency": json_data.get('currency', ''),
                "created_from": sr_json_data.get('source', ''),
                "created_by": sr_json_data.get('created_by', '')                
            },
            "checked_in_user": sales_return_record.get('json_data', {}).get('check_in_user', ''),
            "checked_in_remarks": sales_return_record.get('json_data', {}).get('check_in_remarks', ''),
            "check_in_time": sales_return_record.get('json_data', {}).get('check_in_time', ''),
            "order_json_data": order_json_data,
            "grn_number" : "",
            "grn_reference" : "",
            "grn_date" : "",
            "grn_created_by" : "",
            "grn_total_value": 0,
            "grn_quantity": 0,
            "credit_note_number" : sales_return_record.get('credit_note_number', ''),
        }
        if sr_grn_data:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_number'] = sr_grn_data.get('grn_number', '')
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_reference'] = sr_grn_data.get('grn_reference', '')
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_date'] = sr_grn_data.get('grn_date', '')
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['accepted_quantity'] = sr_grn_data.get('accepted_quantity', 0)
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['rejected_quantity'] = sr_grn_data.get('rejected_quantity', 0)
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['return_quantity'] = sr_grn_data.get('return_quantity', 0)
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['short_quantity'] = sr_grn_data.get('short_quantity', 0)
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_total_value'] = sr_grn_data.get('grn_total_value', 0)
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_created_by'] = sr_grn_data.get('created_by', '')
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_quantity'] = sr_grn_data.get('grn_quantity', 0)

    def get_batch_details(self, sales_return_record: dict = {}, unique_sales_return_key: tuple = (), unique_sr_sku_key: tuple = (), unique_sr_batch_key: tuple = (), sr_grn_batch_data: dict = {}):
        """
        Get the batch details for a sales return record and store it in the final data dictionary.

        Args:
            sales_return_record (dict): The sales return record containing batch details.
            unique_sales_return_key (str): The unique key for the sales return record.
            unique_sr_sku_key (str): The unique key for the SKU in the sales return record.
            unique_sr_batch_key (str): The unique key for the batch in the sales return record.
            grn_batch_data (dict): The GRN batch data for the sales return record.
        """
        if sr_grn_batch_data:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key] = {
                "batch_number": sr_grn_batch_data.get('batch_number', ''),
                "batch_reference": sr_grn_batch_data.get('batch_reference', ''),
                "manufactured_date": sr_grn_batch_data.get('manufactured_date', ''),
                "expiry_date": sr_grn_batch_data.get('expiry_date', ''),
                "mrp": sr_grn_batch_data.get('mrp', ''),
                "unit_price": sr_grn_batch_data.get('unit_price', ''),
                "return_quantity" : sr_grn_batch_data.get('return_quantity', 0),
                "rejected_quantity" : sr_grn_batch_data.get('rejected_quantity', 0),
                "accepted_quantity" : sr_grn_batch_data.get('accepted_quantity', 0),
                "grn_quantity": sr_grn_batch_data.get('grn_quantity', 0),
                "reason": sales_return_record.get('reason', ''),
                "serial_numbers": sr_grn_batch_data.get('serial_numbers', '')
            }
        else:
            serial_numbers = self.return_id_serial_numbers_map.get(sales_return_record.get('return_id', ''), {}).get(sales_return_record.get('id', ''), {})
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['batch_details'][unique_sr_batch_key] = {
                "batch_number": sales_return_record.get('batch_number', ''),
                "batch_reference": sales_return_record.get('batch_reference', ''),
                "manufactured_date": get_date_time(self.timezone, sales_return_record.get('manufactured_date', '')),
                "expiry_date": get_date_time(self.timezone, sales_return_record.get('expiry_date', '')),
                "mrp": sales_return_record.get('mrp', ''),
                "unit_price": sales_return_record.get('unit_price', ''),
                "reason": sales_return_record.get('reason', ''),
                "return_quantity" : sales_return_record.get('return_quantity', 0),
                "rejected_quantity" : sales_return_record.get('rejected_quantity', 0),
                "accepted_quantity": sales_return_record.get('accepted_quantity', 0),
                "grn_quantity": 0,
                "serial_numbers": serial_numbers
            }

    def prepare_pack_details(self, sales_return_record, unique_sales_return_key, unique_sr_sku_key):
        """
        Prepare the pack details for a sales return record
        
        Args :
            sales_return_record (dict): The sales return record containing pack details.
            unique_sales_return_key (str): The unique key for the sales return record.
            unique_sr_sku_key (str): The unique key for the SKU in the sales return record.
        """
        pack_uom = 0
        sku_measurement_type = sales_return_record.get('sku_measurement_type','')
        sku_code = sales_return_record.get('sku_code', '')
        if sales_return_record.get('sr_sku_json', {}):
            pack_uom = sales_return_record.get('sr_sku_json', {}).get('pack_uom_quantity', 0) or 0
        self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['sales_uom_quantity'] = self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['return_quantity']
        if pack_uom != 0:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['base_uom_quantity'] = self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['return_quantity'] * pack_uom
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['pack_uom_quantity'] = pack_uom
        self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['sale_uom'] = self.sku_pack_details.get((sku_code, pack_uom),{}).get('pack_id','') or ''
        self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['uom'] = sku_measurement_type

    def get_final_data_list(self):
        """
        Returns the final data list after processing the sales return data.

        Returns:
            list: The final data list.
        """
        for sales_return_key, sales_return_value in self.final_data_dict.items():
            sr_items_detail = sales_return_value.get('items', {})

            if self.request_data.get('line_level') not in ['true', True]:
                for sr_item_key, sr_items_detail in sr_items_detail.items():
                    sr_items_detail['batch_details'] = list(sr_items_detail['batch_details'].values())

            sales_return_value['items'] = list(sales_return_value['items'].values())
        return list(self.final_data_dict.values())


    def get_sr_item_sku_level_details(self, sales_return_record, unique_sales_return_key):
        """
        formats the sales return data at sku level
        """
        document_type = sales_return_record.get('document_type', '')
        return_id = sales_return_record.get('return_id', '')
        invoice_number = sales_return_record.get('invoice_reference', '')
        order_reference = sales_return_record.get('order_reference', '')
        sku_code = sales_return_record.get('sku_code', '')
        sku_measurement_type = sales_return_record.get('sku_measurement_type','')
        line_reference = sales_return_record.get('line_reference', '')
        batch_key = sales_return_record.get('batch_number', '') or sales_return_record.get('batch_reference', '')

        unique_sr_sku_key = (document_type, return_id, invoice_number, order_reference, sku_code, batch_key, line_reference)

        json_data = sales_return_record.get('json_data', {}) or {}
        sr_json_data = sales_return_record.get('sr_json_data', {}) or {}
        sr_sku_json_data = sales_return_record.get('sr_sku_json', {}) or {}
        return_quantity = sales_return_record.get('return_quantity', 0) or 0
        rejected_quantity = sales_return_record.get('rejected_quantity', 0) or 0
        order_quantity = sales_return_record.get('order_quantity', 0) or 0
        original_accepted_qty = sales_return_record.get('accepted_quantity', 0) or 0
        short_quantity = max(0, return_quantity - rejected_quantity - original_accepted_qty)
        reason = sales_return_record.get('reason', '')

        serial_numbers = self.return_id_serial_numbers_map.get(sales_return_record.get('return_id', ''), {}).get(sales_return_record.get('id', ''), {})
        if not serial_numbers:
            serial_numbers = {"accepted": [], "rejected": []}
        if  unique_sr_sku_key not in self.final_data_dict[unique_sales_return_key]['items']:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key] = {
                "sku_code": sales_return_record.get('sku_code', ''),
                "sku_desc": sales_return_record.get('sku_desc', ''),
                "sku_brand": sales_return_record.get('sku_brand', ''),
                "sku_url": sales_return_record.get('sku_url', ''),
                "weight": json_data.get('weight', ''),
                "line_reference": sales_return_record.get('line_reference', ''),
                "order_line_reference": sr_sku_json_data.get('order_line_reference', ''),
                "taxes" : {
                    "cgst": json_data.get('cgst', 0),
                    "igst": json_data.get('igst', 0),
                    "sgst": json_data.get('sgst', 0),
                    "cess": json_data.get('cess', 0),
                    "cgst_amount": json_data.get('cgst', 0) * (sales_return_record.get('unit_price', 0) / 100),
                    "igst_amount": json_data.get('igst', 0) * (sales_return_record.get('unit_price', 0) / 100),
                    "sgst_amount": json_data.get('sgst', 0) * (sales_return_record.get('unit_price', 0) / 100),
                    "cess_amount": json_data.get('cess', 0) * (sales_return_record.get('unit_price', 0) / 100),
                },
                "order_reference": sales_return_record.get('order_reference', ''),
                "invoice_reference": sales_return_record.get('invoice_reference', ''),
                "invoice_date": get_date_time(self.timezone, sales_return_record.get('invoice_date', '')),
                "invoice_quantity": sales_return_record.get('invoice_quantity', 0) or 0,
                "open_quantity": sales_return_record.get('quantity', 0) or 0,
                "order_quantity": order_quantity,
                "return_quantity": return_quantity,
                "rejected_quantity": rejected_quantity,
                "accepted_quantity": max(0, return_quantity - rejected_quantity),
                "cancelled_quantity": sales_return_record.get('cancelled_quantity', 0) or 0,
                "short_quantity": short_quantity,
                "batch_number": sales_return_record.get('batch_number', ''),
                "batch_reference": sales_return_record.get('batch_reference', ''),
                "manufactured_date": get_date_time(self.timezone, sales_return_record.get('manufactured_date', '')),
                "expiry_date": get_date_time(self.timezone, sales_return_record.get('expiry_date', '')),
                "mrp": sales_return_record.get('mrp', ''),
                "unit_price": sales_return_record.get('unit_price', ''),
                "aux_data": {
                    "currency": json_data.get('currency', ''),
                    "created_from": sr_json_data.get('source', ''),
                    "created_by": sr_json_data.get('created_by', '')
                },
                "order_json_data": sales_return_record.get('order_json_data', {}) or {},
                "grn_quantity": self.sr_batch_id_grn_quantity_map.get(sales_return_record.get('id', ''), 0),
                "serial_numbers": serial_numbers,
                "reason": reason,
                "checked_in_user": sales_return_record.get('json_data', {}).get('check_in_user', ''),
                "checked_in_remarks": sales_return_record.get('json_data', {}).get('check_in_remarks', ''),
                "check_in_time": sales_return_record.get('json_data', {}).get('check_in_time', ''),
            }
        else:
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['invoice_quantity'] += sales_return_record.get('invoice_quantity', 0) or 0
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['return_quantity'] += return_quantity
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['rejected_quantity'] += rejected_quantity
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['accepted_quantity'] += max(0, return_quantity - rejected_quantity)
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['cancelled_quantity'] += sales_return_record.get('cancelled_quantity', 0) or 0
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['open_quantity'] += sales_return_record.get('quantity', 0) or 0
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['short_quantity'] += short_quantity
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['grn_quantity'] += self.sr_batch_id_grn_quantity_map.get(sales_return_record.get('id', ''), 0)
            # calculate taxes
            for tax in self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['taxes']:
                self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['taxes'][tax] += json_data.get(tax, 0)
            # add serial numbers
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['serial_numbers']['accepted'] += serial_numbers.get('accepted', [])
            self.final_data_dict[unique_sales_return_key]['items'][unique_sr_sku_key]['serial_numbers']['rejected'] += serial_numbers.get('rejected', [])

        

def get_sales_return_callback_data(warehouse, return_id):
    """
    Get the sales return callback data for the given return ids.

    Args:
        warehouse (User): The warehouse user.
        return_ids (list): The list of return ids.

    Returns:
        dict: The sales return callback data.
    """
    request_data = {
        "return_id" : return_id
    }
    if isinstance(warehouse, int):
        warehouse = User.objects.get(id=warehouse)
    final_data_dict, errors = SalesReturnDataMixin(warehouse, warehouse, request_data, is_integration=True).get_sales_return_data()
    if errors:
        return {}
    return final_data_dict
