#package imports
from json import loads
import pandas as pd
from copy import deepcopy
from collections import defaultdict

#django imports
from django.db.models import F, Sum
from django.http import JsonResponse
from django.db import transaction
from django.utils import timezone as djano_timezone
from django.core.cache import cache

#wms imports
from wms_base.models import UserProfile, User
from wms_base.wms_utils import init_logger
from wms.celery import app as celery_app

#core operations imports
from core_operations.views.common.main import WMSListView, get_multiple_misc_values
from core_operations.views.services.packing_service import PackingService

#inventory imports
from inventory.models import StockDetail, LocationMaster

#outbound imports
from outbound.models import (
    OrderDetail, SellerOrderSummary, CustomerMaster, Picklist, AuditEntry,
    StagingInfo, OrderShipmentItems, ShipmentInvoiceItems, PickAndPassStrategy
)


log = init_logger('logs/packing_view.log')

class PackingView(WMSListView):
    def get(self, request, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = request.GET
        self.timezone = self.warehouse.userprofile.timezone or "Asia/Calcutta"
        self.customer_reference = self.request_data.get('customer_reference', '')
        self.final_data, self.errors = {}, []
        self.get_packing_details_based_on_request_type()
        if self.errors:
            return JsonResponse({"errors": self.errors}, status=400)

        if self.request_type == 'packlist_label':
            packlist_label_data = self.prepare_packlist_data()
            return JsonResponse(packlist_label_data, status=200)

        return JsonResponse(self.final_data, status=200)

    def put(self, request, *args, **kwargs):
        self.set_user_credientials()
        try:
            self.request_data = loads(request.body)
        except Exception:
            self.request_data = request.POST.get("data") or {}

        if not self.request_data:
            return JsonResponse({"errors": ["Invalid request data"]}, status=400)

        self.errors, self.cache_ids = [], []
        self.final_data = {}
        try:
            self.request_type = self.request_data.get('request_type', 're_packing')
            self.picklist_numbers = self.request_data.get('picklist_numbers')
            self.customer_reference = self.request_data.get('customer_reference')
            self.lpn_numbers = []
            req_lpn_numbers = self.request_data.get('lpn_numbers', [])
            if req_lpn_numbers:
                self.lpn_numbers = req_lpn_numbers.split(',')
            for lpn_number in self.lpn_numbers:
                key = f"stock_update_{lpn_number}"
                cache_status = cache.add(key, "True", timeout=100)
                if not cache_status:
                    self.delete_cache_ids()
                    return JsonResponse({"errors": ["Stock details are being updated!"]}, status=400)
                else:
                    self.cache_ids.append(lpn_number)
            self.update_type = self.request_data.get('update_type', 'update')
        except Exception:
            self.request_type = 're_packing'
        if self.request_type == 'update_stock':
            self.update_stock_with_packing_details()
        elif self.request_type == 're_packing':
            self.update_repacking_details()
        self.delete_cache_ids()
        if self.errors:
            return JsonResponse({"errors": self.errors}, status=400)
        return JsonResponse({"message": "success"}, status=200)

    def get_packing_details_based_on_request_type(self):
        '''
        Get packing details based on tquest type
        '''
        self.request_type = self.request_data.get('request_type', '')
        if self.request_type and self.request_type == 'lpn_update':
            self.get_lpn_details_for_update()
        elif self.request_type and self.request_type == 'dispatch_label':
            self.get_dispatch_details()
        else:
            self.get_default_packing_details()
        
    
    def get_dispatch_details(self):
        '''
        Get dispatch label details
        '''
        self.lpn_number = self.request_data.get('lpn_number')
        self.reference_number = self.request_data.get('reference_number')

        if not self.lpn_number and not self.reference_number:
            self.errors.append("LPN number or reference number is required")
            return

        #dont consider manifest closed lpns if searched with lpn number
        manifest_closed_invoices = []
        if self.request_type == 'dispatch_label' and self.lpn_number and not self.reference_number:
            manifest_closed_invoices = list(OrderShipmentItems.objects.filter(order_shipment__status=3, package_reference=self.lpn_number, order_shipment__user_id=self.warehouse.id).values_list('shipment_invoice__reference_number', flat=True))
        
        sos_filters = {
            'order__user': self.warehouse.id,
            'order_status_flag__in': ['customer_invoices', 'delivery_challans'],
            'invoice_reference': self.reference_number
        }
        if self.lpn_number:
            sos_filters.update({'lpn_number': self.lpn_number})
        
        sos_objs = list(SellerOrderSummary.objects.filter(**sos_filters).values('invoice_reference', 'challan_number', 'updation_date', 'order__customer_identifier__customer_reference', 'order__customer_identifier__route__name', 'order__customer_identifier__route__route_id', 'lpn_number', 'order__customer_identifier__name'))
        if not sos_objs:
            return JsonResponse({"errors": ["No dispatch details found"]}, status=400)
        final_data = {}
        for sos_obj in sos_objs:
            reference_number = sos_obj.get('invoice_reference','')
            invoice_date = sos_obj.get('updation_date', '')
            lpn_number = sos_obj.get('lpn_number', '')
            formatted_invoice_date = invoice_date.strftime('%d/%m/%Y') if invoice_date else ''
            if reference_number in manifest_closed_invoices:
                continue
            unique_key = (reference_number, lpn_number)
            if not final_data.get(unique_key):
                final_data[unique_key] = {
                    'reference_number': reference_number,
                    'lpn_number': lpn_number,
                    'invoice_date': formatted_invoice_date,
                    'customer_reference': sos_obj.get('order__customer_identifier__customer_reference'),
                    'route_name': sos_obj.get('order__customer_identifier__route__name'),
                    'route_id': sos_obj.get('order__customer_identifier__route__route_id'),
                    'customer_name': sos_obj.get('order__customer_identifier__name')
                }
        self.final_data = {'data': list(final_data.values())}
        return self.final_data
        

    #####Get packing label details start#####

    def get_default_packing_details(self):
        '''
        Get default packing details
        '''
        self.picklist_numbers = self.request_data.get('picklist_numbers')
        self.order_reference = self.request_data.get('order_reference')
        self.lpn_number = self.request_data.get('lpn_number')
        if self.picklist_numbers and isinstance(self.picklist_numbers, str) and not self.picklist_numbers.isdigit():
            self.errors.append("Invalid picklist number")
            return

        self.get_packing_details()

    def get_packing_details(self):
        '''
        Get packing details
        '''
        self.packing_details, self.sos_details, self.customer_details, self.packing_details = [], {}, {}, []
        self.get_sos_details()
        self.get_warehouse_details()
        if not self.sos_details:
            self.get_lpn_number_status()
            return

        self.get_lpn_details()
        self.get_audit_status()
        if self.packing_details:
            self.prepare_packing_data()
        else:
            #get lpn number status
            self.get_lpn_number_status()
    

    def get_lpn_number_status(self):

        if self.final_data.get('packing_details'):
            return
        self.final_data['packing_details'] = [{}]
        request_dict = {
            "request_headers": {
                "Warehouse": self.request.headers.get("Warehouse"),
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }
        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        params = {
            "warehouse": self.warehouse.username,
            "lpn_number": self.lpn_number
        }
        lpn_details, packing_service_errors = packing_service_instance.get_lpn_number_details(params=params)
        lpn_status = 'Not Valid'
        if lpn_details:
            usable = lpn_details[0].get('usable', False)
            dropped = lpn_details[0].get('dropped', False)
            blocked = lpn_details[0].get('blocked', False)
            if usable and not dropped and not blocked:
                lpn_status = 'Open'
            else:
                lpn_status = 'Invalid Lpn'

        self.final_data['packing_details'][0] = {
            "lpn_number": self.lpn_number,
            "status": lpn_status
        }
        return

    def prepare_packlist_data(self):
        """
        Prepare the packlist data by iterating through the packing details and
        updating the item details for each pack record.

        Returns:
            dict: The prepared packlist data.
        """
        for pack_record in self.final_data['packing_details']:
            items = pack_record.get('items', [])
            if items:
                item_details = {}
                self.prepare_line_level_pack_data(item_details, items)
                pack_record['items'] = list(item_details.values())
        return self.final_data

    def prepare_line_level_pack_data(self, item_details, items):
        """
        Prepares line-level pack data by iterating through the items and updating the item_details dictionary.

        Args:
            item_details (dict): A dictionary containing the details of each item.
            items (list): A list of items to process.

        Returns:
            None
        """
        for item_record in items:
            transaction_id = item_record.get('transaction_id', '')
            if transaction_id:
                sku_code = item_record.get('sku_code', '')
                sku_details = self.sku_details.get(sku_code, {})
                sku_desc = sku_details.get('sku_desc', '')
                sku_size = sku_details.get('sku_size', '')
                unique_key = (sku_code, sku_desc, sku_size)
                if not item_details.get(unique_key):
                    item_details[unique_key] = {
                        "sku_code": sku_code,
                        "sku_desc": sku_desc,
                        "sku_style": sku_details.get('sku_style', ''),
                        "sku_size": sku_size,
                        "packed_quantity": 0,
                        "inner_lpns": [],
                        "no_of_inner_lpns": 0
                    }
                packed_quantity = item_record.get('packed_quantity', 0) or 0
                item_details[unique_key]['packed_quantity'] += packed_quantity

                #unique id details
                inner_lpn = item_record.get('inner_lpn', '')
                if inner_lpn and inner_lpn not in item_details[unique_key]["inner_lpns"]:
                    item_details[unique_key]["inner_lpns"].append(inner_lpn)
                    item_details[unique_key]["no_of_inner_lpns"] += 1

            items = item_record.get('items', [])
            if items:
                self.prepare_line_level_pack_data(item_details, items)

    def get_sos_details(self):
        '''
        Get seller order summary details
        '''
        self.sos_details, self.sku_details, customer_ids, self.picklist_status, self.lpn_invoice_dict = {}, {}, [], set(), defaultdict(int)
        self.current_zone, self.last_picked_zone, self.storage_type, self.customer_references = '', '', '', set()
        self.is_picked_lpn = False
        exclude_dict = {}
        self.sos_filter = {
            "order__user": self.warehouse.id,
            "order_status_flag": "processed_orders",
        }
        if self.customer_reference:
            self.sos_filter.update({"order__customer_identifier__customer_reference": self.customer_reference})
        if self.request_type == 'lpn_detail_view':
            self.sos_filter.pop('order_status_flag', None)
            self.sos_filter['order_status_flag__in'] = ['processed_orders', 'customer_invoices', 'delivery_challans']
        if self.picklist_numbers:
            self.sos_filter.update({"picklist__picklist_number__in": self.picklist_numbers.split(',')})
        if hasattr(self, 'order_reference') and self.order_reference:
            self.sos_filter.update({"order__order_reference": self.order_reference})
        if self.request_type in ['packlist_label', 'shipment_label']:
            self.sos_filter.pop('order_status_flag', None)
        if self.request_type == 'shipment_label':
            exclude_dict.update({"order_status_flag": "cancelled"})
        if self.lpn_number:
            if not self.picklist_numbers:
                stock_filters = {
                    "sku__user": self.warehouse.id,
                    "lpn_number": self.lpn_number,
                    "receipt_type__in": ['so_picking', 'so_dispense']
                }
                if self.request_type == 'lpn_detail_view':
                    stock_filters.update({"quantity__gt": 0})
                if hasattr(self, 'order_reference') and self.order_reference:
                    stock_filters['grn_number'] = self.order_reference
                # Fetching picklist number from the stock details for the given LPN number
                stock_lpn = StockDetail.objects.filter(**stock_filters).order_by('-updation_date').values('transact_number', 'receipt_number', 'json_data__picked_lpn', 'location__zone__zone', 'location__zone__storage_type')
                stock_picklist_numbers = set()
                stock_receipt_numbers = set()
                if stock_lpn:
                    for lpn in stock_lpn:
                        stock_picklist_numbers.add(lpn.get('transact_number'))
                        stock_receipt_numbers.add(lpn.get('receipt_number'))
                        if lpn.get('json_data__picked_lpn'):
                            self.is_picked_lpn = True
                        self.current_zone, self.storage_type = lpn.get('location__zone__zone'), lpn.get('location__zone__storage_type')
                    if self.request_type == 'lpn_detail_view':
                        self.sos_filter.update({"id__in": stock_receipt_numbers})
                    self.sos_filter.update({"picklist__picklist_number__in": stock_picklist_numbers})
            
            if not self.sos_filter.get('picklist__picklist_number__in'):
                # check for pick and pass scan if scanned and in open status return picking in progress
                if self.request_type == 'lpn_detail_view':
                    is_pick_and_pass_available = self.get_pick_and_pass_details()
                    if is_pick_and_pass_available:
                        return
                self.sos_filter.update({"lpn_number": self.lpn_number})
        
        
        if all(key not in self.sos_filter for key in ['lpn_number', 'picklist__picklist_number', 'order__order_reference', 'picklist__picklist_number__in', 'order__customer_identifier__customer_reference']):
            return
        
        manifest_closed_invoices = []
        if self.request_type == 'lpn_detail_view':
            manifest_closed_invoices = list(OrderShipmentItems.objects.filter(order_shipment__status=3, package_reference=self.lpn_number, order_shipment__user_id=self.warehouse.id).values_list('shipment_invoice__reference_number', flat=True))
            if manifest_closed_invoices:
                exclude_dict.update({
                    'invoice_reference__in': manifest_closed_invoices
                })

        sos_details_list = list(SellerOrderSummary.objects.filter(**self.sos_filter).exclude(**exclude_dict).order_by('creation_date').values(
            'quantity', 'invoice_reference', 'lpn_number', 'challan_number', transaction_id = F('id'), order_reference = F('order__order_reference'), sku_code = F('order__sku__sku_code'), sku_desc = F('order__sku__sku_desc'),
            picklist_number = F('picklist__picklist_number'), customer_id=F('order__customer_id'), customer_name=F('order__customer_name'), customer_reference=F('order__customer_identifier__customer_reference'), order_type=F('order__order_type'), sku_style = F('order__sku__style_name'), zone = F('picklist__location__zone__zone'),
            sku_size=F('order__sku__sku_size'), customer_po_num = F('order__customer_po_num'), picklist_status = F('picklist__status'),location_id = F('picklist__location_id'), sub_zone=F('picklist__location__sub_zone__zone'), invoice_date=F('updation_date')))
        if not sos_details_list:
            return

        self.last_picked_zone = sos_details_list[-1].get('zone')
        
        location_ids = set()
        location_transaction_map = defaultdict(set)
        picklist_numbers = []
        for sos_dict in sos_details_list:
            reference_number = sos_dict.get('invoice_reference', '')
            if reference_number and reference_number in manifest_closed_invoices:
                continue
            location_ids.add(sos_dict.get('location_id'))
            self.sos_details[sos_dict['transaction_id']] = sos_dict
            location_transaction_map[sos_dict.get('location_id')].add(sos_dict['transaction_id'])
            customer_id = sos_dict.get('customer_id', '')
            self.customer_references.add(sos_dict.get('customer_reference'))
            if customer_id and customer_id not in customer_ids:
                customer_ids.append(customer_id)
            self.sku_details[sos_dict['sku_code']] = {
                "sku_code": sos_dict['sku_code'],
                "sku_desc": sos_dict['sku_desc'],
                "sku_style": sos_dict['sku_style'],
                "sku_size": sos_dict['sku_size']
            }
            if sos_dict.get('invoice_reference'):
                self.lpn_invoice_dict[sos_dict['lpn_number']] = sos_dict['invoice_reference']
            elif sos_dict.get('challan_number'):
                self.lpn_invoice_dict[sos_dict['lpn_number']] = sos_dict['challan_number']
            self.picklist_numbers = sos_dict.get('picklist_number', '')
            picklist_numbers.append(sos_dict.get('picklist_number', ''))
            # self.picklist_numbers = sos_dict.get('picklist_number', '')
        self.picklist_numbers = list(set(picklist_numbers))
        zones_data = list(LocationMaster.objects.filter(zone__user=self.warehouse.id, id__in=location_ids).values('id', 'zone__zone', 'sub_zone__zone'))
        location_zones_dict = {}
        for zone_data in zones_data:
            location_zones_dict[zone_data['id']] = zone_data
        for location_id, transaction_ids in location_transaction_map.items():
            zone_data = location_zones_dict.get(location_id, {})
            for transaction_id in transaction_ids:
                self.sos_details[transaction_id]['zone'] = zone_data.get('zone__zone', '')
                self.sos_details[transaction_id]['sub_zone'] = zone_data.get('sub_zone__zone', '')

        self.picklist_status = set(Picklist.objects.filter(picklist_number__in = self.picklist_numbers, user_id=self.warehouse.id).values_list('status', flat=True))

        self.get_staging_info_dropped_lpn()
        if customer_ids:
            self.get_customer_details(customer_ids)
    
    def get_pick_and_pass_details(self):
        """
        return if an entry exists in pick and pass with open status
        """
        pick_and_pass_objs = PickAndPassStrategy.objects.filter(lpn_number=self.lpn_number, warehouse_id=self.warehouse.id, status="open")
        if pick_and_pass_objs.exists():
            self.final_data['packing_details'] = [{
                "lpn_number": self.lpn_number,
                "status": 'picking_in_progress',
                "current_zone": self.last_picked_zone
            }]
            return True
        return False

    def get_staging_info_dropped_lpn(self):
        """
        Retrieves the dropped LPNs (carton numbers) from the StagingInfo table.

        Returns:
            list: A list of dropped LPNs (carton numbers).
        """
        self.dropped_lpns = list(StagingInfo.objects.filter(user_id=self.warehouse.id, \
            picklist_number__in=self.picklist_numbers, status=0).values_list('carton_no', flat=True))

    def get_customer_details(self, customer_ids):
        '''
        Get customer details
        '''
        customer_details_list = CustomerMaster.objects.filter(user=self.warehouse.id, customer_id__in=customer_ids).values('id',
            'name', 'last_name', 'customer_id', 'customer_code', 'customer_reference', 'address', 'shipping_address', 'city', 'state', 'country',
            'phone_number', 'email_id', 'tin_number', 'pan_number', routeid=F('route__route_id'), route_name=F('route__name'), route_json_data = F('route__json_data'))
        for customer_dict in customer_details_list:
            route_json_data = customer_dict.get('route_json_data', {}) or {}
            customer_dict['storage_area'] = route_json_data.get('attr_storage_area', '')
            uniq_key = customer_dict.get('customer_reference', '')
            self.customer_details[uniq_key] = customer_dict

    def get_warehouse_details(self):
        '''
        Get warehouse details
        '''
        warehouse_details = list(UserProfile.objects.filter(user_id=self.warehouse.id).values(
            'user_id', 'cin_number', 'location', 'city','state', 'country', 'wh_phone_number', 'pin_code','gst_number','wh_address',
            'address', 'pan_number', username=F('user__username'), company_name=F('company__company_name')))
        if warehouse_details:
            self.final_data['warehouse_details'] = warehouse_details[0]

    def get_lpn_details(self):
        '''
        Get lpn details
        '''
        invoice = False
        if self.request_type == 'lpn_detail_view':
            invoice = True
        request_dict = {
            "request_headers": {
                "Warehouse": self.request.headers.get("Warehouse"),
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }
        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        if isinstance(self.picklist_numbers, list):
            self.picklist_numbers = ','.join(map(str, self.picklist_numbers))
        params = {
            "warehouse": self.warehouse.username,
            "transaction_number": self.picklist_numbers,
            "transaction_type": 'so_packing',
            "invoice": invoice,
            "lpn_number": self.lpn_number
        }
        if self.request_type in ['packlist_label', 'shipment_label']:
            params['lpn_number'] = ''
            params['is_summary'] = True
        if self.request_type in ['lpn_detail_view']:
            params['is_summary'] = True
        self.packing_details, packing_service_errors = packing_service_instance.get_packing_details(params)
        if packing_service_errors:
            self.errors.extend(packing_service_errors)
    
    def get_audit_status(self):
        audit_status = AuditEntry.objects.filter(warehouse_id=self.warehouse.id, audit_reference=self.lpn_number, status__in=[1,2]).values_list('status', flat=True).first() or 0
        if audit_status == 1:
            self.audit_status = 'QC Pending'
        elif audit_status == 2:
            self.audit_status = 'Pending Resolution'
        else:
            self.audit_status = False

    def prepare_packing_data(self):
        '''
        Prepares the packing data by iterating through the packing details and generating the final packing details.

        Returns:
            None
        '''
        self.final_packing_details = {}
        self.released_lpns = {}
        for transaction_reference_details in self.packing_details:
            lpn_details = transaction_reference_details.get('packed_details', [])
            for lpn_record in lpn_details:
                lpn_number = lpn_record.get('lpn_number', '')
                self.released_lpns[lpn_number] = lpn_record.get('usable', False)
                if self.lpn_number and lpn_number != self.lpn_number:
                    continue
                items = lpn_record.get('items', []) or []
                if items:
                    self.get_packing_item_level_details(items, items, lpn_number, '')

        self.final_data['packing_details'] = []
        for _, data_dict in self.final_packing_details.items():
            data_dict['no_of_items'] = len(data_dict.get('sku_codes', []))
            data_dict['zones'] = ' / '.join(data_dict.get('zones_list', []))
            self.final_data['packing_details'].append(data_dict)

    def get_packing_item_level_details(self, header_items, items, lpn_number, inner_lpn):
        '''
        Retrieves the item-level details for packing.

        Args:
            header_items (list): The list of header items.
            items (list): The list of items to retrieve details for.
            lpn_number (str): The LPN (License Plate Number) associated with the items.

        Returns:
            None
        '''
        #add subzone for each item
        for item in header_items:
            transaction_id = item.get('transaction_id', '')
            if transaction_id and self.sos_details.get(transaction_id, {}):
                item['sub_zone'] = self.sos_details[transaction_id].get('sub_zone', '')

        for item_record in items:
            transaction_id = item_record.get('transaction_id', '')
            item_record["inner_lpn"] = inner_lpn
            if transaction_id:
                sos_record = self.sos_details.get(transaction_id, {})
                if not sos_record:
                    continue
                order_reference = sos_record.get('order_reference', '')
                zone = sos_record.get('zone', '')
                customer_id = sos_record.get('customer_id', '')
                customer_name = sos_record.get('customer_name','')
                customer_reference = sos_record.get('customer_reference', '')
                customer_details = self.customer_details.get(customer_reference, {})
                if self.lpn_number and lpn_number != self.lpn_number:
                    continue
                invoice_reference = sos_record.get('invoice_reference', '')
                sku_code = item_record.get('sku_code', '')
                packed_quantity = item_record.get('packed_quantity', 0) or 0
                invoice_date = sos_record.get('invoice_date', '')
                invoice_date = invoice_date.strftime('%d/%m/%Y')
                if not invoice_reference:
                    invoice_date = ''
                transaction_id = item_record.get('transaction_id', '')
                if transaction_id and self.sos_details.get(transaction_id, {}):
                    item_record['sub_zone'] = self.sos_details[transaction_id].get('sub_zone', '')

                packing_id = item_record.get('packing_id', '')
                unique_key = (lpn_number, order_reference, packing_id)
                if inner_lpn:
                    unique_key = (lpn_number, order_reference)
                if not self.final_packing_details.get(unique_key):
                    status, current_zone = self.get_lpn_status()
                    self.final_packing_details[unique_key] = {
                        "lpn_number": lpn_number,
                        "order_reference": order_reference,
                        "picklist_number": sos_record.get('picklist_number', ''),
                        "customer_po_number": sos_record.get('customer_po_num', ''),
                        "order_type": sos_record.get('order_type', ''),
                        "items": [item_record],
                        "sku_codes": [sku_code],
                        "total_quantity": packed_quantity,
                        "customer_details": customer_details,
                        "status": status,
                        'invoice_number': invoice_reference,
                        'invoice_date': invoice_date,
                        'current_zone': current_zone,
                        'customer_references': list(self.customer_references)
                    }
                    if zone:
                        self.final_packing_details[unique_key]['zones_list'] = [zone]
                else:
                    if sku_code not in self.final_packing_details[unique_key]['sku_codes']:
                        self.final_packing_details[unique_key]['sku_codes'].append(sku_code)
                    if zone not in self.final_packing_details.get(unique_key, {}).get('zones_list'):
                        self.final_packing_details[unique_key]['zones_list'].append(zone)
                    self.final_packing_details[unique_key]['total_quantity'] += packed_quantity
                    self.final_packing_details[unique_key]['items'].append(item_record)
                    
            inner_items = item_record.get('items', [])
            inner_lpn_number = item_record.get('lpn_number', '')
            if items:
                self.get_packing_item_level_details(header_items, inner_items, lpn_number, inner_lpn_number)

    #####Get packing label details end#####

    #####Get packing update LPN details start#####
    
    def delete_cache_ids(self):
        try:
            for cache_id in self.cache_ids:
                key = f"stock_update_{cache_id}"
                cache.delete(key)
        except Exception:
             pass


    def get_lpn_details_for_update(self):
        '''
        Get LPN details for fetch the packing details
        '''
        self.get_packing_details_with_lpn_number()
        if self.errors:
            return self.errors

        self.get_and_prepare_packing_details()

    def get_packing_details_with_lpn_number(self):
        '''
        Get lpn details from packing service
        '''
        self.lpn_number = self.request_data.get('lpn_number')
        if not self.lpn_number:
            self.errors.append("LPN number is required")
            return
        request_dict = {
            "request_headers": {
                "Warehouse": self.request.headers.get("Warehouse"),
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }
        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        params = {
            "warehouse": self.warehouse.username,
            "lpn_number": self.lpn_number,
            "transaction_type": "so_packing",
            "status": "closed"
        }
        self.packing_details, packing_service_errors = packing_service_instance.get_packing_details(params)
        if packing_service_errors:
            self.errors.extend(packing_service_errors)

    def get_and_prepare_packing_details(self):
        '''
        Get and prepare packing details
        '''
        self.picklist_number, self.sos_ids, self.lpn_details = '', [], {}
        if self.packing_details:
            for transaction_record in self.packing_details:
                self.picklist_number = transaction_record.get('transaction_number', '')
                for pack_record in transaction_record.get('packed_details', []):
                    items = pack_record.get('items', [])
                    if items:
                        pack_record['packed_quantity'] = 0
                        self.validate_pack_item_details(items, pack_record)
                    package_reference = pack_record.get('lpn_number')
                    if package_reference == self.lpn_number:
                        self.lpn_details = pack_record
                if self.picklist_number and self.sos_ids and self.lpn_details:
                    self.get_and_prepare_picklist_details()
                else:
                    self.errors.append(f'LPN {self.lpn_number} not found')
        else:
            self.errors.append(f'LPN {self.lpn_number} not found')

    def validate_pack_item_details(self, items, lpn_record):
        '''
        Inner lpn detail details
        '''
        for item in items:
            transaction_id = item.get('transaction_id', '')
            quantity = item.get('packed_quantity', 0)
            if item.get('json_data', {}) != None:
                pack_uom_quantity = item.get('json_data', {}).get('pack_uom_quantity', 1 )
                quantity = quantity / pack_uom_quantity
            item['packed_quantity'] = quantity
            self.sos_ids.append(transaction_id)

            inner_items = item.get('items', [])
            if inner_items:
                item['packed_quantity'] = 0
                self.validate_pack_item_details(inner_items, item)

            lpn_record['packed_quantity'] += item['packed_quantity']
            package_reference = item.get('lpn_number')
            if package_reference == self.lpn_number:
                self.lpn_details = item

    def get_and_prepare_picklist_details(self):
        '''
        Get and prepare picklist details
        '''
        order_reference, picked_qty_details = '', {}
        sos_objects = list(SellerOrderSummary.objects.filter(order__user=self.warehouse.id, picklist__picklist_number=self.picklist_number, order_status_flag='processed_orders').values(
            'id', 'quantity', 'picklist__picklist_number', 'order__order_reference'))
        for sos_record in sos_objects:
            order_reference_ = sos_record.get('order__order_reference')
            picklist_number = sos_record.get('picklist__picklist_number')
            if sos_record['id'] in self.sos_ids:
                order_reference = order_reference_
            quantity = sos_record.get('quantity', 0) or 0
            unique_key = (str(picklist_number), order_reference_)
            if not picked_qty_details.get(unique_key):
                picked_qty_details[unique_key] = 0
            picked_qty_details[unique_key] += quantity

        total_order_qty = 0
        order_objs = list(OrderDetail.objects.filter(user=self.warehouse.id, order_reference=order_reference).values('order_reference').annotate(total_qty=Sum('original_quantity')))
        if order_objs:
            total_order_qty = order_objs[0].get('total_qty', 0)

        self.final_data = {
            "order_reference": order_reference,
            "picklist_number": self.picklist_number,
            "total_order_quantity": total_order_qty,
            "total_picklist_quantity": picked_qty_details.get((self.picklist_number, order_reference), 0),
            "packing_details": self.lpn_details,
            "packed_quantity": self.lpn_details.get('packed_quantity', 0)
        }

    #####Get packing update LPN details end#####

    def update_stock_with_packing_details(self):
        '''
        Update stock details with packing details
        '''
        self.customer_details = {}
        self.primary_sorted_lpns = set()
        self.lpn_number = ''

        misc_types = ['pigeon_hole_sorting']
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        if not self.request_data.get('picklist_numbers'):
            self.lpn_number = self.request_data.get('lpn_details').get('packed')[0]
        self.get_lpn_details()
        if not self.request_data.get('picklist_numbers') and self.packing_details:
            self.lpn_number = ''
            self.picklist_numbers = self.get_transaction_numbers_from_packing_details()
        self.get_sos_details()
        self.sos_ids = list(self.sos_details.keys())
        if self.sos_ids:
            self.get_stock_details()
            if self.stock_df.empty:
                self.errors.append("No stock details found")
                return
            if self.update_type in ["delete", "update"] and self.lpn_numbers:
                self.update_lpn_number_stocks()

        self.new_stock_objects = []
        self.new_stock_lpn_quantity_map = {}
        if self.packing_details:
            self.pack_transaction_details()
            self.validate_and_prepare_stock_data()
            self.update_picked_lpns_in_staging_info()
            self.update_sorted_lpn_in_staging_info()
        self.create_and_update_stocks()
    

    def get_transaction_numbers_from_packing_details(self):
        '''
        Get transaction numbers from packing details
        '''
        self.transaction_numbers = []
        for transaction_reference_details in self.packing_details:
            self.transaction_numbers.append(transaction_reference_details.get('transaction_number'))
        
        return ",".join(self.transaction_numbers)

    def get_stock_details(self):
        '''
        Get stock details
        '''
        stock_objects = StockDetail.objects.filter(sku__user=self.warehouse.id, receipt_number__in=self.sos_ids, receipt_type__in=['so_picking', 'so_dispense'], quantity__gt=0)
        stock_data = [(obj.id, obj.receipt_number, obj.receipt_type,
                   obj.sku_id, obj.quantity, obj.lpn_number)
                  for obj in stock_objects]
        self.stock_df = pd.DataFrame(stock_data, columns=['id', 'receipt_number', 'receipt_type',
                                                       'sku_id', 'quantity', 'lpn_number'])
        if hasattr(self, 'stock_df'):  # Check if self.stock_df exists
            obj_dict = {obj.id: obj for obj in stock_objects}  # Create object dictionary
            self.stock_df['object'] = self.stock_df['id'].apply(lambda x: obj_dict.get(x))

        self.stock_df_copy = self.stock_df.copy()

       
    def update_lpn_number_stocks(self):
        '''
        Lpn stock deletion for update/delete packing
        '''
        for lpn_number in self.lpn_numbers:
            for index, stock_record in self.stock_df.loc[(self.stock_df['lpn_number'] == lpn_number )].iterrows():
                stock_quantity = stock_record['quantity']
                if stock_quantity:
                    if stock_record['object'].json_data.get('primary_sorting_allowed', False):
                        self.primary_sorted_lpns.add((stock_record['lpn_number'], stock_record['receipt_number']))
                    for index, inner_stock_record in self.stock_df.loc[(self.stock_df['receipt_number'] == stock_record['receipt_number']) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense']) & (self.stock_df['lpn_number'].isin([None, ''])))].iterrows():
                        inner_stock_record['object'].quantity += stock_quantity
                        self.stock_df.loc[(self.stock_df['id'] == inner_stock_record['id']), 'quantity'] += stock_quantity
                        self.stock_df.loc[(self.stock_df['id'] == inner_stock_record['id']), 'object'] = inner_stock_record['object']
                        break
                    else:
                        stock_record['object'].lpn_number = None
                        self.stock_df.loc[(self.stock_df['id'] == stock_record['id']), 'object'] = stock_record['object']
                        self.stock_df.loc[(self.stock_df['id'] == stock_record['id']), 'lpn_number'] = None

                if stock_record['object'].lpn_number:
                    stock_record['object'].quantity = 0
                    self.stock_df.loc[(self.stock_df['id'] == stock_record['id']), 'quantity'] = 0
                    self.stock_df.loc[(self.stock_df['id'] == stock_record['id']), 'object'] = stock_record['object']

    def pack_transaction_details(self):
        '''
        Get transaction details from packing details
        '''
        self.transaction_details, self.inner_carton_details = {}, {}

        for transaction_reference_details in self.packing_details:
            lpn_details = transaction_reference_details.get('packed_details', [])
            for lpn_record in lpn_details:
                lpn_number = lpn_record.get('lpn_number', '')
                packed_quantity = lpn_record.get('packed_quantity', 0) or 0
                items = lpn_record.get('items', []) or []
                self.get_items_detils(items, lpn_number)
                transaction_id = lpn_record.get('transaction_id', '')
                if transaction_id:
                    unique_key = (lpn_number, transaction_id)
                    if not self.transaction_details.get(unique_key):
                        self.transaction_details[unique_key] = packed_quantity
                    else:
                        self.transaction_details[unique_key] += packed_quantity

    def get_items_detils(self, items, lpn_number):
        '''
        Get items details
        '''
        if items:
            self.pack_inner_carton_transaction_details(items, lpn_number)

    def prepare_unique_transaction_details(self, transaction_id, lpn_number, packed_quantity):
        '''
        Prepare unique transaction details
        '''
        unique_key = (lpn_number, transaction_id)
        if not self.transaction_details.get(unique_key):
            self.transaction_details[unique_key] = packed_quantity
        else:
            self.transaction_details[unique_key] += packed_quantity

    def pack_inner_carton_transaction_details(self, items, lpn_number):
        '''
        Get item level pack details from packing service
        '''
        for item_record in items:
            inner_lpn_number = item_record.get('lpn_number', '')
            packed_quantity = item_record.get('packed_quantity', 0) or 0
            if inner_lpn_number:
                if not self.inner_carton_details.get(lpn_number):
                    self.inner_carton_details[lpn_number] = [inner_lpn_number]
                else:
                    self.inner_carton_details[lpn_number].append(inner_lpn_number)
                items = item_record.get('items', []) or []
                if items:
                    self.pack_inner_carton_transaction_details(items, lpn_number)

            transaction_id = item_record.get('transaction_id', '')
            if transaction_id:
                self.prepare_unique_transaction_details(transaction_id, lpn_number, packed_quantity)

    def update_inner_lpn_numbers_with_outer_lpn(self, inner_carton_numbers, lpn_number, transaction_id, packed_qty):
        '''
        Update inner carton transaction details with outer lpn
        '''
        if inner_carton_numbers:
            for inner_lpn_number in inner_carton_numbers:
                for index, stock_record in self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense'])) & (self.stock_df['lpn_number'] == inner_lpn_number)].iterrows():
                    for index, lpn_stock_record in self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense'])) & (self.stock_df['lpn_number'] == lpn_number)].iterrows():
                        lpn_stock_record['object'].quantity += stock_record.quantity
                        lpn_stock_record['object'].lpn_number = lpn_number
                        self.stock_df.loc[self.stock_df['id'] == lpn_stock_record['id'], 'object'] = lpn_stock_record['object']
                        self.stock_df.loc[self.stock_df['id'] == lpn_stock_record['id'], 'lpn_number'] = lpn_number
                        self.stock_df.loc[self.stock_df['id'] == lpn_stock_record['id'], 'quantity'] = lpn_stock_record['object'].quantity
                        packed_qty -= stock_record.quantity
                        stock_record['object'].quantity = 0
                        self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'object'] = stock_record['object']
                        self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'quantity'] = stock_record['object'].quantity
                        break
                    else:
                        stock_record['object'].lpn_number = lpn_number
                        self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'object'] = stock_record['object']
                        self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'lpn_number'] = lpn_number
                        packed_qty -= stock_record.quantity
        return packed_qty

    def validate_update_stock_with_packing_details(self, transaction_id, lpn_number, packed_qty):
        '''
        Update stock with packing details
        '''
        for index, stock_record in self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense']))].iterrows():
            if stock_record.lpn_number:
                continue
            stock_qty = stock_record.quantity
            if stock_qty == packed_qty:
                availble_carton, packed_qty = self.update_lpn_sku_unique_stock_details(transaction_id, lpn_number, packed_qty)
                if not availble_carton:
                    stock_record['object'].lpn_number = lpn_number
                    self.stock_df.loc[index, 'lpn_number'] = lpn_number
                    if lpn_number in self.request_data.get('lpn_details', {}).get('packed', []):
                        stock_record['object'].json_data.pop('picked_lpn', '')
                    packed_qty = 0
            else:
                inner_carton_sku, packed_qty = self.update_lpn_sku_unique_stock_details(transaction_id, lpn_number, packed_qty)
                if not inner_carton_sku:
                    new_stock_dict = deepcopy(stock_record['object'].__dict__)
                    del new_stock_dict['id']
                    del new_stock_dict['_state']
                    new_stock_dict['quantity'] = packed_qty
                    new_stock_dict['lpn_number'] = lpn_number
                    if lpn_number in self.request_data.get('lpn_details', {}).get('packed', []): #if repacking happened then remove the flag picked_lpn
                        new_stock_dict['json_data'].pop('picked_lpn', '')
                    stock_record['object'].quantity -= packed_qty
                    self.stock_df.loc[index, 'quantity'] -= packed_qty
                    self.new_stock_lpn_quantity_map[lpn_number] = packed_qty
                    packed_qty = 0
                    self.new_stock_objects.append(StockDetail(**new_stock_dict))

            self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'object'] = stock_record['object']
            if packed_qty == 0:
                break

    def update_lpn_sku_unique_stock_details(self, transaction_id, lpn_number, packed_qty):
        '''
        Update unique stock details with lpn number
        '''
        availble_carton = False
        for index, lpn_stock in self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense']) & (self.stock_df['lpn_number'] == lpn_number))].iterrows():
            availble_carton = True
            lpn_stock['object'].quantity += packed_qty
            lpn_stock['object'].lpn_number = lpn_number
            if lpn_number in self.request_data.get('lpn_details', {}).get('packed', []):
                lpn_stock['object'].json_data.pop('picked_lpn', '')
            self.stock_df.loc[self.stock_df['id'] == lpn_stock['id'], 'object'] = lpn_stock['object']
            self.stock_df.loc[self.stock_df['id'] == lpn_stock['id'], 'lpn_number'] = lpn_number
            self.stock_df.loc[self.stock_df['id'] == lpn_stock['id'], 'quantity'] += packed_qty
            packed_qty = 0
            break
        return availble_carton, packed_qty

    def validate_and_prepare_stock_data(self):
        '''
        Validate and prepare stock detail records
        '''
        for unique_key, packed_qty in self.transaction_details.items():
            lpn_number, transaction_id = unique_key
            inner_carton_numbers = self.inner_carton_details.get(lpn_number, [])
            packed_qty = self.stock_with_lpn_number_availble(transaction_id, lpn_number, packed_qty)
            if not packed_qty:
                continue

            packed_qty = self.update_inner_lpn_numbers_with_outer_lpn(inner_carton_numbers, lpn_number, transaction_id, packed_qty)
            if packed_qty:
                self.validate_update_stock_with_packing_details(transaction_id, lpn_number, packed_qty)

    def stock_with_lpn_number_availble(self, transaction_id, lpn_number, packed_qty):
        reduce_stock_qty, inc_stock_qty = 0, 0
        for index, stock_record in self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense'])) & (self.stock_df['lpn_number'] == lpn_number )].iterrows():
            stock_qty = stock_record.quantity
            if stock_qty == packed_qty:
                packed_qty = 0
            elif stock_qty < packed_qty:
                updated_qty = packed_qty - stock_qty
                reduce_stock_qty += updated_qty
                stock_record['object'].quantity = packed_qty
                self.stock_df.loc[index, 'quantity'] = packed_qty
                packed_qty = 0
            else:
                updated_qty = stock_qty - packed_qty
                inc_stock_qty += updated_qty
                stock_record['object'].quantity = packed_qty
                self.stock_df.loc[index, 'quantity'] = packed_qty
                packed_qty = 0

        if reduce_stock_qty or inc_stock_qty:
            for index, stock_record in self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense'])) & (self.stock_df['lpn_number'].isnull())].iterrows():
                if reduce_stock_qty:
                    stock_record['object'].quantity -= reduce_stock_qty
                    self.stock_df.loc[index, 'quantity'] -= reduce_stock_qty
                elif inc_stock_qty:
                    stock_record['object'].quantity += inc_stock_qty
                    self.stock_df.loc[index, 'quantity'] += inc_stock_qty
        return packed_qty

    def create_and_update_stocks(self):
        '''
        Create or Update stock details
        '''
        with transaction.atomic('default'):
            delete_stock_ids = []
            stock_df_list = self.stock_df['object'].tolist()
            for stock_obj in stock_df_list:
                if stock_obj.quantity == 0 and stock_obj.lpn_number:
                    delete_stock_ids.append(stock_obj.id)
            if self.stock_df['object'].tolist():
                self.get_updated_stock_df()
                if self.updated_stock:
                    StockDetail.objects.bulk_update_with_rounding(self.updated_stock, ['lpn_number', 'quantity', 'json_data'])
            if delete_stock_ids:
                StockDetail.objects.filter(id__in=delete_stock_ids).delete()

            if self.new_stock_objects:
                StockDetail.objects.bulk_create_with_rounding(self.new_stock_objects)
    

    def get_updated_stock_df(self):
        self.updated_stock = []
        for index, row in self.stock_df.iterrows():
            if self.stock_df_copy.loc[index, 'quantity'] != row['object'].quantity or self.stock_df_copy.loc[index, 'lpn_number'] != row['object'].lpn_number:
                self.updated_stock.append(row['object'])

    
    def update_repacking_details(self):
        '''
        Update repacking details
        '''
        self.fetch_unique_request_data()
        if self.errors:
            return
        self.fetch_unique_sos_ids()
        self.prepare_and_update_repacking_data()
        if self.errors:
            return

    def fetch_unique_request_data(self):
        '''
        Fetch unique request data
        '''
        self.order_references, self.sku_codes, self.source_lpns, self.destination_lpns = set(), set(), set(), set()
        for request_data in self.request_data:
            self.order_references.add(request_data.get('order_reference', ''))
            self.sku_codes.add(request_data.get('sku_code', ''))
            self.source_lpns.add(request_data.get('source_lpn', ''))
            self.destination_lpns.add(request_data.get('destination_lpn', ''))
        
        self.order_references = list(self.order_references)
        self.sku_codes = list(self.sku_codes)
        self.source_lpns = list(self.source_lpns)
        self.destination_lpns = list(self.destination_lpns)
        if not self.order_references or not self.sku_codes:
            self.errors.append("No order references or SKU codes provided")
    
    def fetch_unique_sos_ids(self):
        '''
        Fetch unique sos ids
        '''
        self.sos_data_dict = dict(SellerOrderSummary.objects.filter(order__user=self.warehouse.id, order__order_reference__in=self.order_references, order__sku__sku_code__in=self.sku_codes, order_status_flag='processed_orders').values_list('id','picklist__picklist_number'))
        self.sos_ids = list(self.sos_data_dict.keys())

    def prepare_and_update_repacking_data(self):
        '''
        Prepare and update repacking data
        '''
        self.location_dict = {}
        self.zone_dict = {}
        self.stock_objects = StockDetail.objects.filter(sku__user=self.warehouse.id, receipt_number__in=self.sos_ids, receipt_type__in=['so_picking', 'so_dispense'],status=1)
        self.stock_detail_df = pd.DataFrame(self.stock_objects.values('id', 'receipt_number', 'receipt_type', 'quantity', 'lpn_number', 'location_id', 'grn_number', sku_code=F('sku__sku_code'),
                                batch_number=F('batch_detail__batch_no'),expiry_date=F('batch_detail__expiry_date'),manufactured_date=F('batch_detail__manufactured_date'),batch_reference=F('batch_detail__batch_reference'),location_name=F('location__location'),zone_name=F('location__zone_id'), picklist_number=F('transact_number')))
        self.stock_detail_df = self.stock_detail_df.where(self.stock_detail_df.notna(), '')
        if self.stock_detail_df.empty:
            self.errors.append("No stock details available for the provided LPN numbers and SKU codes")
            return
        
        for obj in self.stock_objects:
            self.stock_detail_df.loc[self.stock_detail_df['id'] == obj.id, 'object'] = obj

        self.existing_stock_df = deepcopy(self.stock_detail_df)
        self.existing_stock_df = self.stock_detail_df[self.stock_detail_df['lpn_number'].isin(self.destination_lpns)]
        self.stock_detail_df = self.stock_detail_df[self.stock_detail_df['lpn_number'].isin(self.source_lpns)]
        self.stock_objects = list(self.stock_objects)
        location_list = list(LocationMaster.objects.filter(zone__user=self.warehouse.id,status=1).values('location','id','zone_id','zone__zone'))
    
        for loc in location_list:
            self.location_dict[loc.get('location')] = loc.get('id')
            self.zone_dict[loc.get('zone__zone')] = loc.get('zone_id')

        #update stock with repacking details
        header_level_data = self.update_stock_with_repacking_details()
        if self.errors:
            return

        if header_level_data:
            #update packing service with repacking details
            extra_params = {
                "headers": {
                    "Warehouse": self.request.headers.get("Warehouse"),
                    "Authorization": self.request.headers.get('Authorization', ''),
                },
                "request_meta": self.request.META,
                "request_scheme": self.request.scheme,
            }
            log.info(f"ptl put call packing data {header_level_data} with extra params {extra_params}")
            update_repacking_details_in_packing_service.apply_async(args=[header_level_data, extra_params, self.warehouse.id, self.request.user.id, self.source_lpns])

            if self.stock_detail_df['object'].tolist():
                StockDetail.objects.bulk_update_with_rounding(self.stock_detail_df['object'].tolist(), ['lpn_number', 'quantity', 'location_id'])
            if self.existing_stock_df['object'].tolist():
                StockDetail.objects.bulk_update_with_rounding(self.existing_stock_df['object'].tolist(), ['quantity'])
            if self.new_stock_objects:
                StockDetail.objects.bulk_create_with_rounding(self.new_stock_objects)
    
    def update_stock_with_repacking_details(self):
        '''
        Update stock and prepare update call data with repacking details
        '''
        packing_details_list, self.new_stock_objects = [], []
        self.new_stock_objects_dict = {}
        self.check_stock_assignments = {}
        header_level_data, line_level_data = {}, {}
        for request in self.request_data:
            order_reference = request.get('order_reference', '')
            sku_code = request.get('sku_code', '')
            source_lpn = request.get('source_lpn', '')
            destination_lpn = request.get('destination_lpn', '')
            packed_quantity = request.get('quantity', 0) or 0
            zone = request.get('zone', '') or ''
            location = request.get('location', '') or ''
            batch_no = request.get('batch_no', '') or ''
            picklist_number = request.get('picklist_number', '') or ''

            if order_reference not in header_level_data:
                header_level_data[order_reference] = {
                        "warehouse": self.warehouse.username,
                        "user": self.request.user.username,
                        "status": "closed",
                        "transaction_type": "so_packing"
                }
            self.stock_detail_df = self.stock_detail_df.reset_index(drop=True)
            stock_record = self.stock_detail_df.loc[(self.stock_detail_df['sku_code'] == sku_code) & (self.stock_detail_df['lpn_number'] == source_lpn) & (self.stock_detail_df['grn_number'] == order_reference) & (self.stock_detail_df['picklist_number'] == picklist_number) & (self.stock_detail_df['batch_number'] == batch_no)]
            if stock_record.empty:
                self.errors.append(f"No stock details found for SKU code {sku_code} and LPN number {source_lpn}")
                return
            #prepare packing details to update in packing service
            header_level_data, line_level_data, packing_details_list = self.prepare_repacking_call_data(header_level_data, line_level_data, order_reference, stock_record, sku_code, source_lpn, destination_lpn, packed_quantity, batch_no, packing_details_list, zone, location)

            if self.errors:
                return header_level_data

        for header in header_level_data:
            header_level_data[header]['packing_details'] = packing_details_list
        header_level_data = list(header_level_data.values())
        return header_level_data

    def update_or_create_stock_records(self, min_qty, duplicate_destination_lpn, new_stock_dict, stock_record):
        """
        Update the quantity of existing stock records based on the provided new_stock_dict.

        Args:
            new_stock_dict (dict): A dictionary containing the new stock information.
            duplicate_destination_lpn (bool): A flag indicating whether the destination LPN is a duplicate.

        Returns:
            None
        """
        if min_qty:
            if not duplicate_destination_lpn:
                self.new_stock_objects.append(StockDetail(**new_stock_dict))
            else:
                # Update the quantity of the existing stock record
                for stock in self.new_stock_objects:
                    if stock.lpn_number == new_stock_dict['lpn_number'] and duplicate_destination_lpn:
                        stock.quantity += new_stock_dict['quantity']
                        break
            new_stock_dict['batch_number'] = stock_record['batch_number']
            new_stock_dict['sku_code'] = stock_record['sku_code']
            new_stock_dict['batch_reference'] = stock_record['batch_reference']
        return new_stock_dict

    def repacking_stock_assignments(self, zone, location, source_lpn, destination_lpn, packed_quantity, stock_record, index, unique_key, order_reference):
        """
        Repacks stock assignments from a source LPN to a destination LPN.

        Args:
            zone (str): The zone of the stock assignment.
            location (str): The location of the stock assignment.
            source_lpn (str): The source LPN from which stock is being repacked.
            destination_lpn (str): The destination LPN to which stock is being repacked.
            packed_quantity (int): The quantity of stock being repacked.
            stock_record (dict): The stock record containing information about the stock assignment.
            index (int): The index of the stock assignment in the stock detail dataframe.

        Returns:
            tuple: A tuple containing the following values:
                - new_stock_dict (dict): The updated stock record for the repacked stock.
                - packed_quantity (int): The remaining quantity of stock to be repacked.
                - lpn_numbers (list): The list of LPN numbers involved in the repacking.
                - packed_quantities (list): The list of packed quantities corresponding to the LPN numbers.
        """
        duplicate_destination_lpn = False
        new_stock_dict = {}
        lpn_numbers = [source_lpn, destination_lpn]
        min_qty = min(stock_record['quantity'], packed_quantity)
        pck_qty = stock_record['quantity'] - min_qty
        packed_quantities = [pck_qty, min_qty]
        if not self.existing_stock_df.empty:
            self.existing_stock_df = self.existing_stock_df.reset_index(drop=True)
            existing_records = self.existing_stock_df.loc[(self.existing_stock_df['sku_code'] == stock_record['sku_code']) & (self.existing_stock_df['lpn_number'] == destination_lpn) & (self.existing_stock_df['grn_number'] == order_reference) & (self.existing_stock_df['picklist_number'] == stock_record['picklist_number']) & (self.existing_stock_df['batch_number'] == stock_record['batch_number']) & (self.existing_stock_df['location_name'] == location)]
        else:
            existing_records = pd.DataFrame()
        
        new_unique_key  = unique_key[:-1]
        if stock_record['quantity'] >= packed_quantity:
            rem_qty = stock_record['quantity'] - packed_quantity
            if existing_records.empty:
                if unique_key not in self.new_stock_objects_dict:
                    new_stock_dict = deepcopy(stock_record['object'].__dict__)
                else:
                    new_stock_dict = self.new_stock_objects_dict[unique_key]
                
                if new_unique_key in self.check_stock_assignments:
                    duplicate_destination_lpn = True
                del new_stock_dict['id']
                del new_stock_dict['_state']
                new_stock_dict['quantity'] = min_qty
                new_stock_dict['lpn_number'] = destination_lpn
            stock_record['object'].quantity = rem_qty
            self.stock_detail_df.loc[index, 'quantity'] = rem_qty
            packed_quantity -= min_qty                
            fetch_zone = self.zone_dict.get(zone, None)
            if not fetch_zone:
                self.errors.append(f"Zone {zone} not found")
                return new_stock_dict, packed_quantity, lpn_numbers, packed_quantities
            fetch_loc = self.location_dict.get(location, None)
            if not fetch_loc:
                self.errors.append(f"Location {location} not found")
                return new_stock_dict, packed_quantity, lpn_numbers, packed_quantities
            if existing_records.empty:
                if location and fetch_loc:
                    new_stock_dict['location_id'] = fetch_loc
                if unique_key in self.new_stock_objects_dict:
                    new_stock_dict['quantity'] += min_qty
                    packed_quantities[1] = new_stock_dict['quantity']
                else:
                    self.new_stock_objects_dict.update({unique_key: new_stock_dict})
                    self.check_stock_assignments.update({new_unique_key: new_stock_dict})
                self.update_or_create_stock_records(min_qty, duplicate_destination_lpn, new_stock_dict, stock_record)
        else:
            packed_quantity = packed_quantity - stock_record['quantity']
            self.stock_detail_df.loc[index, 'quantity'] = 0
            if existing_records.empty:
                new_stock_dict = stock_record
        if not existing_records.empty:
            new_stock_dict = existing_records.iloc[index]
            new_stock_dict['quantity'] +=  min_qty
            packed_quantities[1] = new_stock_dict['quantity']
            existing_records.loc[index, 'quantity'] = new_stock_dict['quantity']
            existing_records['object'][0].quantity = new_stock_dict['quantity']
        return new_stock_dict, packed_quantity, lpn_numbers, packed_quantities

    def prepare_repacking_call_data(self, header_level_data, line_level_data, order_reference, stock_records, sku_code, source_lpn, destination_lpn, packed_quantity, batch_no, packing_details_list, zone, location):
        '''Prepare repacking call data'''
        for index, stock_record in stock_records.iterrows():
            if header_level_data and source_lpn and destination_lpn:
                order_info = header_level_data[order_reference]
                order_info['transaction_number'] = str(self.sos_data_dict.get(stock_record['receipt_number'], ''))
                unique_key = (destination_lpn, sku_code, batch_no, order_reference, stock_record['picklist_number'], stock_record['id'])
                expiry_date = stock_record['expiry_date'].date().strftime('%Y-%m-%d') if stock_record.get('expiry_date') and pd.notna(stock_record.get('expiry_date')) else None
                manufactured_date = stock_record['manufactured_date'].date().strftime('%Y-%m-%d') if stock_record.get('manufactured_date') and pd.notna(stock_record.get('manufactured_date')) else None
                
                new_stock_dict, packed_quantity, lpn_numbers, packed_quantities = self.repacking_stock_assignments(zone, location, source_lpn, destination_lpn, packed_quantity, stock_record, index, unique_key, order_reference)

                if self.errors:
                    return header_level_data, line_level_data, packing_details_list
                packing_details_list = self.update_item_level_lpn_data(new_stock_dict, expiry_date, manufactured_date, lpn_numbers, packed_quantities, packing_details_list, order_reference, stock_record['picklist_number'])
            if packed_quantity == 0:
                break
        if packed_quantity > 0:
            self.errors.append(f"Quantity exceeding picked qty for LPN : {source_lpn} , Order Ref : {order_reference} , SKU : {sku_code} for batch {batch_no}")
            return header_level_data, line_level_data, packing_details_list

        return header_level_data, line_level_data, packing_details_list
    
    def update_item_level_lpn_data(self, stock_record, expiry_date, manufactured_date, lpn_numbers, packed_quantities, packing_details_list, order_reference, picklist_number):
        '''Update item level lpn data for repacking'''
        for lpn_number, packed_quantity in zip(lpn_numbers, packed_quantities):
            # Check if lpn_number already exists in packing_details_list
            lpn_index = next((index for index, lpn_data in enumerate(packing_details_list) if lpn_data["lpn_number"] == lpn_number), None)
            
            dest_index = next((index for index, lpn_data in enumerate(packing_details_list) if lpn_data["lpn_number"] == stock_record['lpn_number']), None)

            if stock_record.get('batch_number',None) is not None:
                batch_details =  {
                    "batch_number": stock_record['batch_number'],
                    "batch_reference": stock_record['batch_reference'],
                    "expiry_date": expiry_date,
                    "manufactured_date": manufactured_date,
                }
            else:
                batch_details = {}

            if dest_index is not None:
                # Check if the item is already in the LPN
                for item in packing_details_list[dest_index]["items"]:
                    if (item["sku_code"], item['transaction_id']) == (stock_record['sku_code'], stock_record['receipt_number']):
                        item["packed_quantity"] += packed_quantity
                        break

            if lpn_index is not None:
                unique_items = {(item["sku_code"], item['transaction_id']) for item in packing_details_list[lpn_index]["items"]}
                # Check if the item is already in the LPN
                if (stock_record['sku_code'], stock_record['receipt_number']) not in unique_items:
                    packing_details_list[lpn_index]["items"].append({
                        "transaction_id": int(stock_record['receipt_number']),
                        "sku_code": stock_record['sku_code'],
                        "lpn_number": "",
                        "packed_quantity": packed_quantity,
                        "batch_details": batch_details
                        }
                    )
            else:
                # LPN number does not exist, create new LPN data
                lpn_data = {
                    "lpn_number": lpn_number,
                    "json_data": {"created_by": self.request.user.username, "picked_lpn": True},
                    "items": [
                        {
                            "transaction_id": int(stock_record['receipt_number']),
                            "sku_code": stock_record['sku_code'],
                            "lpn_number": "",
                            "packed_quantity": packed_quantity,
                            "batch_details": batch_details
                        }
                    ]
                }
                packing_details_list.append(lpn_data)
        
        return packing_details_list
    

    def get_lpn_status(self):
        """
        Return the LPN status based on the current state of the LPN.

        If everything is picked, the LPN status is 'picked'.
        If at least one item is in the packing zone, the LPN status is 'packing_in_progress'.
        If the LPN is in the pre_invoice zone or has been invoiced, the LPN status is 'packed'.
        If none of the above conditions are met, the LPN status is an empty string.

        Returns:
            str: The LPN status.
        """
        storage_type_wise_status = {
            "pre_sorting": "pre_sorting",
            "sorting": "sorting",
            "packing": "packing_in_progress",
            "pre_invoice": "packed"
        }

        invoiced = self.lpn_number in self.lpn_invoice_dict
        if self.audit_status:
            return self.audit_status, self.current_zone
        elif invoiced:
            if self.released_lpns.get(self.lpn_number, False) == True:
                return "open", self.current_zone
            return "invoiced", self.current_zone

        if self.is_picked_lpn:
            if self.lpn_number in self.dropped_lpns:
                return "picked", self.current_zone
            else:
                return "picking_in_progress", self.last_picked_zone

        staging_status = storage_type_wise_status.get(self.storage_type)
        if staging_status:
            return staging_status, self.current_zone
        elif len(self.picklist_status) == 1 and "picked" in self.picklist_status:
            return "picked", self.current_zone
        return "", self.current_zone
        
    
    def get_zone_storage_types(self):
        receipt_numbers = list(self.sos_details.keys())
        filters = {
            "sku__user": self.warehouse.id,
            "lpn_number": self.lpn_number,
            "quantity__gt": 0,
            "receipt_type": "so_picking",
            "receipt_number__in": receipt_numbers
        }
        storage_types = list(StockDetail.objects.filter(**filters).values_list("location__zone__storage_type", flat=True))
        return storage_types


    def update_picked_lpns_in_staging_info(self):
        """
        Updates the status of picked LPNS in staging information.

        This method retrieves the list of picked LPNS from the request data and updates their status
        in the StagingInfo model to indicate that they have been processed.

        Returns:
            None
        """
        picked_lpns = self.request_data.get('lpn_details', {}).get('picked', [])

        if not picked_lpns:
            return
        
        # Get stock details for the picked LPNs
        stock_df = self.stock_df.loc[(self.stock_df['lpn_number'].isin(picked_lpns)) & (self.stock_df['receipt_type'].isin(['so_picking', 'so_dispense'])) & (self.stock_df['quantity'] > 0)]
        picked_lpns = list(set(picked_lpns) - set(stock_df['lpn_number'].tolist()))

        # remove lpns if these lpns present in new stock details
        lpns_to_remove = []
        for lpn in picked_lpns:
            if self.new_stock_lpn_quantity_map.get(lpn, 0) > 0:
                lpns_to_remove.append(lpn)
        
        picked_lpns = list(set(picked_lpns) - set(lpns_to_remove))
        if not picked_lpns:
            return

        picklist_numbers = [self.picklist_numbers]
        if isinstance(self.picklist_numbers, list):
            picklist_numbers = self.picklist_numbers

        staging_info_objs = list(StagingInfo.objects
            .filter(user_id=self.warehouse.id, segregation='outbound_staging', carton_no__in=picked_lpns,
                    picklist_number__in=picklist_numbers, status=0)
        )

        for staging_info_obj in staging_info_objs:
            staging_info_obj.status = 1
            staging_info_obj.updation_date = djano_timezone.now()

        StagingInfo.objects.bulk_update(staging_info_objs, ['status', 'updation_date'])


    def update_sorted_lpn_in_staging_info(self):

        if not self.primary_sorted_lpns or self.misc_dict.get('pigeon_hole_sorting') == 'lpn':
            return

        order_lpn_mapping = {}

        for lpn_number, transaction_id in self.primary_sorted_lpns:
            for packed_key, packed_qty in self.transaction_details.items():
                if not packed_qty:
                    continue
                if transaction_id == packed_key[1]:
                    order_reference = self.stock_df.loc[(self.stock_df['receipt_number'] == transaction_id) & (self.stock_df['lpn_number'] == packed_key[0])]['object'].to_list()[0].grn_number
                    order_lpn_mapping[order_reference] = packed_key[0]
                    break

        if not order_lpn_mapping:
            return

        staging_info_objs = list(StagingInfo.objects
            .filter(user=self.warehouse, segregation='outbound_staging', location__zone__storage_type='sorting',
                    carton_no__in=self.lpn_numbers, order_reference__in=order_lpn_mapping, status=0)
        )

        for staging_info_obj in staging_info_objs:
            staging_info_obj.carton_no = order_lpn_mapping[staging_info_obj.order_reference]
            staging_info_obj.updation_date = djano_timezone.now()

        if staging_info_objs:
            StagingInfo.objects.bulk_update(staging_info_objs, ['carton_no', 'updation_date'])


@celery_app.task
def update_repacking_details_in_packing_service(header_level_data, extra_params, warehouse_id, user_id, source_lpns):
    '''Update Packing Service for Repacking'''
    warehouse = User.objects.get(id=warehouse_id)
    user = User.objects.get(id=user_id)
    request_dict = {
        "request_headers": extra_params.get('headers', {}),
        "request_meta": extra_params.get('request_meta', {}),
        "request_scheme": extra_params.get('request_scheme', ''),
    }
    if header_level_data:
        packing_service_instance = PackingService(request_dict, user, warehouse)
        packing_details, packing_service_errors = packing_service_instance.create_packing(header_level_data[0])
        if packing_service_errors:
            log.info("Errors in Packing service for repacking update - %s" % packing_service_errors)

    if source_lpns:
        empty_lpns = list(StockDetail.objects.filter(sku__user=warehouse.id,lpn_number__in=source_lpns,quantity__gt=0,receipt_type__in=['so_picking', 'so_dispense']).values_list('lpn_number', flat=True))
        release_lpns_ = list(set(source_lpns) - set(empty_lpns))
        if release_lpns_:
            release_lpns(warehouse, user, release_lpns_, request_dict)
        else:
            log.info("No cartons to release after PTL Sorting - %s" % source_lpns)

def release_lpns(warehouse, user, lpn_numbers, request_dict):
    """
    Releases the specified LPNs.
    
    Args:
    - warehouse (User): The warehouse user object.
    - user (User): The user object.
    - lpn_numbers (list): The list of LPN numbers to release.
    - request_dict (dict): The request dictionary containing the request headers, meta, and scheme.
    """
    lpn_numbers_str = ",".join(lpn_numbers)
    params = {
        "warehouse": warehouse.username,
        "lpn_number": lpn_numbers_str,
        "status": True,
        "usable": True,
        "dropped": False,
        "blocked": False
    }
    packing_service_instance = PackingService(request_dict, user, warehouse)
    lpn_details, packing_service_errors = packing_service_instance.update_lpn(params)
    if packing_service_errors:
        log.info("Errors in Packing service for release carton - %s" % packing_service_errors)
    
class LPNView(WMSListView):
    """
    View class for handling LPN (License Plate Number) related operations.
    Inherits from WMSListView, which is a base class for WMS (Warehouse Management System) views.
    Methods:
    - put: Handles the PUT request for updating LPN data.
    """
    def put(self, request, *args, **kwargs):
        """
        Handles the PUT request for updating LPN data.
        Args:
        - request: The HTTP request object.
        - *args: Additional positional arguments.
        - **kwargs: Additional keyword arguments.
        Returns:
        - JsonResponse: The JSON response containing the result of the operation.
        """
        self.set_user_credientials()
        try:
            self.request_data = loads(request.body)
        except Exception:
            self.request_data = request.POST.get("data") or {}

        if not self.request_data:
            return JsonResponse({"errors": ["Invalid request data"]}, status=400)

        header_level_data = []
        lpn_numbers = self.request_data.get('lpn_numbers', [])
        if not lpn_numbers:
            return JsonResponse({"errors": ["No LPN numbers provided"]}, status=400)

        extra_params = {
            "headers": {
                "Warehouse": self.request.headers.get("Warehouse"),
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }

        # check for lpn release condition
        misc_types = ['release_carton']
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        release_cartons_misc_value = misc_dict.get('release_carton', 'false')

        shipping_pending_lpns, manifest_close_pending_lpns = [], []
        if release_cartons_misc_value in ['Shipment', 'Manifest', 'Invoice']:
            stock_lpns = list(StockDetail.objects.filter(sku__user=self.warehouse.id, lpn_number__in=lpn_numbers, quantity__gt=0, receipt_type__in=['so_picking', 'so_dispense']).values_list('lpn_number', flat=True))
            lpn_numbers = list(set(lpn_numbers) - set(stock_lpns))

            if release_cartons_misc_value == 'Shipment' and lpn_numbers:
                shipping_pending_lpns = list(ShipmentInvoiceItems.objects.filter(package_reference__in=lpn_numbers, shipment_invoice__user_id=self.warehouse.id, shipment_invoice__status__in=[1, 2]).values_list('package_reference', flat=True))
                log.info(f"shipment is pending for {shipping_pending_lpns} LPNs")
                lpn_numbers = list(set(lpn_numbers) - set(shipping_pending_lpns))

            if release_cartons_misc_value == 'Manifest' and lpn_numbers:
                manifest_close_pending_lpns = list(OrderShipmentItems.objects.filter(package_reference__in=lpn_numbers, order_shipment__user_id=self.warehouse.id, order_shipment__status__in=[1, 2]).values_list('package_reference', flat=True))
                log.info(f"manifest close is pending for {manifest_close_pending_lpns} LPNs")
                lpn_numbers = list(set(lpn_numbers) - set(manifest_close_pending_lpns))
        
        log.info(f"header level data {header_level_data} - extra_params {extra_params} - {self.warehouse.username} - {self.request.user.username} - {lpn_numbers}")
        if lpn_numbers:
            update_repacking_details_in_packing_service.apply_async(args=[header_level_data, extra_params, self.warehouse.id, self.request.user.id, lpn_numbers])
        pending_lpns = shipping_pending_lpns + manifest_close_pending_lpns + stock_lpns
        if pending_lpns and lpn_numbers:
            return JsonResponse({"message": [f"LPN Release Triggered Successfully for {lpn_numbers}",  f"Lpn Not Released for Pending lpns {pending_lpns}"]}, status=200)
        elif pending_lpns:
            errors = []
            if shipping_pending_lpns:
                errors.append(f"Shipment is pending for LPNs {shipping_pending_lpns}")
            if manifest_close_pending_lpns:
                errors.append(f"Manifest is pending for LPNs {manifest_close_pending_lpns}")
            if stock_lpns:
                errors.append(f"invoice is pending for LPNs {stock_lpns}")
            return JsonResponse({"errors": errors}, status=400)
        return JsonResponse({"message": ["LPN Release Triggered Successfully"]}, status=200)
        

class LpnConsolidation(WMSListView):
    """
    LPN Consolidation View
    """

    def put(self, request, *args, **kwargs):
        """
        Handles the PUT request for consolidating LPNs.
        Args:
        - request: The HTTP request object.
        - *args: Additional positional arguments.
        - **kwargs: Additional keyword arguments.
        Returns:
        - JsonResponse: The JSON response containing the result of the operation.
        """
        self.set_user_credientials()
        self.errors = []
        try:
            self.request_data = loads(request.body)
        except Exception:
            self.request_data = request.POST.get("data") or {}
        
        if not self.request_data:
            return JsonResponse({"errors": ["Invalid request data"]}, status=400)
        self.cache_ids = []

        try:
        
            self.validate_request_data()

            if self.errors:
                self.delete_cache_ids()
                return JsonResponse({"errors": self.errors}, status=400)
            
            request_dict = {
                "request_headers": {
                    "Warehouse": self.request.headers.get("Warehouse"),
                    "Authorization": self.request.headers.get('Authorization', ''),
                },
                "request_meta": self.request.META,
                "request_scheme": self.request.scheme,
            }
            packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
            
            consolidation_status, packing_service_errors = packing_service_instance.lpn_consolidation(self.request_data)

            if packing_service_errors:
                self.delete_cache_ids()
                return JsonResponse({"errors": packing_service_errors}, status=400)

            packing_obj = PackingView()
            stock_update_payload = self.prepare_stock_update_payload()
            packing_obj.picklist_numbers = ''
            packing_obj.customer_reference = self.request_data.get('customer_reference', '')
            source_lpns = self.request_data.get('source_lpns')
            destination_lpn = self.request_data.get('destination_lpn')
            packing_obj.lpn_numbers = source_lpns + [destination_lpn]
            packing_obj.request_type = 'update_stock'
            packing_obj.update_type = 'update'
            packing_obj.request = self.request
            packing_obj.request_data = stock_update_payload
            packing_obj.user = self.user
            packing_obj.warehouse = self.warehouse
            packing_obj.errors = []
            packing_obj.update_stock_with_packing_details()

            self.delete_cache_ids()
            return JsonResponse({"message": "LPN Consolidation Successful"}, status=200)
        except Exception as e:
            self.delete_cache_ids()
            log.error(f"Error in LPN Consolidation - {e}")
            return JsonResponse({"errors": ["Error in LPN Consolidation"]}, status=400)
    
    def prepare_stock_update_payload(self):
        """
        converts consolidation payload to stock update payload
        """
        payload = {}
        payload['picklist_numbers'] = ''
        payload['request_type'] = 'update_stock'
        payload['update_type'] = 'update'
        all_lpns = self.request_data.get('source_lpns') + [self.request_data.get('destination_lpn')]
        payload['lpn_numbers'] = ",".join(all_lpns)
        payload['lpn_details'] = {
            "picked": self.request_data.get('source_lpns'),
            "packed": [self.request_data.get('destination_lpn')]
        }
        return payload


    def validate_request_data(self):
        """
        Validates the request data for LPN consolidation.
        Returns:
        - None
        """
        # check transaction_number, source_lpns and destination_lpn
        if not self.request_data.get("source_lpns"):
            self.errors.append("No source LPNs provided")
        if not self.request_data.get("destination_lpn"):
            self.errors.append("No destination LPN provided")
        if not self.request_data.get("transaction_number"):
            self.errors.append("No transaction number provided")
        
        if not self.errors:
            all_lpns = [self.request_data.get('destination_lpn')] + self.request_data.get('source_lpns')
            for lpn in all_lpns:
                cache_key = f"{self.warehouse.username}_lpn_consolidation_{lpn}"
                cache_status = cache.add(cache_key, "True", timeout=120)
                if not cache_status:
                    self.delete_cache_ids()
                    self.errors.append(f"Consolidation is in progress for {lpn}!")
                    return
                else:
                    self.cache_ids.append(cache_key)
    
    def delete_cache_ids(self):
        """
        Deletes the cache IDs for LPN consolidation.
        Returns:
        - None
        """
        for cache_id in self.cache_ids:
            cache.delete(cache_id)


class ScanLpn(WMSListView):
    """
    Scan LPN View
    """

    def put(self, request, *args, **kwargs):
        """
        Handles the PUT request for scanning lpns for picking
        Args:
        - request: The HTTP request object.
        - *args: Additional positional arguments.
        - **kwargs: Additional keyword arguments.
        Returns:
        - JsonResponse: The JSON response containing the result of the operation.
        """
        self.set_user_credientials()
        self.final_response = {
                "message": "success",
                "errors": [],
                "data": []
            }
        self.errors = []
        self.pick_and_pass_objs = []
        try:
            self.request_data = loads(request.body)
        except Exception:
            self.request_data = request.POST.get("data") or {}
        
        if not self.request_data:
            self.final_response['errors'].append("Invalid request data")
            return JsonResponse(self.final_response, status=400)
        self.cache_ids = []
        """

            for scan lpn packing
            {
                "warehouse": "TEST_WH1",
                "lpn_numbers": [
                    "CP00dd0164"
                ],
                "transaction_number": "46224",
                "transaction_type": "so_packing",
                "json_data": {
                },
                "customer_reference": "361"
            }

            for scan lpn wms
            {
                "employee_id": 2,
                "sub_picklist_number": "",
                "lpn_numbers": "CP000165",
                "reference_type": "so_picking",
                "picklist_number": 1217
            }
        """
        self.validate_request_data()

        if self.errors:
            self.final_response['errors'].append(self.errors)
            return JsonResponse(self.final_response, status=400)
        
        #skip pick and pass validation if it is qc scanning
        if self.request_data.get('lpn_scan_at_qc'):
            # check if lpn is already present in putaway if yes then restrict scan lpn
            stock_objs = StockDetail.objects.filter(lpn_number__in=self.request_data.get('lpn_numbers', []), sku__user=self.warehouse.id, quantity__gt=0, receipt_type='grn_packing')
            if stock_objs.exists():
                self.final_response['errors'].append("Destination LPN is already present in Putaway")
                return JsonResponse(self.final_response, status=400)
        else:
            self.validate_picklist_and_pick_and_pass_data()

        #check if scan api is giving success if not throw error if yes create a record in pick and pass for lpn_based_packing as reference_type 
        
        if self.errors:
            self.final_response['errors'].extend(self.errors)
            return JsonResponse(self.final_response, status=400)
    
        if self.pick_and_pass_objs:
            self.final_response['data'] = ['Lpn already mapped to picklist']
            return JsonResponse(self.final_response, status=200)
        
        #check if it is scanned in putaway if yes then restrict it

        request_dict = {
            "request_headers": request.headers,
            "request_meta": request.META,
            "request_scheme": request.scheme,
        }
        scan_lpn_payload = self.prepare_scan_lpn_payload()

        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        scan_lpn_response, packing_service_errors = packing_service_instance.scan_lpn(scan_lpn_payload)

        if packing_service_errors:
            self.final_response['errors'].extend(packing_service_errors)
            return JsonResponse(self.final_response, status=400)

        self.create_scanned_lpn_record()
        if self.errors:
            self.final_response['errors'].extend(self.errors)
            return JsonResponse(self.final_response, status=400)

        self.final_response['data'] = scan_lpn_response
        return JsonResponse(self.final_response, status=200)

    def validate_request_data(self):
        """
        validates if the request data is correct
        """
        valid_fields = ["lpn_numbers", "transaction_number", "transaction_type", "customer_reference", "employee_id"]
        for field in valid_fields:
            if not self.request_data.get(field):
                self.errors.append(f"No {field} provided")
    

    def validate_picklist_and_pick_and_pass_data(self):
        """
        before scanning lpn check if the picklist is in open status
        """
        lpn_number = self.request_data.get('lpn_numbers')
        picklist_number = self.request_data.get('transaction_number')
        filters = {
            "picklist_number": picklist_number,
            "status": "open",
            "user_id": self.warehouse.id
        }
        picklists = Picklist.objects.filter(**filters)
        if not picklists.exists():
            self.errors.append(f"Picklist {picklist_number} not is available for picking")
            return
        
        pick_and_pass_filters = {
            'reference_type': 'lpn_based_picking',
            'lpn_number': lpn_number,
            'status': 'open',
            'warehouse_id': self.warehouse.id,
            "lpn_status": "open",
        }
        self.pick_and_pass_objs = list(PickAndPassStrategy.objects.filter(**pick_and_pass_filters))
        for pick_and_pass_obj in self.pick_and_pass_objs:
            if pick_and_pass_obj.reference_number != picklist_number:
                self.errors.append(f"LPN {lpn_number} is already scanned for picklist {pick_and_pass_obj.reference_number}")
                return

    def prepare_scan_lpn_payload(self):
        """
        returns the scan lpn payload for packing service
        """
        lpn_numbers = self.request_data.get('lpn_numbers').split(',')
        scan_lpn_payload = {
            "warehouse": self.warehouse.username,
            "lpn_numbers": lpn_numbers,
            "transaction_number": self.request_data.get('transaction_number'),
            "transaction_type": "so_packing",
            "json_data": {
                "created_by": self.user.username
            },
            "customer_reference": self.request_data.get('customer_reference'),
            "user": self.user.username
        }
        for key in ["lpn_type", "dropped", "blocked", "allow_multiple_transactions", "lpn_scan_at_qc"]:
            if key in self.request_data:
                scan_lpn_payload[key] = self.request_data.get(key)

        return scan_lpn_payload
    

    def create_scanned_lpn_record(self):
        """
        create a record for scanned lpn in pick and pass for getting pending lpn drops
        """
        # check if entry already exists (lpn, picklist) if yes throw error else create a record

        if self.request_data.get('lpn_scan_at_qc'):
            return

        lpn_number = self.request_data.get('lpn_numbers')
        picklist_number = self.request_data.get('transaction_number')
        filters = {
            "lpn_number": lpn_number,
            "reference_number": picklist_number,
            "lpn_status": "open",
            "reference_type": "lpn_based_picking",
            "warehouse_id": self.warehouse.id
        }
        if PickAndPassStrategy.objects.filter(**filters):
            return
        else:
            self.userprofile_id = self.warehouse.userprofile.id
            pick_and_pass_obj = PickAndPassStrategy(
                lpn_number=lpn_number,
                reference_number=picklist_number,
                lpn_status="open",
                reference_type="lpn_based_picking",
                warehouse_id=self.warehouse.id,
                status="open",
                json_data={"created_by": self.user.username},
                employee_id=self.request_data.get('employee_id'),
                account_id=self.userprofile_id
            )
            pick_and_pass_obj.save()
            return