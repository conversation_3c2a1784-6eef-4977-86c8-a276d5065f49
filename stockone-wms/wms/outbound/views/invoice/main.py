#package imports
import pytz
import base64
import os
import io
from json import dumps, loads
from copy import deepcopy
from collections import OrderedDict, defaultdict
import pandas as pd
import traceback

#django imports
from django.http import (
    HttpRequest, HttpResponse, JsonResponse
)
from django.core.cache import cache
from django.db.models import Q, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Count
from django.db.models.functions import Cast
from django.utils.decorators import method_decorator
from django.db import transaction
from django.test.client import RequestFactory
from django.core.cache import cache
from django.core.files.uploadedfile import InMemoryUploadedFile

#rest framework imports
from rest_framework import filters, viewsets
from rest_framework.response import Response
from rest_framework.authentication import SessionAuthentication, BasicAuthentication

#core operations imports
from core.models import SKUMaster, EANNumbers, MasterDocs
from core_operations.views.common.main import (
    make_data_to_acceptable_params, get_multiple_misc_values,
    get_user_prefix_incremental, get_misc_value, scroll_data, get_user_ip,
    get_local_date_with_time_zone, truncate_float, get_decimal_value,
    WMSListView, get_warehouse, generate_log_message, get_utc_time_from_user_time_input,
    get_misc_options_list
)
from core_operations.views.integration.integration import webhook_integration_3p

#wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger

#inventory imports
from inventory.models import (
    LocationMaster, StagingLocation, LocationMaster,
    StockDetail, SerialNumberTransaction
)
from inventory.views.locator.serial_numbers import get_serial_transaction_mapping
from inventory.views.move.staging_lanes import (
    StagingLaneSet, get_staging_lane_for_order
)
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#inbound imports
from inbound.serializers.grn import GRNSerializer
from inbound.views.grn.validation import POGRNValidation
from inbound.views.grn.asn import ASNSet
from inbound.models import (
    ASNSummary, SellerPOSummary
)

#outbound imports
from outbound.models import (
    OrderDetail, Picklist, SellerOrderSummary, OrderPackaging,
    StagingInfo, InvoiceAdditionalInfo, CustomerMaster, CustomerOrderSummary,
    IRN, ShipmentInvoice, SalesReturnLineLevel, OrderTypeZoneMapping, InvoiceDetail
)

from outbound.serializers import SOSSerializer
from outbound.views.shipment.shipment_invoice import insert_shipment_invoice_data_fun
from outbound.views.invoice.helpers import (
    get_invoice_charges_data, get_serial_number_transaction_data, get_wip_location_stocks
)
from outbound.views.invoice.helpers import get_destination_user

from .auto_invoice import move_to_inv
from .helpers import prepare_grn_asn_input_data
from .invoice_price_master import set_pricing_master_details_for_invoice
from .invoice_data import get_customer_invoice_tab_func
from .delivery_challan import update_dc_sos
from .create_invoice import CreateInvoiceMixin

log = init_logger('logs/invoice.log')

seller_summary_const, date_format_const = 'seller_summary_id', "%Y-%m-%d"
P_INTEGRATION_LOG = '3p integration  %s'

def generate_invoice(dcs, warehouse):
    request = HttpRequest()
    for order_id in dcs:
        seller_ids = []
        for order in OrderDetail.objects.filter(order_id=order_id):
            order_id = '%d:1' % order.order_id
            if order_id not in seller_ids:
               seller_ids.append({seller_summary_const: order_id})
    
        request.user = warehouse
        request.warehouse = warehouse
        request.GET = make_data_to_acceptable_params(seller_ids)
        move_to_inv(request, warehouse)

class CsrfExemptSessionAuthentication(SessionAuthentication):
    def enforce_csrf(self, request):
        return  # To not perform the csrf check previously happe

class SOSViewSet(viewsets.ModelViewSet):
    queryset = SellerOrderSummary.objects.filter()
    serializer_class = SOSSerializer
    filter_backends = [filters.SearchFilter]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    @method_decorator(get_warehouse)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def list(self, request, warehouse: User):
        misc_types = ['outbound_staging_area']
        decimal_limit = get_decimal_value(warehouse.id)
        misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
        outbound_staging_area = misc_dict.get('outbound_staging_area', 'false')
        search_params = {}
        sku_model = [field.name for field in SKUMaster._meta.get_fields()]
        location_model = [field.name for field in LocationMaster._meta.get_fields()]
        orderdetail_model = [field.name for field in OrderDetail._meta.get_fields()]
        picklist_model = [field.name for field in Picklist._meta.get_fields()]
        picklist_numbers = []
        for key, value in request.GET.items():
            if key in sku_model:
                search_params['order__sku__%s' % key] = value
            elif key in ['picklist_number', 'customer_id']:
                reference_numbers = []
                ref_nos = value.split(',')
                for ref_no in ref_nos:
                    if ref_no and ref_no.strip().isdigit():
                        reference_numbers.append(ref_no)
                    else:
                        return JsonResponse({'message': 'Invalid request details, Please check!'}, status=400)
                if 'picklist_number' in key:
                    search_params['picklist__picklist_number__in'] = reference_numbers
                    picklist_numbers = reference_numbers
                elif 'customer_id' in key:
                    search_params['order__customer_id__in'] = reference_numbers
            elif key in location_model:
                search_params['location__%s' % key] = value
            elif key in orderdetail_model:
                if ',' in value:
                    search_params['order__%s__in' % key] = [ele for ele in value.split(',') if ele]
                else:
                    search_params['order__%s' % key] = value
            elif key in picklist_model:
                if ',' in value:
                    search_params['picklist__%s__in' % key] = [ele for ele in value.split(',') if ele]
                else:
                    search_params['picklist__%s' % key] = value
            elif 'orderpackaging' in key:
                if ',' in value:
                    search_params['%s__in' % key] = value.split(',')
                else:
                    search_params['%s' % key] = value
        if outbound_staging_area != 'false':
            invoice_bi_ids = []
            if request.GET.get('type') == 'invoice':
                segregation = 'BEFORE_INVOICE'
                invoice_bi_ids = list(StagingLocation.objects.filter(warehouse=warehouse.id, status=3, segregation=segregation, reference_number__in=picklist_numbers, quantity__gt=0).values_list('transaction_id', flat=True))
            else:   
                segregation = 'PACKING_STAGING'
                invoice_bi_ids = list(StagingLocation.objects.filter(warehouse=warehouse.id, status__in=[1], segregation='BEFORE_INVOICE', quantity__gt=0, reference_number__in=picklist_numbers).values_list('transaction_id', flat=True))
            staging_location_objs_list = list(StagingLocation.objects.filter(warehouse=warehouse.id, transaction_id__in=invoice_bi_ids, status=3, segregation=segregation).values('transaction_id', 'carton__bin_number', 'quantity', 'json_data'))
            transaction_ids, staging_location_details = [], {}
            for staging_record in staging_location_objs_list:
                transaction_id = staging_record.get('transaction_id', '') or ''
                carton_number = staging_record.get('carton__bin_number', '') or ''
                quantity = staging_record.get('quantity', 0.00) or 0.00
                staging_json_data = staging_record.get('json_data', {}) or {}
                if transaction_id and transaction_id not in transaction_ids and not staging_json_data.get('is_invoiced'):
                    transaction_ids.append(transaction_id)
                unique_key = (transaction_id, carton_number)
                if not staging_location_details.get(unique_key):
                    staging_location_details[unique_key] = quantity
                else:
                    staging_location_details[unique_key] += quantity

            search_params['id__in'] = transaction_ids
        queryset = SellerOrderSummary.objects.filter(order__user=warehouse.id, **search_params)
        #scan invoice
        is_scan_invoice = False
        self.cartons_list, self.sku_list = {}, []
        if request.GET.get('is_scan_invoice') == 'true':
            is_scan_invoice = True

        if request.GET.get('for_packing') == 'true' or request.GET.get('processed_pack') == 'true':
            if queryset.exists():
                if queryset[0].picklist.order_type in ['sorting', 'full_carton_pick']:
                    queryset = SellerOrderSummary.objects.filter(order__user=warehouse.id, orderpackaging__status=1, **search_params).order_by('orderpackaging__package_reference'). \
                                        annotate(pending_packing=Sum('quantity', distinct=True) - Sum('orderpackaging__packed_quantity'),
                                        packed_quantity=Sum('orderpackaging__packed_quantity'), package_reference=Cast('orderpackaging__package_reference', CharField()))
                else:
                    if request.GET.get('processed_pack') == 'true':
                        queryset = SellerOrderSummary.objects.filter(order__user=warehouse.id, **search_params).order_by('orderpackaging__package_reference'). \
                                    annotate(pending_packing=Sum('quantity', distinct=True) - Sum('orderpackaging__packed_quantity'),
                                    packed_quantity=Sum('orderpackaging__packed_quantity'), package_reference=Cast('orderpackaging__package_reference', CharField()))
                    else:
                        queryset = SellerOrderSummary.objects.filter(order__user=warehouse.id, **search_params).annotate(pending_packing=Sum('quantity', distinct=True) - Sum('orderpackaging__packed_quantity'), packed_quantity=Sum('orderpackaging__packed_quantity'))
        if not search_params.get('status'):
            queryset = queryset.filter(order_status_flag='processed_orders')

        serializer = SOSSerializer(queryset, many=True)
        sku_stock_list = list(queryset.values('order__sku', 'order__sku__enable_serial_based', 'picklist__stock_id', 'id', 'order__order_type'))
        sku_ids_list=[each_data.get('order__sku') for each_data in sku_stock_list]
        serial_sos_ids, serial_sku_list, serial_sku_stock_list, order_types, serial_mapping_obj_dict, order_pack_obj = [], [], [], [], {}, {}
        if sku_stock_list:
            for each_sku_stock in sku_stock_list:
                serial_sos_ids.append(each_sku_stock.get('id'))
                order_type = each_sku_stock.get('order__order_type', '')
                if order_type and order_type not in order_types:
                    order_types.append(order_type)
                if each_sku_stock.get('order__sku__enable_serial_based',0):
                    serial_sku_list.append(each_sku_stock.get('order__sku',''))  
                    serial_sku_stock_list.append(each_sku_stock.get('picklist__stock_id',''))
            if serial_sku_list and serial_sku_stock_list:
               serial_mapping_obj_dict = get_serial_transaction_mapping(picklist_numbers,'Picklist',serial_sku_list,serial_sku_stock_list,warehouse.id)
        self.get_wip_location_stocks(warehouse, serial_sos_ids, order_types)
        ean_numbers_res = EANNumbers.objects.filter(sku_id__in= sku_ids_list,sku__user__in=[warehouse.id]).values("ean_number", "sku__sku_code")
        ean_numbers_dict = {}
        if ean_numbers_res.exists():
            for ean_row in ean_numbers_res:
                if ean_row['sku__sku_code'] in ean_numbers_dict:
                    ean_numbers_dict[ean_row["sku__sku_code"]].append(ean_row["ean_number"])
                else:
                    ean_numbers_dict[ean_row["sku__sku_code"]] = [ean_row["ean_number"]]
        if serial_mapping_obj_dict:
            order_pack_data = list(OrderPackaging.objects.filter(seller_order_summary__picklist__picklist_number__in  = picklist_numbers, seller_order_summary__order__user=warehouse.id).values('json_data','seller_order_summary__order__sku__sku_code','seller_order_summary__picklist__stock'))
            for each_pack in order_pack_data:
                uniq_key = (str(each_pack.get('seller_order_summary__order__sku__sku_code','')),str(each_pack.get('seller_order_summary__picklist__stock','')))
                if uniq_key in  order_pack_obj:
                    serial_list=order_pack_obj[uniq_key]
                    json_data = each_pack.get('json_data',{})
                    serial_list.extend(json_data.get('serial_numbers',[]))
                    order_pack_obj[uniq_key] = serial_list
                else:
                    json_data = each_pack.get('json_data',{})
                    order_pack_obj[uniq_key] = json_data.get('serial_numbers',[])  

        final_out_data = []
        for ser in serializer.data:
            ser['order_display_key'] = ser['order'].get('order_reference') or ser['order'].get('original_order_id')
            ser['order']['sku']['ean_numbers'] = ean_numbers_dict.get(ser['order']['sku']['sku_code'], [])
            if ser['order']['sku']['enable_serial_based']:
                serial_number_list = list(set(serial_mapping_obj_dict.get((str(ser['order']['sku']['sku_code']),str(ser['picklist_obj']['stock'])),[])) - set(order_pack_obj.get((str(ser['order']['sku']['sku_code']),str(ser['picklist_obj']['stock'])),[])))
                ser['order']['sku']['serial_numbers'] = serial_number_list
            ser['order']['sku']['volume'] = truncate_float(ser['order']['sku']['volume'], decimal_limit)
            ser['order']['quantity'] = truncate_float(ser['order']['quantity'], decimal_limit)
            ser['order']['invoice_amount'] = truncate_float(ser['order']['invoice_amount'], decimal_limit)
            ser['order']['stock_count'] = truncate_float(ser['order']['stock_count'], decimal_limit)
            if outbound_staging_area != 'false':
                transaction_id = ser['id'] or ''
                carton_no = ser.get('package_reference', '') or ''
                unique_key = (transaction_id, carton_no)
                if staging_location_details.get(unique_key):
                    if not carton_no:
                        ser['quantity'] = staging_location_details.get(unique_key)
                    final_out_data.append(ser)
                elif staging_location_details.get((transaction_id, '')):
                    ser['quantity'] = staging_location_details.get((transaction_id, ''))
                    ser['package_reference'] = ser['packed_quantity'] = None
                    final_out_data.append(ser)
                else:
                    continue
            else:
                if not self.stock_df.empty:
                    available_stock_qty = self.stock_df.loc[self.stock_df['receipt_number'] == ser['id']]['quantity'].sum()
                    if available_stock_qty > 0:
                        ser['quantity'] = available_stock_qty
                        final_out_data.append(ser)
            ser['quantity'] = truncate_float(ser['quantity'], decimal_limit)
            if is_scan_invoice:
                self.scan_invoice_data_preparation(ser)

        if not is_scan_invoice:  
            data_dict = {'total_records': queryset.count(), 'data': final_out_data}
        else:
            pending_invoice_items = list(self.cartons_list.values()) + list(self.sku_list)
            data_dict = {
                'data': pending_invoice_items,
                'total_records': len(pending_invoice_items)
            }
        return HttpResponse(dumps(data_dict))

    def scan_invoice_data_preparation(self, sos_record):
        package_reference = sos_record.get('package_reference', '')
        sku_code = sos_record.get('order', {}).get('sku', {}).get('sku_code', '')
        order_reference = sos_record.get('order', {}).get('order_reference', '')
        original_order_id = sos_record.get('order', {}).get('original_order_id', '')
        sku_desc = sos_record.get('order', {}).get('sku', {}).get('sku_desc', '')
        sku_image = sos_record.get('order', {}).get('sku', {}).get('image_url', '')
        quantity = sos_record.get('quantity', 0) or 0
        sos_id = sos_record.get('id')
        invoice_item = {
            'id': sos_id,
            'quantity': quantity,
            'packed_quantity': sos_record.get('packed_quantity', 0) or 0,
            'package_reference': package_reference,
            'picklist_number': sos_record.get('picklist_obj', {}).get('picklist_number', ''),
            'sku_code': sku_code
        }
        processed_record = {
            'package_reference': package_reference,
            'sku_code': sku_code,
            'invoice_data': [invoice_item],
            'sku_codes': [sku_code],
            'total_skus': 1,
            'order_reference': [order_reference],
            'sku_desc': [sku_desc],
            'sku_image': [sku_image],
            'order_display_key': order_reference or original_order_id
        }
        if package_reference:
            if not self.cartons_list.get(package_reference):
                self.cartons_list[package_reference] = processed_record
                self.cartons_list[package_reference]['total_items'] = sos_record.get('packed_quantity', 0) or 0
            else:
                self.cartons_list[package_reference]['invoice_data'].append(invoice_item)
                if sku_code not in self.cartons_list[package_reference]['sku_codes']:
                    self.cartons_list[package_reference]['sku_codes'].append(sku_code)
                    self.cartons_list[package_reference]['sku_desc'].append(sku_desc)
                    self.cartons_list[package_reference]['sku_image'].append(sku_image)
                    self.cartons_list[package_reference]['total_skus'] += 1
                self.cartons_list[package_reference]['total_items'] += sos_record.get('packed_quantity', 0)
        else:
            processed_record.update({
                'total_items': quantity,
                'id': sos_id
            })
            self.sku_list.append(processed_record)
        
    def get_full_invoice_no(self, instance, warehouse):
        full_invoice_number, order_no = '', ''
        order_no, invoice_prefix, full_invoice_number, check_invoice_prefix , inc_status = get_user_prefix_incremental(warehouse, 'invoice', "", create_default="INV")
        return order_no, full_invoice_number

    def update_invoice_no(self, instance, serializer, order_no, full_invoice_number, req_dat, sos_quantity_dict={}, inv_qty_dict=None, delivery_challan='', order_status_flag='customer_invoices'):
        inv_qty_dict = inv_qty_dict or {}
        if instance.order_status_flag == 'customer_invoices':
            log.info("Invoice Already Done for data %s", str(serializer.validated_data))
            return "Invoice Already Done", None
        if instance.quantity == serializer.validated_data['quantity']:
            package_reference = ''
            if req_dat.get('packed_quantity'):
                search_params = {'packed_quantity': req_dat['packed_quantity']}
                if req_dat.get('package_reference'):
                    search_params["package_reference"] = req_dat.get('package_reference')
                order_packaging = instance.orderpackaging_set.filter(**search_params).exclude(status=0)
                for order_pack in order_packaging:
                    package_reference = order_pack.package_reference
                    order_package_json_data = order_pack.json_data or {}
                    if order_package_json_data and order_package_json_data.get('serial_numbers', []):
                        serial_numbers = order_package_json_data.get('serial_numbers', [])
                        self.cartons_serial_numbers[instance.id] = serial_numbers
                        self.request_serial_numbers[instance.id] = []
                        self.list_of_serial_numbers.extend(serial_numbers)
                    StagingInfo.objects.filter(user_id=order_pack.seller_order_summary.order.user, segregation='outbound_staging', order_reference=order_pack.seller_order_summary.picklist.picklist_number, carton_no=order_pack.package_reference).update(status=1)
                    order_pack.status=0
                    order_pack.save()
            inv_qty = inv_qty_dict.get(instance.order_id, 0)
            if not inv_qty:
                inv_qty = 0
            if inv_qty + instance.quantity == instance.order.original_quantity - instance.order.cancelled_quantity:
                order = instance.order
                order.status = 5
                order.save()
            instance.invoice_number = order_no
            instance.full_invoice_number = full_invoice_number
            instance.challan_number = delivery_challan
            instance.invoice_reference = full_invoice_number
            instance.order_status_flag = order_status_flag
            self.update_wip_stock_quantity(instance.id, instance.quantity)
            if self.outbound_staging_area != 'false':
                self.staging_transaction_ids.append(instance.id)
                self.staging_loc_update_dict[(instance.id, package_reference)] = {'transaction_id': instance.id, 'quantity': instance.quantity}
            self.sos_invoices[instance.id] = full_invoice_number or delivery_challan
            sos_json_data = instance.json_data or {}
            sos_json_data.update({"invoice_generated_by": self.request.user.first_name})
            instance.json_data = sos_json_data
            instance.save()
            inv_qty_dict.setdefault(instance.order_id, 0)
            inv_qty_dict[instance.order_id] += instance.quantity
            if self.outbound_staging_area != 'false':
                if self.cartons_serial_numbers.get(instance.id):
                    serial_numbers = self.cartons_serial_numbers.get(instance.id, [])
                else:
                    serial_numbers = [serial['serial_number'] for serial in self.request_serial_numbers.get(instance.id, [])]
                self.staging_data.append({"receipt_number": instance.id, "quantity": instance.quantity, "serial_numbers": serial_numbers, "transaction_id": instance.id, "package_reference": req_dat.get('package_reference')})
                if full_invoice_number :
                    if full_invoice_number in self.invoice_wise_staging_data:
                        self.invoice_wise_staging_data.get(full_invoice_number,'').append({"receipt_number": instance.id, "quantity": instance.quantity, "serial_numbers": serial_numbers, "transaction_id": instance.id, "package_reference": req_dat.get('package_reference')})
                    else:
                        self.invoice_wise_staging_data[full_invoice_number] = [{"receipt_number": instance.id, "quantity": instance.quantity, "serial_numbers": serial_numbers, "transaction_id": instance.id, "package_reference": req_dat.get('package_reference')}]
                if delivery_challan:
                    if delivery_challan in self.invoice_wise_staging_data:
                        self.invoice_wise_staging_data.get(delivery_challan,'').append({"receipt_number": instance.id, "quantity": instance.quantity, "serial_numbers": serial_numbers, "transaction_id": instance.id, "package_reference": req_dat.get('package_reference')})
                    else:
                        self.invoice_wise_staging_data[delivery_challan] = [{"receipt_number": instance.id, "quantity": instance.quantity, "serial_numbers": serial_numbers, "transaction_id": instance.id, "package_reference": req_dat.get('package_reference')}]
        elif instance.quantity > serializer.validated_data['quantity']:
            sos_dict = deepcopy(instance.__dict__)
            rem_keys = ['_state', 'id', 'creation_date', 'updation_date']
            for rem_key in rem_keys:
                del sos_dict[rem_key]
            sos_dict['quantity'] = serializer.validated_data['quantity']
            sos_dict['invoice_number'] = order_no
            sos_dict['full_invoice_number'] = full_invoice_number
            sos_dict['invoice_reference'] = full_invoice_number
            sos_dict['order_status_flag'] = order_status_flag
            sos_dict['challan_number'] = delivery_challan
            sos_json_data = sos_dict.get('json_data', {}) or {}
            if sos_json_data:
                sos_json_data.update({'invoice_generated_by': self.request.user.first_name})
            sos_dict['json_data'] = sos_json_data
            sos_dict['account'] = instance.picklist.user.userprofile
            sos_obj = SellerOrderSummary.objects.create(**sos_dict)
            if self.request_serial_numbers.get(instance.id):
                self.request_serial_numbers[sos_obj.id] = self.request_serial_numbers[instance.id]
                self.request_serial_numbers[instance.id] = []
            self.sos_invoices[sos_obj.id] = full_invoice_number or delivery_challan

            package_reference = ''
            if req_dat.get('packed_quantity'):
                search_params = {}
                if req_dat.get('package_reference'):
                    search_params["package_reference"] = req_dat.get('package_reference')
                order_packaging = instance.orderpackaging_set.filter(**search_params).exclude(status=0)
                for order_pack in order_packaging:
                    order_package_json_data = order_pack.json_data or {}
                    package_reference = order_pack.package_reference
                    if order_package_json_data and order_package_json_data.get('serial_numbers', []):
                        serial_numbers = order_package_json_data.get('serial_numbers', [])
                        self.cartons_serial_numbers[sos_obj.id] = serial_numbers
                        self.request_serial_numbers[sos_obj.id] = []
                        self.list_of_serial_numbers.extend(serial_numbers)
                    order_pack.status= 0
                    order_pack.seller_order_summary_id = sos_obj.id
                    order_pack.save()
            instance.quantity = instance.quantity - serializer.validated_data['quantity']
            inv_qty_dict.setdefault(instance.order_id, 0)
            inv_qty_dict[instance.order_id] += serializer.validated_data['quantity']
            instance.save()
            self.update_wip_stock_quantity(instance.id, serializer.validated_data['quantity'])
            if self.outbound_staging_area != 'false':
                self.staging_transaction_ids.extend([instance.id, sos_obj.id])
                self.staging_loc_update_dict[(instance.id, package_reference)] = {'transaction_id': sos_obj.id, 'quantity': sos_obj.quantity}
                if self.cartons_serial_numbers.get(sos_obj.id):
                    serial_numbers = self.cartons_serial_numbers.get(sos_obj.id, [])
                else:
                    serial_numbers = [serial['serial_number'] for serial in self.request_serial_numbers.get(sos_obj.id, [])]
                self.staging_data.append({"receipt_number": instance.id, "quantity": serializer.validated_data['quantity'], "serial_numbers": serial_numbers, "transaction_id": instance.id, "package_reference": req_dat.get('package_reference')})
        else:
            log.info("Invoice Quantity greater than Picked Quantity for data %s", str(serializer.validated_data))
            return "Invoice Quantity greater than Picked Quantity", None
        
        return "Success", instance

    def update_wip_stock_quantity(self, sos_id, invoice_quantity):
        '''
        Updating wip location stock
        '''
        if not self.stock_df.empty:
            for index, stock_record in self.stock_df.loc[self.stock_df['receipt_number'] == sos_id].iterrows():
                min_qty = min(invoice_quantity, stock_record['quantity'])

                invoice_quantity -= min_qty
                stock_record['quantity'] -= min_qty
                stock_record['object'].quantity -= min_qty

                if invoice_quantity <= 0:
                    break

    def update_invoiced_stock_quantity(self):
        '''
        Update invoiced stock quantity
        '''
        if self.stock_df['object'].tolist():
            StockDetail.objects.bulk_update_with_rounding(self.stock_df['object'].tolist(), ['quantity'])

    def reduce_os_stock(self, instance, warehouse, req_dat):
        os_staging = StagingInfo.objects.filter(location__zone__user=warehouse.id,
                                                order_reference=instance.picklist.picklist_number,
                                                segregation='outbound_staging').exclude(status=0)
        if os_staging.exists() and instance.picklist.stock:
            receipt_type = 'Picklist-' + str(instance.picklist.picklist_number)
            os_stocks = StockDetail.objects.filter(sku_id=instance.picklist.stock.sku_id,
                                                  location_id__in=os_staging.values_list('location', flat=True),
                                                  quantity__gt=0, status=1, receipt_type=receipt_type)
            if os_stocks.exists() and req_dat.get('quantity'):
                req_quantity = float(req_dat['quantity'])
                for os_stock in os_stocks:
                    if not req_quantity:
                        break
                    if os_stock.quantity >= req_quantity:
                        os_stock.quantity = os_stock.quantity - req_quantity
                        req_quantity = 0
                    else:
                        req_quantity = req_quantity - os_stock.quantity
                        os_stock.quantity = 0
                    os_stock.save()
                #os_stocks.refresh_from_db()
                if not os_stocks.filter(quantity__gt=0):
                    os_staging.update(status=0)
            else:
                os_staging.update(status=0)

    def get_request_cartons_details(self, request_data):
        self.req_carton_skus, self.req_carton_sku_qty, self.sos_id_quantity = {}, {}, {}
        self.sku_codes_list, self.pkg_ref_list, self.picklist_numbers = [], [], []
        for req_record in request_data['data']:
            sos_id = req_record.get('id')
            sku_quantity = req_record.get('quantity')
            if not self.sos_id_quantity.get(sos_id):
                self.sos_id_quantity[sos_id] = float(sku_quantity)
            else:
                self.sos_id_quantity[sos_id] += float(sku_quantity)

            package_reference = req_record.get('package_reference')
            if not package_reference:
                continue
            sku_code = req_record.get('sku_code')
            picklist_number = req_record.get('picklist_number')
            carton_skus_list = self.req_carton_skus.get(package_reference, [])
            if not carton_skus_list:
                self.req_carton_skus[package_reference] = []
            if sku_code and sku_code not in carton_skus_list:
                carton_skus_list.append(sku_code)
                self.req_carton_skus[package_reference] = carton_skus_list

            sku_qty_key = (package_reference, sku_code)
            if not self.req_carton_sku_qty.get(sku_qty_key):
                self.req_carton_sku_qty[sku_qty_key] = sku_quantity
            else:
                self.req_carton_sku_qty[sku_qty_key] += sku_quantity

            if package_reference:
                self.pkg_ref_list.append(package_reference)
            if sku_code:
                self.sku_codes_list.append(sku_code)
            if picklist_number:
                self.picklist_numbers.append(picklist_number)

    def get_cartons_details_ord_pkg(self, warehouse):
        self.carton_sku_details, self.carton_sku_qty = {}, {}
        order_pkg_list = list(OrderPackaging.objects.filter(seller_order_summary__order__user=warehouse.id, seller_order_summary__picklist__picklist_number__in=self.picklist_numbers, \
            package_reference__in=self.pkg_ref_list, status=1, packed_quantity__gt=0).values('package_reference', 'seller_order_summary__order__sku__sku_code').distinct().annotate(total_quantity=Sum('packed_quantity')))
        for ord_pkg_record in order_pkg_list:
            package_reference = ord_pkg_record.get('package_reference', '')
            if not self.carton_sku_details.get(package_reference):
                self.carton_sku_details[package_reference] = []
            sku_code = ord_pkg_record.get('seller_order_summary__order__sku__sku_code')
            if sku_code:
                self.carton_sku_details[package_reference].append(sku_code)

            sku_qty_key = (package_reference, sku_code)
            packed_quantity = ord_pkg_record.get('total_quantity')
            if not self.carton_sku_qty.get(sku_qty_key):
                self.carton_sku_qty[sku_qty_key] = packed_quantity
            else:
                self.carton_sku_qty[sku_qty_key] += packed_quantity

    def validate_cartons(self, request_data, warehouse):
        self.validation_error_data = {}
        self.get_request_cartons_details(request_data)
        self.get_cartons_details_ord_pkg(warehouse)
        invalid_cartons = []
        for pkg_reference, req_pkg_sku_list in self.req_carton_skus.items():
            pkg_sku_list = self.carton_sku_details.get(pkg_reference, [])
            if sorted(pkg_sku_list) != sorted(req_pkg_sku_list):
                invalid_cartons.append(pkg_reference)
        for sku_qty_key, req_packed_qty in self.req_carton_sku_qty.items():
            original_packed_qty = self.carton_sku_qty.get(sku_qty_key, 0)
            if original_packed_qty != req_packed_qty:
                package_reference, sku_code = sku_qty_key
                if package_reference not in invalid_cartons:
                    invalid_cartons.append(package_reference)
        if invalid_cartons:
            self.validation_error_data = {
                "error":{
                    'message': 'Please select the all the items under %s package'%','.join(invalid_cartons),
                    'package_references': invalid_cartons
                },
                "status": 400
            }

    def validate_restrict_partial_invoice(self, warehouse):
        #get decimal config data
        decimal_limit = get_decimal_value(warehouse.id, price=True)

        order_ids = list(SellerOrderSummary.objects.filter(order__user=warehouse.id, id__in=list(self.sos_id_quantity.keys())).values_list('order_id', flat=True))
        sos_objects = list(SellerOrderSummary.objects.filter(order__user=warehouse.id, order_id__in=order_ids).values('id', 'order_id', 'order__order_reference', 'quantity', 'order_status_flag'))
        order_references, request_data_quantity = [], {}
        for sos_record in sos_objects:
            sos_id = sos_record.get('id')
            order_id = sos_record.get('order_id')
            request_quantity = float(self.sos_id_quantity.get(sos_id, 0.0) or 0.0)
            if not request_quantity and sos_record.get('order_status_flag') == 'customer_invoices':
                request_quantity = sos_record.get('quantity')
            if not request_data_quantity.get(order_id):
                request_data_quantity[order_id] = request_quantity
            else:
                request_data_quantity[order_id] += request_quantity

            order_reference = sos_record.get('order__order_reference')
            if order_reference not in order_references:
                order_references.append(order_reference)

        is_valid = True
        order_details = list(OrderDetail.objects.filter(user=warehouse.id, order_reference__in=order_references).values('id', 'original_quantity', 'cancelled_quantity'))
        for order_record in order_details:
            order_id = order_record.get('id')
            original_qty = order_record.get('original_quantity', 0) or 0
            cancelled_qty = order_record.get('cancelled_quantity', 0) or 0
            order_availble_qty = original_qty - cancelled_qty
            request_qty = truncate_float(request_data_quantity.get(order_id), decimal_limit)
            order_availble_qty = truncate_float(order_availble_qty, decimal_limit)
            if order_availble_qty != request_qty:
                is_valid = False
                break

        return is_valid

    def update(self, request, *args, **kwargs):
        instance_dict = OrderedDict()
        warehouse = kwargs['warehouse']
        misc_types = ['alternative_location_for_partial_pick', 'short_close_order', 'export_invoice', 'outbound_staging_area','order_wise_inv','dc_for_intra_st', 'restrict_partial_invoice']
        misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
        picklist_numbers = []
        alt_location_pic = misc_dict.get('alternative_location_for_partial_pick','false')
        short_close_order = misc_dict.get('short_close_order','false')
        self.export_invoice = misc_dict.get('export_invoice','false')
        self.outbound_staging_area = misc_dict.get('outbound_staging_area','false')
        self.order_wise_inv = misc_dict.get('order_wise_inv','false')
        self.dc_for_intra_st = misc_dict.get('dc_for_intra_st','false')
        self.restrict_partial_invoice = misc_dict.get('restrict_partial_invoice', 'false')
        extra_params = {'request_user': request.user.username, 'Warehouse': warehouse.username, 'Authorization': request.headers.get('Authorization')}
        ret_status = "Success"
        try:
            request_data = loads(self.request.body)
        except Exception:
            request_data = {}
        if kwargs.get('pk') != 'update_bulk':
            instance = self.queryset.get(pk=kwargs.get('pk'))
            serializer = SOSSerializer(instance, request_data, partial=True)
            if serializer.is_valid():
                instance_dict[instance] = [{'serializer': serializer, 'dat': {}}]
            else:
                return Response(serializer.errors)
        else:
            self.validate_cartons(request_data, warehouse)
            if self.validation_error_data:
                return JsonResponse(self.validation_error_data, status=400)
            if self.restrict_partial_invoice not in ['false', False, '', None]:
                is_valid = self.validate_restrict_partial_invoice(warehouse)
                if not is_valid:
                    return JsonResponse({'error': {'message': 'Partial Invoice Not Allowed!'}}, status=400)
            invoice_additional_info = {}
            self.request_serial_numbers, self.cartons_serial_numbers, self.sos_invoices, self.list_of_serial_numbers, self.sos_ids= {}, {}, {}, [], []
            serial_sos_ids, serial_picklist_numbers, serial_package_references, serial_sos_package_reference, serial_pending_list = [], [], [], {}, []
            for dat in request_data['data']:
                sos_id = dat.get('id')
                if sos_id and sos_id not in self.sos_ids:
                    self.sos_ids.append(sos_id)
                if not invoice_additional_info and self.export_invoice == 'true':
                    invoice_additional_info = dat.get('invoice_data', {})
                serial_no_info = dat.get('serials', [])
                is_serilized = dat.get('enable_serial_based', 0)
                if is_serilized or serial_no_info:
                    picklist_number = dat.get('picklist_number')
                    package_reference = dat.get('package_reference', '')
                    if serial_no_info:
                        if not self.request_serial_numbers.get(sos_id):
                            self.request_serial_numbers[sos_id] = serial_no_info
                        else:
                            self.request_serial_numbers[sos_id].extend(serial_no_info)
                    else:
                        if sos_id not in serial_sos_ids:
                            serial_sos_ids.append(sos_id)
                        if picklist_number and picklist_number not in serial_picklist_numbers:
                            serial_picklist_numbers.append(dat.get('picklist_number'))
                        if package_reference:
                            serial_package_references.append(package_reference)
                            if not serial_sos_package_reference.get(sos_id):
                                serial_sos_package_reference[sos_id] = [package_reference]
                            else:
                                serial_sos_package_reference[sos_id].append(package_reference)
                        serial_pending_list.append(dat)

                qty = dat['quantity']
                if not qty:
                    qty = 0
                if float(qty):
                    instance = self.queryset.filter(pk=dat['id'])
                    if instance.exists():
                        instance = instance[0]
                    else:
                        return Response({"message": "Data not found!"}, status=400)
                    if instance.picklist:
                        pick_no = instance.picklist.picklist_number
                        if pick_no not in picklist_numbers:
                            picklist_numbers.append(instance.picklist.picklist_number)
                    serializer = SOSSerializer(instance, dat, partial=True)
                    if serializer.is_valid():
                        if instance not in instance_dict:
                            instance_dict[instance] = [{'serializer': serializer, 'dat': dat}]
                        else:
                            instance_dict[instance].append({'serializer': serializer, 'dat': dat})
                    else:
                        return Response(serializer.errors)
            if serial_pending_list:
                self.get_serial_numbers_per_sos(serial_sos_ids, serial_picklist_numbers, serial_package_references, serial_sos_package_reference, serial_pending_list)
            if self.sos_ids:
                self.get_wip_location_stocks(warehouse, self.sos_ids)
        if alt_location_pic == 'false' and short_close_order == 'false':
            picklist_obj = Picklist.objects.filter(picklist_number__in=picklist_numbers, order__user=warehouse.id, status='open')
            if picklist_obj:
                return Response({"message": 'Picking in Progress',"invoice_no":''})
        self.staging_data = []
        self.invoice_wise_staging_data = {}
        self.staging_loc_update_dict={}
        self.staging_transaction_ids = []
        seller_order_summary_ids = []
        for data in request_data['data']:
            if data['id'] not in seller_order_summary_ids:
                seller_order_summary_ids.append(data['id'])
        seller_summary = SellerOrderSummary.objects.filter(id__in = seller_order_summary_ids,order__user=warehouse.id)
        sos_quantity_dict = dict(SellerOrderSummary.objects.filter(id__in = seller_order_summary_ids).exclude(order_status_flag='cancelled').values_list('order_id').annotate(Sum('quantity')))
        dict_ = {'empty_supplier':[]}
        order_ids = []
        order_customer_id = []
        order_type = ''
        customer_state_type = 'inter_state'
        order_inv_dict = {}
        for seller_obj in seller_summary:
            order_ids.append(seller_obj.order.id)
            if seller_obj.order.customer_id not in order_customer_id:
                order_customer_id.append(seller_obj.order.customer_id)
            order_type = seller_obj.order.order_type
            dict_['empty_supplier'].append(seller_obj)
        if len(dict_['empty_supplier']) == len(seller_summary):
            supplier_inventory = 'false'
        inv_qty_dict = dict(SellerOrderSummary.objects.filter(order_id__in = order_ids, order_status_flag='customer_invoices').values_list('order_id').annotate(Sum('quantity')))
        cust_tax_type_dict = {}
        if order_customer_id and order_type.lower() == 'stocktransfer':
            customer_master = CustomerMaster.objects.filter(user=warehouse.id,customer_id__in=order_customer_id).values('tax_type','customer_id')
            for each_cus in customer_master:
                if str(each_cus.get('customer_id','')) not in cust_tax_type_dict:
                    tax_type= each_cus.get('tax_type','intra_state')
                    if tax_type == 'inter_state':
                        customer_state_type = 'inter_state' 
                    else:
                        customer_state_type = 'intra_state'   
                    cust_tax_type_dict[str(each_cus.get('customer_id',''))] = customer_state_type
        if supplier_inventory == 'true':
            invoice_numbers = []
            full_invoice_number = ''
            for supplier,seller_obj in dict_.items():
                with transaction.atomic('default'):
                    if not seller_obj:
                        continue
                    for obj in seller_obj:
                        serializer = instance_dict[obj]['serializer']
                        if 'quantity' in serializer.validated_data and serializer.validated_data['quantity']:
                            if obj.order_status_flag != 'processed_orders':
                                ret_status = 'Order Already Processed'
                    order_no, full_invoice_number = self.get_full_invoice_no(seller_obj[0], warehouse)
                    if full_invoice_number not in invoice_numbers:
                        invoice_numbers.append(full_invoice_number)
                    log.info('Invoice number generated %s' % full_invoice_number)
                    if full_invoice_number:
                        for obj in seller_obj:
                            serializer = instance_dict[obj]['serializer']
                            ret_status, instance = self.update_invoice_no(obj, serializer, order_no, full_invoice_number,
                                                                                            instance_dict[obj]['dat'], 
                                                                                            sos_quantity_dict=sos_quantity_dict,
                                                                                            inv_qty_dict=inv_qty_dict)
                if full_invoice_number and seller_obj:
                    for obj in seller_obj:
                        gen_data_dict = {seller_summary_const: ['%s::%s' %(obj.invoice_number, obj.financial_year)],
                        'delivery_challan': obj.challan_number,'Marketplace': obj.order.marketplace}
                        set_pricing_master_details_for_invoice(warehouse, full_invoice_number)
                        errors_list, invoice_data = get_customer_invoice_tab_func(request, warehouse, gen_data_dict, invoice_creation=True)
                        if isinstance(invoice_data, list) and invoice_data:
                            invoice_data = invoice_data[0]
                        serial_numbers_details = self.get_formatted_serial_numbers()
                        insert_shipment_invoice_data_fun(warehouse.id, full_invoice_number, "invoice", extra_params=extra_params)
                        log.info(P_INTEGRATION_LOG % str(obj.full_invoice_number))
                        filters = {
                            "invoice_number": full_invoice_number,
                            "serial_numbers":  serial_numbers_details,
                            "invoice_data": invoice_data,
                            "request_META": {"HTTP_REFERER": request.META.get("HTTP_REFERER")},
                            "original_order_ids": [obj.order.original_order_id]
                        }
                        webhook_integration_3p(warehouse.id, "invoice_creation", filters=filters)
            if full_invoice_number and self.export_invoice == 'true':
                self.update_invoice_additional_info(warehouse, invoice_additional_info, full_invoice_number)
            return Response({"message": ret_status,"invoice_no":invoice_numbers})
        else:
            full_invoice_number,instance,st_challan_number = '', '',''
            pending_picklists = Picklist.objects.filter(order__user=warehouse.id, status='open', picklist_number__in=picklist_numbers)
            if pending_picklists.exists():
                ret_status = "Please Complete Picking"
                return Response({"message": ret_status,"invoice_no":[]})
            instance_invoice_number, instance_financial_year, instance_challan_number, instance_order_order_type, instance_order_marketplace = 5*['']
            order_status_flag = 'customer_invoices'
            self.original_order_ids = []
            with transaction.atomic('default'):
                for instance, instance_vals in instance_dict.items():
                    if self.order_wise_inv == 'true':
                        if (str(instance.order.order_reference)) not in order_inv_dict:
                            full_invoice_number,st_challan_number = '', ''
                            instance_invoice_number, instance_financial_year, instance_challan_number, instance_order_order_type, instance_order_marketplace = 5*['']
                            order_status_flag = 'customer_invoices'
                        else:
                            full_invoice_number,st_challan_number,instance_invoice_number, instance_financial_year, instance_challan_number, instance_order_order_type, instance_order_marketplace,order_status_flag = self.return_customer_wise_inv_det(order_inv_dict.get(str(instance.order.order_reference),''))
                    if instance.order.original_order_id not in self.original_order_ids:
                        self.original_order_ids.append(instance.order.original_order_id)
                    for order_packing_serializer in instance_vals:
                        serializer = order_packing_serializer['serializer']
                        if 'quantity' in serializer.validated_data and serializer.validated_data['quantity']:
                            if instance.order_status_flag != 'processed_orders':
                                ret_status = 'Order Already Processed'
                                continue
                            if not (full_invoice_number or st_challan_number):
                                if instance.order.order_type.lower() == 'stocktransfer' and cust_tax_type_dict.get(str(instance.order.customer_id),'intra_state') == 'intra_state' and self.dc_for_intra_st == 'true':
                                    st_challan_number, cus_type = update_dc_sos(warehouse, instance, status_flag='delivery_challans',update_flag=False)
                                    instance_invoice_number= ''
                                    order_no = ''
                                    order_status_flag = 'delivery_challans'
                                    

                                else:    
                                    order_no, full_invoice_number = self.get_full_invoice_no(instance, warehouse)
                                    instance_invoice_number= order_no
                                    instance_challan_number= instance.challan_number
                                    order_status_flag = 'customer_invoices'
                                
                                instance_financial_year= instance.financial_year
                                instance_order_order_type= instance.order.order_type
                                instance_order_marketplace= instance.order.marketplace

                                if self.order_wise_inv == 'true':
                                    order_inv_dict[str(instance.order.order_reference)]= { 'full_invoice_number' : full_invoice_number,
                                                                                            'instance' : instance,
                                                                                            'st_challan_number' : st_challan_number,
                                                                                            'instance_invoice_number' :instance_invoice_number,
                                                                                            'instance_financial_year' : instance_financial_year,
                                                                                            'instance_challan_number' :instance_challan_number,
                                                                                            'instance_order_order_type' :instance_order_order_type,
                                                                                            'instance_order_marketplace': instance_order_marketplace,
                                                                                            'order_status_flag':order_status_flag}

                            log.info('Invoice number generated %s' % full_invoice_number)
                            if full_invoice_number or st_challan_number:
                                self.reduce_os_stock(instance, warehouse, order_packing_serializer['dat'])
                                ret_status, instance = self.update_invoice_no(instance, serializer, instance_invoice_number, full_invoice_number,
                                                            order_packing_serializer['dat'],  sos_quantity_dict=sos_quantity_dict,
                                                            inv_qty_dict=inv_qty_dict,delivery_challan=st_challan_number,order_status_flag=order_status_flag)
                # else:
                #     serializer.save()
                #     ret_status = "Success"
                if not instance:
                    log.info('check for this %s' % str(full_invoice_number))
            if self.order_wise_inv == 'true':
                customer_wise_inv_nos = []
                for each_cus,inv_details in order_inv_dict.items():
                    each_inv =  self.customer_inv_misc_func(request,warehouse,inv_details,invoice_additional_info)
                    customer_wise_inv_nos.append(each_inv)
                if not self.stock_df.empty:
                    self.update_invoiced_stock_quantity()
                return Response({"message": ret_status,"invoice_no":customer_wise_inv_nos})

            reference_number = full_invoice_number or st_challan_number
            invoice_data,serial_numbers_details = self.get_instance_invoice_data(request,warehouse,instance_invoice_number,full_invoice_number,st_challan_number,instance_financial_year,instance_challan_number,instance_order_marketplace)
            if reference_number:
                dc_check = False
                if str(instance_order_order_type).lower()=="stocktransfer":
                    if order_status_flag == 'delivery_challans' and st_challan_number:
                        dc_check = True
                    self.asn_creation(request, warehouse, reference_number, invoice_data, serial_numbers=serial_numbers_details,dc_check=dc_check)
                    insert_shipment_invoice_data_fun(warehouse.id, reference_number, "stocktransfer", extra_params=extra_params)
                else:
                    insert_shipment_invoice_data_fun(warehouse.id, full_invoice_number, "invoice", extra_params=extra_params)
                set_pricing_master_details_for_invoice(warehouse, reference_number)
                if full_invoice_number:
                    log.info(P_INTEGRATION_LOG % str(full_invoice_number))
                    filters = {
                                "invoice_number": full_invoice_number,
                                "serial_numbers":  serial_numbers_details,
                                "invoice_data": invoice_data,
                                "request_META": {"HTTP_REFERER": request.META.get("HTTP_REFERER")},
                                "original_order_ids": self.original_order_ids
                            }
                    webhook_integration_3p(warehouse.id, "invoice_creation", filters=filters)
            if full_invoice_number and self.export_invoice == 'true':
                self.update_invoice_additional_info(warehouse, invoice_additional_info, full_invoice_number)

            if self.staging_data:
                location, staging_name, location_available = get_staging_lane_for_order(warehouse, instance_order_order_type, invoice=True, location=False, outbound_staging_area=self.outbound_staging_area)
                staginglane = StagingLaneSet()
                if reference_number:
                    staginglane.put(warehouse.id, self.staging_data, staging_name, reference_number, 'invoice', inactivate_serial_number=True, package_references = self.pkg_ref_list)
                self.self_staging_loc_update(warehouse,request,staging_name, package_references = self.pkg_ref_list)
            if not self.stock_df.empty:
                self.update_invoiced_stock_quantity()
            return Response({"message": ret_status,"invoice_no":[reference_number]})

    def self_staging_loc_update(self,warehouse,request,staging_name, package_references=[]):
        update_staging_data, new_staging_locations = [], []
        stage_filter = {'warehouse':warehouse.id, 'status':3, 'segregation':staging_name, 'transaction_id__in':self.staging_transaction_ids}
        staging_locs = StagingLocation.objects.filter(**stage_filter)
        for staging_loc in staging_locs:
            bin_number = ''
            if staging_loc.carton:
                bin_number = staging_loc.carton.bin_number
            unique_key = (staging_loc.transaction_id, bin_number)
            inv_staging_loc_info = self.staging_loc_update_dict.get(unique_key)
            if inv_staging_loc_info:
                invoice_qty = inv_staging_loc_info.get('quantity')
                transaction_id = inv_staging_loc_info.get('transaction_id')
                if invoice_qty == staging_loc.quantity:
                    staging_loc.transaction_id = transaction_id
                    stage_json_data = staging_loc.json_data or {}
                    stage_json_data.update({'is_invoiced': True})
                    staging_loc.json_data = stage_json_data
                else:
                    staging_loc.quantity -= invoice_qty
                    new_staging_loc = deepcopy(staging_loc.__dict__)
                    try:
                        del new_staging_loc['id']
                        del new_staging_loc['_state']
                    except Exception:
                        pass
                    stage_json_data = new_staging_loc.get('json_data', {}) or {}
                    stage_json_data.update({'is_invoiced': True})
                    new_staging_loc.update({'transaction_id': transaction_id, 'quantity': invoice_qty, 'json_data': stage_json_data})
                    new_staging_locations.append(StagingLocation(**new_staging_loc))
                update_staging_data.append(staging_loc)
        StagingLocation.objects.bulk_update_with_rounding(update_staging_data, ['transaction_id', 'quantity', 'json_data'])
        if new_staging_locations:
            StagingLocation.objects.bulk_create_with_rounding(new_staging_locations)
        

    def get_formatted_serial_numbers(self):
        serial_numbers_details = {}
        for sos_id, serials in self.request_serial_numbers.items():
            serial_numbers_details[sos_id] = []
            for serial_number_dict in serials:
                serial_numbers_details[sos_id].append(serial_number_dict.get('serial_number'))
        return serial_numbers_details

    def is_dispensing_order_types(self, warehouse: User, order_types: list = []) -> list:
        non_dispensing_order_types = []
        order_type_zone_mapping_list = list(OrderTypeZoneMapping.objects.filter(user_id=warehouse.id, order_type__in=order_types, status=1).values(
            'order_type', 'json_data'))
        for order_type_record in order_type_zone_mapping_list:
            json_data = order_type_record.get('json_data', {}) or {}
            order_type = order_type_record.get('order_type', '')
            if json_data.get('dispensing', False) == False and order_type not in non_dispensing_order_types:
                non_dispensing_order_types.append(order_type)
        return non_dispensing_order_types

    def get_wip_location_stocks(self, warehouse: User, sos_ids: list = [], order_types: list = []):
        '''
        Get WIP location stocks
        '''
        non_dispensing_order_types = []
        if order_types:
            non_dispensing_order_types = self.is_dispensing_order_types(warehouse, order_types)

        stock_objects = StockDetail.objects.filter(sku__user=warehouse.id, receipt_number__in=sos_ids, quantity__gt=0, location__zone__zone='WIPZONE')
        if not non_dispensing_order_types:
            stock_objects = stock_objects.filter((Q(receipt_type='so_picking') & Q(sku__dispensing_enabled=0)) | (Q(receipt_type='so_dispense') & Q(sku__dispensing_enabled=1)))
        else:
            stock_objects = stock_objects.filter(receipt_type__in=['so_picking', 'so_dispense'])
        self.stock_df = pd.DataFrame(stock_objects.values('id', 'receipt_number', 'receipt_type', 'sku_id', 'grn_number', 'quantity'))

        stock_objects = list(stock_objects)
        for obj in stock_objects:
            self.stock_df.loc[self.stock_df['id'] == obj.id, 'object'] = obj

    def get_instance_invoice_data(self,request,warehouse,instance_invoice_number,full_invoice_number,st_challan_number,instance_financial_year,instance_challan_number,instance_order_marketplace):
        invoice_data, serial_numbers_details = '', {}
        if full_invoice_number and instance_invoice_number:
                gen_data_dict = {seller_summary_const: ['%s::%s' %(instance_invoice_number, instance_financial_year)],
                    'delivery_challan': instance_challan_number,'Marketplace': instance_order_marketplace}
                errors_list, invoice_data = get_customer_invoice_tab_func(request, warehouse, gen_data_dict, invoice_creation=True)
                if isinstance(invoice_data, list) and invoice_data:
                    invoice_data = invoice_data[0]
                serial_numbers_details = self.get_formatted_serial_numbers()
        if st_challan_number :
            inv_request = RequestFactory
            inv_request.user = warehouse
            inv_request.warehouse = warehouse
            inv_request.method= 'GET'
            inv_request.GET = {
                'challan_no' : [st_challan_number],
                'delivery_challan': 'true',
                'invoice_data':True
            }
            serial_numbers_details = self.get_formatted_serial_numbers()
        return invoice_data,serial_numbers_details

    def get_serial_numbers_per_sos(self, sos_ids, picklist_numbers, package_references, sos_package_reference, serial_pending_list):
        sos_serial_numbers = self.get_serial_number_for_sos(sos_ids, picklist_numbers, package_references, sos_package_reference)
        for each_sos in serial_pending_list:
            sos_id = each_sos.get('id', '')
            package_reference = each_sos.get('package_reference', '') or ''
            if not package_reference:
                unique_key = (sos_id, package_reference)
                serial_numbers = sos_serial_numbers.get(unique_key, []) or []
                if not self.request_serial_numbers.get(sos_id):
                    self.request_serial_numbers[sos_id] = serial_numbers
                else:
                    self.request_serial_numbers[sos_id].extend(serial_numbers)

    def get_serial_number_for_sos(self, sos_ids, picklist_numbers, package_references, sos_package_reference):
        invoice_numbers, ex_transaction_ids = [], []
        package_reference_serial_numbers, sos_sku_stock, sos_serial_numbers= {}, {}, {}

        sos_data_list = list(SellerOrderSummary.objects.filter(order__user=self.request.warehouse.id, picklist__picklist_number__in=picklist_numbers).\
            values('id', 'picklist__stock_id', 'picklist_id', 'full_invoice_number', 'challan_number', 'order_status_flag', 'order__sku__sku_code'))
        for sos_record in sos_data_list:
            sku_code = sos_record.get('order__sku__sku_code', '')
            invoice_no = sos_record.get('full_invoice_number')
            sos_id = sos_record.get('id')
            if invoice_no and invoice_no not in invoice_numbers:
                invoice_numbers.append(invoice_no)
                ex_transaction_ids.append(sos_id)
            picklist_id = sos_record.get('picklist_id')
            if picklist_id and picklist_id not in ex_transaction_ids:
                ex_transaction_ids.append(picklist_id)
            order_status_flag = sos_record.get('order_status_flag', '') or ''
            if order_status_flag.lower() == 'processed_orders':
                sos_sku_stock[(sku_code, picklist_id)] = sos_record.get('id')

        invoice_serial_tns = []
        if package_references:
            package_json_data = list(OrderPackaging.objects.filter(seller_order_summary_id__in=sos_ids, seller_order_summary__order__user=self.request.warehouse.id, package_reference__in=package_references).values('json_data', 'package_reference'))
            for each_package in package_json_data:
                package_reference = each_package.get('package_reference', '')
                if not package_reference_serial_numbers.get(package_reference):
                    package_reference_serial_numbers[package_reference] = each_package.get('json_data', {}).get('serial_numbers', [])
        for serial_no_record in picklist_serial_tns:
            serial_id = serial_no_record.get('serial_id')
            if serial_id not in invoice_serial_tns:
                serial_number = serial_no_record.get('serial_number')
                transaction_id = serial_no_record.get('transaction_id')
                sku_code = serial_no_record.get('sku_code')
                sos_id = sos_sku_stock.get((sku_code, transaction_id))
                package_reference_list = sos_package_reference.get(sos_id, [])
                if package_reference_list:
                    for package in package_reference_list:
                        unique_key = (sos_id, package)
                        package_serial_numbers = package_reference_serial_numbers.get(package)
                        if package_serial_numbers and serial_number in package_serial_numbers:
                            if not sos_serial_numbers.get(unique_key):
                                sos_serial_numbers[unique_key] = [serial_no_record]
                            else:
                                sos_serial_numbers[unique_key].append(serial_no_record)
                else:
                    unique_key = (sos_id, '')
                    if not sos_serial_numbers.get(unique_key):
                        sos_serial_numbers[unique_key] = [serial_no_record]
                    else:
                        sos_serial_numbers[unique_key].append(serial_no_record)
        return sos_serial_numbers

    def update_invoice_additional_info(self, warehouse, invoice_additional_info, full_invoice_number):
        invoice_additional_data = {
            'warehouse': warehouse,
            'full_invoice_number': full_invoice_number,
            'json_data': invoice_additional_info,
            'account_id': warehouse.userprofile.id
        }
        InvoiceAdditionalInfo.objects.update_or_create(**invoice_additional_data)

    def asn_creation(self, request, warehouse, full_invoice_number, invoice_data, serial_numbers={}, dc_check=False):
        try:
            user = None
            po_numbers_list = []
            so_st_po_check = False  
            #delivery challan asn payload
            if dc_check:
                sos = SellerOrderSummary.objects.filter(challan_number=full_invoice_number, order__user=warehouse.id, quantity__gt=0, order_status_flag='delivery_challans')
                asn_payload, user = prepare_grn_asn_input_data(sos, warehouse, full_invoice_number, invoice_data=invoice_data, serial_numbers=serial_numbers,dc_check=True)
            else:
                sos = SellerOrderSummary.objects.filter(full_invoice_number=full_invoice_number, order__user=warehouse.id, quantity__gt=0, order_status_flag='customer_invoices')
                asn_payload, user = prepare_grn_asn_input_data(sos, warehouse, full_invoice_number, invoice_data=invoice_data, serial_numbers=serial_numbers,dc_check=False)
            for e_row in asn_payload.get("items"):
                if len(e_row.get("retest_date", ""))==10:
                    e_row["retest_date"] = e_row["retest_date"] + " 23:59"
                if len(e_row.get("expiry_date", ""))==10:
                    e_row["expiry_date"] = e_row["expiry_date"] + " 23:59"
                if len(e_row.get("manufactured_date", ""))==10:
                    e_row["manufactured_date"] =  e_row["manufactured_date"] + " 00:00"  

            valid_ser = GRNSerializer(data=asn_payload)
            message, asn_number, status, error_message_dict= "", "", True, {}
            if valid_ser.is_valid():
                asn_payload = valid_ser.validated_data
            if asn_payload.get('po_reference',''):
                so_st_po_check = True
                        
            validated_error_dict, po_dict_data, grn_data_list, po_numbers_list, grn_extra_dict, _, po_ref_list = POGRNValidation().validate(asn_payload, warehouse=user, is_asn=True,so_st_po=so_st_po_check)
            if validated_error_dict:
                status= False
                error_message_dict = validated_error_dict
            elif po_dict_data:
                asn_number, status, message = ASNSet().create(request, warehouse=user, data=asn_payload, grn_data_list=grn_data_list, grn_extra_dict=grn_extra_dict)
            
            if not status:
                error_message_dict = message if message else "Something went Wrong"
            
            for po_no in po_numbers_list:
                cache.delete(str(po_no))
            
            log.info(
                generate_log_message(
                    "STASNValidationProcessedPayload", asn_number=asn_number, status=status,
                    validation_errors=validated_error_dict, errors=error_message_dict,
                    request_payload=asn_payload, grn_data_list=grn_data_list
                    )) 
        except Exception as e:
            log.info(
                generate_log_message(
                    "STASNCreationFailed",
                    request_payload=asn_payload, grn_data_list=grn_data_list, errors = str(e)
                    )) 
            log.info("ST ASN Creation Failed asn_payload: %s error: %s "%(str(asn_payload), str(e)))
        
        for po_no in po_numbers_list:
            cache.delete(str(po_no))

        return True

    def return_customer_wise_inv_det(self,order_inv_dict):
        full_invoice_number = order_inv_dict.get('full_invoice_number','')
        st_challan_number = order_inv_dict.get('st_challan_number','')
        instance_invoice_number = order_inv_dict.get('instance_invoice_number','')
        instance_financial_year = order_inv_dict.get('instance_financial_year','')
        instance_challan_number = order_inv_dict.get('instance_challan_number','')
        instance_order_order_type = order_inv_dict.get('instance_order_order_type','')
        instance_order_marketplace = order_inv_dict.get('instance_order_marketplace','')
        order_status_flag = order_inv_dict.get('order_status_flag','')
        return full_invoice_number ,st_challan_number,instance_invoice_number, instance_financial_year, instance_challan_number, instance_order_order_type, instance_order_marketplace,order_status_flag
    
    def customer_inv_misc_func(self,request,warehouse,order_inv_dict,invoice_additional_info):
        full_invoice_number = order_inv_dict.get('full_invoice_number','')
        st_challan_number = order_inv_dict.get('st_challan_number','')
        instance_invoice_number = order_inv_dict.get('instance_invoice_number','')
        instance_financial_year = order_inv_dict.get('instance_financial_year','')
        instance_challan_number = order_inv_dict.get('instance_challan_number','')
        instance_order_order_type = order_inv_dict.get('instance_order_order_type','')
        instance_order_marketplace = order_inv_dict.get('instance_order_marketplace','')
        order_status_flag = order_inv_dict.get('order_status_flag','')
        extra_params = {'request_user': request.user.username, 'Warehouse': warehouse.username, 'Authorization': request.headers.get('Authorization')}
        reference_number = full_invoice_number or st_challan_number
        invoice_data,serial_numbers_details = self.get_instance_invoice_data(request,warehouse,instance_invoice_number,full_invoice_number,st_challan_number,instance_financial_year,instance_challan_number,instance_order_marketplace)
        if reference_number:
            dc_check = False
            if str(instance_order_order_type).lower()=="stocktransfer" :
                if order_status_flag == 'delivery_challans' and st_challan_number:
                    dc_check = True
                self.asn_creation(request, warehouse, reference_number, invoice_data, serial_numbers=serial_numbers_details,dc_check=dc_check)
                insert_shipment_invoice_data_fun(warehouse.id, reference_number, "stocktransfer", extra_params=extra_params)
            else:
                insert_shipment_invoice_data_fun(warehouse.id, full_invoice_number, "invoice", extra_params=extra_params)
            set_pricing_master_details_for_invoice(warehouse, reference_number)
            if full_invoice_number:
                log.info(P_INTEGRATION_LOG % str(full_invoice_number))
                filters = {
                                "invoice_number": full_invoice_number,
                                "serial_numbers":  serial_numbers_details,
                                "invoice_data": invoice_data,
                                "request_META": {"HTTP_REFERER": request.META.get("HTTP_REFERER")},
                                "original_order_ids": self.original_order_ids,
                            }
                webhook_integration_3p(warehouse.id, "invoice_creation", filters=filters)
            # invoice_3p_integration(request, user, full_invoice_number, invoice_data=invoice_data, serial_numbers=serial_numbers_details)
        if full_invoice_number and self.export_invoice == 'true':
            self.update_invoice_additional_info(warehouse, invoice_additional_info, full_invoice_number)
        
        if self.invoice_wise_staging_data:
            location, staging_name, location_available = get_staging_lane_for_order(warehouse, instance_order_order_type, invoice=True, location=False, outbound_staging_area=self.outbound_staging_area)
            staginglane = StagingLaneSet()
            if reference_number:
                staginglane.put(warehouse.id, self.invoice_wise_staging_data.get(reference_number,[]), staging_name, reference_number, 'invoice', inactivate_serial_number=True, package_references = self.pkg_ref_list)
            self.self_staging_loc_update(warehouse,request,staging_name)
        return reference_number

class InvoiceSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None):
        return None
 
    def get(self, *args, **kwargs):
        """
        This method handles the GET request for retrieving invoice data.
        It performs various operations such as date format validation, filtering based on search parameters,
        retrieving seller order summaries, preparing serial numbers, taxes dictionary, status dictionary and preparing final invoice data.
        """
        self.set_user_credientials()
        #prepare request data
        self.format_request_data()
        #prepare search params
        errors, sos_search_params = self.validate_date_formats_and_prepare_search_params()
        if errors:
            return errors

        #misc values
        self.fetch_misc_values()

        #fetch sos data
        errors, invoice_numbers, seller_order_summaries, seller_order_summary_data, total_count, page_info = self.fetch_master_data(sos_search_params)
        if errors:
            return errors 
        
        #prepare tax dictionary
        tax_mrp_dict = self.prepare_unique_tax_data(seller_order_summaries)

        #prepare gst dictionary
        gst_dict = self.prepare_customer_gst_data(seller_order_summaries)

        #prepare customer dictionary
        cust_dict = self.prepare_customer_details(seller_order_summaries)

        #prepare quantity details
        uniq_inv_qty, uniq_item_inv_qty, order_references, status_dict, serialized_data = self.prepare_unique_quantity_details(seller_order_summary_data, tax_mrp_dict)

        #get order charges data
        order_charges_data = get_invoice_charges_data(self.warehouse, order_references)
    
        #prepare irn details
        irn_info_dict = self.prepare_unique_irn_details(invoice_numbers)
        
        #fetch serialized data
        self.batch_level_sr_no_data, self.lpn_level_sr_no_data = (({},{}), get_serial_number_transaction_data(self.warehouse, serialized_data, True))[len(serialized_data.get('sku_codes',[]))>0]
        
        invoice = ''
        try:
            total_list = []
            for invoice in invoice_numbers:
                final_list = []
                self.inv_quantity,self.order_mrp = 0,0
                self.irn_number,self.acknowledgement_number,self.acknowledgement_date,cancelled_date,self.einvoice_status = '','','','',''
                invoice_order_dict,batch_dict,invoice_item_dict,order_key_dict,final_dict,invoice_header_dict,lpn_dict = {},{},{},{},{},{},{}
                for data in seller_order_summary_data: 
                    reference_number = data.get('invoice_reference', '')
                    if invoice == reference_number:
                        full_invoice_number = reference_number
                        order_reference = data.get('order__order_reference', '')
                        invoice_sku_code = data.get('order__sku__sku_code', '')
                        batch_key = full_invoice_number + order_reference + invoice_sku_code
                        uniq_comb = (full_invoice_number)
                        inv_quantity = uniq_inv_qty[uniq_comb]
                        order_id = data.get('order_id', '')
                        extra_details = data.get('order__json_data','')
                        order_type = data.get('order__order_type','')
                        order_date = data['order__creation_date'].astimezone(pytz.timezone(self.time_zone)).strftime(date_format_const)
                        created_by = data.get('json_data__invoice_generated_by', '') or ''
                        cancelled_by = data.get('json_data__cancelled_by', '') or ''
                        cancellation_reason = data.get('json_data__cancellation_reason', '') or ''
                        line_quantity = data.get('quantity', 0)
                        lpn_number = data.get('lpn_number', '') or ''
                        order_charges = order_charges_data.get(full_invoice_number, {})
                        shipping_charges = order_charges.get('shipping_charges', 0.00) or 0.00
                        other_charges = order_charges.get('other_charges', 0.00) or 0.00
                        order_charges_list = order_charges.get('order_charges', {})
                        
                        
                        self.fetch_customer_gst_and_irn_data(irn_info_dict, cust_dict, gst_dict, invoice)
                             
                        mrp, unit_price = self.fetch_mrp_and_price_data(data)
                        
                        invoice_amount_without_tax = truncate_float(line_quantity * unit_price,self.decimal_limit)
                                            
                        self.calculate_tax_values(tax_mrp_dict, order_id, invoice_amount_without_tax)


                        uniq_comb = (full_invoice_number,order_reference,invoice_sku_code,mrp, unit_price)
                        inv_item_quantity = uniq_item_inv_qty[uniq_comb]

                        cancelled_date, invoice_date = self.format_data_formats(cancelled_date, status_dict, invoice)
                        
                        if full_invoice_number not in invoice_header_dict:
                            invoice_header_dict[full_invoice_number] = {
                                'warehouse': self.warehouse.username,
                                'invoice_number' : invoice,
                                'invoice_quantity' : truncate_float(inv_quantity,self.decimal_limit),
                                'invoice_date' : invoice_date,
                                'invoice_status' : status_dict.get(invoice, {}).get('order_status_flag', ''),
                                'invoiced_by' : cust_dict[invoice]['json_data__invoice_generated_by'],
                                'cancelled_date' : cancelled_date,
                                'customer_id' : cust_dict[invoice]['order__customer_id'],
                                'customer_gst' : self.customer_gst,
                                'customer_name' : cust_dict[invoice]['order__customer_name'],
                                'customer_state' : cust_dict[invoice]['order__state'],
                                'customer_pincode' : cust_dict[invoice]['order__pin_code'],
                                'irn_number' : self.irn_number,
                                'acknowledgement_number' : self.acknowledgement_number,
                                'acknowledgement_date' : self.acknowledgement_date,
                                'einvoice_status': self.einvoice_status,
                                'discount_amount': truncate_float(self.discount_amount, self.decimal_limit),
                                'invoice_amount_with_tax': truncate_float(invoice_amount_without_tax+self.total_tax, self.decimal_limit),
                                'invoice_amount_without_tax': truncate_float(invoice_amount_without_tax, self.decimal_limit),
                                'customer_reference': self.customer_id_customer_reference_map.get(cust_dict.get(invoice, {}).get('order__customer_id','')),
                                'created_by' : created_by,
                                'cancelled_by' : cancelled_by,
                                'customer': self.cust_details,
                                'cancellation_reason': cancellation_reason,
                                'total_invoice_amount': (truncate_float(invoice_amount_without_tax+self.total_tax, self.decimal_limit)+shipping_charges+other_charges),
                                'shipping_charges': shipping_charges,
                                'order_charges': order_charges_list,
                            }
                        
                        else:
                            invoice_header_dict[full_invoice_number]['discount_amount'] = truncate_float(invoice_header_dict[full_invoice_number]['discount_amount']+self.discount_amount, self.decimal_limit)
                            invoice_header_dict[full_invoice_number]['invoice_amount_without_tax'] = truncate_float(invoice_header_dict[full_invoice_number]['invoice_amount_without_tax']+invoice_amount_without_tax, self.decimal_limit)
                            invoice_header_dict[full_invoice_number]['invoice_amount_with_tax'] = truncate_float(invoice_header_dict[full_invoice_number]['invoice_amount_with_tax']+invoice_amount_without_tax + self.total_tax, self.decimal_limit)
                            invoice_header_dict[full_invoice_number]['total_invoice_amount'] = truncate_float(invoice_header_dict[full_invoice_number]['total_invoice_amount']+invoice_amount_without_tax + self.total_tax, self.decimal_limit)
                        
                        order_key, order_key_dict = self.prepare_order_level_data(data, full_invoice_number, order_key_dict, order_date, order_type, order_reference)

                        sku_key, invoice_item_dict = self.prepare_sku_level_data(data, invoice_item_dict, full_invoice_number, order_reference, invoice_sku_code, inv_item_quantity, mrp, unit_price, extra_details)
                        
                        batch_key, batch_dict = self.prepare_batch_level_data(data, full_invoice_number, order_reference, mrp, unit_price, batch_dict)
                        
                        lpn_key, lpn_dict = self.prepare_lpn_data(data, full_invoice_number, order_reference, invoice_sku_code, lpn_number, mrp, unit_price, line_quantity, lpn_dict)
                        
                        final_dict = self.prepare_final_invoice_data(final_dict, invoice_header_dict, order_key_dict, invoice_item_dict, batch_dict, order_key, sku_key, batch_key, full_invoice_number, lpn_key, lpn_dict)
                final_dict = self.final_invoice_data_tree_preparation(final_dict)
                final_list = list(final_dict.values())
                if final_list:
                    final_list = final_list[0]
                    total_list.append(final_list)
                        
        except Exception as e:
                log.debug(traceback.format_exc())
                log.info('Get Invoice failed for %s, invoice %s and error statement is %s' % (
                str(self.warehouse.username), str(invoice), str(e)))
                response_data = {'error': 'Internal Server Error', 'status': 500}
                return response_data
        page_info['data'] = total_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = total_count
        return page_info
    
    def format_request_data(self):
        '''Format the request data'''
        request_data = self.request.GET
        self.invoice_reference = request_data.get('invoice_number', '')
        self.order_id = request_data.get('order_id', '')
        self.order_reference = request_data.get('order_reference', '')
        self.limit = int(request_data.get('limit', 10))
        self.pagenum = int(request_data.get('pagenum', 1))
        self.time_zone= self.warehouse.userprofile.timezone or 'Asia/Calcutta'
        self.from_date_ = request_data.get('from_date', '')
        self.to_date_ = request_data.get('to_date', '')
        self.einvoice_date_str = request_data.get('einvoice_date', '')
        self.customer_type = request_data.get('customer_type', '')
        self.decimal_limit = get_decimal_value(self.warehouse.id, price=True)
        self.invoice_number, self.from_date, self.to_date, self.einvoice_date = '', '', '', ''
        self.delivery_challan = request_data.get('delivery_challan',False)
        self.order_type = request_data.get('order_type', '')
        if self.delivery_challan in ['true', 'True']:
            self.delivery_challan = True
        self.errors = {}
    
    def validate_date_formats_and_prepare_search_params(self):
        '''Validate the date formats and prepare search params'''
        sos_search_params = {'order__user':self.warehouse.id, 'order_status_flag__in': ['customer_invoices', 'delivery_challans']}
        customer_types = {
            'external': 'External',
            'stock_transfer': 'Stock Transfer',
            'others': 'Others',
            'non_gst' : 'non-GST'
        }
        try:
            if self.from_date_:
                self.from_date = get_utc_time_from_user_time_input(self.from_date_)
                if not self.from_date:
                    self.errors.setdefault('error', []).append({'message':'Invalid From Date'})
            if self.to_date_:
                self.to_date = get_utc_time_from_user_time_input(self.to_date_)
                if not self.to_date:
                    self.errors.setdefault('error', []).append({'message':'Invalid To Date'})
            if self.einvoice_date_str:
                self.einvoice_date = get_utc_time_from_user_time_input(self.einvoice_date_str)
                if not self.einvoice_date:
                    self.errors.setdefault('error', []).append({'message':'Invalid Einvoice Date'})
            if self.errors:
                self.errors['status'] = 400
                return self.errors, sos_search_params
        except Exception:
            self.errors = {'error': [{'message':'Invalid Date Format'}], 'status': 400}
            return self.errors, sos_search_params
        if self.customer_type:
            self.given_cust_types = self.customer_type.split(',')
            self.customer_types = { cust_type.lower() for cust_type in self.given_cust_types }
            invalid_types = self.customer_types - set(customer_types.keys())
            if invalid_types:
                self.errors = {'error': [{'message':'Invalid Customer Type'}], 'status': 400}
                return self.errors, sos_search_params
            valid_customer_types = [customer_types.get(cust_type) for cust_type in list(self.customer_types)]
            sos_search_params['order__customer_identifier__customer_type__in'] = valid_customer_types

        if not self.invoice_reference and not self.order_id and not self.order_reference:
            if self.from_date:
                sos_search_params['updation_date__gte'] = self.from_date
            if self.to_date:
                sos_search_params['updation_date__lte'] = self.to_date
        
        if self.invoice_reference : sos_search_params['invoice_reference'] = self.invoice_reference
        if self.order_reference : sos_search_params['order__order_reference'] = self.order_reference
        if self.order_id : sos_search_params['order__original_order_id'] = self.order_id
        if self.order_type : sos_search_params['order__order_type'] = self.order_type

        if self.einvoice_date:
            einvoiced_invoices = self.fecth_einvoiced_invoices()
            sos_search_params['invoice_reference__in'] = einvoiced_invoices

        return self.errors, sos_search_params
    
    def fecth_einvoiced_invoices(self):
        """
        Fetches the einvoiced invoices based on the specified filters.

        Returns:
            list: A list of einvoiced invoice references.
        """

        irn_filters = {
            'user_id': self.warehouse.id,
            'transact_type': 'invoice',
            'einvoice_date__gt': self.einvoice_date,
        }
        if self.invoice_reference:
            irn_filters['transact_id'] = self.invoice_reference

        einvoiced_invoices = list(IRN.objects.filter(**irn_filters).values_list('transact_id', flat=True))
        return einvoiced_invoices

    def fetch_misc_values(self):
        '''Fetch the misc values'''
        self.einvoice = get_misc_value('einvoice', self.warehouse.id)
    def fetch_master_data(self, sos_search_params):
        '''Fetch the master data from seller order summaries'''
        self.batch_list,self.batch_level_dict = [],{}
        invoice_numbers, seller_order_summary_data, total_count, page_info = [], [], 0, {}
        sos_result_values = [
            'full_invoice_number', 'invoice_reference', 'updation_date', 'creation_date',
            'order__sku__sku_code', 'order__sku__hsn_code', 'order__original_order_id',
            'order__order_reference', 'quantity', 'json_data__mrp', 'json_data__unit_price',
            'order__unit_price', 'picklist__picklist_number', 'order__mrp',
            'order__creation_date', 'order__order_type', 'id', 'order__sku__mrp',
            'order_status_flag', 'order__sku__sku_desc', 'picklist__stock__batch_detail__batch_no',
            'picklist__stock__batch_detail__id', 'order_id', 'json_data__invoice_price_as_per_buyprice',
            'picklist__stock__unit_price', 'order__json_data', 'picklist__stock__batch_detail__manufactured_date',
            'picklist__stock__batch_detail__expiry_date', 'json_data__batch_wac', 'json_data__invoice_generated_by',
            'json_data__cancelled_by', 'json_data__cancellation_reason', 'challan_number', 'lpn_number',
            'picklist__stock__location__location', 'picklist__stock__location__zone__zone', 'order__sku__enable_serial_based'
        ]
        seller_order_summaries = SellerOrderSummary.objects.filter(**sos_search_params).exclude(order_status_flag='processed_orders')
        if self.invoice_reference and not seller_order_summaries:
            self.errors = {'error': [{'message':'Invoice Number Not Found'}], 'status': 400}
            return self.errors, invoice_numbers, seller_order_summaries, seller_order_summary_data, total_count, page_info
        invoice_numbers = list(seller_order_summaries.exclude(invoice_reference='').distinct().values_list('invoice_reference', flat=True))
        total_count = len(invoice_numbers)
        page_info = scroll_data(self.request, invoice_numbers, limit=self.limit, request_type='GET', page_num=self.pagenum)
        invoice_numbers = page_info['data']
        seller_order_summary_data = seller_order_summaries.filter(invoice_reference__in=invoice_numbers).values(*sos_result_values)

        return self.errors, invoice_numbers, seller_order_summaries, seller_order_summary_data, total_count, page_info
    
    def prepare_unique_status_dict(self, seller_order_summary_data):
        '''Prepare unique status dictionary from seller order summaries'''
        status_dict = {}
        #invoice date and status preparation
        for invoice_data in seller_order_summary_data:
            invoice_number = invoice_data.get('challan_number', '') or invoice_data.get('invoice_reference', '')
            status_dict[invoice_number] = {}
            status_dict[invoice_number]['creation_date'] = invoice_data['creation_date']
            status_dict[invoice_number]['updation_date'] = invoice_data['updation_date']
            status_dict[invoice_number]['order_status_flag'] = invoice_data['order_status_flag']
        
        return status_dict
    
    def prepare_unique_tax_data(self, seller_order_summaries):
        '''Prepare unique tax dictionary from customer order summaries'''
        cos_result_values = ['order__order_reference','order__original_order_id',\
                                'order__sku__sku_code','cess_tax', 'discount',\
                                'mrp', 'invoice_date', 'cgst_tax', 'sgst_tax', 'igst_tax', 'order_id',\
                                'order__sku__mrp','mrp','order__sellerordersummary__invoice_reference'
                            ]              
        sos_order_ids = list(seller_order_summaries.values_list('order_id', flat=True))
        
        #Tax details preparation
        cus_search_params = {'order__user': self.warehouse.id}
        customer_order_summaries = CustomerOrderSummary.objects.filter(**cus_search_params, order_id__in=sos_order_ids).values(*cos_result_values)
        tax_mrp_dict = {}
        for customer_order_summary in customer_order_summaries:
            order_id = customer_order_summary.get('order_id', None)
            if order_id not in tax_mrp_dict:
                tax_mrp_dict[order_id] = customer_order_summary
        
        return tax_mrp_dict
    
    def prepare_customer_gst_data(self, seller_order_summaries):
        '''Prepare customer gst dictionary from customer master data'''
        customer_values = [
            'customer_id', 'customer_reference', 'customer_code', 'email_id', 'name', 'address', 'shipping_address', 
            'city', 'state', 'country', 'pincode', 'latitude', 'longitude', 'pan_number',
            'customer_shelf_life', 'phone_number', 'price_type', 'customer_type'
        ]
        customer_ids = list(seller_order_summaries.values_list('order__customer_id', flat=True))
        customer_data = CustomerMaster.objects.filter(customer_id__in = customer_ids, user=self.warehouse.id)\
                        .values(*customer_values,customer_sequence=F('customer_priority'), customer_route=F('route__route_id'),
                                customer_gst=F('tin_number'))
        gst_dict = {}
        self.customer_id_customer_reference_map = {}
        self.customer_details = {}
        for data in customer_data:
            customer_reference = data.get('customer_reference', '')
            customer_id = str(data.get('customer_id', ''))
            gst_number = data.get('customer_gst', '')
            data['price_id'] = data.get('price_type', '')
            gst_dict[customer_id] = gst_number
            self.customer_id_customer_reference_map[customer_id] = customer_reference
            self.customer_details[customer_reference] = data

        return gst_dict
    
    def prepare_customer_details(self, seller_order_summaries):
        '''Prepare customer details dictionary from seller order summaries'''
        customer_details = seller_order_summaries.values('invoice_reference', 'challan_number', 'order__customer_id', 'order__state','order__pin_code','order__customer_name','json_data__invoice_generated_by')
        cust_dict = {}
        for customer_detail in customer_details:
            full_invoice_number = customer_detail.get('invoice_reference','')
            details_dict = customer_detail.copy()
            details_dict.pop('invoice_reference', None)
            customer_id = str(details_dict['order__customer_id'])
            details_dict['order__customer_id'] = customer_id
            customer_state = str(details_dict['order__state'])
            details_dict['order__state'] = customer_state
            customer_pincode = str(details_dict['order__pin_code'])
            details_dict['order__pin_code'] = customer_pincode
            details_dict['json_data__invoice_generated_by'] = customer_detail.get('json_data__invoice_generated_by','')
            cust_dict[full_invoice_number] = details_dict

        return cust_dict
    
    def prepare_unique_quantity_details(self, seller_order_summary_data, tax_mrp_dict):
        '''Prepare unique quantity details from seller order summaries'''
        uniq_inv_qty,uniq_item_inv_qty, order_references, status_dict, serialised_data = {},{}, set(), defaultdict(dict), defaultdict(set)
        for data in seller_order_summary_data:
            invoice_number = data.get('invoice_reference', None)
            order_reference = data.get('order__order_reference', None)
            sku_code = data.get('order__sku__sku_code', None)
            order_id = data.get('order_id', '')
            mrp_dict = tax_mrp_dict.get(order_id, {}) or {}
            mrp = mrp_dict.get('mrp', 0.00) or 0.00
            unit_price = data.get('order__unit_price', 0.00) or 0.00
            serialised_skku = data.get('order__sku__enable_serial_based', 0) or 0
            order_references.add(order_reference)
            if data.get('json_data__mrp'):
                mrp = data.get('json_data__mrp', 0.00) or 0.00
            if data.get('json_data__unit_price'):
                unit_price = data.get('json_data__unit_price', 0.00) or 0.00
            if data.get('json_data__invoice_price_as_per_buyprice') == True:
                unit_price =  data.get('picklist__stock__unit_price', 0.00) or 0.00
            mrp = truncate_float(mrp,self.decimal_limit)
            unit_price = truncate_float(unit_price,self.decimal_limit)
            uniq_comb = (invoice_number)
            if uniq_inv_qty.get(uniq_comb,''):
                uniq_inv_qty[uniq_comb] += data['quantity']
            else:
                uniq_inv_qty[uniq_comb] = data['quantity']
            uniq_comb = (invoice_number,order_reference,sku_code,mrp,unit_price)
            if uniq_item_inv_qty.get(uniq_comb,''):
                uniq_item_inv_qty[uniq_comb] += data['quantity']
            else:
                uniq_item_inv_qty[uniq_comb] = data['quantity']
            status_dict[invoice_number]['creation_date'] = data['creation_date']
            status_dict[invoice_number]['updation_date'] = data['updation_date']
            status_dict[invoice_number]['order_status_flag'] = data['order_status_flag']
            if serialised_skku:
                serialised_data['reference_numbers'].add(invoice_number)
                serialised_data['sku_codes'].add(sku_code)
        
        return uniq_inv_qty, uniq_item_inv_qty, order_references, status_dict, serialised_data
    
    def prepare_unique_irn_details(self, invoice_numbers):
        '''Prepare unique irn details from irn data'''
        irn_data_list = list(IRN.objects.filter(user=self.warehouse.id, transact_id__in=invoice_numbers, transact_type='invoice')\
                        .values('transact_id', 'irn', 'ackno', 'ack_date', 'status'))
        irn_info_dict = {}
        for irn in irn_data_list:
            reference_number = irn.get('transact_id', '')
            if reference_number not in irn_info_dict:
                irn_info_dict[reference_number] = irn
        
        return irn_info_dict
    
    def fetch_customer_gst_and_irn_data(self, irn_info_dict, cust_dict, gst_dict, invoice):
        '''Prepare customer gst and irn data'''
        irn_data = irn_info_dict.get(invoice, {}) or {}
        customer_id = cust_dict.get(invoice, {}).get('order__customer_id', '')
        customer_reference = self.customer_id_customer_reference_map.get(customer_id,'')
        if self.einvoice == 'true':
            self.irn_number = irn_data.get('irn', '')
            self.acknowledgement_number = irn_data.get('ackno', '')
            ack_date = irn_data.get('ack_date', '')
            self.acknowledgement_date = ack_date.astimezone(pytz.timezone(self.time_zone)).strftime("%Y-%m-%d %H:%M:%S") if ack_date else ''
            if irn_data.get('status') == 0:
                self.einvoice_status = 'created'
            elif irn_data.get('status') == 1:
                self.einvoice_status = 'cancelled'
        self.cust_details = {}
        if self.customer_details.get(customer_reference):
            self.cust_details = self.customer_details.get(customer_reference)
        if not gst_dict.get(customer_id,''):
            self.customer_gst = ''
        else :
            self.customer_gst = gst_dict.get(customer_id, '')
    
    def calculate_tax_values(self, tax_mrp_dict, order_id, invoice_amount_without_tax):
        '''Prepare tax values from tax mrp dictionary'''
        self.discount_amount, self.cgst_percentage, self.sgst_percentage, self.igst_percentage, self.cess_percentage = 0, 0, 0, 0, 0
        if tax_mrp_dict and tax_mrp_dict.get(order_id, {}):
            tax_dict = tax_mrp_dict.get(order_id)
            self.discount_amount = tax_dict.get('discount', 0)
            self.cgst_percentage = tax_dict.get('cgst_tax', 0)
            self.sgst_percentage = tax_dict.get('sgst_tax', 0)
            self.igst_percentage = tax_dict.get('igst_tax', 0)
            self.cess_percentage = tax_dict.get('cess_tax', 0)
            self.order_mrp = tax_dict.get('mrp',0)
        self.cgst =  truncate_float(invoice_amount_without_tax * self.cgst_percentage/100,self.decimal_limit)
        self.sgst =  truncate_float(invoice_amount_without_tax * self.sgst_percentage/100,self.decimal_limit)
        self.igst =  truncate_float(invoice_amount_without_tax * self.igst_percentage/100,self.decimal_limit)
        self.cess =  truncate_float(invoice_amount_without_tax * self.cess_percentage/100,self.decimal_limit)
        self.total_tax = self.cgst+self.sgst+self.igst+self.cess
    
    def fetch_mrp_and_price_data(self, data):
        '''Prepare mrp and price data from seller order summaries'''
        if data.get('json_data__mrp'):
            mrp = data.get('json_data__mrp', 0) or 0.00
            mrp = truncate_float(mrp,self.decimal_limit)
        else:
            mrp = truncate_float(data.get('order__mrp'), self.decimal_limit)
        if data.get('json_data__unit_price'):
            unit_price =  data.get('json_data__unit_price', 0) or 0.00
            unit_price = truncate_float(unit_price,self.decimal_limit)
        else:
            unit_price =  truncate_float(data.get('order__unit_price', 0), self.decimal_limit)
        if data.get('json_data__invoice_price_as_per_buyprice') == True:
            unit_price =  data.get('picklist__stock__unit_price', 0.00) or 0.00
            unit_price = truncate_float(unit_price,self.decimal_limit)
        
        return mrp, unit_price
    
    def format_data_formats(self, cancelled_date, status_dict, invoice):
        '''Prepare date formats from status dictionary'''
        if status_dict[invoice]['order_status_flag'] == 'cancelled':
            invoice_date = status_dict[invoice]['creation_date']
            invoice_date = invoice_date.astimezone(pytz.timezone(self.time_zone)).strftime(date_format_const)
            cancelled_date = status_dict[invoice]['updation_date']
            cancelled_date = cancelled_date.astimezone(pytz.timezone(self.time_zone)).strftime(date_format_const)
        else:
            invoice_date = status_dict[invoice]['updation_date'].astimezone(pytz.timezone(self.time_zone)).strftime(date_format_const)

        return cancelled_date, invoice_date
    
    def prepare_order_level_data(self, data, full_invoice_number, order_key_dict, order_date, order_type, order_reference):
        '''Prepare order level data from seller order summaries'''
        order_key = (full_invoice_number, order_reference)
        if order_key not in order_key_dict:
            order_key_dict[order_key] = {
                'order_id': data['order__original_order_id'],
                'order_date': order_date,
                'order_type' : order_type,
                'order_reference': order_reference,
            }
        else:
            order_key_dict[order_key].update({
                'order_id': data['order__original_order_id'],
                'order_date': order_date,
                'order_type' : order_type,
                'order_reference': order_reference,
            })
        
        return order_key, order_key_dict
    
    def prepare_sku_level_data(self, data, invoice_item_dict, full_invoice_number, order_reference, invoice_sku_code, inv_item_quantity, mrp, unit_price, extra_details):
        '''Prepare sku level data from seller order summaries'''
        sku_key = (full_invoice_number, order_reference, invoice_sku_code, mrp,unit_price)
        if sku_key not in invoice_item_dict:
            invoice_item_dict[sku_key] = {
                'sku_code': invoice_sku_code,
                'sku_desc': data.get('order__sku__sku_desc', ''),
                'invoice_quantity' : inv_item_quantity,
                'hsn_code' : data.get('order__sku__hsn_code',''),
                'mrp' : truncate_float(mrp, self.decimal_limit),
                'unit_price': truncate_float(unit_price, self.decimal_limit),
                'invoice_amount': truncate_float(inv_item_quantity * unit_price, self.decimal_limit),
                'tax_percent': {
                    "cgst": self.cgst_percentage,
                    "sgst": self.sgst_percentage,
                    "igst": self.igst_percentage,
                    "cess": self.cess_percentage
                },
                'tax_amount': {
                    "cgst": self.cgst,
                    "sgst": self.sgst,
                    "igst": self.igst,
                    "cess": self.cess
                },
                'aux_data': extra_details
            } 
        else:
            tax_amount_dict = invoice_item_dict[sku_key].get('tax_amount', {})
            invoice_item_dict[sku_key]['tax_amount'] = {
                "cgst": tax_amount_dict.get('cgst', 0.00) + self.cgst,
                "sgst": tax_amount_dict.get('sgst', 0.00) + self.sgst,
                "igst": tax_amount_dict.get('igst', 0.00) + self.igst,
                "cess": tax_amount_dict.get('cess', 0.00) + self.cess
            }
        
        return sku_key, invoice_item_dict
    
    def prepare_batch_level_data(self, data, full_invoice_number, order_reference, mrp, unit_price, batch_dict):
        '''Prepare batch level data from seller order summaries'''
        batch_no = data.get('picklist__stock__batch_detail__batch_no', '') or ''
        sku_code = data.get('order__sku__sku_code', '')
        manufactured_date = get_local_date_with_time_zone(self.time_zone,data.get('picklist__stock__batch_detail__manufactured_date'),send_date=True).strftime('%Y-%m-%d') if data.get('picklist__stock__batch_detail__manufactured_date', '') else ''
        expiry_date = get_local_date_with_time_zone(self.time_zone,data.get('picklist__stock__batch_detail__expiry_date'),send_date=True).strftime('%Y-%m-%d') if data.get('picklist__stock__batch_detail__expiry_date', '') else ''
        batch_wac = data.get('json_data__batch_wac', None) or None
        location = data.get('picklist__stock__location__location', '') or ''
        zone = data.get('picklist__stock__location__zone__zone', '') or ''

        batch_key = (full_invoice_number, order_reference, sku_code, batch_no, mrp, unit_price)
        serial_numbers_key = (full_invoice_number, sku_code, batch_no)
        if batch_key not in batch_dict:
            batch_dict[batch_key] = {
                'batch_no': batch_no,
                'manufactured_date' : manufactured_date,
                'expiry_date' : expiry_date,
                'invoice_quantity' : data.get('quantity'),
                'mrp' : mrp,
                'unit_price' : unit_price,
                'serial_numbers' : self.batch_level_sr_no_data.get(serial_numbers_key, []),
                'batch_wac': batch_wac,
                'tax_amount': {
                    "cgst": self.cgst,
                    "sgst": self.sgst,
                    "igst": self.igst,
                    "cess": self.cess
                },
                'location_details': {}
            }  
        else:
            quantity = batch_dict[batch_key].get('invoice_quantity', 0.00) or 0.00
            batch_tax_amount_dict = batch_dict[batch_key].get('tax_amount', {})
            batch_total_quantity = quantity + data.get('quantity')
            batch_dict[batch_key]['invoice_quantity'] = batch_total_quantity
            batch_dict[batch_key]['tax_amount'] = {
                'cgst': batch_tax_amount_dict.get('cgst', 0.00) + self.cgst,
                'sgst': batch_tax_amount_dict.get('sgst', 0.00) + self.sgst,
                'igst': batch_tax_amount_dict.get('igst', 0.00) + self.igst,
                'cess': batch_tax_amount_dict.get('cess', 0.00) + self.cess
            }

        if location:
            if not batch_dict[batch_key].get('location_details',{}).get(location,{}):
                batch_dict[batch_key]['location_details'][location] = {
                    'zone': zone,
                    'location': location,
                    'quantity': data.get('quantity')
                }
            else:
                batch_dict[batch_key]['location_details'][location]['quantity'] += data.get('quantity')

        return batch_key, batch_dict
    
    def prepare_lpn_data(self, data, full_invoice_number, order_reference, invoice_sku_code, lpn_number, mrp, unit_price, line_quantity, lpn_dict):
        '''Prepare lpn data from seller order summaries'''
        lpn_key = ''
        if lpn_number:
            lpn_key = (full_invoice_number, order_reference, invoice_sku_code,lpn_number, mrp, unit_price)
            serial_numbers_key = (full_invoice_number, invoice_sku_code, lpn_number)
            if lpn_key not in lpn_dict:
                lpn_dict[lpn_key] = {
                    'lpn_number': data.get('lpn_number', ''),
                    'quantity': line_quantity,
                    'packed_serial_numbers': self.lpn_level_sr_no_data.get(serial_numbers_key, [])
                }
            else:
                lpn_dict[lpn_key]['quantity'] += line_quantity
        
        return lpn_key, lpn_dict
    
    def prepare_final_invoice_data(self, final_dict, invoice_header_dict, order_key_dict, invoice_item_dict, batch_dict, order_key, sku_key, batch_key, full_invoice_number, lpn_key, lpn_dict):
        '''Prepare final invoice data from seller order summaries'''
        if full_invoice_number not in final_dict:
            final_dict[full_invoice_number] = invoice_header_dict.get(full_invoice_number)
            final_dict[full_invoice_number]['orders'] = {order_key: order_key_dict.get(order_key)}
            final_dict[full_invoice_number]['orders'][order_key]['items'] = {sku_key : invoice_item_dict.get(sku_key)}
            final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['batch_details'] = { batch_key :batch_dict.get(batch_key)}
            final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['lpn_details'] = {lpn_key: lpn_dict.get(lpn_key)}
        else:
            if not final_dict[full_invoice_number]['orders'].get(order_key,''):
                final_dict[full_invoice_number]['orders'][order_key] = order_key_dict.get(order_key)
                final_dict[full_invoice_number]['orders'][order_key]['items'] = {sku_key : invoice_item_dict.get(sku_key)}
                final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['batch_details'] = { batch_key :batch_dict.get(batch_key)}
                final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['lpn_details'] = {lpn_key: lpn_dict.get(lpn_key)}
            else:
                if not final_dict[full_invoice_number]['orders'][order_key]['items'].get(sku_key,''):
                    final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key] = invoice_item_dict.get(sku_key)
                    final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['batch_details'] = { batch_key :batch_dict.get(batch_key)}
                    final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['lpn_details'] = {lpn_key: lpn_dict.get(lpn_key)}
                else:
                    if not final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['batch_details'].get(batch_key,''):
                        final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['batch_details'][batch_key] =  batch_dict.get(batch_key)
                    if not final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['lpn_details'].get(lpn_key,''):
                        final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['lpn_details'][lpn_key] = lpn_dict.get(lpn_key)
                    else:
                        final_dict[full_invoice_number]['orders'][order_key]['items'][sku_key]['lpn_details'][lpn_key]['quantity'] += lpn_dict.get(lpn_key).get('quantity')
                        
        
        return final_dict
    
    def final_invoice_data_tree_preparation(self, final_dict):
        '''Prepare final invoice data tree from seller order summaries'''
        for full_invoice_number, invoice_data in final_dict.items():
            for order_reference, order_data in invoice_data['orders'].items():
                for sku, sku_data in order_data['items'].items():
                    for batch, batch_data in sku_data['batch_details'].items():
                        batch_data['location_details'] = list(batch_data['location_details'].values())
                        if isinstance(sku_data['batch_details'], list):
                            continue
                        else:
                            sku_data['batch_details'] = list(sku_data['batch_details'].values())
                    lpn_details = []
                    for lpn, lpn_data in sku_data['lpn_details'].items():
                        if not lpn:
                            continue
                        lpn_details.append(lpn_data)
                    sku_data['lpn_details'] = lpn_details    
                order_data['items'] = list(order_data['items'].values())
            invoice_data['orders'] = list(invoice_data['orders'].values())
        
        return final_dict
    
    
    def get_order_type_details(self, order_types):
        
        order_types_data = list(OrderTypeZoneMapping.objects.filter(order_type__in=order_types, user=self.warehouse.id).values('order_type', 'order_classification', 'json_data'))
        
        for order_type in order_types:
            if order_type.lower() == 'stocktransfer':
                self.stock_transfer_order_types.add(order_type)
        
        for data in order_types_data:
            order_type = data.get('order_type', '')
            order_classification = data.get('order_classification', '')
            json_data = data.get('json_data', {})
            document_type = json_data.get('document_type', '')
            if (order_classification == 'STOCK_TRANSFER' and document_type not in ('delivery_challan')):
                self.stock_transfer_order_types.add(order_type)
     
    def prepare_stocktransfer_data(self, sos_objs, invoice_reference, invoice_number):
        
        if not invoice_reference:
            return
        processed_data_ids = []
        for sos_obj in sos_objs:
            order_type = sos_obj.order.order_type
            order_rederence = sos_obj.order.order_reference
            if order_type not in self.stock_transfer_order_types:
                continue
            
            order_json_data = sos_obj.order.json_data
            po_reference = order_json_data.get('po_reference', '') or ''
            po_reference = (po_reference, order_rederence)[po_reference == '']
            key = (invoice_number, po_reference)
            if key in processed_data_ids:
                continue
            
            customer_code = sos_obj.order.customer_identifier.customer_code if sos_obj.order.customer_identifier_id else ''
            if not customer_code:
                continue
        
            if key not in self.wh_stock_transfer_data[customer_code]['invoice_ref_data']:
                self.wh_stock_transfer_data[customer_code]['invoice_ref_data'][key] = {
                    'invoice_reference': invoice_reference,
                    'invoice_number': invoice_number,
                    'order_reference': order_rederence,
                    'po_reference': po_reference,
                }
            if invoice_number not in self.wh_stock_transfer_data[customer_code]['invoice_number_map']:
                self.wh_stock_transfer_data[customer_code]['invoice_number_map'][invoice_number] = po_reference
        
    def update_stock_transfer_data(self):
        
        log.info(f"Updating invoice reference for stock transfer orders,stock transfer data {self.wh_stock_transfer_data}")
        for wh_name, data in self.wh_stock_transfer_data.items():
            warehouse = get_destination_user(wh_name)
            if not (warehouse and data.get('invoice_ref_data',{}) and data.get('invoice_number_map',{})):
                continue
            self.update_inv_reference_in_st_wh(warehouse, data)
        if self.asn_objs:
            ASNSummary.objects.bulk_update_with_rounding(self.asn_objs, ['invoice_number'])
        if self.sps_objs:
            SellerPOSummary.objects.bulk_update_with_rounding(self.sps_objs, ['invoice_number'])
        
    def update_inv_reference_in_st_wh(self, warehouse, data):
        #get ASN Summary objects
        log.info(f"Updating invoice reference for ASN Summary objects, data {data}")
        invoice_number_map = data.get('invoice_number_map', {})
        invoice_ref_data = data.get('invoice_ref_data', {})
        asn_id_reference_map = {}
        asn_filters = {
            'source_warehouse_id': warehouse.id,
            'purchase_order__open_po__po_name__in': set(invoice_number_map.values()),
            'invoice_number__in': list(invoice_number_map.keys())
        }
        asn_values = [
            'id', 'account_id', 'invoice_number', 'purchase_order__open_po__po_name'
        ]
        asn_sum_objs = ASNSummary.objects.select_related('purchase_order__open_po').filter(**asn_filters).only(*asn_values)
        if not asn_sum_objs.exists():
            return
        for obj in asn_sum_objs:
            invoice_number = obj.invoice_number
            po_reference = obj.purchase_order.open_po.po_name
            key = (invoice_number, po_reference)
            st_data = invoice_ref_data.get(key, {}) or {}
            invoice_reference = st_data.get('invoice_reference', '')
            if invoice_reference:
                obj.invoice_number = invoice_reference
                asn_id_reference_map[obj.id] = invoice_reference
                self.asn_objs.append(obj)
        
        if not asn_id_reference_map:
            return
        sps_filters = {
            'asn_id__in': list(asn_id_reference_map.keys()),
            'asn__source_warehouse_id': warehouse.id,
        }
        values = ['asn_id', 'account_id', 'invoice_number']
        sps_objs = SellerPOSummary.objects.filter(**sps_filters).only(*values)
        for obj in sps_objs:
            asn_id = obj.asn_id
            invoice_number = asn_id_reference_map.get(asn_id, '')
            if invoice_number:
                obj.invoice_number = invoice_number
                self.sps_objs.append(obj)
                
    def update_invoice_additional_data(self, full_invoice_number, invoice_additional_data):
        '''
        create or update invoice additional info objects payload
        '''
        inv_object = InvoiceAdditionalInfo.objects.filter(warehouse_id=self.warehouse.id, full_invoice_number=full_invoice_number)
        if inv_object.exists():
            info_obj = inv_object.first()
            json_data = info_obj.json_data
            json_data.update(
                {
                    'invoice_additional_data': invoice_additional_data
                }
            )
            info_obj.json_data = json_data
            self.update_inv_info_objs.append(info_obj)
        else:
            info_obj = InvoiceAdditionalInfo(
                warehouse_id=self.warehouse.id,
                account_id=self.warehouse.userprofile.id,
                full_invoice_number=full_invoice_number,
                json_data={
                    'invoice_additional_data': invoice_additional_data
                }
            )
            self.create_inv_info_objs.append(info_obj)  
            
    def create_or_update_invoice_additional_info(self):
        '''
        Create or update invoice additional info objects
        '''
        if self.create_inv_info_objs:
            InvoiceAdditionalInfo.objects.bulk_create(self.create_inv_info_objs)
        if self.update_inv_info_objs:
            InvoiceAdditionalInfo.objects.bulk_update(self.update_inv_info_objs, ['json_data']) 
    
    def invoice_update_callback(self):
        '''
        Trigger Invoice Callback.
        '''
        
        headers_data = {
            'Authorization': self.request.headers.get('Authorization',''),
            'Warehouse': self.warehouse.username
        }
        filters = {
            'request_META': self.request.META,
            'headers': headers_data,
            'dc_check': False,
        }
        for invoice_number, invoice_reference in self.invoice_map.items():
            int_filters = deepcopy(filters)
            order_references = self.invoice_order_reference_map.get(invoice_number, {}) or {}
            int_filters.update(
                {
                    'invoice_number': invoice_reference,
                    'order_references': list(order_references)
                }
            )
            webhook_integration_3p(self.warehouse.id, 'invoice_updation', filters=int_filters)
            
    def put(self, request, *args, **kwargs):
        """
        Handle the HTTP PUT request for updating invoice data.

        Args:
            request (HttpRequest): The HTTP request object.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: The JSON response containing the updated data and status code.
        """
        log.info("Request update invoice reference username %s, Ip Address %s, and params are %s"% (str(self.request.user.username),str(get_user_ip(self.request)), str(self.request.body)))
        try:
            request_data = loads(self.request.body)
        except Exception:
            request_data = []

        if not request_data:
            return {'error': [{'message':'Invalid Payload'}], 'status': 400}
        if type(request_data) != list:
            return {'error': [{'message':'Request data should be list'}], 'status': 400}
        final_output_data = {'data': []}
        ex_invoice_reference, bulk_order_objs, order_types = [], [], []
        self.invoice_map, self.wh_stock_transfer_data = {}, defaultdict(lambda: defaultdict(dict))
        self.asn_objs, self.sps_objs, self.stock_transfer_order_types = [], [], set()
        self.update_inv_info_objs, self.create_inv_info_objs = [], []
        self.invoice_order_reference_map = defaultdict(set)
        self.set_user_credientials()   
        unique_invoice_references, unique_invoice_numbers = self.prepare_unique_values(request_data)
        einvoiced_invoices = IRN.objects.filter(user_id=self.warehouse.id, transact_id__in=self.invoice_map.keys(), transact_type='invoice')
        if einvoiced_invoices.exists():
            return JsonResponse({'errors': ['E-Invoice generated not allowing to update invoice reference'], 'status': 400})
        if unique_invoice_references:
            ex_invoice_reference = list(SellerOrderSummary.objects.filter(order__user=self.warehouse.id, invoice_reference__in=unique_invoice_references, order_status_flag__in=['customer_invoices', 'delivery_challans']).values_list('invoice_reference', flat=True).distinct())
        if unique_invoice_numbers:
            order_types = list(SellerOrderSummary.objects.filter(order__user=self.warehouse.id, invoice_reference__in=unique_invoice_numbers, order_status_flag__in=['customer_invoices', 'delivery_challans']).values_list('order__order_type', flat=True).distinct())
            self.get_order_type_details(order_types)

        for each_record in request_data:
            status,status_code = '',''
            filter_data = {'order__user':self.warehouse.id, 'order_status_flag': 'customer_invoices'}
            invoice_number = each_record.get('invoice_number', '')
            invoice_reference = each_record.get('invoice_reference', '')
            invoice_url = each_record.get('invoice_url', '')
            order_reference = each_record.get('order_reference', '')
            invoice_additional_data = each_record.get('invoice_additional_data', {}) or {}

            if not invoice_number and not order_reference:
                return {'error': [{'message':'Either Order Reference or Invoice Number is Mandatory'}], 'status': 400}

            filter_data, status = self.prepare_filter_data(filter_data, invoice_reference, invoice_url, invoice_number, order_reference, ex_invoice_reference, status)

            sos_objs, unique_invoice_numbers, full_invoice_number, ex_invoice_reference, status = self.prepare_unique_sos_values(filter_data, status)

            if len(unique_invoice_numbers) > 1:
                status = "Given order reference have more than one invoice"
            
            final_output_data, status_code, continue_loop = self.validate_invoice_reference_and_order( sos_objs , status, final_output_data, full_invoice_number, invoice_number, order_reference, invoice_reference, ex_invoice_reference, status_code)
            if status:
                return JsonResponse(final_output_data, status=400)
            if continue_loop:
                continue
            
            # update invoice reference for stock transfer orders
            if self.stock_transfer_order_types:
                self.prepare_stocktransfer_data(sos_objs, invoice_reference, invoice_number)
            
            if invoice_additional_data:
                self.update_invoice_additional_data(full_invoice_number, invoice_additional_data)
            
            # Preparing unique order data and update json data
            bulk_order_objs = self.prepare_unique_order_data(sos_objs, bulk_order_objs, each_record)

            encoded_string = each_record.get('encoded_invoice','')
            if encoded_string:
                # Decode the base64 encoded string
                try:
                    invoice_ref = invoice_reference.replace("/", "-")
                    decoded_data = base64.b64decode(encoded_string)
                    with open(f"{invoice_ref}{self.warehouse.id}.pdf", 'wb') as f:
                        f.write(decoded_data)
                except Exception as e:
                    log.info('Error decoding or writing PDF file for invoice %s and error statement is %s' % (invoice_reference, str(e)))
                    final_output_data['data'].append({'message':"Error decoding or writing PDF file", 'invoice_number':invoice_number, 'order_reference': order_reference, 'invoice_reference': invoice_reference})
                    status_code = 400
                    return JsonResponse(final_output_data, status=status_code)
            
            if invoice_url or encoded_string:
                master_docs_objs = MasterDocs.objects.filter(user_id=self.warehouse.id, master_type='invoice', master_id__in=unique_invoice_numbers)
                if master_docs_objs.exists():
                    master_docs_objs.update(document_url = invoice_url)
                else:
                    final_output_data, status_code = self.prepare_and_save_invoice_pdf(invoice_url, unique_invoice_numbers, invoice_reference, invoice_number, order_reference, final_output_data, status_code)
                    if status_code == 400:
                        return JsonResponse(final_output_data, status=status_code)
            
            final_output_data['data'].append({'message':"Request data update Successfully", 'invoice_number':invoice_number, 'order_reference': order_reference, 'invoice_reference': invoice_reference})
            status_code = 200
        if bulk_order_objs:
            OrderDetail.objects.bulk_update_with_rounding(bulk_order_objs,['json_data'])
        # update saved invoice data
        # update irn objects
        self.update_saved_invoice(self.invoice_map)
        self.update_irn_objects(self.invoice_map)
        self.update_serial_numbers(self.invoice_map)
        self.update_stock_transfer_data()
        self.create_or_update_invoice_additional_info()
        self.invoice_update_callback()
        return JsonResponse(final_output_data, status=status_code)

    def prepare_unique_values(self, request_data):
        '''This method is used to prepare unique invoice references from request data'''
        unique_invoice_references, unique_invoice_numbers = [], set()
        for each_item in request_data:
            invoice_reference = each_item.get('invoice_reference', '')
            invoice_number = each_item.get('invoice_number', '')
            if invoice_reference and invoice_reference not in unique_invoice_references:
                unique_invoice_references.append(invoice_reference)
            if invoice_number:
                unique_invoice_numbers.add(invoice_number)
            self.invoice_map[invoice_number] = invoice_reference
        
        return unique_invoice_references, unique_invoice_numbers
    
    def update_saved_invoice(self, invoice_map):
        '''This method is used to update saved invoice data'''
        invoice_detail_objs = InvoiceDetail.objects.filter(warehouse_id=self.warehouse.id, invoice_reference__in=invoice_map.keys())
        for invoice_detail in invoice_detail_objs:
            invoice_number = invoice_detail.invoice_number
            invoice_reference = invoice_map.get(invoice_number, '')
            invoice_detail.invoice_reference = invoice_reference
        
        InvoiceDetail.objects.bulk_update(invoice_detail_objs, ['invoice_reference'])
    
    def update_irn_objects(self, invoice_map):
        '''This method is used to update irn objects after invoice update'''
        irn_objs = IRN.objects.filter(user_id=self.warehouse.id, transact_id__in=invoice_map.keys(), transact_type='invoice')

        for irn_obj in irn_objs:
            invoice_number = irn_obj.transact_id
            invoice_reference = invoice_map.get(invoice_number, '')
            irn_obj.transact_id = invoice_reference
        
        IRN.objects.bulk_update(irn_objs, ['transact_id'])


    def update_serial_numbers(self, invoice_map):
        """
        updates invoice reference for the given invoice numbers
        """

        misc_types = ['sku_serialisation']
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        if not misc_dict.get('sku_serialisation') in ['true', True]:
            return
        invoice_numbers = list(invoice_map.keys())
        serialnumbertransaction_objs = SerialNumberTransaction.objects.filter(warehouse=self.warehouse.id, reference_type="invoice", reference_number__in=invoice_numbers)
        for serialnumbertransaction_obj in serialnumbertransaction_objs:
            invoice_number = serialnumbertransaction_obj.reference_number
            invoice_reference = invoice_map.get(invoice_number, '')
            serialnumbertransaction_obj.reference_number = invoice_reference
        SerialNumberTransaction.objects.bulk_update(serialnumbertransaction_objs, ['reference_number'])


    def prepare_filter_data(self, filter_data, invoice_reference, invoice_url, invoice_number, order_reference, ex_invoice_reference, status):
        '''This method is used to prepare filter data for invoice update'''
        if not invoice_reference and not invoice_url:
            status = "Please give required fields for update"
        if invoice_reference and invoice_reference in ex_invoice_reference:
            status = "Given Invoice Reference already available"
        if invoice_number:
            filter_data['full_invoice_number'] = invoice_number
        if order_reference:
            filter_data['order__order_reference'] = order_reference
        
        return filter_data, status

    def prepare_unique_sos_values(self, filter_data, status):
        '''This method is used to prepare unique sos values'''
        values = [
            'full_invoice_number', 'invoice_reference', 'order__order_reference',
            'order__order_type', 'order__json_data', 'order__customer_identifier__customer_code',
            'order__customer_identifier_id'
        ]
        sos_objs = SellerOrderSummary.objects.select_related('order', 'order__customer_identifier').filter(**filter_data).only(*values)
        if not sos_objs.exists():
            status = "Data not found!"
        unique_invoice_numbers, full_invoice_number, ex_invoice_reference = [], '', ''
        sos_data = list(sos_objs.values('full_invoice_number', "invoice_reference", "order__order_reference").distinct())
        for sos_record in sos_data:
            if not full_invoice_number:
                full_invoice_number = sos_record.get('full_invoice_number')
            if not ex_invoice_reference:
                ex_invoice_reference = sos_record.get('invoice_reference')
            self.invoice_order_reference_map[sos_record.get('full_invoice_number')].add(sos_record.get('order__order_reference'))
            unique_invoice_numbers.append(sos_record.get('full_invoice_number',''))
        
        return sos_objs, unique_invoice_numbers, full_invoice_number, ex_invoice_reference, status

    def prepare_unique_order_data(self, sos_objs, bulk_order_objs, each_record):
        '''This method is used to prepare unique order data'''
        order_ids = list(sos_objs.values_list('order__order_reference',flat=True).distinct())
        order_det_objs = []
        if order_ids :
            order_det_objs = OrderDetail.objects.filter(user=self.warehouse.id,order_reference__in=order_ids)
        for order in order_ids:
            aux_json_data = each_record.get('order',{}).get('aux_data',{})
            if aux_json_data:
                ord_obj = order_det_objs.filter(order_reference = order)
                for each_ord in ord_obj:
                    if each_ord.json_data:
                        ord_json_data = each_ord.json_data
                        ord_json_data.update(aux_json_data)
                    else:
                        each_ord.json_data = aux_json_data
                    bulk_order_objs.append(each_ord)
        
        return bulk_order_objs

    def validate_invoice_reference_and_order(self, sos_objs , status, final_output_data, full_invoice_number, invoice_number, order_reference, invoice_reference, ex_invoice_reference, status_code, continue_loop=False):
        '''This method is used to validate invoice reference and order'''
        if status:
            final_output_data['data'].append({'message':status, "invoice_number": invoice_number, 'order_reference': order_reference})
            status_code = 400
            continue_loop = True
            return final_output_data, status_code, continue_loop
        if invoice_reference:
            if full_invoice_number != ex_invoice_reference:
                status = "Invoice updated already"
                final_output_data['data'].append({'message':status, "invoice_number": invoice_number, 'order_reference': order_reference})
                status_code = 400
                continue_loop = True
                return final_output_data, status_code, continue_loop

            sales_return_list = (SalesReturnLineLevel.objects.filter(sales_return__warehouse=self.warehouse.id ,reference_number=ex_invoice_reference).values_list('reference_number', flat=True).distinct())
            if sales_return_list:
                final_output_data['data'].append({'message': "Sales Return done against to given invoice reference, hence not allowing for update", "invoice_number": invoice_number, 'order_reference': order_reference})
                status_code = 400
                continue_loop = True
                return final_output_data, status_code, continue_loop

            shipment_validation, shipment_ids = self.validate_shipment_invoice_data(ex_invoice_reference)

            if not shipment_validation:
                final_output_data['data'].append({'message': "Shipment done for given invoice reference, hence not allowing for update", "invoice_number": invoice_number, 'order_reference': order_reference})
                status_code = 400
                continue_loop = True
                return final_output_data, status_code, continue_loop

            sos_objs.update(invoice_reference=invoice_reference)
            ShipmentInvoice.objects.filter(user_id=self.warehouse.id, id__in=shipment_ids).update(reference_number=invoice_reference)
        
        return final_output_data, status_code, continue_loop

    def validate_shipment_invoice_data(self, ex_invoice_reference):
        '''This method is used to validate shipment invoice data'''
        shipment_ids, shipment_validation = [], True
        shipment_info = list(ShipmentInvoice.objects.filter(user_id=self.warehouse.id, reference_number=ex_invoice_reference).values('id', 'quantity', 'reserved_quantity'))
        for shipment_record in shipment_info:
            shipment_id = shipment_record.get('id', '')
            if shipment_id and shipment_id not in shipment_ids:
                shipment_ids.append(shipment_id)

            quantity = shipment_record.get('quantity', 0) or 0
            reserved_quantity = shipment_record.get('reserved_quantity', 0) or 0
            if quantity != reserved_quantity:
                shipment_validation = False
                break
        
        return shipment_validation, shipment_ids

    def prepare_and_save_invoice_pdf(self, invoice_url, unique_invoice_numbers, invoice_reference, invoice_number, order_reference, final_output_data, status_code):
        '''This method is used to prepare and save invoice pdf'''
        try:
            # Assuming you have the normal PDF file named "inv001.pdf"
            # replace / with -
            invoice_ref = invoice_reference.replace("/", "-")
            normal_pdf_path = f"{invoice_ref}{self.warehouse.id}.pdf"

            uploaded_file = ''
            if os.path.exists(normal_pdf_path):
                # Read the content of the PDF file into memory
                with open(normal_pdf_path, "rb") as f:
                    file_content = io.BytesIO(f.read())

                # Create an InMemoryUploadedFile object
                uploaded_file = InMemoryUploadedFile(
                    file=file_content,
                    field_name=None,  # Field name from form, if applicable
                    name=normal_pdf_path,  # Desired filename
                    content_type="application/pdf",
                    size=len(file_content.getvalue()),
                    charset=None  # Character encoding, if applicable
                )
            master_docs_list = []
            for each_invoice in unique_invoice_numbers:
                data_dict = {
                    'master_id': each_invoice,
                    'master_type': 'invoice',
                    'extra_flag': invoice_reference,
                    'document_url': invoice_url,
                    'user_id': self.warehouse.id,
                    'account_id': self.warehouse.userprofile.id
                }
                master_docs_obj= MasterDocs(**data_dict)
                master_docs_obj.uploaded_file = uploaded_file
                master_docs_list.append(master_docs_obj)
            if master_docs_list:
                MasterDocs.objects.bulk_create_with_rounding(master_docs_list)
            # Clean up temporary files if needed
            if os.path.exists(normal_pdf_path):
                os.remove(normal_pdf_path)
        except Exception as e:
            log.info('Error uploading PDF file for invoice %s and error statement is %s' % (invoice_reference, str(e)))
            final_output_data['data'].append({'message':"Error uploading PDF file", 'invoice_number':invoice_number, 'order_reference': order_reference, 'invoice_reference': invoice_reference})
            status_code = 400
        
        return final_output_data, status_code

    def post(self, request, *args, **kwargs):
        log.info("Request create invoice reference username %s, Ip Address %s, and params are %s"% (str(self.request.user.username),str(get_user_ip(self.request)), str(self.request.body)))
        try:
            self.request_data = loads(self.request.body)
        except Exception:
            self.request_data = []

        if not self.request_data:
            return {'error': [{'message':'Invalid Payload'}], 'status': 400}

        if not isinstance(self.request_data, list):
            return {'error': [{'message':'Request data should be list'}], 'status': 400}

        self.set_user_credientials()

        create_invoice_objects = CreateInvoiceMixin(self.request, self.request_data, self.user, self.warehouse, extra_params = {"order_status_flag": "customer_invoices"})
        invoice_result = create_invoice_objects.generate_invoice()
        errors = invoice_result.get("errors", [])
        if errors:
            return JsonResponse(invoice_result, status=400)
        return JsonResponse(invoice_result, status=201)


class ExternalInvoiceSet(WMSListView):
    """
    A view to handle the creation of external invoices in the WMS system.
    """
    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to create an invoice.
        This method processes the incoming request to create an invoice by performing the following steps:
        1. Parse the request body to JSON.
        2. Set user credentials.
        3. Validate the invoice request.
        4. Prepare the invoice creation payload.
        5. Trigger the invoice creation process.
        6. Update the invoice details.
        Args:
            request (HttpRequest): The HTTP request object.
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.
        Returns:
            JsonResponse: A JSON response with the result of the invoice creation process.
            - If there are validation errors, returns a JSON response with the errors and a 400 status code.
            - If the invoice is successfully created, returns a JSON response with the invoice details and a 201 status code.
        """

        try:
            self.request_data = loads(self.request.body)
        except Exception:
            return JsonResponse({'status': 'error', 'message': 'Invalid  payload'}, status=400)

        self.set_user_credientials()
        self.errors = []

        # Validate the invoice request
        if not self.validate_invoice_request():
            return JsonResponse({'errors': self.errors}, status=400)

        self.prepare_create_invoice_payload()

        invoice_result = self.trigger_invoice_creation()
        if invoice_result.get('errors'):
            return JsonResponse(invoice_result, status=400)

        # Update invoice details
        invoice_result = self.update_invoice_details(invoice_result)
        return JsonResponse(invoice_result, status=201)


    def validate_invoice_request(self) -> bool:
        """
        Validates the invoice request by performing a series of checks.
        This method validates the request data, invoice reference, stock details, and
        serial numbers and fetches necessary configurations and data. If any validation fails,
        it records the errors and returns False. Otherwise, it returns True.
        Returns:
            bool: True if the invoice request is valid, False otherwise.
        """

        self.validate_request_data()
        if self.errors:
            return False

        self.validate_invoice_reference()
        if self.errors:
            return False

        self.fetch_required_configurations()
        self.fetch_sos_data()
        if self.errors:
            return False

        self.validate_stock_details()
        if self.errors:
            return False

        self.validate_serial_numbers()
        return not self.errors


    def trigger_invoice_creation(self):
        """
        Triggers the creation of an invoice using the CreateInvoiceMixin.
        This method prepares the necessary parameters and invokes the 
        `generate_invoice` method from the CreateInvoiceMixin to create 
        an invoice.

        Utilizes:
            - CreateInvoiceMixin: A mixin class responsible for handling invoice 
              creation logic.

        Returns:
            dict: The generated invoice details.
        """

        extra_params = {
            "order_status_flag": "customer_invoices",
            "stock_df": self.stock_df,
            "serial_transactions": self.serial_transactions,
        }
        # Generate invoice
        create_invoice_objects = CreateInvoiceMixin(
            self.request, self.invoice_payload, self.user, self.warehouse, extra_params=extra_params
        )
        return create_invoice_objects.generate_invoice()


    def validate_request_data(self):
        """
        Validates the request data for invoice creation.

        This method checks for the presence of mandatory fields in the request data
        and validates the consistency of the data provided. It populates various
        dictionaries and sets used for further processing and validation.
        """

        item_mandatory_fields = ['order_reference', 'sku_code', 'quantity']
        self.order_references, self.sku_codes, self.unique_dict_keys = set(), set(), set()
        self.order_dict, self.invoice_qty_dict, self.serial_number_lpn_dict, self.serial_item_dict = {}, {}, {}, {}
        self.lpn_numbers = []

        self.custom_attributes = self.request_data.get('custom_attributes', {})

        if not self.request_data.get('items'):
            self.errors.append("Items are mandatory")
            return

        for item in self.request_data.get('items', []):
            # Check for mandatory fields in each item
            for field in item_mandatory_fields:
                if not item.get(field):
                    self.errors.append(f"{field} is a mandatory item field")

            self.order_references.add(item.get('order_reference', ''))
            self.sku_codes.add(item.get('sku_code'))
            invoice_key = (item.get('order_reference'), item.get('sku_code'))

            # Check for duplicate order references and SKU codes
            if item.get('order_reference') not in self.order_dict:
                self.order_dict[item.get('order_reference')] = {
                    'sku_codes': set(),
                    'line_references': set(),
                    'batch_numbers': set(),
                    'sale_prices': set(),
                }
            self.order_dict[item.get('order_reference')]['sku_codes'].add(item.get('sku_code'))
            if item.get('line_reference'):
                self.order_dict[item.get('order_reference')]['line_references'].add(item.get('line_reference'))
                self.unique_dict_keys.add('line_reference')
                invoice_key = invoice_key + (item.get('line_reference'),)
            if item.get('sale_price'):
                item['sale_price'] = float(item.get('sale_price', 0))
                self.order_dict[item.get('order_reference')]['sale_prices'].add(item.get('sale_price'))
                self.unique_dict_keys.add('sale_price')
                invoice_key = invoice_key + (item.get('sale_price'),)
            if item.get('batch_number'):
                self.order_dict[item.get('order_reference')]['batch_numbers'].add(item.get('batch_number'))
                self.unique_dict_keys.add('batch_number')
                invoice_key = invoice_key + (item.get('batch_number'),)

            if invoice_key not in self.invoice_qty_dict:
                self.invoice_qty_dict[invoice_key] = 0
            self.invoice_qty_dict[invoice_key] += item.get('quantity', 0)

            # Serial Numbers validation
            temp_serial_qty, temp_lpn_serial_qty = 0, 0
            if item.get('serial_numbers'):
                self.serial_item_dict[tuple(item.get('serial_numbers'))] = item
                temp_serial_qty = len(item.get('serial_numbers'))
                if temp_serial_qty != item.get('quantity'):
                    self.errors.append("Item quantity not matching with Serial Numbers count")
            for serial_number in item.get('serial_numbers', []):
                if serial_number in self.serial_number_lpn_dict:
                    self.errors.append("Serial Number already exists in the request")
                self.serial_number_lpn_dict[serial_number] = ''

            # LPN details validation
            temp_qty = 0
            for lpn_data in item.get('lpn_details', []):
                if not lpn_data.get('lpn_number'):
                    self.errors.append("LPN is a mandatory field in LPN details")
                else:
                    self.lpn_numbers.append(lpn_data.get('lpn_number'))
                if not lpn_data.get('quantity'):
                    self.errors.append("quantity is a mandatory field in LPN details")
                else:
                    temp_qty += lpn_data.get('quantity')
                # LPN Serial Numbers validation
                if lpn_data.get('serial_numbers'):
                    lpn_serial_count = len(lpn_data.get('serial_numbers', []))
                    temp_lpn_serial_qty += lpn_serial_count
                    if lpn_serial_count != lpn_data.get('quantity'):
                        self.errors.append("LPN quantity not matching with LPN Serial Numbers count")
                    if not set(lpn_data.get('serial_numbers')).issubset(item.get('serial_numbers', [])):
                        self.errors.append("LPN Serial Numbers should be subset of Item Serial Numbers")
                for serial_number in lpn_data.get('serial_numbers', []):
                    if self.serial_number_lpn_dict.get(serial_number):
                        self.errors.append(f"Serial number already exists in the request with LPN - {self.serial_number_lpn_dict[serial_number]}")
                    self.serial_number_lpn_dict[serial_number] = lpn_data.get('lpn_number')
            if temp_serial_qty and item.get('lpn_details') and temp_serial_qty != temp_lpn_serial_qty:
                self.errors.append("Item Serial Numbers quantity not matching with LPN Serial Numbers quantity")
            if item.get('lpn_details') and temp_qty != item.get('quantity'):
                self.errors.append("Item quantity not matching with LPN quantity")


    def validate_invoice_reference(self):
        """
        Validates the invoice reference in the request data.

        Checks if the 'invoice_reference' key is present in the request data. If it is,
        verifies whether an invoice with the same reference already exists for the current
        warehouse. If such an invoice exists, an error message is appended to the errors list.
        """

        if not self.request_data.get('invoice_reference'):
            return

        if SellerOrderSummary.objects.filter(invoice_reference=self.request_data.get('invoice_reference'), order__user=self.warehouse.id, order_status_flag__in=['customer_invoices', 'delivery_challans']).exists():
            self.errors.append("Invoice reference already exists")


    def fetch_required_configurations(self):
        """
        Fetches and sets the required configurations for the invoice process.

        This method retrieves miscellaneous configuration values and options
        related to the warehouse and sets the corresponding attributes for
        the instance.

        Retrieves:
            misc_dict (dict): Dictionary containing miscellaneous configuration
            values for the warehouse.
            misc_options_dict (dict): Dictionary containing miscellaneous options
            for the warehouse.
        """

        misc_types = ['enable_dispense', 'order_wise_inv', 'einvoice', 'sku_serialisation']
        misc_options_types = ['packing_mandatory_for_invoice']

        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        misc_options_dict = get_misc_options_list(misc_options_types, self.warehouse)

        self.enable_dispense, self.order_wise_invoice, self.einvoice_enabled, self.sku_serialisation = False, False, False, False

        if misc_dict.get('enable_dispense') == 'true':
            self.enable_dispense = True

        if misc_dict.get('order_wise_inv') == 'true':
            self.order_wise_invoice = True

        if misc_dict.get('einvoice') == 'true':
            self.einvoice_enabled = True

        if misc_dict.get('sku_serialisation') == 'true':
            self.sku_serialisation = True

        self.pack_mandatory_order_types = misc_options_dict.get('packing_mandatory_for_invoice', [])


    def fetch_sos_data(self):
        """
        Fetches Seller Order Summary (SOS) data based on the provided order dictionary and filters.

        This method initializes dictionaries and sets to store SOS data, constructs filters based on the 
        order dictionary, and queries the SellerOrderSummary model. It processes the query results to 
        populate the initialized dictionaries and sets. Additionally, it performs validations to ensure 
        that orders of multiple types or customers are not allowed and that the given quantities do not 
        exceed the picked quantities.
        """

        self.sos_id_dict, self.sos_qty_dict = {}, {}
        self.order_types, self.picklist_numbers, self.customer_ids, self.serial_transaction_ids = set(), set(), set(), set()

        if self.order_wise_invoice and self.request_data.get('invoice_reference') and len(self.order_references) > 1:
            self.errors.append("Multiple orders are not allowed with one invoice reference when order-iwse invoice is enabled")
            return

        sos_filter = {
            'order__user': self.warehouse.id,
            'order_status_flag': 'processed_orders',
            'quantity__gt': 0
        }
        q_filters = Q()

        for order_reference, data_dict in self.order_dict.items():
            q_dict = {
                'order__order_reference': order_reference,
                'order__sku__sku_code__in': data_dict['sku_codes'],
            }
            if data_dict['line_references']:
                q_dict['order__line_reference__in'] = data_dict['line_references']
            if data_dict['sale_prices']:
                q_dict['order__unit_price__in'] = data_dict['sale_prices']
            if data_dict['batch_numbers']:
                q_dict['picklist__stock__batch_detail__batch_no__in'] = data_dict['batch_numbers']
            q_filters |= Q(**q_dict)

        values_dict = {
            'order_reference': F('order__order_reference'),
            'order_type': F('order__order_type'),
            'sku_code': F('order__sku__sku_code'),
            'is_serial_sku' : F('order__sku__enable_serial_based'),
            'line_reference': F('order__line_reference'),
            'sale_price': F('order__unit_price'),
            'customer_id': F('order__customer_id'),
            'picklist_number': F('picklist__picklist_number'),
            'batch_number': F('picklist__stock__batch_detail__batch_no'),
        }

        sos_data = SellerOrderSummary.objects.filter(q_filters, **sos_filter).values('id', 'quantity', **values_dict)
        if not sos_data:
            self.errors.append("No data found for given order references")
            return

        for each in sos_data:
            self.order_types.add(each['order_type'])
            self.picklist_numbers.add(each['picklist_number'])
            self.customer_ids.add(each['customer_id'])
            # Construct SOS ID dictionary
            self.sos_id_dict[each['id']] = each
            sos_key = (each['order_reference'], each['sku_code'])
            for key in self.unique_dict_keys:
                sos_key += (each[key],)
            if sos_key not in self.sos_qty_dict:
                self.sos_qty_dict[sos_key] = {}
            self.sos_qty_dict[sos_key][each['id']] = each['quantity']
            if each.get('is_serial_sku'):
                self.serial_transaction_ids.add(each['id'])

        if len(self.order_types) > 1:
            self.errors.append("Orders of multiple order types are not allowed")
            return
        if len(self.customer_ids) > 1:
            self.errors.append("Orders of multiple customers are not allowed")
            return

        self.order_types = list(self.order_types)
        self.picklist_numbers = list(self.picklist_numbers)
        # Validate quantity
        for invoice_key, quantity in self.invoice_qty_dict.items():
            if invoice_key not in self.sos_qty_dict:
                self.errors.append(f"No data found for order reference {invoice_key[0]} and sku code {invoice_key[1]}")
            elif quantity > sum(list(self.sos_qty_dict.get(invoice_key, {}).values())):
                self.errors.append(f"Given Quantity exceeds picked quantity for order reference {invoice_key[0]} and sku code {invoice_key[1]}")


    def validate_stock_details(self):
        """
        Validates the stock details for the current order.

        This method performs the following validations:
        1. Checks if the picked stock has been moved to the PRE INVOICE / WIP location.
        2. Validates that all LPNs (License Plate Numbers) are picked and not null.
        3. Ensures that all picked LPNs have been dropped at the staging location.
        4. Verifies that the stock quantity matches the order quantity for each SOS (Stock Order Sheet) ID.
        """

        pack_before_invoice = self.order_types[0] in self.pack_mandatory_order_types or 'all' in self.pack_mandatory_order_types

        extra_params = {
            'pack_before_invoice': pack_before_invoice
        }
        self.stock_df = get_wip_location_stocks(self.warehouse, list(self.sos_id_dict.keys()), self.order_types, self.enable_dispense, extra_params)

        if self.stock_df.empty:
            self.errors.append("Picked stock not moved to PRE INVOICE / WIP location")
            return

        stock_lpns = self.stock_df[self.stock_df['lpn_number'].notnull()]['lpn_number'].tolist()
        if set(self.lpn_numbers) - set(stock_lpns):
            self.errors.append(f"Invalid LPNs - {','.join(set(self.lpn_numbers) - set(stock_lpns))}")
            return

        dropped_lpns = set()
        picked_lpns = self.stock_df[(self.stock_df['picked_lpn'] == True) & (self.stock_df['lpn_number'].notnull())]['lpn_number'].tolist()
        if picked_lpns:
            dropped_lpns = set(StagingInfo.objects
                .filter(user_id=self.warehouse.id, carton_no__in=picked_lpns, picklist_number__in=self.picklist_numbers,
                        status=0)
                .values_list('carton_no', flat=True)
                .distinct()
            )
            undropped_lpns = list(set(picked_lpns) - dropped_lpns)
            if undropped_lpns:
                self.errors.append(f"LPNs {','.join(undropped_lpns)} are pending to be dropped")
                return

        for sos_id, sos_dict in self.sos_id_dict.items():
            sos_quantity = sos_dict['quantity']
            stock_quantity = self.stock_df[self.stock_df['receipt_number'] == sos_id]['quantity'].sum()
            if stock_quantity < sos_quantity:
                self.errors.append(f"Picked stock not moved to PRE INVOICE / WIP location for order reference {sos_dict['order_reference']} and sku code {sos_dict['sku_code']}")
            sos_dict['lpn_numbers'] = set(self.stock_df[self.stock_df['receipt_number'] == sos_id]['lpn_number'].tolist())


    def validate_serial_numbers(self):
        """
        Validates the serial numbers provided in the request against the serial numbers
        available in the system for the given SOS (Stock Order Shipment) IDs.
        This method performs the following steps:

        1. Retrieves serial number transaction details based on the provided filters.
        2. Prepares and organizes serial number data for each SOS ID, including:
           - Serial numbers
           - LPN (License Plate Number) serial numbers
        3. Validates the request serial numbers by comparing them with the available
           serial numbers in the system for each SOS ID.
        If any invalid serial numbers are found, an error message is appended to the
        `self.errors` list, specifying the invalid serial numbers, order reference, 
        and SKU code.
        """

        self.serial_transactions = {}

        if not (self.sku_serialisation and self.serial_transaction_ids):
            return

        filter_data = {
            'filters': {
                'transact_id__in': self.sos_id_dict,
                'reference_type': 'so_picking',
                'status': 1
            }
        }
        serialnumber_get_instance = SerialNumberTransactionMixin(self.user, self.warehouse, filter_data)
        self.serial_transactions = serialnumber_get_instance.get_sntd_details()

        # Frame Serial Number data for each SOS ID
        for data in self.serial_transactions.get('data', []):
            transact_id = data.get('transact_id', '')
            serial_numbers = data.get('serial_numbers', [])
            lpn_number = data.get('lpn_number', '')
            if not serial_numbers:
                continue
            # Serial Numbers data preparation
            if 'serial_numbers' not in self.sos_id_dict[transact_id]:
                self.sos_id_dict[transact_id]['serial_numbers'] = []
            self.sos_id_dict[transact_id]['serial_numbers'].extend(serial_numbers)
            # LPN Serial Numbers data preparation
            if lpn_number:
                if 'lpn_serial_numbers' not in self.sos_id_dict[transact_id]:
                    self.sos_id_dict[transact_id]['lpn_serial_numbers'] = {}
                if lpn_number not in self.sos_id_dict[transact_id]['lpn_serial_numbers']:
                    self.sos_id_dict[transact_id]['lpn_serial_numbers'][lpn_number] = []
                self.sos_id_dict[transact_id]['lpn_serial_numbers'][lpn_number].extend(serial_numbers)

        # Validate request Serial Numbers
        for serial_numbers, serial_item in self.serial_item_dict.items():
            temp_serials = set(serial_numbers)
            serial_key = (serial_item['order_reference'], serial_item['sku_code'])
            for key in self.unique_dict_keys:
                serial_key += (serial_item[key],)
            for sos_id in self.sos_qty_dict.get(serial_key, {}).keys():
                if not temp_serials:
                    break
                temp_serials = temp_serials - set(self.sos_id_dict[sos_id].get('serial_numbers', []))
            if temp_serials:
                self.errors.append(f"Invalid Serial Numbers - {','.join(temp_serials)} for order reference {serial_item['order_reference']} and sku code {serial_item['sku_code']}")


    def frame_sos_id_level_payload(self, sos_key, quantity, lpn_number='', serial_numbers=None):
        """
        Constructs and appends payload data for each SOS (Stock Order Shipment) ID based on the given quantity.

        This method iterates through the SOS quantities associated with the provided sos_key, and for each SOS ID,
        it calculates the transaction quantity, updates the SOS quantity dictionary, and appends the transaction
        details to the invoice payload.

        Args:
            sos_key (str): The key to identify the SOS quantities in the sos_qty_dict.
            quantity (int): The total quantity to be processed and added to the payload.
            lpn_number (str, optional): The LPN (License Plate Number) associated with the transaction. Defaults to ''.
            serial_numbers (list, optional): A list of serial numbers associated with the transaction. Defaults to None.
        """

        if serial_numbers is None:
            serial_numbers = []
        transact_serial_numbers = []

        for sos_id, sos_quantity in self.sos_qty_dict.get(sos_key, {}).items():
            if quantity <= 0:
                break
            if sos_quantity <= 0:
                continue

            # LPN Number payload preparation
            if lpn_number:
                if lpn_number not in self.sos_id_dict[sos_id]['lpn_numbers']:
                    continue
                # LPN Serial Numbers payload preparation
                if serial_numbers:
                    transact_serial_numbers = set(serial_numbers) & set(self.sos_id_dict[sos_id].get('lpn_serial_numbers', {}).get(lpn_number, set()))
                    if not transact_serial_numbers:
                        continue
                    serial_numbers = list(set(serial_numbers) - transact_serial_numbers)
            # Serial Numbers payload preparation
            elif serial_numbers:
                transact_serial_numbers = set(serial_numbers) & set(self.sos_id_dict[sos_id].get('serial_numbers', set()))
                if not transact_serial_numbers:
                    continue
                serial_numbers = list(set(serial_numbers) - transact_serial_numbers)

            transaction_qty = min(quantity, sos_quantity)
            self.sos_qty_dict[sos_key][sos_id] -= transaction_qty
            quantity -= transaction_qty
            self.invoice_payload.append({
                'transaction_id': sos_id,
                'quantity': transaction_qty,
                'lpn_number': lpn_number,
                'serial_numbers': list(transact_serial_numbers),
                'custom_attributes': self.custom_attributes,
            })


    def prepare_create_invoice_payload(self):
        """
        Prepares the payload for creating an invoice.

        This method initializes the `invoice_payload` attribute as an empty list.
        It then iterates over the items in `request_data` and constructs a unique key
        (`sos_key`) for each item based on the order reference, SKU code, and other unique
        dictionary keys. It processes the quantity and LPN (License Plate Number) details
        for each item and frames the payload at the SOS ID level.

        If LPN details are present, it processes each LPN's number and quantity.
        If LPN details are not present, it processes the item's quantity directly.
        """

        self.invoice_payload = []

        for item in self.request_data['items']:

            sos_key = (item.get('order_reference'), item.get('sku_code'))
            for key in self.unique_dict_keys:
                sos_key += (item.get(key),)
            quantity = item.get('quantity', 0)

            for lpn in item.get('lpn_details', []):
                lpn_number = lpn.get('lpn_number')
                lpn_quantity = lpn.get('quantity')
                serial_numbers = lpn.get('serial_numbers', [])
                self.frame_sos_id_level_payload(sos_key, lpn_quantity, lpn_number, serial_numbers)

            if not item.get('lpn_details'):
                serial_numbers = item.get('serial_numbers', [])
                self.frame_sos_id_level_payload(sos_key, quantity, serial_numbers=serial_numbers)

        return self.invoice_payload


    def update_invoice_details(self, invoice_result):
        """
        Updates the invoice details based on the provided invoice_result and request data.

        Args:
            invoice_result (dict): A dictionary containing the invoice details to be updated.

        Returns:
            dict: The updated invoice_result dictionary. Invoice Reference will be replaced with the new invoice reference.

        Updates:
            - Updates the invoice reference in the invoice_result and related database records.
            - Decodes and writes the encoded URL to a PDF file if provided.
            - Updates or creates a master document record with the invoice URL.

        Raises:
            Exception: If there is an error decoding or writing the PDF file.
        """

        invoice_reference = self.request_data.get('invoice_reference')
        invoice_url = self.request_data.get('invoice_url')
        encoded_url = self.request_data.get('encoded_url')
        full_invoice_number = invoice_result.get('invoice_references', [])[0]

        invoice_obj = InvoiceSet()
        invoice_obj.warehouse = self.warehouse

        if invoice_reference:
            invoice_result['invoice_references'] = [invoice_reference]
            if invoice_result.get('inv_data'):
                invoice_result['inv_data'][0]['inv_ref'] = invoice_reference

            # Invoice Reference updation
            SellerOrderSummary.objects.filter(invoice_reference=full_invoice_number, order__user=self.warehouse.id, order_status_flag__in=['customer_invoices', 'delivery_challans']).update(invoice_reference=invoice_reference)
            InvoiceDetail.objects.filter(invoice_reference=full_invoice_number, warehouse_id=self.warehouse.id).update(invoice_reference=invoice_reference)
            ShipmentInvoice.objects.filter(user_id=self.warehouse.id, reference_number=full_invoice_number).update(reference_number=invoice_reference)

            filter_data = {'full_invoice_number': full_invoice_number, 'order__user': self.warehouse.id}
            invoice_obj.invoice_order_reference_map = defaultdict(set)
            sos_objs, *_ = invoice_obj.prepare_unique_sos_values(filter_data, '')

            # Prepare SOS data for stock transfer
            invoice_obj.asn_objs, invoice_obj.sps_objs = [], []
            invoice_obj.stock_transfer_order_types = set()
            invoice_obj.wh_stock_transfer_data = defaultdict(lambda: defaultdict(dict))
            invoice_obj.get_order_type_details(self.order_types)
            invoice_obj.prepare_stocktransfer_data(sos_objs, invoice_reference, full_invoice_number)

            # Invoice Reference updation in Stock Transfer ASNs
            if invoice_obj.wh_stock_transfer_data:
                invoice_obj.update_stock_transfer_data()
            # Invoice Reference updation in IRN
            if self.einvoice_enabled:
                IRN.objects.filter(user_id=self.warehouse.id, transact_id=full_invoice_number, transact_type='invoice').update(transact_id=invoice_reference)
            # Invoice Reference updation in Serial Number Transaction
            if self.sku_serialisation and self.serial_transaction_ids:
                SerialNumberTransaction.objects.filter(warehouse=self.warehouse.id, reference_type='invoice', reference_number=full_invoice_number).update(reference_number=invoice_reference)

        if encoded_url:
            try:
                invoice_ref = invoice_reference.replace("/", "-")
                decoded_data = base64.b64decode(encoded_url)
                with open(f"{invoice_ref}{self.warehouse.id}.pdf", 'wb') as f:
                    f.write(decoded_data)
            except Exception as e:
                log.error(f"Error decoding or writing PDF file for invoice {invoice_reference} and error statement is {str(e)}")

        if invoice_url or encoded_url:
            master_docs_objs = MasterDocs.objects.filter(user_id=self.warehouse.id, master_type='invoice', master_id=full_invoice_number)
            if master_docs_objs.exists():
                master_docs_objs.update(document_url=invoice_url)
            else:
                invoice_obj.prepare_and_save_invoice_pdf(invoice_url, [full_invoice_number], invoice_reference, '', '', {'data': []}, 200)

        return invoice_result
    

class InvoiceSerialScan(WMSListView):
    """"""
    
    def validate_request_data(self):
        """
        Validates the request data to ensure all required fields are present.
        """
        self.serial_numbers, self.order_references  = set(), set()
        required_feilds = ['serial_number', 'order_reference']
        for item in self.request_data:
            for field in required_feilds:
                if not item.get(field):
                    self.errors.append(f"{field} is a mandatory field")
                    continue
                if field == 'serial_number':
                    self.serial_numbers.add(item.get(field))
                if field == 'order_reference':
                    self.order_references.add(item.get(field))
                    
    def get_misc_details(self):
        """
        Get misc details for the warehouse.
        """
        self.serial_number_mapping = False
        misc_types = ['serial_number_mapping']
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        
        serial_number_mapping = misc_dict.get('serial_number_mapping','at_picklist') or 'at_picklist'
        if serial_number_mapping == 'at_invoice':
            self.serial_number_mapping = True
                    
    def get_sos_data(self):
        """
        Get SOS data for the given order references.
        """
        sos_filter = {
            'order__user': self.warehouse.id,
            'order_status_flag': 'processed_orders',
            'order__order_reference__in': self.order_references
        }
        sos_values = [
            'id', 'order__order_reference', 'order__sku_code', 'order__line_reference', 'quantity',
            'picklist__stock__batch_detail__batch_no', 'picklist__stock__location__location',
        ]
        sos_objects = SellerOrderSummary.objects.select_related('order', 'picklist','picklist__stock').filter(**sos_filter).only(*sos_values)
        sos_obj_list, self.sos_ids = [], defaultdict(int)
        for obj in sos_objects:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['sos_transact_id'] = obj_dict.get('id', '')
            obj_dict['order_reference'] = obj.order.order_reference
            obj_dict['sku_code'] = obj.order.sku_code
            obj_dict['line_reference'] = obj.order.line_reference
            batch_no = ''
            if obj.picklist.stock.batch_detail:
                batch_no = obj.picklist.stock.batch_detail.batch_no
            obj_dict['batch_number'] = batch_no
            obj_dict['location'] = obj.picklist.stock.location.location
            obj_dict['object'] = obj
            sos_obj_list.append(obj_dict)
            self.sos_ids[obj_dict.get('id')] += obj.quantity
            
        self.sos_df = pd.DataFrame(sos_obj_list)
        
        if self.sos_df.empty:
            self.errors.append("No data found for given order references")
            return        
        stock_values = [
            'id', 'receipt_number', 'lpn_number'
        ]
        extra_params = {
            'fields': stock_values,
        }
        self.stock_df = get_wip_location_stocks(self.warehouse, list(self.sos_ids.keys()), extra_params=extra_params)
        
        self.sos_stock_df = pd.merge(self.sos_df, self.stock_df, left_on='sos_transact_id', right_on='receipt_number', how='inner')
        if self.sos_stock_df.empty:
            self.errors.append("No stock data found for given order references")
            
        self.sos_stock_df['lpn_number'] = self.sos_stock_df['lpn_number'].fillna('')
    
    def get_serialization_data(self):
        """
        Get Serial Number Transaction data for the given serial numbers.
        """
        filters = {
            'warehouse': self.warehouse.id,
            'serial_number__in': self.serial_numbers,
            'reference_type': 'so_picking',
            'status': 1
        }
        values = [
            'serial_number', 'reference_number', 'reference_type', 'transact_id', 'transact_type',
            'sku_code', 'batch_number', 'location', 'status', 'lpn_number'
        ]
        serial_objects = SerialNumberTransaction.objects.filter(**filters).only(*values)
        serial_data_list = []
        for obj in serial_objects:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['lpn_number'] = obj_dict.get('lpn_number','') or ''
            obj_dict['batch_number'] = obj_dict.get('batch_number','') or ''
            obj_dict['object'] = obj
            serial_data_list.append(obj_dict)
            
        self.serial_data = pd.DataFrame(serial_data_list)
        if self.serial_data.empty:
            self.errors.append("No data found for given serial numbers")
            return
        self.serial_data['lpn_number'] = self.serial_data['lpn_number'].fillna('').astype(str).str.strip().replace("", "")

        for data in self.request_data:
            serial_number = data.get('serial_number')
            
            serial_data = self.serial_data[self.serial_data['serial_number'] == serial_number]
            if serial_data.empty:
                self.errors.append(f"No data found for serial number {serial_number}")
                continue
            
        mapped_serial_filters = {
            'warehouse': self.warehouse.id,
            'transact_id__in' : list(self.sos_ids.keys()),
            'reference_type__in': ['so_picking', 'invoice'],
            'status': 1
        }
        self.mappped_transact_id_count = dict(SerialNumberTransaction.objects.filter(**mapped_serial_filters).values_list('transact_id').annotate(transact_id_count=Count('transact_id')))
        self.fully_mapped_ids = []
        for id, quantity in self.mappped_transact_id_count.items():
            if quantity == int(self.sos_ids.get(id, 0)):
                self.fully_mapped_ids.append(id)
                    
    def get_serial_data(self):
        """
        Get Serial Number Transaction data for the given serial numbers
        and Seller Order Summary Data for the order reference.
        """
        self.get_sos_data()
        if self.errors:
            return
        self.get_serialization_data()
        
    def map_serial_data(self, serial_data, sos_data, status, serial_number, order_reference):
        
        log.info(f"sos_object_data {sos_data.to_dict(orient='records')}")
        sos_object_data = sos_data.sort_values('line_reference').iloc[0]
        serial_object = serial_data.iloc[0]['object']
        if status == 'inactive':
            if serial_object:
                transact_id = int(serial_object.transact_id)
                serial_object.transact_id = 0
                resp_dict = {
                    'transact_id': transact_id,
                    'serial_number': serial_number,
                    'order_reference': order_reference,
                    'status': 'removed',
                }
                self.serial_update_data.append(serial_object)
                self.response_data.append(resp_dict)
        elif status == 'active':
            if serial_object and not sos_object_data.empty:
                transact_id = int(sos_object_data['sos_transact_id'])
                serial_object.transact_id = transact_id
                resp_dict = {
                    'transact_id': transact_id,
                    'serial_number': serial_number,
                    'order_reference': order_reference,
                    'status': 'scanned',
                }
                self.serial_update_data.append(serial_object)
                self.response_data.append(resp_dict)
                

    def validate_undo_scans(self, serial_data, sos_data, serial_number, order_reference):
        """
        Validate the undo scans for the serial number.
        """
        transact_ids = list(sos_data['sos_transact_id'].values)
        if serial_data['transact_id'][0] not in transact_ids:
            self.errors.append(f"Invalid Serial number {serial_number}, Serial number not found for order reference {order_reference}")
            return False
        
        return True
                
    def validate_scan_data(self, serial_data, sos_data, serial_number, order_reference):
        """
        Validate the scan data for the serial number.
        """
        if serial_data['transact_id'].values.any():
            self.errors.append(f"Serial number {serial_number} already scanned")
            return False, sos_data
        
        sos_data = sos_data[~sos_data['sos_transact_id'].isin(self.fully_mapped_ids)]
        if sos_data.empty:
            self.errors.append(f"SKU is completely scanned for order reference {order_reference}")
            return False, sos_data

        return True, sos_data
                
    def process_serial_scan(self):
        """
        Validate the serial numbers and process the serial scan.
        """
        self.serial_update_data = []
        for data in self.request_data:
            serial_number = data.get('serial_number')
            order_reference = data.get('order_reference')
            status = data.get('status','active') or 'active'
            
            serial_data = self.serial_data[self.serial_data['serial_number'] == serial_number]
            sos_data = self.sos_stock_df[self.sos_stock_df['order_reference'] == order_reference]
            if sos_data.empty:
                self.errors.append(f"No data found for order reference {order_reference}")
                continue
            
            if status == 'inactive':
                if not self.validate_undo_scans(serial_data, sos_data, serial_number, order_reference):
                    continue
            
            elif status == 'active':
                error_status, sos_data = self.validate_scan_data(serial_data, sos_data, serial_number, order_reference)
                if not error_status:
                    continue
            
            # check whether there are records are the available after merrging the serial data with sos data on sku code, batch , location and zone
            left_on = ['sku_code', 'batch_number', 'location', 'lpn_number']
            right_on = ['sku_code', 'batch_number', 'location', 'lpn_number']
            validate_scan_data = pd.merge(serial_data, sos_data, left_on=left_on, right_on=right_on, how='inner')
            if validate_scan_data.empty:
                self.errors.append(f"Serial Number {serial_number} data not found for order reference {order_reference}")
                continue
            
            # get sos data order by line reference and get first record
            self.map_serial_data(serial_data, validate_scan_data, status, serial_number, order_reference)
            
        if self.serial_update_data:
            SerialNumberTransaction.objects.bulk_update(self.serial_update_data, ['transact_id'])
    
    def put(self, request, *args, **kwargs):
        """
        Handle the HTTP POST request for scanning serial numbers.

        Args:
            request (HttpRequest): The HTTP request object.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: The JSON response containing the scanned serial numbers and status code.
        """
        try:
            self.request_data = loads(self.request.body)
        except Exception:
            return JsonResponse({'status': 'error', 'errors': ['Invalid  payload'], 'data':[]}, status=400)

        self.set_user_credientials()
        self.errors, self.response_data = [], []

        try:
            self.validate_request_data()
            if self.errors:
                return JsonResponse({'status': 'failed', 'errors': self.errors, 'data':[]}, status=400)
            
            self.get_misc_details()
            if not self.serial_number_mapping:
                return JsonResponse({'status': 'failed','errors': ['Order serial mapping at invoice is not enabled'], 'data':[]}, status=400)
            
            self.get_serial_data()
            if self.errors:
                return JsonResponse({'status':'failed', 'errors': self.errors, 'data':[]}, status=400)

            self.process_serial_scan()
            if self.errors:
                return JsonResponse({'status':'failed', 'errors': self.errors, 'data':self.response_data}, status=400)
            return JsonResponse({'status': 'success', 'data':self.response_data, 'message': 'Serial numbers scanned successfully'}, status=200)
        except Exception as e:
            log.info(f"Error scanning serial numbers and error statement is {str(e)}")
            log.debug(traceback.format_exc())
            return JsonResponse({'status': 'error', 'errors': [f"Error scanning serial numbers{e}"], 'data':[]}, status=400)
