#package imports
from copy import deepcopy

#django and pandas imports
import pandas as pd
from django.db.models import F
from collections import defaultdict

#wms base imports
from wms_base.models import User

#core operations imports
from core.models import SKUMaster, EANNumbers
from core_operations.views.common.main import (
    get_multiple_misc_values , get_decimal_value, truncate_float,
    get_local_date_known_timezone, get_misc_options_list
)
from core_operations.views.services.packing_service import PackingService

#inventory imports
from inventory.models import (
    LocationMaster, StockDetail
)
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#outbound imports
from outbound.models import (
    OrderDetail, Picklist, SellerOrderSummary, StagingInfo, AuditEntry
)
from outbound.views.invoice.helpers import get_wip_location_stocks

class InvoicePreviewDetails:
    def __init__(self, request, request_data: dict, request_user:User, warehouse:User):
        self.request = request
        self.request_data = request_data
        self.user = request_user
        self.warehouse = warehouse
        self.timezone = self.warehouse.userprofile.timezone or "Asia/Calcutta"

    def get_invoice_preview_detail_view(self):
        self.errors, self.final_detail = [], []
        self.sos_ids, self.order_types, self.picklist_numbers, self.ean_numbers = [], [], [], {}
        self.lpn_numbers_set = set()
        self.customer_refs, self.serialized_picklists, self.serilized_data, self.lpn_wise_serilized_data, self.sku_codes, self.suspended_qty_dict = set(), set(), defaultdict(list), defaultdict(list), set(), defaultdict(float)

        search_params = self.get_sos_filters()
        if self.errors:
            return self.final_detail, self.errors

        self.decimal_limit = get_decimal_value(self.warehouse.id)
        self.get_sos_details(search_params)
        if not self.sos_details:
            self.errors.append('No data found!')
            return self.final_detail, self.errors

        #Get configurations
        self.get_invoice_preview_misc_details()

        # get ean numbers
        self.ean_numbers = self.get_ean_numbers(self.sku_codes)
        if not self.allow_multi_customer_lpn_scan_at_invoice and len(self.customer_refs) > 1:
            self.errors.append('invoice for multiple customers is not allowed')
            return self.final_detail, self.errors

        self.get_stock_details()

        if self.serialized_picklists:
            self.get_serialized_picklist_data()

        #lpn get details
        if not self.lpn_wise_invoice_preview:
            self.get_lpn_details_from_stock()
        else:
            self.get_packing_service_data()

        self.get_reusable_lpn_data()

        if self.errors:
            return self.final_detail, self.errors

        self.get_audit_lpn_data()

        self.prepare_data_for_invoice()
        return self.final_detail, self.errors

    def get_reusable_lpn_data(self):
        '''
        Get reusable Lpns.
        '''
        self.reusable_lpns = set()
        if not self.packing_enabled:
            return
        request_dict = {
            "request_headers": {
                "Warehouse": self.request.headers.get('Warehouse', ''),
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }
        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        lpn_numbers = list(self.lpn_numbers_set)
        params = {
            "warehouse": self.warehouse.username,
            "lpn_number": ",".join(lpn_numbers),
            "reusable": True,
            "limit": len(lpn_numbers),
        }
        self.lpn_details, packing_service_errors = packing_service_instance.get_lpn_number_details(params)
        if packing_service_errors:
            self.errors.extend(packing_service_errors)
            return
        for lpn_detail in self.lpn_details:
            lpn_number = lpn_detail.get('lpn_number', '')
            self.reusable_lpns.add(lpn_number)

    def get_serialized_picklist_data(self):
        '''
        Get Serialized picklist Data.
        '''
        request_data = {
            'filters' :{
                'reference_number__in': self.serialized_picklists,
                'reference_type': 'so_picking',
                'status': 1
            }
        }
        serialnumberinstance = SerialNumberTransactionMixin(self.user, self.warehouse, request_data)
        serial_data = serialnumberinstance.get_sntd_details()

        for data in serial_data.get('data', []):
            lpn_number = data.get('lpn_number', '') or ''
            transact_id = data.get('transact_id', '') or ''
            serial_numbers = data.get('serial_numbers', []) or []
            if transact_id:
                self.serilized_data[(transact_id, lpn_number)].extend(serial_numbers)
                if lpn_number and self.lpn_wise_invoice_preview:
                    self.lpn_wise_serilized_data[lpn_number].extend(serial_numbers)


    def get_invoice_preview_misc_details(self):
        '''
        Get misc details
        '''
        self.packing_enabled, self.dispense_enabled, self.pack_before_invoice, self.lpn_wise_invoice_preview, self.order_serial_mapping = True, False, False, False, False
        self.sales_uom_enabled, self.merge_stock_ids, self.allow_multi_customer_lpn_scan_at_invoice = False, False, True

        misc_types = ['packing_switch','enable_dispense', 'lpn_wise_invoice_preview', 'order_serial_mapping', 'enable_sales_uom', 'merge_stock_ids_in_invoice', 'allow_multi_customer_lpn_scan_at_invoice']
        misc_options_types = ['packing_mandatory_for_invoice']

        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        packing_before_invoice_misc_options = get_misc_options_list(misc_options_types, self.warehouse)

        #Packing switch config
        if misc_dict.get('packing_switch','false') in ['false', False, '', None]:
            self.packing_enabled = False

        #Dispensing configuration
        if misc_dict.get('enable_dispense','false') not in ['false', False, '', None]:
            self.dispense_enabled = True

        #lpn wise invoice preview
        if misc_dict.get('lpn_wise_invoice_preview', 'false') in ['true', True]:
            self.lpn_wise_invoice_preview = True

        #Serial Mapping Config
        if misc_dict.get('order_serial_mapping', 'false') == 'at_invoice':
            self.order_serial_mapping = True

        #sale uom configuration
        if misc_dict.get('enable_sales_uom', 'false') in ['true', True]:
            self.sales_uom_enabled = True

        #Merge stock id at invoice config
        if misc_dict.get('merge_stock_ids_in_invoice', 'false') in ['true', True]:
            self.merge_stock_ids = True

        if misc_dict.get('allow_multi_customer_lpn_scan_at_invoice') in ['false', None, False]:
            self.allow_multi_customer_lpn_scan_at_invoice = False

        #Packing mandatory configuration
        if self.order_types:
            if self.order_types[0].lower() in packing_before_invoice_misc_options.get('packing_mandatory_for_invoice', []) or 'all' in packing_before_invoice_misc_options.get('packing_mandatory_for_invoice', []):
                self.pack_before_invoice = True

    def get_stock_details(self):
        self.stock_df = pd.DataFrame()
        extra_params = {
            "invoice": True,
            "pack_before_invoice": self.pack_before_invoice,
            "lpn_number": self.request_data.get('lpn_number', ''),
            "fields": ['id', 'receipt_number', 'receipt_type', 'sku_id', 'grn_number', 'quantity', 'lpn_number', 'picked_lpn', 'batch_detail__batch_no'],
        }
        if self.sos_ids:
            self.stock_df = get_wip_location_stocks(self.warehouse, self.sos_ids, self.order_types, self.dispense_enabled, extra_params)
        if not self.stock_df.empty:
            self.stock_df['batch_detail__batch_no'] = self.stock_df['batch_detail__batch_no'].fillna('')
            self.validate_picked_lpn_dropped_or_not()
        return self.stock_df

    def validate_picked_lpn_dropped_or_not(self):
        """
        Validates if the picked LPNs (License Plate Numbers) have been dropped or not.

        This method checks the list of LPN numbers that have been marked as picked in the stock dataframe.
        It then queries the StagingInfo table to check if these LPNs have been dropped or not.
        LPNs that have been dropped are removed from the stock dataframe.

        Returns:
            None
        """
        lpn_numbers = self.stock_df[self.stock_df['picked_lpn'] == True]['lpn_number'].tolist()
        if lpn_numbers:
            lpn_numbers = [lpn for lpn in lpn_numbers if lpn]
        staging_picklist_numbers = [int(picklist) for picklist in self.picklist_numbers]
        dropped_lpns = list(StagingInfo.objects.filter(user_id=self.warehouse.id, carton_no__in=lpn_numbers, \
            picklist_number__in=staging_picklist_numbers, status=0).values_list('carton_no', flat=True))
        undropped_lpns = list(set(lpn_numbers) - set(dropped_lpns))
        if undropped_lpns:
            self.stock_df = self.stock_df[~self.stock_df['lpn_number'].isin(undropped_lpns)]

    def get_request_picklist_customer_details(self, key, value, search_params):
        '''
        Get picklist number and customer id from request data with comma separated values
        '''
        reference_numbers = []
        ref_nos = value.split(',')
        for ref_no in ref_nos:
            if ref_no and ref_no.strip():
                reference_numbers.append(ref_no)
            else:
                self.errors.append('Invalid request details, Please check!')
                return search_params
        if 'picklist_numbers' in key:
            search_params['picklist__picklist_number__in'] = reference_numbers
        elif 'customer_id' in key:
            search_params['order__customer_id__in'] = reference_numbers
        elif 'order_reference' in key:
            search_params['order__order_reference__in'] = reference_numbers
        return search_params

    def get_comma_supparated_values(self, key, value, bulk_key, search_key, search_params):
        '''
        Get comma separated values
        '''
        if ',' in value:
            search_params[bulk_key % key] = [ele for ele in value.split(',') if ele]
        else:
            search_params[search_key % key] = value
        return search_params

    def get_sos_filters(self):
        '''
        Get sos filters
        '''
        sku_model = [field.name for field in SKUMaster._meta.get_fields()]
        location_model = [field.name for field in LocationMaster._meta.get_fields()]
        orderdetail_model = [field.name for field in OrderDetail._meta.get_fields()]
        picklist_model = [field.name for field in Picklist._meta.get_fields()]

        search_params = {
            "order__user":self.warehouse.id,
            "order_status_flag": "processed_orders"
        }

        for key, value in self.request_data.items():
            if key in sku_model:
                search_params['order__sku__%s' % key] = value
            elif key in ['picklist_numbers', 'customer_id', 'order_reference']:
                search_params = self.get_request_picklist_customer_details(key, value, search_params)
                if self.errors:
                    return search_params
            elif key in location_model:
                search_params['location__%s' % key] = value
            elif key in orderdetail_model:
                search_params = self.get_comma_supparated_values(key, value, 'order__%s__in', 'order__%s__in', search_params)
            elif key in picklist_model:
                search_params = self.get_comma_supparated_values(key, value, 'picklist__%s__in', 'picklist__%s', search_params)
            elif 'orderpackaging' in key:
                search_params = self.get_comma_supparated_values(key, value, '%s__in', '%s', search_params)

        lpn_numbers = self.request_data.get('lpn_number', '')
        if lpn_numbers:
            search_params = self.get_lpn_stock_id(lpn_numbers, search_params)

        return search_params

    def get_lpn_stock_id(self, lpn_numbers, search_params):
        """
        Retrieves the stock IDs associated with the given LPN numbers.

        Args:
            lpn_numbers (str): Comma-separated LPN numbers.
            search_params (dict): Dictionary containing search parameters.

        Returns:
            dict: Updated search parameters with the LPN-related stock IDs.
        """
        lpn_list = [lpn for lpn in lpn_numbers.split(',') if lpn]
        if lpn_list:
            sos_ids = list(StockDetail.objects.filter(sku__user=self.warehouse.id, lpn_number__in=lpn_list, location__zone__segregation='outbound_staging', quantity__gt=0).values_list('receipt_number', flat=True))
            if sos_ids:
                search_params['id__in'] = sos_ids
            else:
                self.errors.append('No data found for the given LPN numbers!')
        return search_params

    def get_sos_details(self, search_params):
        '''
        Get sos details
        '''
        self.sos_details = list(SellerOrderSummary.objects.filter(**search_params).values('quantity', transaction_id=F('id'), order_reference=F('order__order_reference'),
            picklist_number=F('picklist__picklist_number'), order_quantity=F('order__original_quantity'), picklist_quantity=F('picklist__picklist_quantity'),
            customer_reference=F('order__customer_identifier__customer_reference'), customer_name=F('order__customer_name'), customer_address=F('order__address'),
            sku_code=F('order__sku__sku_code'), sku_description=F('order__sku__sku_desc'), picked_quantity=F('picklist__picked_quantity'),
            batch_reference=F('picklist__stock__batch_detail__batch_reference'), manufactured_date=F('picklist__stock__batch_detail__manufactured_date'),
            expiry_date=F('picklist__stock__batch_detail__expiry_date'), batch_number=F('picklist__stock__batch_detail__batch_no'), location=F('picklist__stock__location__location'),
            sku_batch_based=F('order__sku__batch_based'), order_type=F('order__order_type'), pack_uom_quantity=F('order__json_data__pack_uom_quantity'), is_serialized=F('order__sku__enable_serial_based'),
            pack_id = F('order__json_data__pack_id'), sku_image=F('picklist__sku__image_url'), mrp=F('order__mrp'), line_reference=F('order__line_reference'), picked_date=F('picklist__json_data__confirmation_time'),
            suspended_quantity=F('order__suspended_quantity'), batch_mrp=F('picklist__stock__batch_detail__mrp'),))

        for sos_record in self.sos_details:
            sos_record['order_quantity'] = truncate_float(sos_record['order_quantity'], self.decimal_limit)
            sos_record['picklist_quantity'] = truncate_float(sos_record['picklist_quantity'], self.decimal_limit)
            sos_record['picked_quantity'] = truncate_float(sos_record['quantity'], self.decimal_limit)
            sos_record['quantity'] = truncate_float(sos_record['quantity'], self.decimal_limit)
            sos_record['suspended_quantity'] = truncate_float(sos_record['suspended_quantity'], self.decimal_limit)
            self.format_detatime(sos_record, ['manufactured_date', 'expiry_date'])
            sos_record['batch_details'] = {}
            if sos_record.get('is_serialized', 0) == 1:
                self.serialized_picklists.add(sos_record.get('picklist_number', ''))
            if sos_record['batch_number'] or sos_record['batch_reference']:
                sos_record['batch_details'] = {
                    "batch_number": sos_record['batch_number'],
                    "batch_reference": sos_record['batch_reference'],
                    "manufactured_date": sos_record['manufactured_date'],
                    "expiry_date": sos_record['expiry_date'],
                    "batch_mrp": sos_record['batch_mrp'],
                }

            if str(sos_record['picklist_number']) not in self.picklist_numbers:
                self.picklist_numbers.append(str(sos_record['picklist_number']))
            if sos_record['transaction_id'] not in self.sos_ids:
                self.sos_ids.append(sos_record['transaction_id'])
            if sos_record['order_type'] not in self.order_types:
                self.order_types.append(sos_record['order_type'])
            suspended_qty_key = (sos_record.get('order_reference', ''), sos_record.get('sku_code', ''), sos_record.get('line_reference', ''))
            if suspended_qty_key not in self.suspended_qty_dict:
                self.suspended_qty_dict[suspended_qty_key] = sos_record.get('suspended_quantity', 0)
            self.customer_refs.add(sos_record['customer_reference'])
            self.sku_codes.add(sos_record.get('sku_code'))

    def get_lpn_details_from_stock(self):
        '''
        Get lpn details from stock
        '''
        self.stock_lpn_details = {}
        for _, stock_record in self.stock_df.iterrows():
            receipt_number = stock_record.get('receipt_number', '')
            lpn_number = stock_record.get('lpn_number', '')
            if not lpn_number:
                continue
            self.lpn_numbers_set.add(lpn_number)
            if not self.stock_lpn_details.get(receipt_number, []):
                self.stock_lpn_details[receipt_number] = []
            lpn_record = {
                'lpn_number': lpn_number,
                'packed_quantity': stock_record.get('quantity', 0),
            }
            self.stock_lpn_details[receipt_number].append(lpn_record)

    def get_packing_service_data(self):
        '''
        Get packing details from packing service
        '''
        self.packing_data, self.transaction_id_carton_mapping = {}, {}

        if not self.packing_enabled:
            return

        request_dict = {
            "request_headers": {
                "Warehouse": self.request.headers.get('Warehouse', ''),
                "Authorization": self.request.headers.get('Authorization', ''),
            },
            "request_meta": self.request.META,
            "request_scheme": self.request.scheme,
        }

        packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
        params = {
            "warehouse": self.warehouse.username,
            "transaction_number": ','.join(self.picklist_numbers),
            "transaction_type": 'so_packing',
            "invoice": True,
        }
        self.packing_details, packing_service_errors = packing_service_instance.get_packing_details(params)
        if self.packing_enabled and packing_service_errors:
            self.errors.extend(packing_service_errors)
            return

        for transaction_details in self.packing_details:
            lpn_details = transaction_details.get('packed_details', [])
            for lpn_record in lpn_details:
                lpn_number = lpn_record.get('lpn_number', '')
                self.packing_data[lpn_number] = lpn_record
                self.lpn_numbers_set.add(lpn_number)

                items = lpn_record.get('items', [])
                if items:
                    lpn_record['picked_quantity'] = lpn_record['packed_quantity'] = 0
                    self.get_transaction_level_details(items, lpn_number, lpn_record)

    def get_transaction_level_details(self, items, lpn_number, lpn_record):
        '''
        Get transaction level details
        '''
        for item_record in items:
            transaction_id = item_record.get('transaction_id', '')
            if transaction_id:
                if not self.transaction_id_carton_mapping.get(transaction_id, []):
                    self.transaction_id_carton_mapping[transaction_id] = [lpn_number]
                else:
                    self.transaction_id_carton_mapping[transaction_id].append(lpn_number)
                quantity = item_record.get('packed_quantity', 0)

                # add serial numbers to the item record
                serial_key = (transaction_id, lpn_number)
                serial_numbers = self.serilized_data.get(serial_key, []) or []
                item_record['serial_numbers'] = serial_numbers

                if item_record.get('json_data') is not None:
                    pack_uom_quantity = item_record.get('json_data', {}).get('pack_uom_quantity', 1 )
                    quantity = quantity / pack_uom_quantity
                item_record['picked_quantity'] = item_record['packed_quantity'] = quantity

            items = item_record.get('items', [])
            if items:
                item_record['picked_quantity'] = item_record['packed_quantity'] = 0
                self.get_transaction_level_details(items, lpn_number, item_record)

            lpn_record['packed_quantity'] += item_record['packed_quantity']
            lpn_record['picked_quantity'] += item_record['picked_quantity']
        
    def get_audit_lpn_data(self):
        '''
        Get audit lpn data
        '''
        audit_filters = {
            'warehouse': self.warehouse,
            'audit_reference__in': self.lpn_numbers_set,
            'status__in': [1, 2]  # Exclude status 3 (completed)
        }
        exclude_filters = {}
        audit_types = self.request_data.get('allow_audit_types')
        if audit_types:
            audit_types = audit_types.split(',')
            exclude_filters['audit_master__reference_type__in'] = audit_types
        self.audit_lpn_numbers = set(AuditEntry.objects.filter(**audit_filters).exclude(**exclude_filters).values_list('audit_reference', flat=True))

    def prepare_data_for_invoice(self):
        '''
        Prepare data for invoice
        '''
        final_data_dict = {}
        for sos_record in self.sos_details:
            transaction_id = sos_record.get('transaction_id', '')
            order_reference = sos_record.get('order_reference', '')
            valid_transaction_for_invoice = True
            sos_record['ean_numbers'] = self.ean_numbers.get(sos_record.get('sku_code', ''), [])
            if not self.stock_df.empty:
                # Get the stock details for the transaction id
                stock_quantity_df = self.stock_df.loc[self.stock_df['receipt_number'] == transaction_id]
                available_stock_qty = stock_quantity_df['quantity'].sum()

                if available_stock_qty > 0:
                    # Convert DataFrame to list of dictionaries for easier processing
                    stock_details_list = []
                    for _, row in stock_quantity_df.iterrows():
                        stock_details_list.append(row.to_dict())

                    # Update sos_record quantity
                    sos_record['quantity'] = available_stock_qty
                    if self.sales_uom_enabled and sos_record['pack_uom_quantity'] not in [None,0]:
                        sos_record['quantity'] = int(sos_record['quantity'] / sos_record['pack_uom_quantity'])
                        if sos_record.get('pack_id'):
                            sos_record.update({
                                'pack_id': sos_record.get('pack_id'),
                                'pack_uom_quantity': sos_record.get('pack_uom_quantity'),
                                'base_uom_quantity': int(sos_record['quantity']*sos_record['pack_uom_quantity'])
                            })
                else:
                    valid_transaction_for_invoice = False
            else:
                valid_transaction_for_invoice = False
                stock_details_list = []

            if valid_transaction_for_invoice:
                final_data_dict = self.prepare_valid_invoice_preview_data(transaction_id, order_reference, sos_record, final_data_dict, stock_details_list)

        self.final_detail = list(final_data_dict.values())

    def prepare_valid_invoice_preview_data(self, transaction_id, order_reference, sos_record, final_data_dict, stock_details_list):
        '''
        Prepare valid invoice preview data based on the given parameters.

        Args:
            transaction_id (int): The ID of the transaction.
            order_reference (str): The reference of the order.
            sos_record (dict): The SOS record.
            final_data_dict (dict): The final data dictionary.
            stock_details_list (list): List of stock details dictionaries.

        Returns:
            dict: The final data dictionary with the prepared invoice preview data.
        '''
        if not self.lpn_wise_invoice_preview:
            final_data_dict = self.prepare_valid_invoice_with_flat_structure(transaction_id, sos_record, final_data_dict, stock_details_list)
        else:
            final_data_dict = self.prepare_valid_invoice_data_lpn_level(transaction_id, order_reference, sos_record, final_data_dict, stock_details_list)
        return final_data_dict


    def prepare_merged_stock_data(self, transaction_id, lpn_number, sos_record, final_data_dict, stock_details_list, is_lpn_level=False, order_reference=None, packed_quantity=None):
        '''
        Helper method to prepare merged stock data for both flat structure and LPN level.

        Args:
            transaction_id (int): The ID of the transaction.
            lpn_number (str): The LPN number.
            sos_record (dict): The SOS record.
            final_data_dict (dict): The final data dictionary.
            stock_details_list (list): List of stock details dictionaries.
            is_lpn_level (bool): Whether this is for LPN level data.
            order_reference (str): The order reference (only used for LPN level).
            packed_quantity (float): The packed quantity (only used for flat structure).

        Returns:
            dict: Updated final_data_dict with merged stock data.
        '''
        # If stock_details_list is empty, return the final_data_dict unchanged
        if not stock_details_list:
            return final_data_dict
        
        pack_uom_quantity = sos_record.get('pack_uom_quantity', 0) or 0

        # Process each stock detail separately to handle multiple stocks for one transaction_id
        for stock_detail in stock_details_list:
            # Extract fields for merge_stock_ids key
            sku_code = sos_record.get('sku_code', '')
            batch_number = stock_detail.get('batch_detail__batch_no', '')
            order_reference = sos_record.get('order_reference', '')
            picklist_number = sos_record.get('picklist_number', '')
            line_reference = sos_record.get('line_reference', '')
            stock_quantity = stock_detail.get('quantity', 0) or 0
            if self.sales_uom_enabled and pack_uom_quantity not in [None, 0]:
                stock_quantity = int(stock_quantity / pack_uom_quantity)

            if lpn_number and lpn_number != stock_detail.get('lpn_number', ''):
                continue

            # Determine the unique key based on configuration
            if self.merge_stock_ids:
                unique_key = (lpn_number, sku_code, line_reference, batch_number, order_reference, picklist_number)
            else:
                # For LPN level without merge_stock_ids, use empty string as first element
                if is_lpn_level and not self.merge_stock_ids and lpn_number:
                    unique_key = ('', lpn_number)
                else:
                    unique_key = (transaction_id, lpn_number)

            # Get serial numbers
            serial_key = (transaction_id, lpn_number)
            if is_lpn_level and lpn_number:
                serial_numbers = self.lpn_wise_serilized_data.get(lpn_number, [])
            else:
                serial_numbers = self.serilized_data.get(serial_key, []) or []
                
            # Get suspended quantity
            suspended_qty_key = (order_reference, sku_code, line_reference)
            sos_record['suspended_quantity'] = 0
            if self.suspended_qty_dict.get(suspended_qty_key, 0) > 0:
                sos_record['suspended_quantity'] = min(self.suspended_qty_dict[suspended_qty_key], stock_quantity)
                self.suspended_qty_dict[suspended_qty_key] -= sos_record['suspended_quantity']
            

            # Initialize or update record
            if not final_data_dict.get(unique_key):
                # Initialize new record
                if is_lpn_level and lpn_number and self.packing_data.get(lpn_number):
                    # For LPN level with packing data
                    final_data_dict[unique_key] = self.packing_data.get(lpn_number, {})
                    final_data_dict[unique_key]['order_reference'] = order_reference
                    final_data_dict[unique_key]['serial_numbers'] = serial_numbers
                else:
                    # For flat structure or LPN level without packing data
                    final_data_dict[unique_key] = deepcopy(sos_record)
                    final_data_dict[unique_key]['transaction_id'] = (transaction_id, str(transaction_id))[self.merge_stock_ids]
                    final_data_dict[unique_key]['serial_numbers'] = serial_numbers

                    # Initialize quantity based on the source
                    if not is_lpn_level and packed_quantity is not None:
                        # For flat structure with available stock
                        final_data_dict[unique_key]['quantity'] = packed_quantity
                    else:
                        final_data_dict[unique_key]['quantity'] = stock_quantity

                    # Initialize stock_quantity dictionary with quantity and serial numbers
                    final_data_dict[unique_key]['stock_quantity'] = {
                        transaction_id: {
                            'quantity': stock_quantity,
                            'serial_numbers': serial_numbers[:] if serial_numbers else []
                        }
                    }
            else:
                # Update serial numbers using set operations
                existing_serials = set(final_data_dict[unique_key]['serial_numbers'])
                new_serials = list(set(serial_numbers) - existing_serials)
                final_data_dict[unique_key]['serial_numbers'].extend(new_serials)
                
                if is_lpn_level and lpn_number:
                    return final_data_dict

                if self.merge_stock_ids and str(transaction_id) not in final_data_dict[unique_key].get('transaction_id', '').split(','):
                    final_data_dict[unique_key]['transaction_id'] += ',' + str(transaction_id)

                # Update quantity
                if not is_lpn_level and packed_quantity is not None:
                    final_data_dict[unique_key]['quantity'] += packed_quantity
                else:
                    final_data_dict[unique_key]['quantity'] = final_data_dict[unique_key].get('quantity', 0) + stock_quantity
                final_data_dict[unique_key]['picked_quantity'] = final_data_dict[unique_key].get('picked_quantity', 0) + sos_record.get('picked_quantity', 0)
                final_data_dict[unique_key]['picklist_quantity'] = final_data_dict[unique_key].get('picklist_quantity', 0) + sos_record.get('picklist_quantity', 0)
                    
                # Update stock_quantity with quantity and serial numbers
                if transaction_id not in final_data_dict[unique_key]['stock_quantity']:
                    final_data_dict[unique_key]['stock_quantity'][transaction_id] = {
                        'quantity': stock_quantity,
                        'serial_numbers': serial_numbers[:] if serial_numbers else []
                    }
                else:
                    final_data_dict[unique_key]['stock_quantity'][transaction_id]['quantity'] = stock_quantity
                    existing_stock_serials = set(final_data_dict[unique_key]['stock_quantity'][transaction_id].get('serial_numbers', []))
                    new_stock_serials = list(set(serial_numbers) - existing_stock_serials)
                    final_data_dict[unique_key]['stock_quantity'][transaction_id].setdefault('serial_numbers', []).extend(new_stock_serials)

        return final_data_dict

    def prepare_valid_invoice_with_flat_structure(self, transaction_id, sos_record, final_data_dict, stock_details_list):
        '''
        Prepares a valid invoice with a flat structure.

        Args:
            transaction_id (int): Seller Order Summary ID.
            sos_record (dict): The SOS record.
            final_data_dict (dict): The final data dictionary.
            stock_details_list (list): List of stock details dictionaries.

        Returns:
            dict: The final data dictionary with the prepared invoice.
        '''
        available_stock = self.stock_lpn_details.get(transaction_id, [])
        if available_stock:
            for each_stock_record in available_stock:
                lpn_number = each_stock_record.get('lpn_number', '')

                # Set reusable flag
                if lpn_number and lpn_number in self.reusable_lpns:
                    each_stock_record['reusable'] = True
                elif lpn_number and lpn_number not in self.reusable_lpns:
                    each_stock_record['reusable'] = False

                if lpn_number and lpn_number in self.audit_lpn_numbers:
                    continue
                # Handle pack UOM quantity conversion
                packed_quantity = each_stock_record.get('packed_quantity', 0)
                if self.sales_uom_enabled and sos_record.get('pack_uom_quantity') not in [None, 0] and packed_quantity:
                    packed_quantity = int(packed_quantity / sos_record.get('pack_uom_quantity'))
                    each_stock_record['packed_quantity'] = packed_quantity
                    if sos_record.get('pack_id'):
                        each_stock_record.update({
                            'pack_id': sos_record.get('pack_id'),
                            'pack_uom_quantity': sos_record.get('pack_uom_quantity'),
                            'base_uom_quantity': int(packed_quantity * sos_record['pack_uom_quantity'])
                        })

                # Use the common helper method for merging
                final_data_dict = self.prepare_merged_stock_data(
                    transaction_id,
                    lpn_number,
                    sos_record,
                    final_data_dict,
                    stock_details_list,
                    packed_quantity=packed_quantity
                )

                # Update the record with stock-specific data
                unique_key = self.get_unique_key(transaction_id, lpn_number, sos_record, stock_details_list[0] if stock_details_list else {})
                if unique_key in final_data_dict:
                    final_data_dict[unique_key].update(each_stock_record)
        else:
            # No available stock case
            lpn_number = ''

            # Use the common helper method for merging
            final_data_dict = self.prepare_merged_stock_data(
                transaction_id,
                lpn_number,
                sos_record,
                final_data_dict,
                stock_details_list,
                packed_quantity=None
            )

        return final_data_dict

    def get_unique_key(self, transaction_id, lpn_number, sos_record, stock_detail, is_lpn_level=False):
        '''
        Helper method to get the unique key based on configuration.

        Args:
            transaction_id (int): The ID of the transaction.
            lpn_number (str): The LPN number.
            sos_record (dict): The SOS record.
            stock_detail (dict): The stock detail.
            is_lpn_level (bool): Whether this is for LPN level data.

        Returns:
            tuple: The unique key for the record.
        '''
        sku_code = sos_record.get('sku_code', '')
        batch_number = stock_detail.get('batch_detail__batch_no', '') or sos_record.get('batch_number', '')
        order_reference = sos_record.get('order_reference', '')
        picklist_number = sos_record.get('picklist_number', '')
        line_reference = sos_record.get('line_reference', '')

        if self.merge_stock_ids:
            # Use lpn_number, sku_code, batch_number, order_reference, and picklist_number for the key
            # Removed location and added order_reference and picklist_number
            return (lpn_number, sku_code, line_reference, batch_number, order_reference, picklist_number)
        elif is_lpn_level and lpn_number:
            return ('', lpn_number)
        else:
            return (transaction_id, lpn_number)

    def prepare_valid_invoice_data_lpn_level(self, transaction_id, order_reference, sos_record, final_data_dict, stock_details_list):
        '''
        Prepare valid invoice data at LPN level.

        This method prepares the valid invoice data at the LPN (License Plate Number) level.
        It takes the transaction ID, order reference, SOS (Sales Order Shipment) record, and a dictionary to store the final data.
        It retrieves the LPN numbers associated with the transaction ID and checks if they exist in the packing data.
        If an LPN number exists in the packing data, it creates a unique key and adds the corresponding data to the final data dictionary.
        If no LPN numbers are found, it creates a unique key using the transaction ID and adds the SOS record to the final data dictionary.

        Parameters:
            transaction_id (int): The ID of the transaction.
            order_reference (str): The reference of the order.
            sos_record (dict): The SOS record.
            final_data_dict (dict): The dictionary to store the final data.
            stock_details_list (list): List of stock details dictionaries.

        Returns:
            dict: The final data dictionary with the valid invoice data at the LPN level.
        '''
        lpn_numbers = self.transaction_id_carton_mapping.get(transaction_id, [])

        if lpn_numbers:
            for lpn_number in lpn_numbers:
                if lpn_number and lpn_number in self.audit_lpn_numbers:
                    continue
                if lpn_number and self.packing_data.get(lpn_number):
                    unique_key = ('', lpn_number)
                    if not final_data_dict.get(unique_key):
                        final_data_dict[unique_key] = self.packing_data.get(lpn_number, {})
                        final_data_dict[unique_key]['order_reference'] = order_reference
                        final_data_dict[unique_key]['serial_numbers'] = self.lpn_wise_serilized_data.get(lpn_number, [])
        else:
            # No LPN numbers case
            lpn_number = ''

            # Use the common helper method for merging
            final_data_dict = self.prepare_merged_stock_data(
                transaction_id,
                lpn_number,
                sos_record,
                final_data_dict,
                stock_details_list,
                is_lpn_level=True,
                order_reference=order_reference,
                packed_quantity=None  # Not used for LPN level
            )

        return final_data_dict

    def format_detatime(self, sos_record, keys):
        """
        Format the datetime values in the sos_record dictionary based on the provided keys.
        """
        for key in keys:
            if sos_record.get(key, ''):
                sos_record[key] = get_local_date_known_timezone(self.timezone, sos_record[key], True).strftime("%Y-%m-%d")

    
    def get_ean_numbers(self, sku_codes):
        """
        returns ean numbers the 
        """
        ean_number_details = {}
        ean_number_objects = list(EANNumbers.objects.filter(sku__user=self.warehouse.id, sku__sku_code__in=sku_codes).values('sku__sku_code', 'ean_number'))
        for ean_record in ean_number_objects:
            sku_code = ean_record.get('sku__sku_code')
            if not ean_number_details.get(sku_code):
                ean_number_details[sku_code] = []
            ean_number_details[sku_code].append(ean_record.get('ean_number'))
        return ean_number_details