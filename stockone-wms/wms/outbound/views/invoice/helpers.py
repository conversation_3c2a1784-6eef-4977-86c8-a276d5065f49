#pacakge reference
import json
import os
import math
import pandas as pd
from copy import deepcopy
from collections import defaultdict

#django imports
from django.template import loader
from django.db.models import Q, Case, When, F, IntegerField
from django.db.models.functions import Cast

#core imports
from core_operations.views.common.main import (
    get_misc_value, get_user_time_zone, create_default_zones,
    get_local_date_known_timezone, upload_master_file, truncate_float,
    get_user_prefix_incremental, get_invoice_html_data, get_admin, get_misc_options_list
)
from wms_base.wms_utils import init_logger

#inventory imports
from inventory.models import StockDetail, ZoneMaster, LocationMaster
from inventory.models.serial_numbers import SerialNumberTransaction

from wms_base.models import User
from core.models import SKUMaster, TempJson

#inbound imports
from inbound.serializers.grn import GRNSerializer
from inbound.views.grn.validation import POGRNValidation
from inbound.views.grn.asn import ASNSet

#outbound imports
from outbound.models import (
    CustomerMaster, OrderPackaging, OrderTypeZoneMapping, StagingInfo, InvoiceAdditionalInfo,
    OrderCharges, SellerOrderSummary, InvoiceDetail
)
from core_operations.views.services.packing_service import PackingService

from wms.celery import app as celery_app

log = init_logger('logs/invoice.log')


def construct_sell_ids(request, warehouse, cancel_inv=False):
    seller_summary_dat = request.get('seller_summary_id', '')
    if isinstance(seller_summary_dat, list):
        seller_summary_dat = seller_summary_dat[0]
    seller_summary_dat = seller_summary_dat.split('<<>>')
    return construct_sell_ids_func(seller_summary_dat, warehouse, cancel_inv)

def construct_sell_ids_func(invoices, warehouse, cancel_inv=False):
    '''
    Required Sample
    invoices -> ['HSR/21-22/037']    -> Array of Invoice IDs
    user ->  <User: ud_te_blr_wh1>   -> User Object
    cancel_inv -> True               -> Booelan Value
    '''
    if warehouse.userprofile.user_type != 'marketplace_user':
        sell_ids = {'order__user': warehouse.id}
        field_mapping = {'sku_code': 'order__sku__sku_code', 'order_id': 'order_id', 'order_id_in': 'order__order_id__in'}
    if cancel_inv:
        del field_mapping['order_id_in']
        field_mapping['full_invoice_number_in'] = 'invoice_reference__in'
    for data_id in invoices:
        splitted_data = data_id.split(':')
        common_id = 'full_invoice_number_in' if cancel_inv else 'order_id_in'
        sell_ids.setdefault(field_mapping[common_id], [])
        sell_ids[field_mapping[common_id]].append(splitted_data[0])
    return sell_ids

def get_invoice_types(warehouse):
    invoice_types = get_misc_value('invoice_types', warehouse.id)
    if invoice_types in ['', 'false']:
        invoice_types = ['Tax Invoice']
    else:
        invoice_types = invoice_types.split(',')
    return invoice_types

def get_sos_full_invoice_number(warehouse):
    inv_no, invoice_prefix, full_invoice_number, check_invoice_prefix , inc_status = get_user_prefix_incremental(warehouse, 'invoice', "", create_default="INV")
    return inv_no, full_invoice_number

def get_destination_user(customer_code):
    #Get destination warehouse object
    user = None
    if customer_code:
        dest_user_obj = User.objects.filter(username=customer_code)
        if dest_user_obj.exists():
            user=dest_user_obj[0]
    return user

def get_dest_user_batch_attributes(user):
    #get batch attributes of destination warehouse
    batch_attributes = get_misc_value('additional_batch_attributes', user.id)
    if batch_attributes not in ['False', 'None', False, None, '']:
        return batch_attributes.split(',')
    else:
        return []

def prepare_serial_numbers_data(serial_data):
    
    batch_level_data, lpn_level_data = defaultdict(list), defaultdict(list)
    for item in serial_data:
        sku_code = item.get('sku_code', '')
        batch_number = item.get('batch_number', '')
        lpn_number = item.get('lpn_number', '') or ''
        serial_numbers = item.get('serial_numbers', []) or []
        if serial_numbers:
            batch_level_data[(sku_code,batch_number)].extend(serial_numbers)
            if lpn_number:
                lpn_level_data[(sku_code,batch_number,lpn_number)].extend(serial_numbers)
    return batch_level_data, lpn_level_data

def prepare_grn_asn_input_data(sos, warehouse, invoice_number, serial_numbers=[],dc_check=False):
    try:
        data_dict = {}
        sos_obj = sos[0]
        batch_attributes = []
        time_zone = get_user_time_zone(warehouse)
        asn_reference = str(sos_obj.invoice_number).zfill(10)
        invoice_date = get_local_date_known_timezone(time_zone, sos_obj.updation_date, True).strftime("%Y-%m-%d")
        if sos_obj.order:
            order = sos_obj.order
        else:
            order = sos_obj.seller_order.order
        customer_name, customer_reference, customer_code,spoc_name,challan_number = '', '', '', '',''
        po_reference_check = False
        customer_master_filters = {"user": warehouse.id, "customer_id": order.customer_id}
        if str(order.order_type).lower()=="stocktransfer":
            customer_master_filters["customer_type"] = "Stock Transfer"
            if order.json_data and order.json_data.get('create_st_po',False):
                po_reference_check = True

        customer_master_obj = CustomerMaster.objects.filter(**customer_master_filters)
        if customer_master_obj.exists():
            customer_master_obj = customer_master_obj[0]
            customer_name = customer_master_obj.name
            customer_reference = customer_master_obj.customer_reference
            spoc_name = customer_master_obj.spoc_name
            customer_code= customer_master_obj.customer_code
        
        inv_url, inv_path, inv_file = '','',''
        
        order_json_data = order.json_data
        po_reference, po_number = '', ''
        if order_json_data.get('po_reference', ''):
            po_reference = order_json_data.get('po_reference', '')
            grn_type = 'ASNSave'
        else:
            po_number = order.order_reference
            grn_type = 'PO'
        
        #Add PO Number
        data_dict = {
            "po_number": po_number, "po_reference": po_reference, "grn_date": invoice_date, 'customer_code': customer_code,
            "grn_type": grn_type, "customer_name": customer_name, "customer_reference": customer_reference,
            "invoice_number": invoice_number, "invoice_date": invoice_date, "challan_number": "",
            "supplier_id":  warehouse.id, "tcs": 0, "lr_number":"", "carrier_name": "", "remarks": "",
            "invoice_url": inv_url, "num_cartons":0,'invoice_file_path':inv_path,
            "spoc_name": spoc_name, "is_st_asn" : True, 'asn_reference': asn_reference
            }
        #get destination warehouse object with customer code
        user = get_destination_user(customer_code)

        #get configured batch attributes for destination warehouse
        batch_attributes = get_dest_user_batch_attributes(user)

        if po_reference_check:
            data_dict['po_reference'] = data_dict.pop('po_number')
        sos_ids = list(sos.values_list('id', flat=True))
        sku_codes = list(sos.values_list('order__sku__sku_code', flat=True))

        #prepare putzone unique values
        sku_putzone_dict = prepare_put_zone_unique_values(order, customer_code, sku_codes)
        
        #prepare orderpackaging unique values
        carton_dict = prepare_carton_unique_values(sos_ids)

        #fetch misc values
        invoice_price_as_per_buyprice_ordertypes = get_misc_options_list(['invoice_price_as_per_buyprice'], warehouse).get('invoice_price_as_per_buyprice', [])
        
        # fetch invoice additional info
        additional_info_dict = fetch_invoice_additional_info(warehouse, [invoice_number])
        
        # prepare serial number data
        batch_level_serial_data, lpn_level_serial_data = prepare_serial_numbers_data(serial_numbers)

        data_dict["num_cartons"]=len(carton_dict)
        items = {}
        for so in sos:
            order = so.order
            batch_dict = {}
            cod = order.customerordersummary_set.filter()[0]
            picked_quantity = 0
            if so.picklist.stock:
                stock_dict = so.picklist.stock.__dict__
                picked_quantity = so.quantity
                if so.picklist.stock.batch_detail:
                    batch_dict = so.picklist.stock.batch_detail.__dict__
            tax = cod.cgst_tax + cod.sgst_tax + cod.igst_tax
            amount = order.unit_price * so.quantity
            unit_tax_amt = (order.unit_price/100) * (tax+cod.cess_tax)
            total_amount = amount + ((amount/100)* tax + cod.cess_tax)
            order_json_data = order.json_data 

            put_zone = sku_putzone_dict.get(order.sku.sku_code)
            if not put_zone:
                put_zone_ = ZoneMaster.objects.filter(zone='DEFAULT', user=warehouse.id)
                if not put_zone_.exists():
                    create_default_zones(warehouse, 'DEFAULT', 'DFLT1', 99999)
                    put_zone_ = ZoneMaster.objects.filter(zone='DEFAULT', user=warehouse.id)[0]
                else:
                    put_zone_ = put_zone_[0]
                put_zone = put_zone_.zone

            item_dict = {
                "sku_code": order.sku.sku_code,
                "sku_desc": order.sku.sku_desc,
                "quantity": order.original_quantity,
                "po_quantity": order.original_quantity,
                "invoice_quantity": picked_quantity,
                "received_quantity": picked_quantity,
                "rejected_quantity": 0,
                "accepted_quantity": picked_quantity,
                "return_quantity": 0,
                "po_price": order.unit_price,
                "tax": tax,
                "unit_tax_amt": unit_tax_amt,
                "cess_tax": cod.cess_tax,
                "total_amount":total_amount,
                "reason": "",
                "id":0,
                "carton":[],
                "serial_numbers": [],
                "put_zone" : put_zone,
                "aux_data": {
                    "order_details": {
                        "order_reference": order.order_reference,
                    },
                    'invoice_additional_info': additional_info_dict.get(invoice_number, {})
                },
                "lpns": {}
            }
            if batch_dict:
                item_dict = update_batch_details(batch_dict, stock_dict, item_dict, time_zone, order, invoice_price_as_per_buyprice_ordertypes, batch_attributes)
            item_dict = update_item_dict_details(order_json_data, order, po_reference_check, so.id, item_dict, batch_dict, batch_level_serial_data)
            unique_key, items = prepare_unique_asn_items(item_dict, order_json_data, items)
            if so.lpn_number:
                items = prepare_lpn_details(unique_key, items, so.lpn_number, picked_quantity, lpn_level_serial_data)
        items = list(items.values())
        for item in items:
            item['lpns'] = list(item['lpns'].values())
        data_dict['items'] = items
    except Exception:
        pass
    return data_dict, user

def prepare_unique_asn_items(item_dict, order_json_data, items):
    """
    Prepare unique ASN items based on the given item dictionary, order JSON data, and items dictionary.

    Args:
        item_dict (dict): The dictionary containing item details.
        order_json_data (dict): The JSON data of the order.
        items (dict): The dictionary containing unique ASN items.

    Returns:
        tuple: A tuple containing the unique key and updated items dictionary.
    """
    unique_key = (item_dict.get('po_number', ''), item_dict.get('po_reference', ''),
                  order_json_data.get('line_reference', ''), order_json_data.get('po_line_reference', ''),
                  item_dict.get('sku_code', ''), item_dict.get('batch_number', ''), item_dict.get('batch_reference', ''),
                  item_dict.get('vendor_batch_number', ''), item_dict.get('mrp', ''), item_dict.get('buy_price', ''))
    if not items.get(unique_key):
        items[unique_key] = item_dict
    else:
        items[unique_key]['invoice_quantity'] += item_dict['invoice_quantity']
        items[unique_key]['received_quantity'] += item_dict['received_quantity']
        items[unique_key]['accepted_quantity'] += item_dict['accepted_quantity']
    return unique_key, items

def prepare_lpn_details(unique_key, items, lpn_number, picked_quantity, lpn_level_serial_data):
    """
    Prepare LPN details for a given unique key, items, LPN number, and picked quantity.

    Args:
        unique_key (str): The unique key for the item.
        items (dict): The dictionary containing the items.
        lpn_number (str): The LPN number.
        picked_quantity (int): The picked quantity.

    Returns:
        dict: The updated dictionary of items.
    """
    sku_code = items[unique_key]['sku_code']
    batch_number = items[unique_key].get('batch_number', '') or items[unique_key].get('batch_reference', '')
    key = (sku_code, batch_number, lpn_number)
    serial_numbers = lpn_level_serial_data.get(key, []) or []
    if not items[unique_key]['lpns'].get(lpn_number):
        items[unique_key]['lpns'][lpn_number] = {
            "lpn_number": lpn_number,
            "packed_quantity": picked_quantity,
            "quantity_type": "accepted_quantity",
            "serial_numbers": serial_numbers
        }
    else:
        items[unique_key]['lpns'][lpn_number]['packed_quantity'] += picked_quantity
        items[unique_key]['lpns'][lpn_number]['serial_numbers'].extend(serial_numbers)
    return items

def prepare_put_zone_unique_values(order, customer_code, sku_codes):
    ''' Prepare putzone unique values '''
    sku_putzone_dict = {}
    if order.customer_id:
        recieving_wh = list(User.objects.filter(username__in=[customer_code]).values_list('id', flat=True))
        if recieving_wh:
            sku_objs = list(SKUMaster.objects.filter(sku_code__in=sku_codes, user=recieving_wh[0]).values('sku_code', 'zone__zone'))
            for sku in sku_objs:
                sku_putzone_dict[sku['sku_code']] = sku['zone__zone']
    return sku_putzone_dict

def prepare_carton_unique_values(sos_ids):
    ''' Prepare orderpackaging unique values '''
    carton_data = OrderPackaging.objects.filter(seller_order_summary_id__in=sos_ids).values('package_reference', 'packed_quantity')
    carton_dict={}
    for carton in carton_data:
        if carton['package_reference'] in carton_dict:
            carton_dict[carton['package_reference']] += carton['packed_quantity']
        else:
            carton_dict[carton['package_reference']]=carton['packed_quantity']

    return carton_dict

def update_batch_details(batch_dict, stock_dict, item_dict, time_zone, order, invoice_price_as_per_buyprice_ordertypes, batch_attributes):
    '''Update batch details '''
    date_formats = ['manufactured_date', 'expiry_date', 'retest_date', 'best_before_date', 'reevaluation_date']
    for key in date_formats:
        batch_att = batch_dict.get(key)
        if batch_att:
            item_dict.update({
                key : get_local_date_known_timezone(time_zone, batch_att, True).strftime("%Y-%m-%d")
            })

    price = order.unit_price
    if order.order_type and order.order_type.lower() in invoice_price_as_per_buyprice_ordertypes or 'all' in invoice_price_as_per_buyprice_ordertypes:
        price = stock_dict.get('unit_price',0) or 0
    mrp = batch_dict.get('mrp', 0) or 0
    item_dict.update({
        "weight": batch_dict.get("weight"),
        "buy_price" : price,
        "mrp": mrp,
        "batch_reference" : batch_dict.get("batch_reference", ""),
        "vendor_batch_number" : batch_dict.get("vendor_batch_no"),
    })
    #Batch Creation Fails If any of Batch Reference, VBN or Batch Number Not Exists
    if (not (batch_dict.get("batch_reference") or batch_dict.get("vendor_batch_no"))) or 'batch_number' in batch_attributes:
        item_dict["batch_number"] = batch_dict.get("batch_no")
    return item_dict

def update_item_dict_details(order_json_data, order, po_reference_check, sos_id, item_dict, batch_dict, batch_level_serial_data):
    '''Update Item level details '''
    sku_code = item_dict.get('sku_code', '')
    batch_number = item_dict.get('batch_number', '') or item_dict.get('batch_reference', '')
    serial_numbers = batch_level_serial_data.get((sku_code,batch_number), []) or []
    if order_json_data.get('line_reference', ''):
        item_dict['aux_data']['order_details']['line_reference'] = order_json_data.get('line_reference', '')
        item_dict['aux_data']['po_line_reference'] = order_json_data.get('line_reference', '')
    if order_json_data.get('po_line_reference', ''):
        item_dict['aux_data']['po_line_reference'] = order_json_data.get('po_line_reference', '')
    if order_json_data.get('po_reference', ''):
        item_dict['po_reference'] = order_json_data.get('po_reference', '')
    else:
        item_dict['po_number'] = order.order_reference
    if po_reference_check:
        item_dict['po_reference'] = item_dict.pop('po_number','')
    if serial_numbers:
        item_dict['serial_numbers'].extend(serial_numbers)
    if order.sku.json_data:
        item_dict.update(order.sku.json_data)
    if order.json_data and not order.json_data.get("weight", ""):
        item_dict["weight"] = batch_dict.get('weight', '')

    return item_dict

def save_invoice_as_file(request, warehouse, invoice_data, invoice_no,upload_file=True):
    try:
        invoice_no = invoice_no.split('/')[-1]
    except Exception:
        pass

    path = 'static/master_docs/%s/customer_invoices' % warehouse.id
    try:
        os.makedirs(path, exist_ok = True)
    except OSError:
        pass
    filename = "%s/%s.html" % (path, invoice_no)
    file = open(filename, "w")
    file.write(str(invoice_data))
    file.close()
    if upload_file:
        return upload_master_file(request, warehouse, invoice_no, 'Customer Invoices', master_file=filename, extra_flag='')
    else:
        return file,filename

def common_calculations(arg_data):
    locals().update(arg_data)
    item_data = arg_data.get('item_dict', {})
    unit_price = arg_data.get('unit_price', 0)
    quantity = arg_data.get('quantity', 0)
    discount = arg_data.get('discount', 0)
    unit_discount = arg_data.get('unit_discount', 0)
    dat = arg_data.get('dat', 0)
    is_gst_invoice = arg_data.get('is_gst_invoice', 0)
    marginal_flag = arg_data.get('marginal_flag', 0)
    cgst_tax = arg_data.get('cgst_tax', 0)
    sgst_tax = arg_data.get('sgst_tax', 0)
    igst_tax = arg_data.get('igst_tax', 0)
    utgst_tax = arg_data.get('utgst_tax', 0)
    cess_tax = arg_data.get('cess_tax', 0)
    profit_price = arg_data.get('profit_price', 0)
    hsn_summary = arg_data.get('hsn_summary', 0)
    gst_summary = arg_data.get('gst_summary', 0)
    partial_order_quantity_price = arg_data.get('partial_order_quantity_price', 0)
    _total_tax = arg_data.get('_total_tax', 0)
    sku_packs = arg_data.get('sku_packs', 0)
    total_invoice = arg_data.get('total_invoice', 0)
    total_amt = arg_data.get('total_amt', 0)
    total_cost = arg_data.get('total_cost', 0)
    total_membership_discount_amount = arg_data.get('total_membership_discount_amount', 0)
    total_discount = arg_data.get('total_discount', 0)
    total_taxable_amt = arg_data.get('total_taxable_amt', 0)
    display_customer_sku = arg_data.get('display_customer_sku', 0)
    customer_sku_codes = arg_data.get('customer_sku_codes', 0)
    data = arg_data.get('data', 0)
    order_id = arg_data.get('order_id', 0)
    title = arg_data.get('title', 0)
    tax_type = arg_data.get('tax_type', '')
    vat = arg_data.get('vat', 0)
    mrp_price = arg_data.get('mrp_price', 0)
    shipment_date = arg_data.get('shipment_date', 0)
    count = arg_data.get('count', 0)
    total_taxes = arg_data.get('total_taxes', 0)
    imei_data = arg_data.get('imei_data', 0)
    taxable_cal = arg_data.get('taxable_cal', 0)
    taxes_dict = arg_data.get('taxes_dict', 0)
    imei_data_sku_wise = arg_data.get('imei_data_sku_wise', 0)
    is_unfufilled = arg_data.get('is_unfufilled', 0)
    membership_discount_value = arg_data.get('membership_discount_value', 0)
    shipping_amt = arg_data.get('shipping_amt', 0)
    shipping_taxable_amt = arg_data.get('shipping_taxable_amt', 0)
    shipping_sgst_amt = arg_data.get('shipping_sgst_amt', 0)
    shipping_cgst_amt = arg_data.get('shipping_cgst_amt', 0)
    shipping_igst_amt = arg_data.get('shipping_igst_amt', 0)
    shipping_cess_amt = arg_data.get('shipping_cess_amt', 0)
    total_quantity = arg_data.get('total_quantity', 0)
    decimal_limit = arg_data.get('decimal_limit')
    decimal_limit_price = arg_data.get('decimal_limit_price', 'false')
    order_discount = discount
    unit_discount = float(order_discount)/item_data.get('order__original_quantity', 0)
    is_pharma = arg_data.get('is_pharma', 0)
    rsp = arg_data.get('rsp',0)
    total_rsp = arg_data.get('total_rsp',0)
    pack_id = arg_data.get('pack_id', '')
    if float(is_unfufilled) > 0:
        quantity = float(is_unfufilled)
    amt = (unit_price * quantity)
    total_rsp +=  (rsp*quantity)
    total_cost += amt
    total_amt += (mrp_price * quantity)
    base_price = "%.2f" % (unit_price * quantity)
    hsn_code = ''
    if item_data.get('order__sku__hsn_code', '') and "_" in str(item_data.get('order__sku__hsn_code')):
        hsn_code = str(item_data.get('order__sku__hsn_code')).split("_")[0]
    else:
        try:
            hsn_code = str(item_data.get('order__sku__hsn_code'))
        except Exception:
            hsn_code = ""

    if is_gst_invoice:
        #gst and hsn summary
        _tax, total_taxes, taxes_dict, tax_amount, taxable_amt, gst_summary, hsn_summary = prepare_gst_and_hsn_summary_data(amt, marginal_flag, cgst_tax, sgst_tax, igst_tax, cess_tax, utgst_tax, profit_price, total_taxes, is_unfufilled, hsn_code, hsn_summary, gst_summary)
    else:
        _tax = (amt * (vat / 100))

    total_tax_per = taxes_dict['cgst_tax'] + taxes_dict['sgst_tax'] + taxes_dict['igst_tax'] + taxes_dict['cess_tax'] + taxes_dict['utgst_tax']
    discount_percentage = 0
    if(mrp_price):
        discount = mrp_price - (unit_price * ( 1 + (total_tax_per/100)))
        discount_percentage = (float(discount / mrp_price))*100
        if decimal_limit_price != 'false':
            discount_percentage = truncate_float(discount_percentage, decimal_limit_price)
    total_quantity += quantity
    partial_order_quantity_price += (float(unit_price) * float(quantity))
    _total_tax += _tax
    invoice_amount = _tax + amt
    total_invoice += _tax + amt
    total_taxable_amt += amt + float(shipping_taxable_amt)
    if marginal_flag:
        total_invoice = total_invoice - tax_amount
        amt  = taxable_amt if taxable_amt >= 0 else 0
        invoice_amount = invoice_amount - tax_amount
        taxable_cal = taxable_cal + float(amt)
        total_taxable_amt = taxable_cal
    sku_code = item_data.get('order__sku__sku_code', '')
    sku_desc = item_data.get('order__sku__sku_desc', '')
    measurement_type = item_data.get('order__sku__measurement_type', '')
    if display_customer_sku == 'true':
        customer_sku_code_ins = customer_sku_codes.filter(customer__customer_id=item_data.get('order__customer_id'),
                                                          sku__sku_code=sku_code)
        if customer_sku_code_ins:
            sku_code = customer_sku_code_ins[0]['customer_sku_code']

    temp_imeis = []
    imei_data.append(temp_imeis)
    imei_data_sku_wise.extend(temp_imeis)
    if math.ceil(quantity) == quantity:
        quantity = int(quantity)
    quantity = truncate_float(quantity, decimal_limit)
    #calculate membership discount
    total_membership_discount_amount, membership_discount_amount = calculate_membership_discount_amount(total_membership_discount_amount, membership_discount_value, mrp_price, unit_price, quantity, is_unfufilled)

    if not membership_discount_value:
        total_discount += discount
    if sku_packs:
        sku_packs = int(quantity //sku_packs)
    else:
        sku_packs= 0
    shipping_amt = shipping_taxable_amt + shipping_sgst_amt + shipping_cgst_amt + shipping_igst_amt + shipping_cess_amt

    data.append(
        {'order_id': order_id, 'sku_code': sku_code, 'sku_desc': sku_desc,
            'title': title, 'invoice_amount': invoice_amount, 'membership_discount_amount': membership_discount_amount,
         'quantity': quantity, 'tax': _tax, 'unit_price': unit_price, 'tax_type': tax_type,
         'vat': vat, 'mrp_price': mrp_price, 'discount': discount, 'unit_discount':unit_discount, 'sku_class': dat.sku.sku_class,
         'sku_category': item_data.get('order__sku__sku_category', ''), 'sku_size': item_data.get('order__sku__sku_size', ''), 'amt':amt, 'taxes': taxes_dict,
         'base_price': base_price, 'hsn_code': hsn_code, 'imeis': temp_imeis,'is_pharma':is_pharma,
         'discount_percentage': discount_percentage, 'id': dat.id, 'shipment_date': shipment_date,'sno':count,
         'shipping_taxable_amt': shipping_taxable_amt, 'shipping_sgst_amt': shipping_sgst_amt,
         'shipping_cgst_amt': shipping_cgst_amt,'rsp':rsp,
         'shipping_igst_amt': shipping_igst_amt, 'shipping_cess_amt': shipping_cess_amt, 'shipping_amt': shipping_amt,
         'expiry_date':arg_data.get('expiry_date', ''), 'batchno':arg_data.get('batchno', ''), 'sku_brand': arg_data.get('sku_brand', ''),
         'measurement_type': measurement_type, 'sku_packs':sku_packs,  'is_unfufilled': is_unfufilled, 'total_quantity': total_quantity, 'pack_id': pack_id})
    return data,total_amt,total_cost,total_invoice,_total_tax,total_taxable_amt,taxable_cal,total_quantity, partial_order_quantity_price, sku_packs, total_discount, total_membership_discount_amount,total_rsp

def prepare_gst_and_hsn_summary_data(amt, marginal_flag, cgst_tax, sgst_tax, igst_tax, cess_tax, utgst_tax, profit_price, total_taxes, is_unfufilled, hsn_code, hsn_summary, gst_summary):
    tax_amount, taxable_amt = 0, 0
    if marginal_flag:
        cess_amt = 0
        marginal_tax = cgst_tax + sgst_tax + igst_tax + utgst_tax
        tax_amount = (profit_price * marginal_tax)/(100 + marginal_tax)
        taxable_amt = profit_price - tax_amount
        if cgst_tax:
            cgst_amt = tax_amount/2
            sgst_amt = tax_amount/2
            igst_amt, utgst_amt = 0, 0
        elif igst_tax:
            igst_amt = tax_amount
            cgst_amt, sgst_amt, utgst_amt = 0, 0, 0
        elif utgst_tax:
            utgst_amt = tax_amount
            cgst_amt, sgst_amt, igst_amt = 0, 0, 0
        else :
            cgst_amt,sgst_amt,igst_amt,utgst_amt = 0, 0 ,0, 0

    else:
        cgst_amt = float(cgst_tax) * (float(amt) / 100)
        sgst_amt = float(sgst_tax) * (float(amt) / 100)
        igst_amt = float(igst_tax) * (float(amt) / 100)
        utgst_amt = float(utgst_tax) * (float(amt) / 100)
        cess_amt = float(cess_tax) * (float(amt) / 100)
    taxes_dict = {'cgst_tax': cgst_tax, 'sgst_tax': sgst_tax, 'igst_tax': igst_tax, 'utgst_tax': utgst_tax,
                    'cess_tax': cess_tax, 'cgst_amt': cgst_amt, 'sgst_amt': sgst_amt,
                    'igst_amt': igst_amt, 'taxable': amt,
                    'utgst_amt': utgst_amt, 'cess_amt': cess_amt}
    total_taxes['cgst_amt'] += float(taxes_dict['cgst_amt'])
    total_taxes['sgst_amt'] += float(taxes_dict['sgst_amt'])
    total_taxes['igst_amt'] += float(taxes_dict['igst_amt'])
    total_taxes['utgst_amt'] += float(taxes_dict['utgst_amt'])
    total_taxes['cess_amt'] += float(taxes_dict['cess_amt'])
    total_taxes['taxable'] += float(taxes_dict['taxable'])
    _tax = float(taxes_dict['cgst_amt']) + float(taxes_dict['sgst_amt']) + float(taxes_dict['igst_amt']) + \
            float(taxes_dict['utgst_amt']) + float(taxes_dict['cess_amt'])
    if is_unfufilled == 0:
        if int(cgst_tax + sgst_tax + igst_tax + utgst_tax+cess_tax) == 0:
            gst_summary_key = str('EXEMPTED')
        else:
            gst_summary_key = str('GST') + "-" + str(int(cgst_tax + sgst_tax + igst_tax + utgst_tax+cess_tax)) + '%'
        hsn_summary_key = str(hsn_code)
        temp_total_amt = float(amt+sgst_amt+cgst_amt+igst_amt+utgst_amt+cess_amt)
        if hsn_summary.get(hsn_summary_key, ''):
            temp_exclude_taxes_amt = float(amt)
            hsn_summary[hsn_summary_key]['taxable'] += float(temp_exclude_taxes_amt)
            hsn_summary[hsn_summary_key]['sgst_amt'] += float(sgst_amt)
            hsn_summary[hsn_summary_key]['cgst_amt'] += float(cgst_amt)
            hsn_summary[hsn_summary_key]['igst_amt'] += float(igst_amt)
            hsn_summary[hsn_summary_key]['utgst_amt'] += float(utgst_amt)
            hsn_summary[hsn_summary_key]['cess_amt'] += float(cess_amt)
            hsn_summary[hsn_summary_key]['total'] += float(temp_total_amt)
        else:
            hsn_summary[hsn_summary_key] = {}
            temp_exclude_taxes_amt = float(amt)
            hsn_summary[hsn_summary_key]['taxable'] = float(temp_exclude_taxes_amt)
            hsn_summary[hsn_summary_key]['sgst_amt'] = float(sgst_amt)
            hsn_summary[hsn_summary_key]['cgst_amt'] = float(cgst_amt)
            hsn_summary[hsn_summary_key]['igst_amt'] = float(igst_amt)
            hsn_summary[hsn_summary_key]['utgst_amt'] = float(utgst_amt)
            hsn_summary[hsn_summary_key]['cess_amt'] = float(cess_amt)
            hsn_summary[hsn_summary_key]['tax_percent'] = str(int(cgst_tax + sgst_tax + igst_tax + utgst_tax+cess_tax))
            hsn_summary[hsn_summary_key]['total'] = float(temp_total_amt)
        if gst_summary.get(gst_summary_key, ''):
            temp_exclude_taxes_amt = float(amt)
            gst_summary[gst_summary_key]['taxable'] += float(temp_exclude_taxes_amt)
            gst_summary[gst_summary_key]['sgst_amt'] += float(sgst_amt)
            gst_summary[gst_summary_key]['cgst_amt'] += float(cgst_amt)
            gst_summary[gst_summary_key]['igst_amt'] += float(igst_amt)
            gst_summary[gst_summary_key]['utgst_amt'] += float(utgst_amt)
            gst_summary[gst_summary_key]['cess_amt'] += float(cess_amt)
            gst_summary[gst_summary_key]['total'] += float(temp_total_amt)
        else:
            gst_summary[gst_summary_key] = {}
            temp_exclude_taxes_amt = float(amt)
            gst_summary[gst_summary_key]['taxable'] = float(temp_exclude_taxes_amt)
            gst_summary[gst_summary_key]['sgst_amt'] = float(sgst_amt)
            gst_summary[gst_summary_key]['cgst_amt'] = float(cgst_amt)
            gst_summary[gst_summary_key]['igst_amt'] = float(igst_amt)
            gst_summary[gst_summary_key]['utgst_amt'] = float(utgst_amt)
            gst_summary[gst_summary_key]['cess_amt'] = float(cess_amt)
            gst_summary[gst_summary_key]['total'] = float(temp_total_amt)
    
    return _tax, total_taxes, taxes_dict, tax_amount, taxable_amt, gst_summary, hsn_summary

def calculate_membership_discount_amount(total_membership_discount_amount, membership_discount_value, mrp_price, unit_price, quantity, is_unfufilled):
    '''Calculate membership discount amount'''
    membership_discount_amount = 0
    if membership_discount_value and not is_unfufilled:
        if float(mrp_price) > float(unit_price):
            membership_discount_amount = (float(mrp_price)*float(quantity)) * (membership_discount_value/100)
            discount_val = float(mrp_price)*float(quantity) - float(unit_price)*float(quantity)
            if float(discount_val) > 0 and float(discount_val) <= float(membership_discount_amount):
                membership_discount_amount = float(discount_val)
            total_membership_discount_amount += membership_discount_amount
        
    return total_membership_discount_amount, membership_discount_amount

def build_multi_invoice(invoices_data, warehouse, css=False,api_invoice=False):
    #  it will create a Multi invoice template
    user_profile = warehouse.userprofile
    invoice_type = get_misc_value('invoice_type', warehouse.id)
    if invoice_type == 'false':
        invoice_type = 'a4'
    einvoice = get_misc_value('einvoice', warehouse.id)
    company_name = ''
    try:
        admin_user = get_admin(warehouse)
        company_name = admin_user.userprofile.company.company_name
        if company_name:
            company_name = (company_name.lower()).replace(' ', '_')
    except Exception:
        company_name = ''
    #if not stock_transfer:
        #if not (not invoice_data['detailed_invoice'] and invoice_data['is_gst_invoice']):
            #return json.dumps(invoice_data, cls=DjangoJSONEncoder)
    invoices_list = {'invoices' : []}
    complete_html = ''
    for invoice_data in invoices_data:
        titles = ['']
        import math
        if invoice_data.get("customer_invoice", "") != True:
            title_dat = get_misc_value('invoice_titles', warehouse.id)
            if title_dat != 'false':
                titles = title_dat.split(",")
        invoice_data['html_data'] = get_invoice_html_data(invoice_data)
        invoice_data['user_type'] = user_profile.user_type
        invoice_data['titles'] = titles
        perm_hsn_summary = invoice_data.get('perm_hsn_summary','false')
        perm_gst_summary = invoice_data.get('perm_gst_summary','false')
        if len(invoice_data.get('hsn_summary',{}).keys()) == 0:
            invoice_data['perm_hsn_summary'] = 'false'
        if len(invoice_data.get('gst_summary',{}).keys()) == 0:
            invoice_data['perm_gst_summary'] = 'false'
        invoice_height = 1358
        if 'side_image' in invoice_data.keys() and 'top_image' in invoice_data.keys():
            if not invoice_data['side_image'] and invoice_data['top_image']:
                invoice_height = 1350 if 1350 > len(invoice_data['data'])*105 else len(invoice_data['data'])*105
            if not invoice_data['top_image'] and invoice_data['side_image']:
                invoice_height = 1358
        inv_height = invoice_height  # total invoice height
        inv_details = 317  # invoice details height
        inv_footer = 95  # invoice footer height
        inv_totals = 127  # invoice totals height
        inv_header = 47  # invoice tables headers height
        inv_product = 47  # invoice products cell height
        inv_summary = 47  # invoice summary headers height
        inv_total = 27  # total display height
        inv_charges = 20  # height of other charges
        inv_totals = inv_totals + len(invoice_data.get('order_charges',[])) * inv_charges

        render_data = []
        render_space = 0
        hsn_summary_length = len(invoice_data.get('hsn_summary',{}).keys()) * inv_total
        gst_summary_length = len(invoice_data.get('gst_summary',{}).keys()) * inv_total
        if perm_hsn_summary == 'true' or perm_gst_summary == 'true':
            render_space = inv_height - (
            inv_details + inv_footer + inv_totals + inv_header + inv_summary + inv_total + hsn_summary_length + gst_summary_length)
        else:
            render_space = inv_height - (inv_details + inv_footer + inv_totals + inv_header + inv_total)
        no_of_skus = int(render_space / inv_product)
        data_length = len(invoice_data['data'])
        data_value = 0
        no_of_sku_count = 0
        if get_misc_value('show_imei_invoice', warehouse.id) == 'false':
            invoice_data['imei_data'] = []
            for data in invoice_data['data']:
                data['imeis'] = []
        if invoice_data.get('imei_data',''):
            count = 0
            for imei in invoice_data['imei_data']:
                for imei_count in range(len(imei)+1):
                    count+=imei_count
            if count != data_length:
                no_of_sku_count = int(count/2)
            if no_of_sku_count and no_of_sku_count + data_length > 14:
                data_value = 1
                data_length = no_of_sku_count + data_length

        invoice_data['empty_data'] = []
        if data_length >= no_of_skus:

            needed_space = inv_footer + inv_footer + inv_total
            if perm_hsn_summary == 'true' or perm_gst_summary == 'true':
                needed_space = needed_space + inv_summary + hsn_summary_length + gst_summary_length
            temp_render_space = 0
            temp_render_space = inv_height - (inv_details + inv_header)
            temp_no_of_skus = int(temp_render_space / inv_product)
            number_of_pages = int(math.ceil(float(data_length) / temp_no_of_skus))
            if data_value :
                number_of_pages = number_of_pages + 1
            for i in range(number_of_pages):
                temp_page = {'data': invoice_data['data'][i * temp_no_of_skus: (i + 1) * temp_no_of_skus], 'empty_data': []}
                render_data.append(temp_page)
            if int(math.ceil(float(data_length) / temp_no_of_skus)) == 0:
                temp_page = {'data': invoice_data['data'], 'empty_data': []}
                render_data.append(temp_page)
            last = len(render_data) - 1
            data_length = len(render_data[last]['data'])

            if no_of_skus < data_length:
                render_data.append({'empty_data': [], 'data': [render_data[last]['data'][data_length - 1]]})
                render_data[last]['data'] = render_data[last]['data'][:data_length - 1]

            last = len(render_data) - 1
            data_length = len(render_data[last]['data'])
            empty_data = [""] * (no_of_skus - data_length)
            render_data[last]['empty_data'] = empty_data
            invoice_data['data'] = render_data
        else:
            temp = invoice_data['data']
            invoice_data['data'] = []
            if no_of_sku_count > 0:
                data_length = data_length+no_of_sku_count
            no_of_space = (13 - data_length)
            if no_of_space < 0:
                no_of_space = 0
            empty_data = [""] * no_of_space
            invoice_data['data'].append({'data': temp, 'empty_data': empty_data})
        top = ''
        if "marketplace" in invoice_data:
            invoice_data["marketplace"] = "OFFLINE"
        if "order_reference" in invoice_data:
            if 'url_prefix' in invoice_data:
                invoice_data['barcode_url'] = invoice_data['url_prefix'] + invoice_data['barcode_url']
        if api_invoice:
            invoice_data['titles'] = ['Original for Receipient']
        invoices_list['invoices'].append(invoice_data)
        if css:
            c = {'name': 'invoice'}
            top_ = '../wms_base/templates/toggle/invoice/top1.html'
            top_name = '/top1.html'
            top_path = '../wms_base/templates/toggle/invoice/'
            if company_name:
                company_path = file_path+company_name
                if os.path.exists(os.getcwd()+company_path.replace('..', '')):
                    top_ = top_path+company_name+top_name
            top = loader.get_template(top_)
            top = top.render(c)
        else:
            file_name = '/einvoice.html'
            if invoice_type == '3_inch':
                file_name = '/customer_invoice.html'
            path = '../wms_base/templates/toggle/invoice'+file_name
            file_path = '../wms_base/templates/toggle/invoice/'
            if einvoice == 'true':
                path = '../wms_base/templates/toggle/invoice/einvoice.html'
                file_name = '/einvoice.html'
            if company_name:
                company_path = file_path+company_name
                if os.path.exists(os.getcwd()+company_path.replace('..', '')):
                    if not os.path.exists(os.getcwd()+company_path.replace('..', '')+file_name):
                        file_name = '/customer_invoice.html'
                    path = file_path+company_name+file_name
            html = loader.get_template(path)
            html = html.render(invoice_data)
            temp = top + html
            complete_html += temp

    return complete_html

@celery_app.task
def update_lpn_after_picklist_cancellation(warehouse, user, body, extra_params):
    '''
    update lpn details.
    '''
    warehouse = User.objects.get(id=warehouse)
    user = User.objects.get(id=user)
    packing_service_instance = PackingService(extra_params, user, warehouse)
    packing_service_instance.update_lpn(body)

def update_stock_qty(warehouse, sos_ids, receipt_types: list=None, extra_params=None, stock_objs=None):
    '''
    Update stock quantity as zero for which stocks have sos ids as receipt number
    '''

    # when updating stock check if order_ref as lpn_number is on send transaction_numbers for releasing lpns else send lpns for releasing
    # order_reference_as_lpn_number

    if not receipt_types:
        receipt_types = ['so_picking', 'so_dispense']
    if not stock_objs:
        stock_objs = StockDetail.objects.filter(sku__user=warehouse.id, receipt_number__in=sos_ids, receipt_type__in=receipt_types)
    stock_objs.update(quantity=0)
    try:
        # release lpns after picklist/invoice cancel
        stock_details = stock_objs.values('lpn_number', 'transact_number')
        lpns, picklist_numbers = set(), set()
        for stock_detail in stock_details:
            lpn = stock_detail.get('lpn_number', '')
            if lpn:
                lpns.add(lpn)
            picklist_numbers.add(str(stock_detail.get('transact_number', '')))

        if lpns and extra_params and extra_params.get('request_headers'):
            extra_params_ = {'request_headers':
                {
                    'Authorization': extra_params.get('request_headers', {}).get('Authorization', ''),
                    'Warehouse': warehouse.username
                }
            }
            request_user = extra_params.pop('request_user', '')
            lpn_str = ",".join(lpns)
            body = {
                "warehouse": warehouse.username,
                "transaction_number": ",".join(picklist_numbers),
                "lpn_number": lpn_str,
                "status": True,
                "usable": True,
                "dropped": False,
                "blocked": False
            }

            if extra_params.get('order_reference_as_lpn_number', False) in ['true', True]:
                body['lpn_number'] = ''

            update_lpn_after_picklist_cancellation.apply_async(args=[warehouse.id, request_user.id, body, extra_params_])
    except Exception as e:
        log.info(f"Error in releasing lpns: {str(e)}")

def is_dispensing_order_types(warehouse: User, order_types: list = []) -> list:
    non_dispensing_order_types = []
    order_type_zone_mapping_list = list(OrderTypeZoneMapping.objects.filter(user_id=warehouse.id, order_type__in=order_types, status=1).values(
        'order_type', 'json_data', 'receipt_type', 'zone__zone'))
    for order_type_record in order_type_zone_mapping_list:
        json_data = order_type_record.get('json_data', {}) or {}
        order_type = order_type_record.get('order_type', '')
        if json_data.get('dispensing', False) == False and order_type not in non_dispensing_order_types:
            non_dispensing_order_types.append(order_type)

    return non_dispensing_order_types

def get_wip_location_stocks(warehouse: User, sos_ids: list = [], order_types: list = [], dispense_enabled = False, extra_params=None):
    '''
    Get WIP location stocks
    '''
    extra_params = extra_params or {}
    pack_before_invoice = extra_params.get('pack_before_invoice')
    non_dispensing_order_types = is_dispensing_order_types(warehouse, order_types) if order_types else []
    filters = {'sku__user': warehouse.id, 'receipt_number__in': sos_ids, 'quantity__gt': 0}
    stock_values = ['id', 'receipt_number', 'receipt_type', 'sku_id', 'grn_number', 'quantity', 'lpn_number', 'picked_lpn']
    if extra_params.get('fields', []):
        stock_values = extra_params.get('fields')
    lpn_numbers = extra_params.get('lpn_number', '')
    if lpn_numbers:
        lpn_list = [lpn for lpn in lpn_numbers.split(',') if lpn]
        filters['lpn_number__in'] = lpn_list
    if dispense_enabled:
        filters['location__zone__zone'] = 'WIPZONE'
    else:
        filters['location__zone__storage_type'] = 'pre_invoice'

    exclude_filters = {}
    if pack_before_invoice:
        filters['lpn_number__isnull'] = False
        exclude_filters['lpn_number'] = ""

    stock_objects = StockDetail.objects.filter(**filters).exclude(**exclude_filters).annotate(picked_lpn=Case(When(json_data__picked_lpn=True, then=True), default=False))
    if not non_dispensing_order_types and dispense_enabled:
        stock_objects = stock_objects.filter((Q(receipt_type='so_picking') & Q(sku__dispensing_enabled=0)) | (Q(receipt_type='so_dispense') & Q(sku__dispensing_enabled=1)))
    else:
        stock_objects = stock_objects.filter(receipt_type__in=['so_picking', 'so_dispense'])
    stock_df = pd.DataFrame(stock_objects.values(*stock_values))

    stock_objects = list(stock_objects)
    for obj in stock_objects:
        stock_df.loc[stock_df['id'] == obj.id, 'object'] = obj
    return stock_df

def prepare_address(address):
    """Prepare e-invoice address."""
    special_chars = {'!', '#', '$', '@', '\\', '`', '~', '’', '-'}
    addr1, addr2 = [], []
    count = 0

    for i, chr in enumerate(address):
        increment = 4 if chr in special_chars else 1
        if count + increment < 98:
            addr1.append(chr)
        else:
            addr2.append(chr)
        count += increment

    if addr2 and len(addr2) < 3:
        addr2.append(' '*(3-len(addr2)))

    return ''.join(addr1), ''.join(addr2)

def flatten_dict(data, parent_key='', sep='_'):
    """ Flattens a multi-level dictionary. """
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)



def get_locations_for_post_invoice(warehouse, order_references):
    """
    returns order reference and location suggestied for post invoice
    """
    #check existing location for order_references
    #give suggestions for only new orders
    staging_objs = StagingInfo.objects.filter(order_reference__in=order_references, segregation='post_invoice', user_id=warehouse.id, status=0)
    existing_order_location_map = {}
    new_orders_location_map = {}
    available_locations_str = []
    for obj in staging_objs:
        existing_order_location_map[obj.order_reference] = obj.location
    new_order_references = list(set(order_references) - set(existing_order_location_map.keys()))

    available_locations = list(LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__storage_type = 'post_invoice', max_capacity__gt=F('filled_capacity')).order_by('fill_sequence'))
    if not available_locations:
        available_locations = create_default_zones(warehouse, 'POST_INVOICE_DFLT', 'post_invoice_dflt', 999999, 'outbound_staging', 'post_invoice')
    #get count of orders and move to location

    order_index = 0
    for location in available_locations:
        available_slots_in_location = location.max_capacity - location.filled_capacity
        while order_index < len(new_order_references) and available_slots_in_location > 0:
            new_orders_location_map[new_order_references[order_index]] = location
            available_slots_in_location -= 1
            order_index += 1
        available_locations_str.append(location.location)

    return existing_order_location_map, new_orders_location_map, available_locations_str



def fetch_invoice_additional_info(warehouse, inv_ref_list=[], challan_list=[]):
    """ Fetch Invoice Additional Info. """
    additional_info_dict = {}
    reference_numbers = inv_ref_list + challan_list
    inv_additional_objs = list(InvoiceAdditionalInfo.objects.filter(warehouse_id=warehouse.id, full_invoice_number__in = reference_numbers).values('full_invoice_number', 'json_data'))
    for data in inv_additional_objs:
        reference = data.get('full_invoice_number','')
        json_data = data.get('json_data', {}) or {}
        info_dict = flatten_dict(json_data)
        if info_dict:
            additional_info_dict[reference] = info_dict
    return additional_info_dict

def get_order_charges(warehouse, original_order_ids):
    '''
    Get Order Charges for the first invoice
    '''
    shipping_charges, other_charges, order_charges = {}, {}, defaultdict(list)
    order_charges_obj = OrderCharges.objects.filter(user_id= warehouse.id, order_id__in= original_order_ids, order_type='order')
    order_charges_data = list(order_charges_obj.values('order_id','charge_name', 'charge_amount', 'charge_tax_value','id'))
    for order_chrg in order_charges_data:
        order_id = order_chrg['order_id']
        charge_name = order_chrg['charge_name'].lower()
        if 'shipping' in charge_name:
            if order_chrg['order_id'] not in shipping_charges:
                shipping_charges[order_chrg['order_id']] = order_chrg['charge_amount']+order_chrg['charge_tax_value']
        else:
            if order_id not in other_charges:
                other_charges[order_id] = {
                    'total_charge_amount' : order_chrg['charge_amount'],
                }
            else:
                other_charges[order_id]['total_charge_amount'] += order_chrg['charge_amount'] 
        del order_chrg['id']
        del order_chrg['order_id']
        order_charges[order_id].append(order_chrg)
    
    return shipping_charges, other_charges, order_charges

def get_invoice_charges_data(warehouse, order_references):
    '''
    Get Order Charges for the first invoice
    '''
    invoice_data = defaultdict(dict)
    sos_objects = SellerOrderSummary.objects.filter(order__user=warehouse.id, order__order_reference__in=order_references, order_status_flag__in=['customer_invoices','delivery_challans'])
    original_order_ids = set(sos_objects.values_list('order__original_order_id', flat=True))
    invoice_records = list(sos_objects.annotate(invoice_number_int=Cast('invoice_number', IntegerField())).order_by('order__original_order_id', 'invoice_number_int').distinct('order__original_order_id').\
                        values('order__original_order_id', 'order__order_reference', 'invoice_reference', 'challan_number', 'invoice_number'))
    shipping_charges, other_charges, order_charges_data = get_order_charges(warehouse, original_order_ids)
    for row in invoice_records:
        reference_number = row.get('invoice_reference', '')
        order_reference = row.get('order__order_reference')
        original_order_id = row.get('order__original_order_id')
        shipping_charge = shipping_charges.get(original_order_id, 0)
        other_charge = other_charges.get(original_order_id, {}).get('total_charge_amount', 0)
        order_charges = order_charges_data.get(original_order_id, [])
        if reference_number not in invoice_data:
            invoice_data[reference_number] = {
                'shipping_charges': shipping_charge,
                'other_charges': other_charge,
                'order_charges': {
                    order_reference: order_charges
                }
            }
        else:
            invoice_data[reference_number]['shipping_charges'] += shipping_charge
            invoice_data[reference_number]['other_charges'] += other_charge
            invoice_data[reference_number]['order_charges'][order_reference] = order_charges

    return invoice_data

def get_serial_number_transaction_data(warehouse, serial_search_data, merge=False):
    """
    Function to get serial number transaction data in batch level and lpn level.

    Args:
        warehouse (User): Instance of User representing the warehouse.
        serial_search_data (dict): Dictionary containing search data with 'reference_numbers' key.

    Returns:
        dicts: batch_level_data, lpn_level_data containing serial number data at batch level and lpn level respectively.
    """
    
    batch_level_data, lpn_level_data = defaultdict(list), defaultdict(list)
    
    filter_dict = {
        'warehouse': warehouse.id,
        'reference_number__in': serial_search_data.get('reference_numbers', []),
        'reference_type': 'invoice',
        'sku_code__in': serial_search_data.get('sku_codes', []),
        'status__in': [0, 1]
    }
    values_list = ['reference_number', 'reference_type', 'transact_id', 'serial_number', 'sku_code', 'batch_number', 'lpn_number']
    serial_numbers_data = list(SerialNumberTransaction.objects.filter(**filter_dict).values(*values_list))
    
    for data in serial_numbers_data:
        transact_id = data.get('transact_id', '')
        reference_number = data.get('reference_number','')
        sku_code = data.get('sku_code', '')
        batch_number = data.get('batch_number','')
        lpn_number = data.get('lpn_number','') or ''
        serial_number = data.get('serial_number','')
        if not merge:
            batch_level_key = (reference_number, sku_code, batch_number, transact_id)
            lpn_level_key = (reference_number, sku_code, lpn_number, transact_id)
        else:
            batch_level_key = (reference_number, sku_code, batch_number)
            lpn_level_key = (reference_number, sku_code, lpn_number)
        if serial_number:
            # frame batch level and lpn level serial data
            batch_level_data[batch_level_key].append(serial_number)
            if lpn_number:
                lpn_level_data[lpn_level_key].append(serial_number)
        
    return batch_level_data, lpn_level_data


def validate_and_create_asn(request, user, asn_payload):
    """
    Validate ASN payload and create ASN

    Args:
        request: HTTP request object
        user (User): User object
        asn_payload (dict): ASN payload data

    Returns:
        tuple: (asn_number, status, error_message_dict, po_numbers_list, po_ref_list)
    """
    valid_ser = GRNSerializer(data=asn_payload)
    message, asn_number, status, error_message_dict = "", "", True, {}
    po_numbers_list, po_ref_list = [], []

    if valid_ser.is_valid():
        asn_payload = valid_ser.validated_data

    so_st_po_check = False
    if asn_payload.get('po_reference', ''):
        so_st_po_check = True

    validated_error_dict, po_dict_data, grn_data_list, po_numbers_list, grn_extra_dict, _, po_ref_list = POGRNValidation().validate(
        asn_payload, warehouse=user, is_asn=True, so_st_po=so_st_po_check
    )

    log.info(f'ASN Validation Response: {validated_error_dict}, {po_dict_data}, {grn_data_list}, {grn_extra_dict}')

    if validated_error_dict:
        status = False
        error_message_dict = validated_error_dict
    elif po_dict_data:
        asn_number, status,_ = ASNSet().create(
            request, warehouse=user, data=asn_payload,
            grn_data_list=grn_data_list, grn_extra_dict=grn_extra_dict
        )
    else:
        status = False
        error_message_dict = {"header": ["Something went Wrong"]}

    return asn_number, status, error_message_dict, po_numbers_list, po_ref_list


class PostASNCreation:
    """
    Class to process ASN creation for stock transfer orders
    """

    def __init__(self, warehouse, full_invoice_number, retrigger=False):
        """
        Initialize the ASN Creation Processor

        Args:
            warehouse (User): Warehouse user object
            full_invoice_number (str): Full invoice number
            retrigger (bool): Flag to indicate if this is a retrigger
        """
        self.warehouse = warehouse
        self.full_invoice_number = full_invoice_number
        self.retrigger = retrigger
        
    def post_asn_creation(self, sos_objects, asn_number, status, error_message_dict, asn_payload):
        """
        Process after ASN creation for stock transfer orders

        Args:
            sos_objects (QuerySet): SellerOrderSummary objects
            asn_number (str): ASN number generated
            status (bool): Status of ASN creation
            error_message_dict (dict): Error messages if ASN creation failed
            asn_payload (dict): ASN payload data

        Returns:
            None
        """
        try:
            self.st_asn_payload = TempJson.objects.filter(
                warehouse=self.warehouse,
                model_name = 'ST_ASNPayload',
                model_reference=self.full_invoice_number
            )
            self.st_asn_errors = TempJson.objects.filter(
                warehouse=self.warehouse,
                model_name = 'ST_ASNErrors',
                model_reference=self.full_invoice_number
            )
            log.info(f'ASN retrigger payload: {asn_payload}, asn_number: {asn_number}, status: {status}, error_message_dict: {error_message_dict}')
            if status:
                self.update_seller_order_summary(sos_objects, asn_number)
                self.delete_temp_json_data()
            else:
                self.store_asn_payload(asn_payload)
                self.store_asn_errors(error_message_dict)
        except Exception as e:
            log.error(f"Error in ASNCreationProcessor for invoice {self.full_invoice_number}: {str(e)}")

    def delete_temp_json_data(self):
        """
        Delete TempJson data related to ASN creation
        """
        if self.st_asn_payload.exists():
            self.st_asn_payload.delete()
        if self.st_asn_errors.exists():
            self.st_asn_errors.delete()

    def update_seller_order_summary(self, sos_objects, asn_number):
        """
        Update SellerOrderSummary objects with ASN number

        Args:
            sos_objects (QuerySet): SellerOrderSummary objects
            asn_number (str): ASN number generated
        """
        sos_to_update = []
        for sos in sos_objects:
            json_data = sos.json_data or {}
            json_data['asn_number'] = asn_number
            sos.json_data = json_data
            sos_to_update.append(sos)

        if sos_to_update:
            SellerOrderSummary.objects.bulk_update(sos_to_update, ['json_data'])
        
        if self.retrigger:
            inv_det_upadte_objs = []
            invoice_detail_obj = InvoiceDetail.objects.filter(warehouse_id=self.warehouse.id, invoice_reference = self.full_invoice_number)
            for obj in invoice_detail_obj:
                json_data = obj.json_data or {}
                json_data['asn_number'] = asn_number
                obj.json_data = json_data
                inv_det_upadte_objs.append(obj)
            if inv_det_upadte_objs:
                InvoiceAdditionalInfo.objects.bulk_update(inv_det_upadte_objs, ['json_data'])        
            
                
    def store_asn_payload(self, asn_payload):
        """
        Store ASN payload in TempJson

        Args:
            asn_payload (dict): ASN payload data
        """
        if self.st_asn_payload.exists():
            return
        TempJson.objects.create(
            warehouse=self.warehouse,
            model_name='ST_ASNPayload',
            model_reference=self.full_invoice_number,
            model_json=json.dumps(asn_payload),
            account_id=self.warehouse.userprofile.id
        )

    def store_asn_errors(self, error_message_dict):
        """
        Store ASN errors in TempJson

        Args:
            error_message_dict (dict): Error messages if ASN creation failed
        """
        asn_errors = ''
        for key, value in error_message_dict.items():
            # Handle case where value is a list of lists
            if value and isinstance(value, list):
                for sublist in value:
                    # Flatten the list of lists
                    if isinstance(sublist, list):
                        asn_errors += '*'.join(sublist)
                    else:
                        asn_errors += '*' + str(sublist)
        if self.st_asn_errors.exists():
            self.st_asn_errors.update(model_json=json.dumps(asn_errors))
            return
        TempJson.objects.create(
            warehouse=self.warehouse,
            model_name='ST_ASNErrors',
            model_reference=self.full_invoice_number,
            model_json=json.dumps(asn_errors),
            account_id=self.warehouse.userprofile.id
        )


