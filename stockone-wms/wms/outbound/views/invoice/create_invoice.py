#package imports
import pandas as pd
from datetime import datetime
from collections import defaultdict
from copy import deepcopy

#django imports
from django.db.models import F, Sum
from django.db import transaction
from django.core.cache import cache
from django.test.client import RequestFactory
from wms.celery import app as celery_app

#wms base imports
from wms_base.models import User
from wms_base.wms_utils import init_logger

#core operations imports
from core_operations.views.common.main import (
    get_multiple_misc_values, get_decimal_value,
    truncate_float, get_misc_options_list,
    get_incremental, preapre_incremental_prefix_details
)
from core_operations.views.integration.integration import webhook_integration_3p
from core_operations.views.services.packing_service import PackingService
from core_operations.views.common.validation import update_order_header_status
from core_operations.views.common.user_attributes import validate_attributes
from core.models import (
    IncrementalSegregationMaster, IncrementalTable, MiscDetailOptions
)

#inventory imports
from inventory.models import StockDetail, LocationMaster
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#inbound imports

#outbound imports
from outbound.models import (
    SellerOrderSummary, OrderDetail, Picklist, InvoiceAdditionalInfo,
    OrderTypeZoneMapping, PriceMaster, PickAndPassStrategy,
    StagingInfo, AuditEntry
)
from outbound.views.invoice.einvoice import trigger_einvoice_integration
from outbound.views.orders.utils import validate_transaction_lock
from outbound.views.shipment.shipment_invoice import insert_shipment_invoice_data_fun
from outbound.views.invoice.helpers import (
    get_locations_for_post_invoice, prepare_grn_asn_input_data, validate_and_create_asn, PostASNCreation,
    get_wip_location_stocks
)
from outbound.views.masters.pricingmaster.pricing_master import PriceApplicationPriority

from .invoice_data import get_customer_invoice_tab_func
from .save_invoice_data import SaveInvoiceData

log = init_logger('logs/creation_invoice.log')

@celery_app.task
def update_packing_status(user, warehouse, packing_details, extra_params):
    '''
    Update packing status as completed after invoice creation
    '''

    warehouse = User.objects.get(username=warehouse)
    user = User.objects.get(username=user)

    request_dict = {
        "request_headers": extra_params.get('headers', {}),
        "request_meta": extra_params.get('request_meta', {}),
    }
    for picklist_number, packing_data in packing_details.items():
        for status, lpn_numbers in packing_data.items():
            params = {
                "warehouse": warehouse.username,
                "transaction_number": str(picklist_number),
                "transaction_type": "so_packing",
                "lpn_numbers": lpn_numbers,
            }
            if extra_params.get("lpn_transaction_changes_details"):
                params['transaction_details'] = extra_params.get("lpn_transaction_changes_details", {}).get(str(picklist_number), [])
            packing_service_instance = PackingService(request_dict, user, warehouse)
            packing_details, packing_service_errors = packing_service_instance.update_packing_status(params, status)
            if packing_service_errors:
                log.info(f"Packing status updated for picklist number - {picklist_number} failed with {packing_service_errors}")
            if status == "completed":
                if extra_params.get("release_carton_at_invoice"):
                    lpn_numbers_str = ",".join(lpn_numbers)
                    params = {
                        "warehouse": warehouse.username,
                        "lpn_number": lpn_numbers_str,
                        "status": True,
                        "usable": True,
                        "dropped": False,
                        "blocked": False
                    }
                    lpn_details, packing_service_errors = packing_service_instance.update_lpn(params)
                    if packing_service_errors:
                        log.info("Update LPN for picklist number - %s and lpn_numbers - %s failed with %s" % (picklist_number, str(lpn_numbers_str), str(packing_service_errors)))
                        
@celery_app.task
def asn_creation(request_data, warehouse_id, full_invoice_number, serial_numbers=[], dc_check=False):
    try:
        po_numbers_list, po_ref_list = [], []
        user = None
        request_user_id = request_data.get('user', None)
        request_user = ''
        warehouse = User.objects.get(id=warehouse_id)
        if not warehouse:
            log.info(f"ASN Creation Failed, Warehouse with id {warehouse_id} not found")
            return False
        if request_user_id:
            request_user = User.objects.get(id=request_user_id)
        request = RequestFactory()
        request.user = request_user
        request.warehouse = warehouse
        request.headers = request_data.get('headers', {})
        request.META = request_data.get('request_meta', {})
        request.FILES = request_data.get('files', {})

        #delivery challan asn payload
        sos_filters = {
            'order__user': warehouse.id,
            'quantity__gt': 0,
            'invoice_reference': full_invoice_number
        }
        if dc_check:
            sos_filters['order_status_flag'] = 'delivery_challans'
        else:
            sos_filters['order_status_flag'] = 'customer_invoices'
            
        sos = SellerOrderSummary.objects.filter(**sos_filters)
        asn_payload, user = prepare_grn_asn_input_data(sos, warehouse, full_invoice_number, serial_numbers=serial_numbers,dc_check=dc_check)
        for e_row in asn_payload.get("items"):
            if len(e_row.get("retest_date", ""))==10:
                e_row["retest_date"] = e_row["retest_date"] + " 23:59"
            if len(e_row.get("expiry_date", ""))==10:
                e_row["expiry_date"] = e_row["expiry_date"] + " 23:59"
            if len(e_row.get("manufactured_date", ""))==10:
                e_row["manufactured_date"] =  e_row["manufactured_date"] + " 00:00"

        # Validate and create ASN
        asn_number, status, error_message_dict, po_numbers_list, po_ref_list = validate_and_create_asn(request, user, asn_payload)
        
        log.info(f'create asn response log,asn_number: {asn_number}, status: {status},errors: {error_message_dict},po_numbers: {po_numbers_list}, po_ref_numbers: {po_ref_list}')

        # Process after ASN creation using the ASNCreationProcessor class
        processor = PostASNCreation(warehouse, full_invoice_number)
        processor.post_asn_creation(sos, asn_number, status, error_message_dict, asn_payload)

        # Clear cache
        for po_no in po_numbers_list + po_ref_list:
            cache_key = f'{user.id}##{po_no}'
            cache.delete(str(cache_key))

        log.info('ST-ASN Response: '+ str(asn_number) + 'Payload: '+ str(asn_payload) + ' validation_errors: '+ str(error_message_dict)+ ' status :'+ str(status))
    except Exception as e:
        log.info("ST ASN Creation Failed error: %s "%(str(e)))
        return False
    for po_no in po_numbers_list + po_ref_list:
        cache.delete(str(po_no))

    return True

class CreateInvoiceMixin:
    def __init__(self, request, request_data, request_user: User, warehouse: User, extra_params: dict):
        self.request = request
        self.request_data = request_data
        self.user = request_user
        self.warehouse = warehouse
        self.account_id = warehouse.userprofile.id
        self.decimal_limit = get_decimal_value(self.warehouse.id)
        self.price_decimal_limit = get_decimal_value(self.warehouse.id, 'price')

        self.batch_size = 500
        self.extra_params = extra_params or {}
        self.order_status_flag = self.extra_params.get('order_status_flag', 'customer_invoices')
        self.is_async_task = self.extra_params.get('async_task', False) or False

    def generate_invoice(self):
        self.errors, self.new_invoice_reference, self.redis_cache_ids, self.invoice_ref_details = [], [], [], {}
        self.result_data = {
            "errors": [],
            "invoice_references": []
        }
        self.custom_attributes, self.customer_price_types, self.order_type_price_types = {}, {}, {}
        self.price_type, self.sku_price_details, self.sku_category_price_details = "", {}, {}
        self.existing_order_locations, self.order_type_document_details, self.new_order_locations, self.order_ref_zone_map, self.serial_transaction_data = {}, {}, {}, {}, {}
        self.cancelled_order_references, self.cancelled_sku_codes = set(), set()
        self.current_serial_transaction_data = defaultdict(lambda: defaultdict(list))
        try:
            self.validate_request_details()
            if self.errors:
                self.result_data['errors'] = self.errors
                return self.result_data
            
            #handle duplicates
            new_added_ids = []
            for sos_id in self.sos_ids:
                cache_status = cache.add(sos_id, "True", timeout=150)
                if not cache_status:
                    self.result_data['errors'] = ["Invoice Generation is in-progress, please try again!"]
                    self.sos_ids = new_added_ids
                    self.delete_cache_ids()
                    return self.result_data
                else:
                    new_added_ids.append(sos_id)

            self.get_incremental_details()
            self.get_sos_details()
            if self.serial_transaction_ids:
                self.get_current_serial_transaction_data()
                self.validate_serial_numbers()
            if self.errors:
                self.result_data['errors'] = self.errors
                self.delete_cache_ids()
                return self.result_data
            self.get_invoice_misc_details()
            self.validate_invoice_creation_checks()
            if self.errors:
                self.result_data['errors'] = self.errors
                self.delete_cache_ids()
                return self.result_data

            self.get_stock_detail()
            if not self.lpn_wise_invoice_preview and self.invoice_lpn_details:
                self.validate_full_packing_invoice()
                if self.errors:
                    self.result_data['errors'] = self.errors
                    self.delete_cache_ids()
                    return self.result_data

            if self.price_application_at_invoice:
                self.get_price_application_data()

            self.new_sos_objects = []
            
            self.validate_sku_details()
            if self.errors:
                self.result_data['errors'] = self.errors
                self.delete_cache_ids()
                return self.result_data
            
            self.new_request_data = self.get_formated_request_data()
            
            self.validate_and_prepare_invoice_data()

            if self.errors:
                self.result_data['errors'] = self.errors
                self.delete_cache_ids()
                return self.result_data
            for reference_number, customers in self.reference_number_customer_map.items():
                if len(customers) > 1:
                    self.result_data['errors'] = [f'invoice for multiple customers is not allowed for customers {list(customers)}']
                    self.delete_cache_ids()
                    return self.result_data

            self.create_and_update_invoice_data()
            self.result_data['invoice_references'] = self.new_invoice_reference
            self.result_data['inv_data'] = list(self.invoice_ref_details.values())
            self.delete_cache_ids()
            return self.result_data

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("Generate Create Invoice failed with error- %s" % str(e))
            self.errors.append('Create Invoice Failed!')
            self.result_data['errors'] = self.errors
            self.delete_cache_ids()
            return self.result_data
        
    def get_invoice_group_priority(self, ):
        """
        Get the invoice group priority from the warehouse misc options.
        """
        options_filters = {
            'misc_detail__user': self.warehouse.id,
            'misc_detail__misc_type': 'invoice_group_priority',
        }
        misc_options = list(MiscDetailOptions.objects.filter(**options_filters).values('misc_key', 'misc_value'))
        misc_options_dict = {}
        for misc_option in misc_options:
            misc_options_dict[misc_option.get('misc_key')] = int(misc_option.get('misc_value'))
            if int(misc_option.get('misc_value')) > self.max_priority:
                self.max_priority = int(misc_option.get('misc_value'))
        return misc_options_dict
    
    def get_formated_request_data(self):
        '''
        Get formated request data
        '''
        if self.invoice_group_priority_dict:
            return sorted(self.request_data, key=lambda x: self.invoice_group_priority_dict.get(x.get('invoice_group', ''),self.max_priority+1))
        return self.request_data

    def get_invoice_misc_details(self):
        '''
        Get invoice misc details
        '''
        self.order_wise_invoice, self.restrict_partial_invoice, self.dispense_enabled, self.release_carton_at_invoice, self.lpn_wise_invoice_preview, self.customer_level_packing = False, False, False, False, False, False
        self.price_application_at_invoice, self.auto_generate_einvoice, self.invoice_split_on_invoice_group, self.invoice_group_priority = False, False, False, False
        self.order_cancel_on_invoice, self.invoice_callback_at_order_level, self.invoice_to_picklist_quantity, self.restrict_invoice_at_picking, self.flag_order_level_discrepancy = False, False, False, False, False
        self.invoice_group_priority_dict, self.max_priority = {}, 0
        misc_types = [
            'order_wise_inv', 'enable_dispense', 'release_carton', 'lpn_wise_invoice_preview', 'price_application', 'invoice_callback_at_order_level',
            'auto_generate_einvoice', 'sku_limit_for_invoice', 'invoice_split_on_invoice_group', 'customer_level_packing',
            'invoice_group_priority', 'invoice_to_picklist_quantity', 'pigeon_hole_sorting', 'restrict_invoice_at_picking',
            'flag_order_level_discrepancy'
        ]
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        #order wise invoice
        if misc_dict.get('order_wise_inv', 'false') in ['true', True]:
            self.order_wise_invoice = True

        #dispense enabled
        if misc_dict.get('enable_dispense','false') in ['true', True]:
            self.dispense_enabled = True

        #lpn wise invoice preview
        if misc_dict.get('lpn_wise_invoice_preview', 'false') in ['true', True]:
            self.lpn_wise_invoice_preview = True

        #auto generate einvoice
        if misc_dict.get('auto_generate_einvoice', 'false') in ['true', True]:
            self.auto_generate_einvoice = True

        if misc_dict.get('restrict_invoice_at_picking', 'false') in ['true', True]:
            self.restrict_invoice_at_picking = True

        #release carton at invoice
        if misc_dict.get('release_carton','false') == 'Invoice':
            self.release_carton_at_invoice = True

        #price application at invoice creation
        if misc_dict.get('price_application', 'false') == 'at_invoice_creation':
            self.price_application_at_invoice = True
        
        #invoice group priority
        if misc_dict.get('invoice_group_priority', 'false') in ['true', True]:
            self.invoice_group_priority = True
            
        #invoice split on invoice group  
        if misc_dict.get('invoice_split_on_invoice_group', 'false') in ['true', True]:
            self.invoice_split_on_invoice_group = True
            self.invoice_group_priority_dict = ({},self.get_invoice_group_priority())[self.invoice_group_priority==True]
        
        if misc_dict.get('customer_level_packing', 'false') in ['true', True]:
            self.customer_level_packing = True
        
        if misc_dict.get('invoice_callback_at_order_level', 'false') in ['true', True]:
            self.invoice_callback_at_order_level = True
        
        if misc_dict.get('invoice_to_picklist_quantity', 'false') in ['true', True]:
            self.invoice_to_picklist_quantity = True
            
        if misc_dict.get('flag_order_level_discrepancy', 'false') in ['true', True]:
            self.flag_order_level_discrepancy = True
            
        self.pigeon_hole_sorting = misc_dict.get('pigeon_hole_sorting', 'location') or 'location'

        #restrict partial invoice
        if not self.sos_df.empty:
            misc_types = ['restrict_partial_invoice','order_cancel_on_invoice', 'outbound_staging_lanes']
            misc_options_type_dict = get_misc_options_list(misc_types, self.warehouse)
            restrict_partial_invoice_order_types = misc_options_type_dict.get('restrict_partial_invoice', [])
            order_cancel_on_invoice_order_types = misc_options_type_dict.get('order_cancel_on_invoice',[])
            post_invoice_staging_lane = misc_options_type_dict.get('outbound_staging_lanes', [])
            self.post_invoice_staging_lane = True if "post_invoice" in post_invoice_staging_lane else False
            if self.sos_df['order_type'][0].lower() in restrict_partial_invoice_order_types or 'all' in restrict_partial_invoice_order_types:
                self.restrict_partial_invoice = True
            
            if self.sos_df['order_type'][0].lower() in order_cancel_on_invoice_order_types or 'all' in order_cancel_on_invoice_order_types:
                self.order_cancel_on_invoice = True
        
        #no of skus for invoice a time
        if misc_dict.get('sku_limit_for_invoice', ''):
            self.no_skus_for_invoice = misc_dict.get('sku_limit_for_invoice', '')
        else:
            self.no_skus_for_invoice = ''
        
    def validate_request_details(self):
        '''
        validating invoice creation request data
        '''
        if not self.request_data:
            self.errors.append("Request data shouldn't be empty")
            return

        self.sos_ids, self.sos_id_quantity, self.invoice_lpn_details, self.request_lpn_numbers, self.serial_numbers_request_data = set(), {}, {}, [], defaultdict(lambda: defaultdict(list))
        serial_numbers_set = set()
        for row_data in self.request_data:
            sos_id = row_data.get('transaction_id', '')
            sku_quantity = row_data.get('quantity','')
            pack_uom_quantity = row_data.get('pack_uom_quantity', 1)
            serial_numbers = row_data.get('serial_numbers', []) or []
            if not sos_id:
                self.errors.append("Transaction id shouldn't be empty")
                return
            if not sku_quantity:
                self.errors.append("Invoice Quantity should be greater than Zero")
                return

            if sos_id not in self.sos_ids:
                self.sos_ids.add(sos_id)
            
            if not self.sos_id_quantity.get(sos_id):
                self.sos_id_quantity[sos_id] = float(sku_quantity)
            else:
                self.sos_id_quantity[sos_id] += float(sku_quantity)

            #packing details
            lpn_number = row_data.get('lpn_number', '') or ''
            if serial_numbers:
                if len(serial_numbers) != len(set(serial_numbers)) or len(serial_numbers_set.intersection(set(serial_numbers))) > 0:
                    self.errors.append("Duplicate Serial Numbers are not allowed")
                    return
                self.serial_numbers_request_data['sos_ids'][sos_id].extend(serial_numbers)
                self.serial_numbers_request_data['lpn_level'][(sos_id, lpn_number)].extend(serial_numbers)
                serial_numbers_set.update(set(serial_numbers))
            if lpn_number:
                self.request_lpn_numbers.append(lpn_number)
                self.prepare_request_lpn_details_for_validation(lpn_number, sos_id, sku_quantity, pack_uom_quantity)
        self.sos_ids = list(self.sos_ids)

    def prepare_request_lpn_details_for_validation(self, lpn_number, sos_id, sku_quantity, pack_uom_quantity):
        '''
        Prepare request lpn details for validation
        '''
        if not self.invoice_lpn_details.get(lpn_number, {}):
            self.invoice_lpn_details[lpn_number] = {}
        if not self.invoice_lpn_details[lpn_number].get(sos_id, 0):
            self.invoice_lpn_details[lpn_number][sos_id] = 0
        self.invoice_lpn_details[lpn_number][sos_id] += float(sku_quantity) * pack_uom_quantity

    def prepare_sos_filter(self):
        '''
        preparing sos filter
        '''
        self.sos_filter = {
            'id__in': self.sos_ids,
            'order__user': self.warehouse.id,
            'quantity__gt': 0,
            'order_status_flag': 'processed_orders'
        }

    def get_sos_details(self):
        '''
        Get sos details
        '''
        self.prepare_sos_filter()
        
        self.request_data_quantity, self.existing_sos_quantity, self.sos_id_order_reference, self.picked_quantity_dict, self.invoiced_picked_quantity = {}, {}, {}, defaultdict(int), defaultdict(int)
        self.order_types, self.order_references, self.picklist_numbers, self.customer_ids, self.serial_transaction_ids, picklist_ids = set(), set(), set(), set(), set(), set()
        
        
        sos_objects = SellerOrderSummary.objects.filter(**self.sos_filter)
        self.sos_df = pd.DataFrame(sos_objects.values('id', 'quantity', 'order_id', 'picklist_id', 'invoice_number', 'full_invoice_number', 'challan_number', 'order_status_flag', 
            'delivered_flag', 'financial_year',  'invoice_reference', 'json_data', 'json_data', transaction_id=F('id'), order_reference=F('order__order_reference'), order_type=F('order__order_type'), customer_type = F('order__customer_identifier__customer_type'),
            order_quantity = F('order__original_quantity'), picklist_number = F('picklist__picklist_number'), picked_quantity=F('picklist__picked_quantity'), order_original_quantity=F('order__original_quantity'),
            sku_code = F('order__sku__sku_code'), sku_desc = F('order__sku__sku_desc'), sku_category = F('order__sku__sku_category'), batch_reference=F('picklist__stock__batch_detail__batch_reference'), order_cancelled_quantity=F('order__cancelled_quantity'),
            batch_number=F('picklist__stock__batch_detail__batch_no'), expiry_date=F('picklist__stock__batch_detail__expiry_date'), marketplace = F('order__marketplace'), batch_json_data=F('picklist__stock__batch_detail__json_data'), mrp=F('order__mrp'),
            batch_mrp=F('picklist__stock__batch_detail__mrp'), manufactured_date = F('picklist__stock__batch_detail__manufactured_date'), picklist_status = F('picklist__status'), customer_id = F('order__customer_id'), invoice_group = F('order__sku__invoice_group'),
            customer_reference = F('order__customer_identifier__customer_reference'), is_serialized=F('order__sku__enable_serial_based')))
        
        
        sos_objects_list = list(sos_objects)
        for obj in sos_objects_list:
            order_filter_row = self.sos_df[self.sos_df['transaction_id'] == obj.id]
            if not order_filter_row.empty:
                order_details_df = order_filter_row.iloc[0]
                order_reference = order_details_df.order_reference
                picklist_batch_json_data = order_details_df.batch_json_data or {}
                cost_price = picklist_batch_json_data.get('batch_wac', None) or None
                try:
                    cost_price = float(cost_price)
                except Exception:
                    cost_price = None

                self.sos_id_order_reference[obj.id] = {
                    "order_reference": order_reference,
                    "order_type": order_details_df.order_type,
                    "sku_code": order_details_df.sku_code,
                    "sku_category": order_details_df.sku_category,
                    "cost_price": cost_price,
                    "mrp": order_details_df.mrp,
                    "batch_mrp": order_details_df.batch_mrp,
                    "invoice_group": order_details_df.invoice_group,
                    "customer_type" : order_details_df.customer_type,
                }

                self.order_references.add(order_reference)
                self.picklist_numbers.add(order_details_df.picklist_number)
                self.order_types.add(order_details_df.order_type)
                self.customer_ids.add(order_details_df.customer_id)
                
                if order_details_df.is_serialized and not self.serial_numbers_request_data.get('sos_ids', {}).get(obj.id):
                    self.errors.append(f"Serial Numbers are mandatory for SKU - {order_details_df.sku_code} and Order Reference - {order_reference}")
                elif order_details_df.is_serialized and self.sos_id_quantity.get(obj.id,0) != len(self.serial_numbers_request_data.get('sos_ids', {}).get(obj.id, [])):
                    self.errors.append(f"Serial Numbers count should be equal to Invoice Quantity for SKU - {order_details_df.sku_code} and Order Reference - {order_reference}")
                elif order_details_df.is_serialized:
                    self.serial_transaction_ids.add(obj.id)

            # will not work for combo skus
            if order_details_df.picklist_id not in picklist_ids:
                self.picked_quantity_dict[obj.order_id] += order_details_df.picked_quantity
                picklist_ids.add(order_details_df.picklist_id)
            
            quantity = self.sos_id_quantity.get(obj.id,0) or 0
            if quantity and obj.order_id not in self.request_data_quantity:
                self.request_data_quantity[obj.order_id] = quantity
            elif quantity:
                self.request_data_quantity[obj.order_id] += quantity
            self.sos_df.loc[self.sos_df['transaction_id'] == obj.id, 'object'] = obj

        self.order_types = list(self.order_types)
        self.picklist_numbers = list(self.picklist_numbers)
        self.order_references = list(self.order_references)
        self.customer_ids = list(self.customer_ids)

        #check quantity on existing invoices
        filters = {
            'order__user': self.warehouse.id,
            'order_status_flag__in': ['customer_invoices','delivery_challans'],
            'quantity__gt': 0,
            'order__order_reference__in': self.order_references
        }
        values = ['quantity', 'order_id', 'picklist_id', 'picklist__picked_quantity']
        existing_invoices = SellerOrderSummary.objects.select_related('order','picklist').filter(**filters).only(*values)
        for sos in existing_invoices:
            quantity = sos.quantity
            if quantity and sos.order_id not in self.existing_sos_quantity:
                self.existing_sos_quantity[sos.order_id] = quantity
            elif quantity:
                self.existing_sos_quantity[sos.order_id] += quantity
            if sos.picklist_id not in picklist_ids:
                self.invoiced_picked_quantity[sos.order_id] += sos.picklist.picked_quantity
                picklist_ids.add(sos.picklist_id)
                
        
        order_detail_objs = OrderDetail.objects.filter(user = self.warehouse.id, order_reference__in=self.order_references).only('id', 'original_quantity', 'cancelled_quantity', 'quantity', 'order_reference', 'status', 'updation_date')
        order_details_data = []
        for order in order_detail_objs:
            obj_dict = order.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['object'] = order
            order_details_data.append(obj_dict)
        self.order_details_data_df = pd.DataFrame(order_details_data)
        self.order_detail_objects = self.order_details_data_df[['id', 'original_quantity', 'cancelled_quantity', 'quantity']].to_dict('records')               
        
    def validate_serial_numbers(self):
        """Validates the serial numbers for the invoice creation request."""
        serial_numbers_data = self.current_serial_transaction_data.get('serial_numbers', {}) or {}
        for tranaction_id, serial_numbers in self.serial_numbers_request_data.get('sos_ids',{}).items():
            valid_serial_numbers = serial_numbers_data.get(tranaction_id, []) or []
            if not set(serial_numbers).issubset(set(valid_serial_numbers)):
                log.info(f"Invalid Serial Numbers for Transaction ID - {tranaction_id}, serial_numbers - {serial_numbers}, valid_serial_numbers - {valid_serial_numbers}")
                self.errors.append(f"Invalid Serial Numbers")

    def validate_invoice_creation_checks(self):
        #check for empty sos data
        if self.sos_df.empty:
            self.errors.append("No Data Found")
            return

        self.get_order_type_configurations()
        self.validate_order_type_zone_master()

        if self.price_application_at_invoice and self.customer_ids:
            self.validate_customer_order_price_application()
            if self.errors:
                return

        #Invoice generation will not allow if picklist is not completed
        if self.picklist_numbers:
            picklist_objects = Picklist.objects.filter(user_id = self.warehouse.id, picklist_number__in=self.picklist_numbers, status__in=['open', 'hold'])
            pick_and_pass_record = PickAndPassStrategy.objects.filter(warehouse_id=self.warehouse.id, reference_number__in=self.picklist_numbers, status__in=['open'])
            if self.restrict_invoice_at_picking and (picklist_objects.exists() or pick_and_pass_record.exists()):
                self.errors.append("Please Complete Picking")
                return

        #invoice quantity validation
        self.validate_invoice_quantity()
        if not self.quantity_check:
                self.errors.append('Invoice for Excess Quantity Not Allowed for order references %s' % str(', '.join(self.order_references)))
        if not self.is_valid and self.restrict_partial_invoice:
            self.errors.append('Partial Invoice Not Allowed for order references %s' % str(', '.join(self.order_references)))

        #validate custom attributes
        self.validate_custom_attributes()

    def validate_order_type_zone_master(self):
        '''
        Validate order type zone master
        '''
        self.order_type_document_details = {}
        for order_type_record in self.order_type_objs:
            order_type = order_type_record.get('order_type', '')
            order_classification = order_type_record.get('order_classification', '')
            order_json_data = order_type_record.get('json_data', {}) or {}
            document_type = order_json_data.get('document_type', '')
            self.order_type_document_details[order_type] = {
                'document_type': 'invoice',
                'order_classification': order_classification
            }
            if document_type == 'delivery_challan':
                self.order_type_document_details[order_type]['document_type'] = 'delivery_challan'

    def validate_customer_order_price_application(self):
        """
        Validates the customer order price application.

        This method checks the price type for each customer and order type, and sets the price type for the invoice generation.
        If the price type is not available in the customer master or order type configuration, an error message is added to the errors list.
        """
        
        #add price application priority
        
        price_type_data = {
            'customer_ids': self.customer_ids,
            'order_types': self.order_types
        }
        self.price_type = PriceApplicationPriority(self.warehouse, price_type_data).get_price_type()

        if not self.price_type:
            self.errors.append("Price Type is Mandatory for Invoice Generation, Please configure Price Type in Customer Master or Order Type Configuration!")

    def get_order_type_configurations(self):
        '''
        Get order type configurations
        '''
        self.order_type_objs = list(OrderTypeZoneMapping.objects.filter(order_type__in=self.order_types, user=self.warehouse.id).values('order_type', 'price_type', 'order_classification', 'json_data'))

    def validate_custom_attributes(self):
        """
        Validates the custom attributes of the invoice.

        This method extracts the custom attributes from the request data,
        validates them using the `validate_attributes` function, and
        stores the validated custom attributes in the `custom_attributes`
        attribute of the class. Any error messages encountered during
        validation are appended to the `errors` attribute of the class.
        """

        restricted_orders_dict = validate_transaction_lock(self.warehouse, self.order_references, transaction='invoice')
        if restricted_orders_dict:
            self.errors.extend(list(restricted_orders_dict.values()))

        custom_attributes = {}
        for attr_key, attr_value in self.request_data[0].get('custom_attributes', {}).items():
            attr_name = attr_key.replace('attr_', '')
            custom_attributes[attr_name] = attr_value

        error_messages, custom_attributes = validate_attributes(self.warehouse, custom_attributes, 'invoice')
        if error_messages:
            self.errors.extend(error_messages)

        self.custom_attributes = custom_attributes

    def get_stock_detail(self):
        '''
        Get stock detail
        '''
        # Get stock details, if already fetched
        if 'stock_df' in self.extra_params:
            self.stock_df = self.extra_params.get('stock_df')
            return

        self.stock_df = get_wip_location_stocks(self.warehouse, self.sos_ids, self.order_types, self.dispense_enabled)

    def get_price_application_data(self):
        """
        Retrieves the price application data from the PriceMaster model and organizes it into dictionaries.

        Returns:
            None
        """
        price_master_objects = list(PriceMaster.objects.filter(user=self.warehouse.id, price_id=self.price_type, status=True).values(
            'price_id', 'price_description', 'sku_category', 'mark_up_percentage', 'mark_down_percentage', 'rsp', 'discount', 'currency_code', sku_code=F('sku__sku_code')))
        for price_master_record in price_master_objects:
            sku_code = price_master_record.get('sku_code', '')
            sku_category = price_master_record.get('sku_category', '')

            #sku wise price list
            if sku_code:
                if not self.sku_price_details.get(sku_code, {}):
                    self.sku_price_details[sku_code] = []
                self.sku_price_details[sku_code].append(price_master_record)

            #sku category wise price list
            if sku_category:
                if not self.sku_category_price_details.get(sku_category, {}):
                    self.sku_category_price_details[sku_category] = [price_master_record]
                self.sku_category_price_details[sku_category].append(price_master_record)

    def validate_full_packing_invoice(self):
        '''
        Validate full packing invoice
        '''
        self.get_stock_lpn_details_for_validation()
        invalid_lpn_numbers = []
        for lpn_number, sos_transaction_details in self.invoice_lpn_details.items():
            stock_lpn_details = self.stock_lpn_details.get(lpn_number, {})
            for sos_id, stock_qty in stock_lpn_details.items():
                request_sos_qty = sos_transaction_details.get(sos_id, 0)
                if request_sos_qty != stock_qty:
                    if lpn_number not in invalid_lpn_numbers:
                        invalid_lpn_numbers.append(lpn_number)
                        self.errors.append(f"Please select all items in LPN {lpn_number}")
            # Validate invalid LPNs
            if not stock_lpn_details:
                self.errors.append(f"Invalid LPN {lpn_number}")

    def get_stock_lpn_details_for_validation(self):
        '''
        Retrieves the stock LPN details for validation.
        '''
        self.stock_lpn_details = {}
        stock_filters = {
            'sku__user': self.warehouse.id,
            'transact_number__in': self.picklist_numbers,
            'quantity__gt': 0,
        }
        if self.customer_level_packing:
            stock_filters['lpn_number__in'] = self.request_lpn_numbers
            stock_filters['receipt_type__in'] = ['so_picking', 'so_dispense']
            stock_filters.pop('transact_number__in', None)
        lpn_stock_objects = list(StockDetail.objects.filter(**stock_filters).values('lpn_number', 'receipt_number', 'quantity'))
        for stock_record in lpn_stock_objects:
            lpn_number = stock_record.get('lpn_number', '')
            receipt_number = stock_record.get('receipt_number', '')
            if not lpn_number or not receipt_number:
                continue

            if not self.stock_lpn_details.get(lpn_number, {}):
                self.stock_lpn_details[lpn_number] = {}
            if not self.stock_lpn_details[lpn_number].get(receipt_number, 0):
                self.stock_lpn_details[lpn_number][receipt_number] = 0
            self.stock_lpn_details[lpn_number][receipt_number] += stock_record.get('quantity', 0)

    def get_sku_price_details(self, sku_code, sku_category):
        '''
        Get sku price details
        '''
        markup_percentage, invalid_sku_price = 0, True
        price_dict, price_detail = {}, {}
        if self.sku_price_details.get(sku_code, []):
            price_dict = self.sku_price_details.get(sku_code, [])[0]
        elif self.sku_category_price_details.get(sku_category, []):
            price_dict = self.sku_category_price_details.get(sku_category, [])[0]
        else:
            invalid_sku_price = False

        if price_dict:
            markup_percentage = price_dict.get('mark_up_percentage', 0)
            price_detail = {
                "price_id": price_dict.get('price_id', ''),
                "price_description": price_dict.get('price_description', ''),
                "mark_up_percentage": markup_percentage,
                "rsp": price_dict.get('rsp', 0),
                "discount": price_dict.get('discount', 0),
                "currency_code": price_dict.get('currency_code', 'INR'),
                "sku_category": sku_category
            }
        return markup_percentage, invalid_sku_price, price_detail

    def get_invalid_price_list_skus(self):
        '''
        Get invalid price list skus
        '''
        if self.invalid_price_list_skus:
            self.errors.append(f"Price List not available for SKU - {', '.join(self.invalid_price_list_skus)}")
        if self.cost_invalid_skus:
            self.errors.append(f"Cost Price not available for SKU - {', '.join(list(self.cost_invalid_skus))}")
        if self.invalid_cost_price_skus:
            self.errors.append(f"Cost Price should be less than MRP for SKU - {', '.join(list(self.invalid_cost_price_skus))}")

    def get_markup_and_price_details(self, sku_code, sku_category):
        """
        Get the markup percentage and price details for a given SKU code and category.

        Parameters:
        - sku_code (str): The SKU code.
        - sku_category (str): The SKU category.

        Returns:
        - markup_percentage (float): The markup percentage.
        - price_detail (dict): The price details.

        """
        markup_percentage, price_detail = 0, {}
        if self.price_application_at_invoice:
            markup_percentage, invalid_sku_price, price_detail = self.get_sku_price_details(sku_code, sku_category)
            if not invalid_sku_price:
                self.invalid_price_list_skus.append(sku_code)
        return markup_percentage, price_detail

    def invalid_cost_sku(self, batch_mrp, cost_price, sku_code):
        '''
        Invalid cost sku
        '''
        if cost_price in [None, '', 'null']:
            self.cost_invalid_skus.add(sku_code)
        elif batch_mrp != None and cost_price > batch_mrp:
            self.invalid_cost_price_skus.add(sku_code)
    
    def prepare_df_list(self, table_data, obj_column):
        obj_column = obj_column or 'obj'
        temp_list = []
        for obj in table_data:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state')
            obj_dict[obj_column] = obj
            temp_list.append(obj_dict) 
        inc_master_df = pd.DataFrame()
        if temp_list:
            inc_master_df = pd.DataFrame(temp_list)
        return inc_master_df
    
    def get_incremental_details(self):
        '''
        Get incremental details
        '''
        master_filter = {
            'warehouse': self.warehouse.id,
            'reference_type__in': ['invoice', 'delivery_challan'],
            'reference_sub_type__in': ['order_type'],
            'status': True
        }
        default_rec_filter = {
            'warehouse': self.warehouse.id,
            'reference_type__in': ['invoice', 'delivery_challan'],
            'reference_sub_type': 'order_type',
            'reference_value': 'default',
            'status': False
        }
        inc_master_objs = IncrementalSegregationMaster.objects.filter(**master_filter)
        self.inactive_inc_master_objs = IncrementalSegregationMaster.objects.filter(**default_rec_filter)
        self.inc_master_df = self.prepare_df_list(inc_master_objs, 'inc_master')

        if not self.inc_master_df.empty:
            inc_table_ids = list(self.inc_master_df['incremental_record_id'].unique())
            table_filter = {
                'id__in': inc_table_ids,
            }
            inc_table_objs = IncrementalTable.objects.filter(**table_filter)
            self.inc_table_df = self.prepare_df_list(inc_table_objs, 'inc_table')
      
        
    def get_prefix_based_on_grouping(self,order_type, create_default=''):
        invoice_number, full_invoice_number, increment_data = '', '', {}
        if self.order_status_flag == 'customer_invoices':
            reference_type = 'invoice'
        elif self.order_status_flag == 'delivery_challans':
            reference_type = 'delivery_challan'
        if not self.inc_master_df.empty:
            inc_master_record = self.inc_master_df[(self.inc_master_df['reference_type']==reference_type)&(self.inc_master_df['reference_value']==order_type)]
            
            if not inc_master_record.empty:
                inc_table_id = inc_master_record['incremental_record_id'].values[0]
                inc_table_record = self.inc_table_df[self.inc_table_df['id']==inc_table_id]
                if not inc_table_record.empty:
                    prefix = inc_table_record['prefix'].values[0]
                    type_name = inc_table_record['type_name'].values[0]
                    incremental_filters = {
                        'id': inc_table_record['id'].values[0]
                    }
                    count, increment_data = get_incremental(self.warehouse, type_name, default_val=1, is_invoice_format=True, filters=incremental_filters)
                    full_invoice_number, invoice_number = preapre_incremental_prefix_details(increment_data, count, prefix)
                    
            elif 'default' in self.inc_master_df[(self.inc_master_df['reference_type']==reference_type)]['reference_value'].unique():
                inc_table_id = self.inc_master_df[(self.inc_master_df['reference_type']==reference_type)&(self.inc_master_df['reference_value']=='default')]['incremental_record_id'].values[0]
                inc_table_record = self.inc_table_df[self.inc_table_df['id']==inc_table_id]
                if not inc_table_record.empty:
                    prefix = inc_table_record['prefix'].values[0]
                    type_name = inc_table_record['type_name'].values[0]
                    incremental_filters = {
                        'id': inc_table_record['id'].values[0]
                    }
                    count, increment_data = get_incremental(self.warehouse, type_name, default_val=1, is_invoice_format=True, filters=incremental_filters)
                    full_invoice_number, invoice_number = preapre_incremental_prefix_details(increment_data, count, prefix)
            else:
                type_name = f"{reference_type}_{create_default}"
                count, increment_data = get_incremental(self.warehouse, type_name, default_val=1, is_invoice_format=True)
                full_invoice_number, invoice_number = preapre_incremental_prefix_details(increment_data, count, create_default)
        else:
            type_name = f"{reference_type}_{create_default}"
            count, increment_data = get_incremental(self.warehouse, type_name, default_val=1, is_invoice_format=True)
            full_invoice_number, invoice_number = preapre_incremental_prefix_details(increment_data, count, create_default)       
                    
        return full_invoice_number, invoice_number
                
    def get_invoice_order_references(self, unique_key, order_type, marketplace, customer_type):
        if not self.invoice_details.get(unique_key, ''):
            invoice_number, full_invoice_number, challan_number = self.get_invoice_reference_details(order_type)
            if challan_number:
                self.is_delivery_challan = True
            self.reference_number = (challan_number, full_invoice_number)[self.order_status_flag == 'customer_invoices']
            self.invoice_details[unique_key] = (invoice_number, full_invoice_number, challan_number)
            self.invoice_order_references[self.reference_number] = {
                "order_type": order_type,
                "invoice_number" : invoice_number,
                "challan_number": challan_number,
                "marketplace" : marketplace,
                "order_ids": [],
                "serial_numbers": {},
                "order_references" : [],
                "sku_codes": [],
                "customer_type" : customer_type
            }
    
    def validate_sku_details(self):
        '''
        Validate SKU Count
        '''
        self.total_skus = set()
        self.no_skus_for_invoice_check = False
        self.markup_percentage, self.price_detail = {}, {}
        self.invalid_price_list_skus, self.cost_invalid_skus, self.invalid_cost_price_skus = [], set(), set()
        if self.no_skus_for_invoice.isdigit() and int(self.no_skus_for_invoice) > 0:
            self.no_skus_for_invoice_check = True
        for row_data in self.request_data:
            transaction_id = row_data.get('transaction_id', '')
            order_reference_details = self.sos_id_order_reference.get(transaction_id, {})
            sku_code = order_reference_details.get('sku_code', '')
            self.total_skus.add(sku_code)
            sku_category = order_reference_details.get('sku_category', '')
            cost_price = order_reference_details.get('cost_price')
            batch_mrp = order_reference_details.get('batch_mrp', 0) or 0
            invoice_group = order_reference_details.get('invoice_group', '')
            row_data['invoice_group'] = invoice_group
            if self.no_skus_for_invoice_check and len(self.total_skus) > int(self.no_skus_for_invoice):
                self.errors.append(f"No of SKUs selected is more than configured limit of {int(self.no_skus_for_invoice)}")
                break
            if self.price_application_at_invoice:
                self.invalid_cost_sku(batch_mrp, cost_price, sku_code)

            markup_percentage, price_detail = self.get_markup_and_price_details(sku_code, sku_category)
            
            if self.price_application_at_invoice and (self.invalid_price_list_skus or self.cost_invalid_skus or self.invalid_cost_price_skus):
                continue
            if markup_percentage:
                self.markup_percentage[(transaction_id,sku_code)] = markup_percentage
            if price_detail:
                self.price_detail[(transaction_id,sku_code)] = price_detail
                
        if self.price_application_at_invoice:
            self.get_invalid_price_list_skus()
        
    def validate_and_prepare_invoice_data(self):
        '''
        Validate and prepare data for invoice
        '''
        self.invoice_details, self.invoice_order_references, self.packing_details, self.order_wise_invoiced_qty,  self.invoiced_order_reference, self.reference_number = {}, {}, {}, {}, [], ''
        self.update_serialization_data, self.new_serialization_data, self.serialized_picklist_numbers = {}, {}, set()
        self.lpn_transaction_details = {}
        self.stock_picklist_lpn_map, self.invoiced_stock_without_lpn = defaultdict(set), {}
        self.reference_number_customer_map = defaultdict(set)
        self.is_delivery_challan = False
        for row_data in self.new_request_data:
            transaction_id = row_data.get('transaction_id', '')
            lpn_number = row_data.get('lpn_number', '')
            quantity = row_data.get('quantity', 0)
            if quantity == 0:
                continue

            marketplace = row_data.get('marketplace','offline') or ''
            order_reference_details = self.sos_id_order_reference.get(transaction_id, {})
            order_reference = order_reference_details.get('order_reference', '')
            order_type = order_reference_details.get('order_type', '')
            invoice_group = order_reference_details.get('invoice_group', '')

            if not self.order_wise_invoiced_qty.get(order_reference):
                self.order_wise_invoiced_qty[order_reference] = 0
            self.order_wise_invoiced_qty[order_reference] += quantity

            if self.order_type_document_details.get(order_type, {}).get('document_type') == 'delivery_challan':
                self.order_status_flag = 'delivery_challans'
            else:
                self.order_status_flag = 'customer_invoices'

            sku_code = order_reference_details.get('sku_code', '')
            cost_price = order_reference_details.get('cost_price')
            batch_mrp = order_reference_details.get('batch_mrp', 0) or 0
            customer_type = order_reference_details.get('customer_type', '')

            markup_percentage = self.markup_percentage.get((transaction_id, sku_code), 0)
            price_detail = self.price_detail.get((transaction_id, sku_code), {})
            
            unique_key = ('', '')
            if self.order_wise_invoice:
                unique_key = (order_reference, unique_key[1])
            if self.invoice_split_on_invoice_group:
                unique_key = (unique_key[0], invoice_group)
            
            self.get_invoice_order_references(unique_key, order_type, marketplace, customer_type)

            invoice_number, full_invoice_number, challan_number = self.invoice_details.get(unique_key, ('', '', ''))
            if self.reference_number not in self.new_invoice_reference:
                self.new_invoice_reference.append(self.reference_number)
                self.invoice_ref_details[self.reference_number] = {
                    "inv_ref": self.reference_number,
                    "delivery_challan": self.is_delivery_challan
                }

            for index, sos_record in self.sos_df[self.sos_df['transaction_id'] == transaction_id].iterrows():
                if sos_record['quantity'] < quantity:
                    self.errors.append(f"Quantity shouldn't be more than picked_quantity for sku - {sos_record['sku_code']}")
                elif sos_record['quantity'] == quantity:
                    sos_record['object'].order_status_flag = self.order_status_flag
                    sos_record['object'].invoice_number = invoice_number
                    sos_record['object'].lpn_number = lpn_number
                    sos_record['object'].full_invoice_number = full_invoice_number
                    sos_record['object'].invoice_reference = self.reference_number
                    sos_record['object'].challan_number = challan_number
                    self.update_wip_stock_quantity(sos_record['transaction_id'], lpn_number, quantity, sos_record['object'])
                    self.update_sos_invoiced_quantity(sos_record, 0, price_detail, batch_mrp, cost_price, markup_percentage)
                    self.prepare_serialization_update_data(sos_record)
                elif sos_record['quantity'] > quantity:
                    sos_record['object'].quantity = sos_record['quantity'] - quantity
                    sos_json_data = sos_record['object'].json_data or {}
                    sos_json_data['invoice_generated_by'] = self.user.username
                    sos_record['object'].json_data = sos_json_data
                    self.update_wip_stock_quantity(sos_record['transaction_id'], lpn_number, quantity, sos_record['object'])
                    self.prepare_sos_data(sos_record, quantity, lpn_number, unique_key, price_detail, batch_mrp, cost_price, markup_percentage)
                    self.update_sos_invoiced_quantity(sos_record, quantity, price_detail, batch_mrp, cost_price, markup_percentage)
                    self.update_lpn_transaction_details(lpn_number, sos_record)
                self.invoice_order_references[self.reference_number].update({
                    'financial_year': sos_record.get('financial_year'),
                    'order_type': sos_record.get('order_type')
                })
                if 'seller_summary_id' not in self.invoice_ref_details.get(self.reference_number, {}):
                    seller_summary_id =  f"{invoice_number}:{sos_record.get('picklist_number', '')}:{sos_record.get('financial_year')}"
                    self.invoice_ref_details[self.reference_number].update({'seller_summary_id': seller_summary_id})

                self.get_invoice_wise_order_ids_and_packing_details(sos_record, lpn_number)
                self.reference_number_customer_map[self.reference_number].add(sos_record.get('customer_reference'))


    def update_lpn_transaction_details(self, lpn_number, sos_record):
        """
        Update the LPN (License Plate Number) transaction details.

        Args:
            lpn_number (str): The LPN number.
            sos_record (dict): The SOS (Sales Order Shipment) record.

        Returns:
            None
        """
        if lpn_number:
            self.lpn_transaction_details[(sos_record['object'].order_id, sos_record['object'].picklist_id, lpn_number)] = (sos_record['object'].id, sos_record['picklist_number'], lpn_number)
            
    def prepare_serialization_update_data(self, sos_record):
        '''
        Prepare serialization update data
        '''
        if sos_record['id'] in self.serial_transaction_ids:
            lpn_number = sos_record['object'].lpn_number or ''
            key = (sos_record['id'], lpn_number)
            data = {
                'picklist_number': sos_record['picklist_number'],
                'reference_number': self.reference_number,
                'reference_type': 'invoice',
                'transaction_id': sos_record['id'],
                'lpn_number': lpn_number,
                'serial_numbers': self.serial_numbers_request_data.get('lpn_level', {}).get(key, [])
            }
            self.update_serialization_data[(sos_record['id'],lpn_number)] = data
    
    def prepare_serialization_creation_data(self, sos_record, new_data_dict):
        '''
        Prepare serialization creation data
        '''
                
        if sos_record['id'] in self.serial_transaction_ids:
            key = (new_data_dict['full_invoice_number'], new_data_dict['challan_number'], new_data_dict['order_id'], new_data_dict['picklist_id'], new_data_dict['lpn_number'],new_data_dict['quantity'])
            serial_key = (sos_record['id'], new_data_dict['lpn_number'])
            data = {
                'picklist_number': sos_record['picklist_number'],
                'reference_number': self.reference_number,
                'reference_type': 'invoice',
                'old_transaction_id': sos_record['id'],
                'serial_numbers': self.serial_numbers_request_data.get('lpn_level', {}).get(serial_key, []),
                'sku_code': sos_record['sku_code'],
                'batch_number': sos_record['batch_number'],
                'lpn_number': new_data_dict['lpn_number'],
            }
            self.new_serialization_data[key] = data
            

    def get_invoice_wise_order_ids_and_packing_details(self, sos_record, lpn_number):
        '''
        Get invoice wise order ids and packing details
        '''
        if sos_record['order_id'] not in self.invoice_order_references[self.reference_number]['order_ids']:
            self.invoice_order_references[self.reference_number]['order_ids'].append(sos_record['order_id'])
        
        if sos_record['order_reference'] not in self.invoice_order_references[self.reference_number]['order_references']:
            self.invoice_order_references[self.reference_number]['order_references'].append(sos_record['order_reference'])

        if sos_record['sku_code'] not in self.invoice_order_references[self.reference_number]['sku_codes']:
            self.invoice_order_references[self.reference_number]['sku_codes'].append(sos_record['sku_code'])

        picklist_number = sos_record.get('picklist_number', '')
        if lpn_number:
            if not self.packing_details.get(picklist_number, []):
                self.packing_details[picklist_number] = {"completed": {lpn_number}}
            else:
                self.packing_details[picklist_number]["completed"].add(lpn_number)
    
    def update_sos_invoiced_quantity(self, sos_record, quantity, price_dict, mrp, cost_price, markup_percentage):
        '''
        Update sos invoiced quantity
        '''
        self.sos_df.loc[self.sos_df['id'] == sos_record['id'], 'quantity'] -= quantity
        sos_record['object'].quantity = sos_record['quantity'] - quantity
        sos_json_data = sos_record['object'].json_data or {}
        sos_json_data['invoice_generated_by'] = self.user.username
        if self.price_type and cost_price:
            unit_price = cost_price + (cost_price * markup_percentage / 100)
            sos_json_data.update({
                "unit_price": truncate_float(unit_price,self.price_decimal_limit),
                "mrp": mrp,
                "price_details": price_dict,
                "batch_wac": truncate_float(cost_price, self.price_decimal_limit)
            })
        sos_record['object'].json_data = sos_json_data
        self.sos_df.loc[self.sos_df['id'] == sos_record['id'], 'object'] = sos_record['object']

    def get_invoice_reference_details(self, order_type):
        '''
        Get invoice reference incremental details
        '''
        invoice_number, full_invoice_number, challan_number = '', '', ''
        if self.order_status_flag == 'customer_invoices':
            full_invoice_number, invoice_number = self.get_prefix_based_on_grouping(order_type, create_default='INV')
        elif self.order_status_flag == 'delivery_challans':
            challan_number, invoice_number = self.get_prefix_based_on_grouping(order_type, create_default = 'CHN')
        return invoice_number, full_invoice_number, challan_number
    
    def validate_invoice_quantity(self):
        '''Invoice Quantity Validation'''
        self.is_valid,self.quantity_check = True, True
        for order in self.order_detail_objects:
            sos_quantity = self.existing_sos_quantity.get(order['id'],0) or 0
            quantity = self.request_data_quantity.get(order['id'],0) or 0
            invoiced_picked_qty = self.invoiced_picked_quantity.get(order['id'],0) or 0
            picked_quantity = self.picked_quantity_dict.get(order['id'],0) or 0
            total_picked_qty = picked_quantity + invoiced_picked_qty
            order_available_qty = order['original_quantity'] - order['cancelled_quantity'] - order['quantity']
            order_quantity_to_check = (order_available_qty, total_picked_qty)[self.invoice_to_picklist_quantity==True]
            if sos_quantity:
                quantity = quantity + sos_quantity
            if self.decimal_limit:
                quantity = truncate_float(quantity, self.decimal_limit)
            if quantity > order_quantity_to_check:
                log.info(f"Quantity for order {order['id']} is more than available quantity, quantity: {quantity}, available quantity: {order_quantity_to_check}")
                self.quantity_check = False
                break
            if order_quantity_to_check != quantity and self.restrict_partial_invoice:
                log.info(f"Partial Invoice Not Allowed for order {order['id']}, quantity: {quantity}, available quantity: {order_available_qty}")
                self.is_valid = False
                break

    def prepare_sos_data(self, sos_record, quantity, lpn_number, unique_key, price_detail, mrp, cost_price, markup_percentage):
        '''
        Prepare sos data
        '''
        invoice_number, full_invoice_number, challan_number = self.invoice_details.get(unique_key, ('', '', ''))
        sos_data_dict = {
            "account_id": self.account_id,
            "order_id": sos_record.get('order_id', ''),
            "picklist_id": sos_record.get('picklist_id', ''),
            "lpn_number": lpn_number,
            "quantity": quantity,
            "order_status_flag": self.order_status_flag,
            "invoice_number": invoice_number,
            "full_invoice_number": full_invoice_number,
            "challan_number": challan_number,
            "financial_year": sos_record.get('financial_year', ''),
            "invoice_reference": self.reference_number,
            "json_data": sos_record.get('json_data', {}) or {},
            "creation_date": sos_record.get('creation_date', '')
        }
        sos_data_dict['json_data']['invoice_generated_by'] = self.user.username
        if self.price_type and cost_price:
            unit_price = cost_price + (cost_price * markup_percentage / 100)
            sos_data_dict['json_data'].update({
                "unit_price": truncate_float(unit_price, self.price_decimal_limit),
                "mrp": mrp,
                "price_details": price_detail,
                "batch_wac": truncate_float(cost_price, self.price_decimal_limit)
            })
        self.new_sos_objects.append(SellerOrderSummary(**sos_data_dict))
        self.prepare_serialization_creation_data(sos_record, sos_data_dict)

    def update_wip_stock_quantity(self, sos_id, lpn_number, invoice_quantity, sos_object):
        '''
        Updating wip location stock
        '''
        pack_uom_quantity = 0
        sos_json_data = sos_object.json_data or None
        if sos_json_data is not None:
            pack_uom_quantity = sos_object.json_data.get('pack_uom_quantity',1) or 1
        if pack_uom_quantity:
            invoice_quantity = invoice_quantity * pack_uom_quantity
        if not self.stock_df.empty:
            if lpn_number:
                stock_data = self.stock_df.loc[(self.stock_df['receipt_number'] == sos_id) & (self.stock_df['lpn_number'] == lpn_number)]
            else:
                stock_data = self.stock_df.loc[self.stock_df['receipt_number'] == sos_id]
            for index, stock_record in stock_data.iterrows():
                min_qty = min(invoice_quantity, stock_record['quantity'])

                invoice_quantity -= min_qty
                self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'quantity'] -= min_qty

                stock_record['object'].quantity -= min_qty
                if not lpn_number and stock_record['object'].lpn_number:
                    self.stock_picklist_lpn_map[stock_record['object'].transact_number].add(stock_record['object'].lpn_number)
                    self.invoiced_stock_without_lpn[(stock_record['object'].transact_number, stock_record['object'].receipt_number, stock_record['object'].lpn_number)] = stock_record['object'].quantity
                self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'object'] = stock_record['object']

                if invoice_quantity <= 0:
                    break

    def prepare_packing_updation_details(self, seller_order_new_objects):
        """
        Prepares and returns the details of LPN (License Plate Number) transaction changes
        and updates packing information based on the provided seller order objects.
        Args:
            seller_order_new_objects (list): A list of seller order objects containing
                                             details like order ID, picklist ID, LPN number,
                                             and quantity.
        Returns:
            dict: A dictionary containing updated LPN transaction details, including old
                  and new transaction IDs, LPN numbers, and packed quantities.
        """
        lpn_transaction_changes_details = {}
        for obj in seller_order_new_objects:
            old_transaction_id, picklist_number, lpn_number = '', '', ''
            result = self.lpn_transaction_details.get((obj.order_id, obj.picklist_id, obj.lpn_number))
            key = (obj.full_invoice_number, obj.challan_number, obj.order_id, obj.picklist_id, obj.lpn_number, obj.quantity)
            if key in self.new_serialization_data:
                self.new_serialization_data[key]['transaction_id'] = obj.id
            if result is not None:
                old_transaction_id, picklist_number, lpn_number = self.lpn_transaction_details.get((obj.order_id, obj.picklist_id, obj.lpn_number))
            if not lpn_transaction_changes_details.get(picklist_number):
                lpn_transaction_changes_details[str(picklist_number)] = []
            if obj.lpn_number and self.lpn_transaction_details.get((obj.order_id, obj.picklist_id, obj.lpn_number)):
                lpn_transaction_changes_details[str(picklist_number)].append({
                    "old_transaction_id": old_transaction_id,
                    "new_transaction_id": obj.id,
                    "lpn_number": lpn_number
                })

        for unique_key, qty in self.invoiced_stock_without_lpn.items():
            picklist_no, sos_id, lpn_number = unique_key
            if not lpn_transaction_changes_details.get(str(picklist_no)):
                lpn_transaction_changes_details[str(picklist_no)] = []
            is_new_record = False
            for item in lpn_transaction_changes_details[str(picklist_no)]:
                if item.get("old_transaction_id") == sos_id:
                    item["packed_quantity"] = qty
                    is_new_record = True
            if not is_new_record:
                lpn_transaction_changes_details[str(picklist_no)].append({
                    "old_transaction_id": sos_id,
                    "lpn_number": lpn_number,
                    "packed_quantity": qty
                })
            if not self.packing_details.get(picklist_no, {}).get("closed"):
                self.packing_details[picklist_no] = {"closed": {lpn_number}}
            else:
                self.packing_details[picklist_no]["closed"].add(lpn_number)

        return lpn_transaction_changes_details


    def create_and_update_invoice_data(self):
        '''
        Create and update invoice data
        '''
        with transaction.atomic('default'):
            updated_sos_values = []
            for obj in self.sos_df['object'].tolist():
                obj.updation_date = datetime.now()
                updated_sos_values.append(obj)

            lpn_transaction_changes_details, seller_order_new_objects = {}, []
            SellerOrderSummary.objects.bulk_update_with_rounding(updated_sos_values, ['order_status_flag', 'invoice_number', 'full_invoice_number', 'challan_number', 'quantity', 'invoice_reference', 'lpn_number', 'json_data', 'updation_date'], batch_size=self.batch_size)
            if self.new_sos_objects:
                seller_order_new_objects = SellerOrderSummary.objects.bulk_create_with_rounding(self.new_sos_objects, batch_size=self.batch_size)
            lpn_transaction_changes_details = self.prepare_packing_updation_details(seller_order_new_objects)
            if not self.stock_df.empty:
                self.update_invoiced_stock_quantity()
            if self.order_references:
                self.update_order_status()
            if self.custom_attributes:
                self.save_invoice_custom_attributes()
            if not self.stock_picklist_lpn_map and self.packing_details:
                extra_params = {
                    "headers": {
                        "Warehouse": self.request.headers.get("Warehouse"),
                        "Authorization": self.request.headers.get('Authorization', ''),
                    },
                    "request_meta": self.request.META,
                    "release_carton_at_invoice": self.release_carton_at_invoice,
                    "lpn_transaction_changes_details": lpn_transaction_changes_details
                }
                for picklist_number, packing_data in self.packing_details.items():
                    for status, lpn_numbers in packing_data.items():
                        packing_data[status] = list(lpn_numbers)
                update_packing_status.apply_async(args=[self.user.username, self.warehouse.username, self.packing_details, extra_params])
                self.packing_details = {}

            # in case of lpn is present in stock and invoicing without lpn
            zero_qty_picklists = self.prepare_data_for_packing_status_updation(lpn_transaction_changes_details)
            
            #udpate the sorting staging entries
            self.fetch_and_update_sorting_staging_entries(zero_qty_picklists)
            
            #update the audit master entries
            self.update_audit_master_entries(zero_qty_picklists)

        self.post_invoice_process()
        
    def update_audit_master_entries(self, zero_qty_picklists):
        """
        Updates the status of audit master entries for picklists with zero quantity.
        Args:
            zero_qty_picklists (dict): A dictionary where keys are picklist identifiers and values are lists
                of LPN numbers associated with zero quantity picklists.
        Side Effects:
            - Updates the status of matching AuditEntry records in the database from status 1 to status 3.
            - Logs the picklist numbers and LPN numbers for which audit entries were updated.
        """
        
        if not self.flag_order_level_discrepancy:
            return
        
        lpn_numbers = set(self.request_lpn_numbers)
        for _picklist, lpn_numbers_list in zero_qty_picklists.items():
            lpn_numbers.update(lpn_numbers_list)

        if lpn_numbers:
            audit_filters = {
                'warehouse': self.warehouse.id,
                'reference_number__in': self.picklist_numbers,
                'audit_reference__in': lpn_numbers,
                'status': 1
            }
            audit_entries = AuditEntry.objects.filter(**audit_filters)
            if audit_entries.exists():
                audit_entries.update(status=3)
                log.info(f"Updated audit entries for picklist numbers: {self.picklist_numbers} lpn numbers: {lpn_numbers} ")

        
    def fetch_and_update_sorting_staging_entries(self, zero_qty_picklists):
        '''
        Fetch and update sorting staging entries
        '''
        
        if self.pigeon_hole_sorting != 'lpn':
            return
        
        picklist_numbers = self.picklist_numbers
        lpn_numbers = set(self.request_lpn_numbers)

        for _picklist, lpn_numbers_list in zero_qty_picklists.items():
            lpn_numbers.update(lpn_numbers_list)

        if lpn_numbers:
            staging_filters = {
                'picklist_number__in': picklist_numbers,
                'order_reference__in': self.order_references,
                'carton_no__in': list(lpn_numbers),
                'user_id': self.warehouse.id,
                'segregation': 'outbound_staging',
                'location__zone__storage_type': 'pre_invoice',
                'status': 0
            }
            sorting_staging_entries = StagingInfo.objects.filter(**staging_filters)
            if sorting_staging_entries.exists():
                sorting_staging_entries.update(status=1)
                log.info(f"Updated sorting staging entries for picklist numbers: {lpn_numbers} and order references: {self.order_references}, picklist numbers: {picklist_numbers}")
        

    def prepare_data_for_packing_status_updation(self, lpn_transaction_changes_details):
        """
        checks if current lpn is completely invoiced if yes updates the packing status and releases lpn if it is configured.
        """
        zero_qty_picklists = defaultdict(list)
        if self.stock_picklist_lpn_map:
            # check in stock for lpn and picklist if nothing move packing status to completed and release lpn
            picklists, lpns = [], []
            for picklist, lpn_numbers in self.stock_picklist_lpn_map.items():
                picklists.append(picklist)
                lpns.extend(list(lpn_numbers))

            picklist_lpn_qty_map = defaultdict(float)
            stocks = StockDetail.objects.filter(transact_number__in=picklists, lpn_number__in=lpns, sku__user=self.warehouse.id, quantity__gt=0)
            for stock in stocks:
                lpn = stock.lpn_number
                picklist = stock.transact_number
                picklist_lpn_qty_map[(picklist, lpn)] += stock.quantity
  
            # Find picklists and LPNs with quantity 0
            for picklist, lpn_numbers in self.stock_picklist_lpn_map.items():
                for lpn in lpn_numbers:
                    if (picklist, lpn) not in picklist_lpn_qty_map:
                        if not self.packing_details.get(picklist, {}):
                            self.packing_details[picklist] = {}
                        if not self.packing_details.get(picklist, {}).get("completed", {}):
                            self.packing_details[picklist] = {"completed": {lpn}}
                        else:
                            self.packing_details[picklist]["completed"].add(lpn)
                        if lpn in self.packing_details[picklist].get("closed", {}):
                            self.packing_details[picklist]["closed"].remove(lpn)
                        zero_qty_picklists[picklist].append(lpn)

            # Call update packing status for picklists with zero quantity LPNs
            if self.packing_details:
                extra_params = {
                    "headers": {
                        "Warehouse": self.request.headers.get("Warehouse"),
                        "Authorization": self.request.headers.get('Authorization', ''),
                    },
                    "request_meta": self.request.META,
                    "release_carton_at_invoice": self.release_carton_at_invoice,
                    "lpn_transaction_changes_details": lpn_transaction_changes_details
                }
                for picklist_number, packing_data in self.packing_details.items():
                    for status, lpn_numbers in packing_data.items():
                        packing_data[status] = list(lpn_numbers)
                update_packing_status.apply_async(args=[self.user.username, self.warehouse.username, self.packing_details, extra_params])
        return zero_qty_picklists

    def update_invoiced_stock_quantity(self):
        '''
        Update invoiced stock quantity
        '''
        if self.stock_df['object'].tolist():
            StockDetail.objects.bulk_update_with_rounding(self.stock_df['object'].tolist(), ['quantity'])

    def update_order_status(self):
        '''
        Update order status as completed after invoice creation
        '''
        if self.order_references:
            sos_list = list(SellerOrderSummary.objects.filter(order__user=self.warehouse.id, order_status_flag__in=['customer_invoices', 'delivery_challans'], quantity__gt=0, order__order_reference__in=self.order_references).exclude(order__status=5).values(
                'order_id', 'order__original_quantity', 'order__cancelled_quantity').annotate(total_invoice_qty=Sum('quantity')))
            invoiced_order_ids = []
            not_invoiced_orders = {}
            for sos_record in sos_list:
                total_order_qty = sos_record.get('order__original_quantity', 0) - sos_record.get('order__cancelled_quantity', 0)
                if total_order_qty == sos_record['total_invoice_qty']:
                    invoiced_order_ids.append(sos_record['order_id'])
                else:
                    not_invoiced_orders[sos_record['order_id']] = sos_record['total_invoice_qty']
            if invoiced_order_ids:
                log.info(f"Updating order status for order ids - {invoiced_order_ids}")
                invoiced_orders = self.order_details_data_df[self.order_details_data_df['id'].isin(invoiced_order_ids)]
                
                # Update the status and updation_date
                for index, row in invoiced_orders.iterrows():
                    row_obj = row['object']
                    row_obj.status = 5
                    row_obj.updation_date = datetime.now()
                
                # Bulk update with the modified objects
                invoiced_order_objs = invoiced_orders['object'].tolist()
                if invoiced_order_objs:
                    OrderDetail.objects.bulk_update(invoiced_order_objs, ['status', 'updation_date'])
            if self.order_cancel_on_invoice:
                # based on config cancellation of orders
                self.cancel_order_on_invoice(not_invoiced_orders)
            if invoiced_order_ids or self.order_cancel_on_invoice:
                extra_params = {"invoice_details": self.order_wise_invoiced_qty}
                update_order_header_status(self.warehouse, self.order_references, extra_params=extra_params)

        # Triggering webhook for cancelled orders
        if self.cancelled_order_references:
            integration_filters = {
                'order_references': list(self.cancelled_order_references),
                'sku_codes': list(self.cancelled_sku_codes),
            }
            webhook_integration_3p(self.warehouse.id, "cancel_order", filters=integration_filters)

    def cancel_order_on_invoice(self, not_invoiced_orders: dict):
        '''
        Cancel order based on auto cancel order after cancel invoice configuration
        '''
        order_df_filtered = self.order_details_data_df[
            (self.order_details_data_df['order_reference'].isin(self.order_references)) &
            (self.order_details_data_df['status'].astype(int) == 1)
        ]
        
        bulk_open_objs = []
        if not order_df_filtered.empty:
            for _, row in order_df_filtered.iterrows():
                order = row['object']
                if order.quantity:
                    order.cancelled_quantity = order.cancelled_quantity + order.quantity
                    order.quantity = 0
                    total_invoice_qty = not_invoiced_orders.get(order.id, 0)
                    if order.cancelled_quantity == order.original_quantity:
                        order.status = 3
                    elif order.original_quantity - order.cancelled_quantity == total_invoice_qty:
                        order.status = 5
                    else:
                        order.status = 0
                    order.updation_date = datetime.now()
                    bulk_open_objs.append(order)
                    self.cancelled_order_references.add(order.order_reference)
                    self.cancelled_sku_codes.add(order.sku.sku_code)
            if bulk_open_objs:  
                OrderDetail.objects.bulk_update(bulk_open_objs, ['cancelled_quantity', 'quantity', 'status', 'updation_date'])
        

    def prepare_new_transaction_data(self):
        """Prepare new transaction data by mapping old_transaction_id to transaction data."""
        return {(data.get('old_transaction_id'),data.get('lpn_number')): data for _, data in self.new_serialization_data.items()}
    
    def update_serial_transaction_payload(self, serial_transaction_payload, key, transaction_data, status):
        """Helper function to update serial transaction payload structure."""
        transaction_data['status'] = status
        if key in serial_transaction_payload:
            serial_transaction_payload[key]['items'].append(transaction_data)
        else:
            serial_transaction_payload[key] = {
                'reference_number': transaction_data['reference_number'],
                'reference_type': transaction_data['reference_type'],
                'items': [transaction_data]
            }
                  
    def serialization_transaction_data_preparation(self, serial_transaction_data):
        """Prepare transaction data for serialization."""
        serial_transaction_payload = defaultdict(dict)
        for transact_id, transact_id_data in serial_transaction_data.items():
            for data in transact_id_data:
                lpn_number = data.get('lpn_number', '') or ''
                key = (transact_id, lpn_number)
                new_data = deepcopy(data)
                if key in self.update_serialization_data:
                    # Update existing transaction status to Inactive
                    existing_key = (data['reference_number'], data['reference_type'])
                    self.update_serial_transaction_payload(serial_transaction_payload, existing_key, data, status=0)

                    # Create new serial transaction with new reference number
                    new_data.update({
                        'reference_number': self.update_serialization_data.get(key,{}).get('reference_number',''),
                        'reference_type': self.update_serialization_data.get(key,{}).get('reference_type', ''),
                        'lpn_number': self.update_serialization_data.get(key,{}).get('lpn_number',''),
                        'serial_numbers': self.update_serialization_data.get(key,{}).get('serial_numbers', []),
                    })
                    new_key = (new_data['reference_number'], new_data['reference_type'])
                    self.update_serial_transaction_payload(serial_transaction_payload, new_key, new_data, status=1)

                elif key in self.new_serial_transactions_data:
                    invoiced_serial_numbers = set(self.new_serial_transactions_data[key].get('serial_numbers', []))
                    
                    new_data.update({
                        'serial_numbers': list(invoiced_serial_numbers),
                        'reference_number': self.new_serial_transactions_data.get(key,{}).get('reference_number',''),
                        'reference_type': self.new_serial_transactions_data.get(key,{}).get('reference_type',''),
                        'lpn_number': self.new_serial_transactions_data.get(key,{}).get('lpn_number', ''),
                        'transact_id': self.new_serial_transactions_data.get(key,{}).get('transaction_id', '')
                    })

                    data['serial_numbers'] = list(invoiced_serial_numbers)

                    # Update existing transaction Serial Numbers 
                    existing_key = (data['reference_number'], data['reference_type'])
                    self.update_serial_transaction_payload(serial_transaction_payload, existing_key, data, status=0)

                    # Create new Transaction with new reference number (Invoice Number)
                    new_key = (new_data['reference_number'], new_data['reference_type'])
                    self.update_serial_transaction_payload(serial_transaction_payload, new_key, new_data, status=1)

        return serial_transaction_payload
    
    def get_current_serial_transaction_data(self):
        """Get current serial transaction data."""
        filter_data = {
            'filters': {
                'transact_id__in': self.serial_transaction_ids,
                'reference_type': 'so_picking',
                'status': 1
            }
        }
        
        if 'serial_transactions' in self.extra_params:
            serial_transactions = self.extra_params['serial_transactions']
        else:
            serialnumber_get_instance = SerialNumberTransactionMixin(self.user, self.warehouse, filter_data)
            serial_transactions = serialnumber_get_instance.get_sntd_details()
        
        for data in serial_transactions.get('data', []):
            transact_id = data.get('transact_id', '')
            serial_numbers = data.get('serial_numbers', [])
            self.current_serial_transaction_data['serial_data'][transact_id].append(data)
            self.current_serial_transaction_data['serial_numbers'][transact_id].extend(serial_numbers)


    def create_or_update_serial_transaction_data(self):
        """Create serial transaction data and update records."""

        self.new_serial_transactions_data = self.prepare_new_transaction_data()
        self.serial_transaction_data = self.serialization_transaction_data_preparation(self.current_serial_transaction_data.get('serial_data', {}))

        payload = list(self.serial_transaction_data.values())
        log.info(f"Serial Transaction Data Creation/Update Payload: {payload}")
        serialnumbr_put_instance = SerialNumberTransactionMixin(self.user, self.warehouse, payload, False)
        response = serialnumbr_put_instance.create_update_sn_transaction()

        log.info(f"Serial Transaction Data Creation/Update Response: {response}")
        
    def make_async_call(self, function, params):
        """
        Helper function to execute a function either synchronously or asynchronously based on the `is_async_task` flag.
        Args:
            function (callable): The function to be executed.
            params (list): The parameters to be passed to the function.
        Returns:
            None
        """
        if self.is_async_task:
            function(*params)
        else:
            function.apply_async(args=params)
                            
    def post_invoice_process(self):
        '''
        Post invoice integration calls
        '''
        try:
            #drop stock to post invoice if post staging is enabled.
            order_ref_location_map = {}
            if self.post_invoice_staging_lane:
                self.drop_stock_to_post_invoice()
            for order, location in self.existing_order_locations.items():
                order_ref_location_map[order] = location.location
            for order, location in self.new_order_locations.items():
                order_ref_location_map[order] = location.location

            order_ref_filter_map = {} # will work only for 1 order multiple invoices
            extra_params = {'request_user': self.user.username, 'Warehouse': self.warehouse.username, 'Authorization': self.request.headers.get('Authorization', ''), 'order_ref_location_map': order_ref_location_map, 'order_ref_zone_map': self.order_ref_zone_map}
            if len(self.serial_transaction_ids) > 0:
                self.create_or_update_serial_transaction_data()
            for full_invoice_number, invoice_info in self.invoice_order_references.items():
                if full_invoice_number:
                    order_type = invoice_info.get('order_type', '')
                    serial_numbers_details = self.serial_transaction_data.get((full_invoice_number,'invoice'), {}).get('items', []) or []

                    dc_check = False
                    if self.order_status_flag.lower() == 'delivery_challans':
                        dc_check = True

                    order_type_conf_detail = self.order_type_document_details.get(order_type, {})
                    if order_type.lower() == 'stocktransfer' or (order_type_conf_detail.get('order_classification', '') == 'STOCK_TRANSFER'):
                        if order_type_conf_detail.get('document_type', '') not in ['delivery_challan']:
                            # errors_list, invoice_data = get_customer_invoice_tab_func(self.request, self.warehouse, gen_data_dict, invoice_creation=True)
                            reqeuest_dict = {
                                'user': self.request.user.id,
                                'headers': {
                                    'Authorization': self.request.headers.get('Authorization', ''),
                                    'Warehouse': self.warehouse.username
                                },
                                'request_meta': self.request.META,
                                'request_files': self.request.FILES,
                            }
                            self.make_async_call(asn_creation, [reqeuest_dict, self.warehouse.id, full_invoice_number, serial_numbers_details, dc_check])
                        self.make_async_call(insert_shipment_invoice_data_fun, [self.warehouse.id, full_invoice_number, "stocktransfer", extra_params])
                    else:
                        self.make_async_call(insert_shipment_invoice_data_fun, [self.warehouse.id, full_invoice_number, "invoice", extra_params])

                    if full_invoice_number:
                        headers_data = {
                            'Authorization': self.request.headers.get('Authorization',''),
                            'Warehouse': self.warehouse.username
                        }
                        filters = {
                            "invoice_number": full_invoice_number,
                            "serial_numbers":  serial_numbers_details,
                            "request_META": self.request.META,
                            "headers": headers_data,
                            "order_references": invoice_info.get('order_references', []),
                            "dc_check": dc_check,
                            "sku_codes": invoice_info.get('sku_codes', []),
                        }
                        if self.post_invoice_staging_lane:
                            filters['staging_lane_orders'] = order_ref_location_map
                        if self.invoice_callback_at_order_level and len(invoice_info.get('order_references', [])) == 1:
                            order_ref_filter_map[invoice_info.get('order_references', [])[0]] = filters
                            continue
                        webhook_integration_3p(self.warehouse.id, "invoice_creation", filters=filters)
            if self.invoice_callback_at_order_level and order_ref_filter_map:
                for _, filters in order_ref_filter_map.items():
                    webhook_integration_3p(self.warehouse.id, "invoice_creation", filters=filters)

            extra_params = {'request_user': self.user.username, 'auto_generate_einvoice': self.auto_generate_einvoice}
            einvoice_references = [ full_invoice_number for full_invoice_number, invoice_info in self.invoice_order_references.items() if invoice_info.get('customer_type', '') not in ['non-GST']]
            self.make_async_call(insert_invoice_data, [self.warehouse.username, list(self.invoice_order_references.keys()), einvoice_references, None, extra_params])
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("Post Create Invoice integrations failed with error- %s" % str(e))


    def drop_stock_to_post_invoice(self):
        '''
        Drop stock to post invoice
        '''
        #change location in stock detail get location suggestion for post invoice
        # check if some part of order is already dropped if dropped simply move stock and don't increase location quantity
        orders = []
        order_ref_inv_map = {}
        self.order_ref_zone_map = {}
        for invoice_reference, invoice_info in self.invoice_order_references.items():
            order_refs = invoice_info.get('order_references', [])
            orders.extend(order_refs)
            for order in order_refs:
                order_ref_inv_map[order] = invoice_reference
        self.existing_order_locations, self.new_order_locations, _ = get_locations_for_post_invoice(self.warehouse, orders)

        location_id_orders_map = defaultdict(set)
        for order in orders:
            if order not in self.existing_order_locations and order in self.new_order_locations:
                location = self.new_order_locations[order]
                location_id_orders_map[location.id].add(order)
            if order in self.existing_order_locations:
                self.order_ref_zone_map[order] = self.existing_order_locations[order].zone.zone
            elif order in self.new_order_locations:
                self.order_ref_zone_map[order] = self.new_order_locations[order].zone.zone
        
        locations_ids = list(location_id_orders_map.keys())
        location_objs = LocationMaster.objects.filter(id__in=locations_ids).select_related('zone')
        
        for location_obj in location_objs:
            new_orders = location_id_orders_map[location_obj.id]
            location_obj.filled_capacity += len(new_orders)
        
        # create staging info for new orders
        staging_objs = []
        for order in self.new_order_locations:
            stock_info_obj = StagingInfo(
                            account_id = self.account_id,
                            user_id = self.warehouse.id,
                            location_id = self.new_order_locations[order].id,
                            segregation = 'post_invoice',
                            carton_no = '',
                            order_reference = order,
                            invoice_number = order_ref_inv_map[order],
                            json_data = {}
                            )
            staging_objs.append(stock_info_obj)
        StagingInfo.objects.bulk_create(staging_objs)
        
        LocationMaster.objects.bulk_update_with_rounding(location_objs, ['filled_capacity'])
    

    def delete_cache_ids(self):
        ''' Delete Cache Ids'''
        try:
            for sos_id in self.sos_ids:
                cache.delete(sos_id)
        except Exception:
            pass
        

    def save_invoice_custom_attributes(self):
        """
        Saves the custom attributes for the invoice.
        """
        invoice_addtion_info_objects = []
        for full_invoice_number in self.invoice_order_references.keys():
            # Framing custom attributes for invoice
            json_data = {"custom_attributes": self.custom_attributes}
            invoice_addtion_info_objects.append(
                InvoiceAdditionalInfo(
                    warehouse=self.warehouse,
                    full_invoice_number=full_invoice_number,
                    json_data=json_data,
                    account_id=self.account_id,
                )
            )
        if invoice_addtion_info_objects:
            InvoiceAdditionalInfo.objects.bulk_create(invoice_addtion_info_objects)
            

@celery_app.task
def insert_invoice_data(warehouse, full_invoice_numbers, auto_einvoice_refs, url, extra_params=None):
    if extra_params is None:
        extra_params = {}
    request_user = extra_params.get('request_user', '')

    #create a class to insert invoice data
    save_invoice_data = SaveInvoiceData(warehouse, full_invoice_numbers, None, url, None)
    save_invoice_data.save_invoiced_data()

    if extra_params.get('auto_generate_einvoice') and auto_einvoice_refs:
        # trigger einvoice integration data preparation
        trigger_einvoice_integration(auto_einvoice_refs, warehouse, request_user)
