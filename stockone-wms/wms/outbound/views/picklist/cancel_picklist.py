#package imports
import pandas as pd
from collections import defaultdict
import json
import traceback

#django imports
from django.http import HttpResponse, JsonResponse, HttpRequest, QueryDict
from django.db import transaction
from django.db.models import Sum, F, Q
from django.utils import timezone
from django.test.client import RequestFactory

#wms imports
from wms.celery import app
from wms_base.models import User
from wms_base.wms_utils import init_logger

#lms imports
from lms.models import TaskMaster, EmployeeMaster
from lms.rapid import get_task

#core operations imports
from core.models import TempJson, UserZoneMapping
from core_operations.views.common.main import (
    get_multiple_misc_values, get_user_ip, get_user_time_zone,
    get_local_date_known_timezone, get_warehouse
)
from core_operations.views.common.validation import update_order_header_status
from core_operations.views.services.packing_service import PackingService

#inbound imports
from inbound.models import POLocation, ReturnToVendor
from inbound.views.putaway.confirmation import create_putaway_task

#production imports
from production.models.job_order import JOMaterial, JobOrder

#inventory imports
from inventory.models import (
    BAtoSADetail, SerialNumber, SerialNumberTransaction, StockDetail
)

#outbound imports
from outbound.models import (
    OrderDetail, Picklist, SellerOrder, PickAndPassStrategy, SellerOrderSummary, AuditEntry
)
from .helpers import short_close_subsequent_child_picklists_of_combo

#production import
from production.models import BOMMaster

from .constants import BACKFLUSH_JO_TYPES

log = init_logger('logs/cancel_picklist.log')

@get_warehouse
def picklist_delete(request, warehouse: User, pick_ids: list = [], picklist_numbers: list = [], cancel_open_orders:list = None, internal_call=False):
    """ This code will delete the picklist selected"""
    cancel_open_orders = cancel_open_orders or []
    if pick_ids:
        params = {'id__in': pick_ids}
        params['status__in'] = ["open", "short_closed"]
        picklist_id = request.POST.get("picklist_number", "")
        key = 'process'
        log_dict = pick_ids
        task_objs = TaskMaster.objects.filter(task_ref_id__in=pick_ids)
        if task_objs:
            task_objs.update(status=1)
    else:
        picklist_numbers = request.GET.get("picklist_numbers", '')
        picklist_numbers = picklist_numbers.split(',')
        picklist_id = request.GET.get("picklist_id", "")
        if picklist_id:
            picklist_numbers = [picklist_id]
        key = request.GET.get("key", "")
        params = {'picklist_number__in': picklist_numbers, 'status__in':["open", "short_closed"]}
        log_dict = request.GET.dict()
        if TaskMaster.objects.filter(warehouse_id=warehouse.id, reference_number__in=picklist_numbers, status=False, employee_id__isnull=False).exists():
            return HttpResponse("Picking is in progress. Release the picker to proceed with cancellation")
        update_pick_and_pass_strategy(warehouse, picklist_numbers)

    order_ids, rtv_ids, jo_ids, ba_to_sa_ids, combo_order_ids, short_close_picklist_objs = set(), [], [], [], [], []
    order_reference, order_objs, pick_and_pass_picklists, order_picklist_map = '', {}, set(), defaultdict(set)
    order_id_picklist_objs_dict = {}
    picklist_objs = Picklist.objects.filter(user=warehouse.id, **params).select_related('order__sku', 'sku', 'location')
    for picklist_obj in picklist_objs:
        if picklist_obj.order_type in ['RTV']:
            rtv_ids.append(picklist_obj.reference_id)
        elif picklist_obj.order_type in ['BA_TO_SA', 'NTE']:
            ba_to_sa_ids.append(picklist_obj.reference_id)
        elif picklist_obj.order_type in BACKFLUSH_JO_TYPES:
            jo_ids.append(picklist_obj.reference_id)
        elif picklist_obj.classification == 'combo':
            combo_order_ids.append(picklist_obj.order_id)
            order_picklist_map[picklist_obj.order_id].add(picklist_obj.picklist_number)
        if picklist_obj.order:
            if picklist_obj.order_id not in order_ids:
                order_ids.add(picklist_obj.order_id)
                order_objs[picklist_obj.order_id] = picklist_obj.order
            order_id_picklist_objs_dict.setdefault(picklist_obj.order_id, []).append(picklist_obj)
        if picklist_obj.reason == 'Short closure (No Physical Stock)':
            short_close_picklist_objs.append(picklist_obj)
        if picklist_obj.pick_type == 'pick_and_pass':
            pick_and_pass_picklists.add(picklist_obj.picklist_number)

    misc_types = ["short_close_order", "no_stock_switch"]
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    short_close_order = misc_dict.get("short_close_order", "false")
    no_stock_switch = misc_dict.get("no_stock_switch","false")
    TempJson.objects.filter(warehouse_id=warehouse.id,model_id__in=picklist_numbers,model_name__in=['cluster_picking', 'pack_while_pick']).delete()

    log_message = (("Request picklist_cancel_%s,username %s, Ip Address %s, and params are %s") % (str(order_reference), str(request.user.username), str(get_user_ip(request)), str(log_dict)))
    log.info(log_message)
    cancelled_orders_dict = {}

    try:
        if key == "process":
            status_message = 'Picklist is saved for later use'
            if order_ids:
                extra_params = {
                    'order_id_picklist_objs_dict': order_id_picklist_objs_dict,
                    'short_close_picklist_objs': short_close_picklist_objs,
                    'combo_order_ids': combo_order_ids,
                    'no_stock_switch': no_stock_switch,
                    'short_close_order': short_close_order,
                    'cancel_open_orders' : cancel_open_orders
                }
                update_order_picklist(order_objs, order_ids, picklist_objs, warehouse, extra_params=extra_params, internal_call=internal_call)
            if order_ids or rtv_ids or jo_ids or ba_to_sa_ids:
                request_user = None if internal_call else request.user
                update_picklists_and_task_master(order_ids, picklist_objs, warehouse, request_user, rtv_ids, jo_ids, ba_to_sa_ids, pick_and_pass_picklists, internal_call)
            
            if internal_call:
                for order_id, picklist_numbers in order_picklist_map.items():
                    if order_id not in order_ids:
                        continue
                    for picklist_number in picklist_numbers:
                        short_close_subsequent_child_picklists_of_combo(picklist_number, order_id, warehouse)
            
            return HttpResponse(status_message)

        elif key == "delete":
            status_message = 'Picklist is deleted'
            try:
                picklist_cancel_ids, picklist_numbers = set(), set()
                for picklist_obj in picklist_objs:
                    if picklist_obj.sku.enable_serial_based:
                        picklist_cancel_ids.add(picklist_obj.id)
                        picklist_numbers.add(picklist_obj.picklist_number)
                if picklist_cancel_ids:
                    cancel_reserved_serial_numbers(warehouse, list(picklist_cancel_ids), list(picklist_numbers))
            except Exception:
                pass
            if order_objs :
                delete_order_picklist(picklist_objs, warehouse, order_objs, cancelled_orders_dict)

            return HttpResponse(status_message)
            

        else:
            log.info("Invalid key")
            return HttpResponse("Something is wrong there, please check")
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Cancel Picklist failed for %s and params are %s and error statement is %s' % (
        str(warehouse.username), str(log_dict), str(e)))
        return HttpResponse("Something is wrong there, please check")

def update_pick_and_pass_strategy(warehouse, picklist_numbers):
    """
    Update the status of pick and pass strategies associated with the given picklist numbers and warehouse.

    Args:
        warehouse (Warehouse): The warehouse object.
        picklist_numbers (list): A list of picklist numbers.

    Returns:
        None
    """
    pick_and_pass_strategy = list(PickAndPassStrategy.objects.filter(reference_number__in=picklist_numbers, reference_type='so_picking', warehouse_id=warehouse.id, status='open').values('id', 'lpn_number'))
    update_pick_and_pass_ids = []
    for pick_and_pass in pick_and_pass_strategy:
        if not pick_and_pass['lpn_number']:
            update_pick_and_pass_ids.append(pick_and_pass['id'])
    if update_pick_and_pass_ids:
        PickAndPassStrategy.objects.filter(id__in=update_pick_and_pass_ids).update(status='cancelled')

def update_pick_and_pass_sub_zone_wise(warehouse, picklist_number):
    """
    Update the status of pick and pass strategies associated with the given picklist ids and warehouse.

    Args:
        warehouse (Warehouse): The warehouse object.
        picklist_ids (list): A list of picklist ids.

    Returns:
        None
    """
    pick_data = list(Picklist.objects.filter(user_id=warehouse.id, picklist_number=picklist_number, pick_type='pick_and_pass').values('location__sub_zone_id', 'status'))
    open_sub_zones = [pick['location__sub_zone_id'] for pick in pick_data]
    for data in pick_data:
        if data['status'] == 'cancelled':
            continue
        if data['location__sub_zone_id'] in open_sub_zones:
            open_sub_zones.remove(data['location__sub_zone_id'])
    if not open_sub_zones:
        return
    
    filters = {
        'reference_number': picklist_number,
        'reference_type': 'so_picking',
        'warehouse_id': warehouse.id,
        'sub_zone_id__in': open_sub_zones
    }
    pick_and_pass_strategy = list(PickAndPassStrategy.objects.filter(**filters).values('id', 'employee_id', 'lpn_number', 'sub_zone_id'))
    updated_pick_and_pass_objs = []
    for pick_and_pass in pick_and_pass_strategy:
        if not pick_and_pass.get('lpn_number'):
            updated_pick_and_pass_objs.append(pick_and_pass['id'])

    if updated_pick_and_pass_objs:
        PickAndPassStrategy.objects.filter(id__in=updated_pick_and_pass_objs).update(status='cancelled')

def update_order_picklist(order_objs, order_ids, picklist_objs, warehouse, extra_params=None, internal_call=False):
    from core_operations.views.integration.integration import webhook_integration_3p
    if extra_params is None:
        extra_params = {}
    short_close_order = extra_params.get('short_close_order', 'false')
    no_stock_switch = extra_params.get('no_stock_switch', 'false')
    combo_order_ids = extra_params.get('combo_order_ids', [])
    short_close_picklist_objs = extra_params.get('short_close_picklist_objs', [])
    order_id_picklist_objs_dict = extra_params.get('order_id_picklist_objs_dict', {})
    cancel_open_orders = extra_params.get('cancel_open_orders', [])
    log.info("Updating order picklist for warehouse %s and order_ids %s and combo order ids %s" % (str(warehouse.username), str(order_ids), str(combo_order_ids)))

    orders_to_update, order_references, order_cancel = [], [], False
    cancelled_order_references, sku_codes = set(), set()
    for order_id, order in order_objs.items():
        cancel_order = order.order_type.lower() in cancel_open_orders or 'all' in cancel_open_orders
        if order_id in combo_order_ids:
            remaining_qty, _ = get_remaining_combo_reserved(picklist_objs, order_id, internal_call)
        else:
            if no_stock_switch == 'true' and short_close_order == 'true':
                remaining_qty_obj = list(set(order_id_picklist_objs_dict.get(order.id, [])) - set(short_close_picklist_objs))
            else:    
                remaining_qty_obj = order_id_picklist_objs_dict.get(order.id, [])

            remaining_qty = get_remaining_qty(remaining_qty_obj, order)
        if remaining_qty and remaining_qty > 0:
            order.status = 1
            if order.original_quantity == order.quantity:
                order.quantity = remaining_qty
            else:
                order.quantity = order.quantity + remaining_qty
            order.quantity = min(order.quantity, order.original_quantity)
            if cancel_order:
                order_cancel = True
                order.cancelled_quantity = order.quantity
                order.quantity = 0
                if order.original_quantity == order.cancelled_quantity:
                    order.status = 3
                else:
                    order.status = 0
                json_data = order.json_data or {}
                json_data['order_cancellation_reason'] = 'Auto Cancellation due to short pick'
                order.json_data = json_data
                cancelled_order_references.add(order.order_reference)
                sku_codes.add(order.sku.sku_code)
            
            order_references.append(order.order_reference)
            orders_to_update.append(order)
        else:
            order_ids.remove(order.id)
    
    if orders_to_update:
        update_fields = ['quantity', 'status', 'cancelled_quantity', 'json_data'] if order_cancel else ['quantity', 'status']
        OrderDetail.objects.bulk_update_with_rounding(orders_to_update, update_fields)
        update_order_header_status(warehouse, order_references)

    if cancelled_order_references:
        integration_filters = {
            'order_references': list(cancelled_order_references),
            'sku_codes' : list(sku_codes)
        }
        webhook_integration_3p(warehouse.id, "cancel_order", filters=integration_filters)

def get_remaining_qty(remaining_qty_obj, order):
    remaining_qty = 0
    for obj in remaining_qty_obj:
        order_json = order.json_data
        if order_json:
            pack_uom = order_json.get('pack_uom_quantity', '')
        if pack_uom:
            remaining_qty += obj.reserved_quantity / pack_uom
        else:
            remaining_qty += obj.reserved_quantity
    return remaining_qty


def update_picklists_and_task_master(order_ids, picklist_objs, warehouse, request_user, rtv_ids=None, jo_ids=None, ba_to_sa_ids=None, pick_and_pass_picklists=None, internal_call=False):
    """
    Update the status of picklist objects and task master objects based on the picked and not picked picklist objects.
    """
    rtv_ids = rtv_ids or []
    jo_ids = jo_ids or []
    ba_to_sa_ids = ba_to_sa_ids or []
    pick_and_pass_picklists = pick_and_pass_picklists or []
    log.info("Updating picklists for warehouse %s and order_ids %s and rtv_ids %s and jo_ids %s and ba_to_sa_ids %s and pick_and_pass_picklists %s" % (str(warehouse.username), str(order_ids), str(rtv_ids), str(jo_ids), str(ba_to_sa_ids), str(pick_and_pass_picklists)))

    picked_objs, not_picked_objs, not_picked_objs_ids, jo_cancelled_qty_dict, ba_to_sa_refs = [], [], [], {}, defaultdict(set)
    cancel_serial_transact_ids, picklist_numbers, sku_codes, zones_data = set(), set(), set(), defaultdict(list)

    # Framing the picklist objects based on the picked and not picked
    for picklist_obj in picklist_objs:
        if picklist_obj.order_id and order_ids and picklist_obj.order_id not in order_ids:
            # Exclude the picklist objects which are not in the order_ids
            continue
        if picklist_obj.picked_quantity > 0:
            picked_objs.append(picklist_obj)
            if picklist_obj.reference_id in ba_to_sa_ids and picklist_obj.order_type in ['BA_TO_SA', 'NTE']:
                ba_to_sa_refs['picked'].add(picklist_obj.reference_number)
        else:
            not_picked_objs.append(picklist_obj)
            if picklist_obj.sku.enable_serial_based:
                cancel_serial_transact_ids.add(picklist_obj.id)
            not_picked_objs_ids.append(picklist_obj.id)
            picklist_numbers.add(picklist_obj.picklist_number)
            if picklist_obj.reference_id in ba_to_sa_ids and picklist_obj.order_type in ['BA_TO_SA', 'NTE']:
                ba_to_sa_refs['not_picked'].add(picklist_obj.reference_number)
        sku_codes.add(picklist_obj.sku.sku_code)
        zones_data[picklist_obj.location.zone_id].append(picklist_obj.sku.sku_code)
    if not_picked_objs_ids:
        if cancel_serial_transact_ids:
            cancel_reserved_serial_numbers(warehouse, list(cancel_serial_transact_ids), list(picklist_numbers))
        update_dict= {
            'status': 1,
            'updation_date': timezone.now()
        }
        if request_user:
            employee_id = EmployeeMaster.objects.filter(user=request_user).first()
            if employee_id:
                update_dict['employee_id'] = employee_id.id
        TaskMaster.objects.filter(task_ref_id__in=not_picked_objs_ids).update(**update_dict)
        # Not picked Picklist updation
        for not_picked_obj in not_picked_objs:
            not_picked_obj.status = 'cancelled'
            not_picked_obj.updation_date = timezone.now()
            if internal_call:
                not_picked_obj.reason = 'Auto Short Pick'
            if not_picked_obj.reference_id in jo_ids and not_picked_obj.order_type in BACKFLUSH_JO_TYPES and not_picked_obj.order_type not in ['BA_TO_SA', 'NTE']:
                jo_cancelled_qty_dict.setdefault(not_picked_obj.reference_id, 0)
                jo_cancelled_qty_dict[not_picked_obj.reference_id] += not_picked_obj.reserved_quantity
        updation_fields = ['status', 'updation_date']
        if internal_call:
            updation_fields.append('reason')
        Picklist.objects.bulk_update_with_rounding(not_picked_objs, updation_fields)
    if picked_objs:
        pick_obj_status = 'picked'
        # Picked Picklist updation
        for picked_obj in picked_objs:
            picked_obj.status = pick_obj_status
            picked_obj.reserved_quantity = 0
            picked_obj.updation_date = timezone.now()
        updation_fields = ['status', 'updation_date']
        Picklist.objects.bulk_update_with_rounding(picked_objs, updation_fields)
    # RTV updation
    if rtv_ids:
        ReturnToVendor.objects.filter(location__zone__user=warehouse.id, id__in=rtv_ids).update(status=3)
        log.info("Updated the RTV for warehouse %s and rtv_ids %s" % (str(warehouse.username), str(rtv_ids)))
    if jo_ids:
        jo_objs = JOMaterial.objects.in_bulk(jo_ids)
        jo_ids, jo_mat_objs = [], []
        for id, qty in jo_cancelled_qty_dict.items():
            jo_mat = jo_objs.get(int(id))
            if not jo_mat: continue

            jo_mat.open_quantity += qty
            jo_mat.status = 1
            jo_ids.append(jo_mat.job_order.id)
            jo_mat_objs.append(jo_mat)
        if jo_mat_objs:
            JOMaterial.objects.bulk_update_with_rounding(jo_mat_objs, ['open_quantity', 'status'])
            job_order = JobOrder.objects.filter(id__in = jo_ids)
            job_order.update(status = 'order-confirmed')
        log.info("Updated the Job Order Material for warehouse %s and jo_ids %s and jo_cancelled_qty_dict %s" % (str(warehouse.username), str(jo_ids), str(jo_cancelled_qty_dict)))
    
    cancel_ba_sa_refs = ba_to_sa_refs['not_picked'] - ba_to_sa_refs['picked']
    if cancel_ba_sa_refs:
        BAtoSADetail.objects.filter(batosa_reference__in=cancel_ba_sa_refs, sku__user=warehouse.id).update(status=0)
        log.info("Updated the BA to SA details for warehouse %s and ba_to_sa_refs %s" % (str(warehouse.username), str(ba_to_sa_refs)))
    
    # for picklist_number in pick_and_pass_picklists:
    #     update_pick_and_pass_sub_zone_wise(warehouse, picklist_number)
    #     log.info("Updated the Pick and Pass Strategy for warehouse %s and picklist %s" % (str(warehouse.username), str(picklist_number)))
    
    filters = {
        "picklist_numbers": list(picklist_numbers),
        "sku_codes": list(sku_codes),
        "zones_data" : zones_data
    }
    if picklist_numbers:
        from core_operations.views.integration.integration import webhook_integration_3p
        webhook_integration_3p(warehouse.id, 'cancel_picklist', filters)

def cancel_reserved_serial_numbers(warehouse, picklist_object_ids, picklist_numbers):
    """
    Cancel reserved serial numbers for a given warehouse and picklist object IDs.

    Args:
        warehouse (Warehouse): The warehouse object.
        picklist_object_ids (list): List of picklist object IDs.

    Returns:
        None
    """
    serial_nos, sku_serial_numbers = [], set()

    sntd_objs = list(SerialNumberTransaction.objects.filter(warehouse=warehouse.id, reference_number__in=picklist_numbers, reference_type='so_picking', transact_id__in=picklist_object_ids, status=2))
    if not sntd_objs:
        return

    for sntd_obj in sntd_objs:
        sntd_obj.status = 3
        sntd_obj.updation_date = timezone.now()
        serial_nos.append(sntd_obj.serial_number)
        unique_key = (sntd_obj.sku_code, sntd_obj.serial_number)
        if unique_key not in sku_serial_numbers:
            sku_serial_numbers.add(unique_key)

    sn_objects = list(SerialNumber.objects.select_related('sku').filter(warehouse=warehouse.id, serial_number__in=serial_nos))
    for sn_obj in sn_objects:
        unique_key = (sn_obj.sku.sku_code, sn_obj.serial_number)
        if unique_key in sku_serial_numbers:
            sn_obj.status = 1

    with transaction.atomic('default'):
        if sntd_objs:
            SerialNumberTransaction.objects.bulk_update(sntd_objs, ['status', 'updation_date'])
        if sn_objects:
            SerialNumber.objects.bulk_update_with_rounding(sn_objects, ['status'])

def delete_order_picklist(picklist_objs, warehouse, order_objs, cancelled_orders_dict):
    for order in order_objs:
        all_seller_orders = SellerOrder.objects.filter(order__user=warehouse.id, order_id__in=order_objs)
        picklist_qty_objs = picklist_objs.filter(order_id=order.id).aggregate(Sum('picked_quantity'), Sum('reserved_quantity'))
        picked_qty = picklist_qty_objs['picked_quantity__sum']
        reserved_qty = picklist_qty_objs['reserved_quantity__sum']
        if picklist_objs.filter(classification='combo', order_id=order.id):
            reserved_qty, picked_qty = get_remaining_combo_reserved(picklist_objs, order.id)
        if not reserved_qty:
            continue

        pick_order = picklist_objs.filter(order_id=order)
        pick_status = 'picked'
        if pick_order.filter(status__icontains='batch'):
            pick_status = 'batch_picked'
        seller_order = all_seller_orders.filter(order_id=order.id, order__user=warehouse.id)
        if seller_order:
            cancelled_orders_dict.setdefault(seller_order[0].id, {})
            cancelled_orders_dict[seller_order[0].id].setdefault('quantity', 0)
            cancelled_orders_dict[seller_order[0].id]['quantity'] = float(
                cancelled_orders_dict[seller_order[0].id]['quantity']) + \
                                                                    float(picked_qty)

        order.cancelled_quantity = order.cancelled_quantity + reserved_qty
        shipping_quantity = 0
        if order.original_quantity == order.cancelled_quantity:
            order.status = 3
        elif order.original_quantity == (order.cancelled_quantity + shipping_quantity):
            order.status = 2
        order.save()
        if picked_qty <= 0 and not seller_order:
            order.save()
            Picklist.objects.filter(status__icontains='open', order_id=order.id).update(status='cancelled')
            continue
        del_seller_order = all_seller_orders.filter(order_id=order.id, order_status='DELIVERY_RESCHEDULED')
        if del_seller_order and pick_status != 'dispatched':
            order.status = 5
            del_seller_order = del_seller_order[0]
            del_seller_order.status = 0
            del_seller_order.order_status = 'PROCESSED'
            del_seller_order.save()
        order.save()
        pick_order.update(reserved_quantity=0, status=pick_status)


def get_remaining_combo_reserved(picklist_objs, order_id, internal_call=False):
    remaining_qty, picked_qty = 0, 0
    if internal_call:
        remaining_qty_objs = Picklist.objects.filter(order_id=order_id, status='open')
    else:
        remaining_qty_objs = picklist_objs.filter(order_id=order_id)
    remaining_qty_objs = list(remaining_qty_objs.values('sku_id', 'order__sku_id').\
                                annotate(res_qty=Sum('reserved_quantity'), picked_qty=Sum('picked_quantity')))
    if remaining_qty_objs:
        bom_mapping = dict(BOMMaster.objects.filter(product_sku_id=remaining_qty_objs[0]['order__sku_id'], bom_type=1, status=1).values_list('material_sku_id', 'material_quantity'))
        if len(remaining_qty_objs) != len(bom_mapping.keys()):
            return 0, 0
        remaining_qtys = []
        for combo in remaining_qty_objs:
            remaining_qty = combo['res_qty']/ bom_mapping.get(combo['sku_id'], 1)
            remaining_qtys.append(remaining_qty)
        if len(set(remaining_qtys)) > 1:
            return 0, 0
    return remaining_qty, picked_qty


def cancel_picklist_callback(warehouse:User, filters={}):
    picklist_numbers = filters.get('picklist_numbers', [])
    search_filters = filters.get('search_parameters', {})

    timezone = get_user_time_zone(warehouse)
    cancel_picklist_objects = list(Picklist.objects.filter(user_id = warehouse.id, picklist_number__in=picklist_numbers, status='cancelled', **search_filters).values(
        'picklist_number', 'picked_quantity', 'cancelled_quantity', 'status', 'reason', 'reference_id', 'order_type', 'stock__location__location', sku_code=F('sku__sku_code'),
        sku_desc = F('sku__sku_desc'), sku_category=F('sku__sku_category'), sku_brand=F('sku__sku_brand'), measurement_type=F('sku__measurement_type'),
        order_reference=F('reference_number'), customer_id=F('order__customer_id'), customer_name=F('order__customer_name'), batch_number=F('stock__batch_detail__batch_no'),
        manufacture_date = F('stock__batch_detail__manufactured_date'), expiry_date=F('stock__batch_detail__expiry_date'), picklist_creation_date=F('creation_date'),
        picklist_updation_date=F('updation_date'), zone=F('stock__location__zone__zone'), batch_reference=F('stock__batch_detail__batch_reference'), aux_data=F('json_data')))

    date_time_fields = ['manufacture_date', 'expiry_date', 'picklist_creation_date', 'picklist_updation_date']
    for picklist_record in cancel_picklist_objects:
        picklist_record['warehouse'] = warehouse.username
        picklist_record['location'] = picklist_record.get('stock__location__location', '')
        del picklist_record['stock__location__location']
        for date_field in date_time_fields:
            if picklist_record.get(date_field):
                picklist_record[date_field] = get_local_date_known_timezone(timezone, picklist_record.get(date_field, ''), True).strftime('%Y-%m-%d %H:%M:%S')

    return cancel_picklist_objects


class CancelPicklistMixin:
    def __init__(self, user, warehouse, request_data, extra_params=None):
        self.user = user
        self.warehouse = warehouse

        self.request_data = request_data
        self.extra_params = extra_params or {}
        self.batch_size = 500

    def cancel_picklist_process(self):
        '''
        Start cancel picklist process
        '''
        try:
            self.cancelled_transaction_quantity = {}
            self.cancelled_picklist = []
            self.sku_codes = []
            self.errors = []

            self.validate_request_data()
            if self.errors:
                return {'errors': self.errors}

            self.fetch_required_configurations()
            self.get_picklist_objects()
            if self.picklist_df.empty:
                return {
                    'errors': self.errors,
                    'cancelled_transaction_details': self.cancelled_transaction_quantity,
                    'picklist_numbers': self.cancelled_picklist,
                    'sku_codes' : self.sku_codes
                }

            self.validating_and_prepare_data()
            if self.errors:
                return {'errors': self.errors}

            self.create_and_update_cancel_picklist_data()
            return {
                'errors': self.errors,
                'cancelled_transaction_details': self.cancelled_transaction_quantity,
                'picklist_numbers': self.cancelled_picklist,
                'sku_codes' : self.sku_codes
            }

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("Cancelled Picklist Process failed with error- %s" % str(e))
            self.errors.append('Picklist Cancellation Failed!')
            return {'errors': self.errors}

    def validate_request_data(self):
        '''
        Validating request payload
        '''
        self.picklist_numbers, self.reference_numbers, self.source_lpns = [], [], []

        if not isinstance(self.request_data, list):
            self.errors.append('Invalid payload')
            return

        for picklist_record in self.request_data:
            picklist_number = picklist_record.get('picklist_number')
            reference_number = picklist_record.get('reference_number')

            if not (picklist_number or reference_number):
                self.errors.append('Picklist number/Reference number is mandatory!')

            if picklist_number not in self.picklist_numbers:
                self.picklist_numbers.append(picklist_number)

            if reference_number not in self.reference_numbers:
                self.reference_numbers.append(reference_number)
            
            if picklist_record.get('source_lpn') and picklist_record.get('source_lpn') not in self.source_lpns:
                self.source_lpns.append(picklist_record.get('source_lpn'))

            items = picklist_record.get('items', []) or []
            for item in items:  
                sku_id = item.get('sku_id', '')
                sku_code = item.get('sku_code', '')
                reference_number = item.get('reference_number', '')
                cancelled_quantity = item.get('cancelled_quantity', 0)

                if reference_number and reference_number not in self.reference_numbers:
                    self.reference_numbers.append(reference_number)

                if not (sku_id or sku_code) or not reference_number or not cancelled_quantity:
                    self.errors.append(f'Picklist Number: {picklist_number} - SKU ID/SKU Code, Reference number and Cancelled quantity is mandatory!')

                if self.errors:
                    break

    def fetch_required_configurations(self):
        """
        Fetches and sets required configuration values for the warehouse.
        """

        misc_types = ['enable_outbound_qc']
        misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

        self.enable_outbound_qc = misc_dict.get('enable_outbound_qc', 'false').lower() == 'true'

    def get_picklist_objects(self):
        '''
        Get picklist details using request data
        '''
        picklist_objects = Picklist.objects.filter(Q(picklist_number__in=self.picklist_numbers) | Q(reference_number__in=self.reference_numbers),
            user_id=self.warehouse.id).order_by('id')

        picklist_values = ['id', 'picklist_number', 'sku_id', 'reference_number', 'reference_id', 'reference_model',
            'stock_id', 'location_id', 'picklist_quantity', 'picked_quantity', 'reserved_quantity', 'cancelled_quantity',
            'order_id', 'remarks', 'json_data',  'creation_date', 'updation_date', 'account_id', 'status'
        ]

        picklist_objs = picklist_objects.only(*picklist_values).select_related('sku', 'order', 'stock__batch_detail')

        picklist_data_list, sos_data_list, order_data_list, stock_data_list = [], [], [], []
        so_order_ids, so_pick_ids, sos_ids = [], [], []

        for picklist_obj in picklist_objs:
            obj_dict = picklist_obj.__dict__.copy()
            obj_dict.pop('_state', None)
            obj_dict['object'] = picklist_obj
            obj_dict['sku_code'] = picklist_obj.sku.sku_code
            obj_dict['sku_desc'] = picklist_obj.sku.sku_desc
            obj_dict['order_mrp'] = picklist_obj.order.mrp
            obj_dict['order_unit_price'] = picklist_obj.order.unit_price
            obj_dict['batch_id'] = picklist_obj.stock.batch_detail_id

            batch_no, batch_reference, manufactured_date, expiry_date = '', '', '', ''
            if picklist_obj.stock.batch_detail:
                batch_no = picklist_obj.stock.batch_detail.batch_no
                batch_reference = picklist_obj.stock.batch_detail.batch_reference
                manufactured_date = picklist_obj.stock.batch_detail.manufactured_date
                expiry_date = picklist_obj.stock.batch_detail.expiry_date
            obj_dict['batch_no'] = batch_no
            obj_dict['batch_reference'] = batch_reference
            obj_dict['manufactured_date'] = manufactured_date
            obj_dict['expiry_date'] = expiry_date
            obj_dict['available_quantity'] = obj_dict.get('picked_quantity', 0) - obj_dict.get('cancelled_quantity', 0)

            picklist_data_list.append(obj_dict)
            if picklist_obj.reference_model == 'OrderDetail':
                so_order_ids.append(picklist_obj.order_id)
                so_pick_ids.append(picklist_obj.id)

        if so_pick_ids:
            sos_objs = SellerOrderSummary.objects.filter(picklist_id__in=so_pick_ids).exclude(order_status_flag='cancelled').select_related('order')
            for sos_obj in sos_objs:
                sos_ids.append(sos_obj.id)
                obj_dict = sos_obj.__dict__.copy()
                obj_dict.pop('_state', None)
                obj_dict['object'] = sos_obj
                sos_data_list.append(obj_dict)

        if sos_ids:
            stock_objs = StockDetail.objects.filter(sku__user=self.warehouse.id, receipt_type='so_picking', receipt_number__in=sos_ids, quantity__gt=0).select_related('sku', 'batch_detail')
            for stock_obj in stock_objs:
                obj_dict = stock_obj.__dict__.copy()
                obj_dict.pop('_state', None)
                obj_dict['object'] = stock_obj
                stock_data_list.append(obj_dict)

        if so_order_ids:
            order_objs = OrderDetail.objects.filter(id__in=so_order_ids).exclude(status='3')
            for order_obj in order_objs:
                obj_dict = order_obj.__dict__.copy()
                obj_dict.pop('_state', None)
                obj_dict['object'] = order_obj
                order_data_list.append(obj_dict)

        self.picklist_df = pd.DataFrame(picklist_data_list)
        self.sos_df = pd.DataFrame(sos_data_list)
        self.order_df = pd.DataFrame(order_data_list)
        self.stock_df = pd.DataFrame(stock_data_list)

    def get_dataframe_conditions(self, item, picklist_number, data_frame_name='self.picklist_df'):
        '''
        Prepare dataframe conditions
        '''
        DATA_FRAME_NAME = data_frame_name

        data_frame_filter = {f"{DATA_FRAME_NAME}['picklist_number']": f"== {picklist_number}"}
        filter_fields = ['reference_id', 'reference_number', 'sku_id', 'batch_id', 'stock_id', 'id', 'sku_code', 'batch_no']
        for field_name in filter_fields:
            field_value = item.get(field_name)
            if field_value:
                if field_name in ['id', 'batch_id', 'sku_id']:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{field_name}']"] = F"== {field_value}"
                else:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{field_name}']"] = F"== '{field_value}'"

        conditions = [f"({column} {condition})" % {'column': column, 'condition': condition} for column, condition in data_frame_filter.items()]
        return conditions

    def validating_and_prepare_data(self):
        '''
        Validating and preparing data for update
        '''

        self.picklist_update_objs, self.sos_update_objs, self.order_update_objs, self.stock_update_objs = [], [], [], []
        self.sos_create_objs, self.stock_create_objs, self.po_loc_create_objs = [], [], []
        self.packing_data_dict = {}
        self.update_order_refs = set()
        self.to_lpn, self.reason = '', ''

        for picklist_record in self.request_data:
            picklist_number = picklist_record.get('picklist_number', '')
            reference_number = picklist_record.get('reference_number', '')
            items = picklist_record.get('items', [])

            self.to_lpn = picklist_record.get('to_lpn', '')
            self.reason = picklist_record.get('reason', '')
            request_details = {
                'from_lpn': picklist_record.get('from_lpn') or '',
                'to_lpn': picklist_record.get('to_lpn') or '',
            }

            if items:
                self.validating_picklist_items(picklist_number, items)
            else:
                self.validating_header_level_data_for_cancel(picklist_number, reference_number, request_details)

    def validating_picklist_items(self, picklist_number, items):
        '''
        Validating picklist data for request items
        '''
        for each_item in items:
            sku_code = each_item.get('sku_code', '')
            cancelled_quantity = each_item.get('cancelled_quantity', 0) or 0

            if cancelled_quantity:
                df_conditions = self.get_dataframe_conditions(each_item, picklist_number, data_frame_name='self.picklist_df')
                df_result_data = self.picklist_df[eval('&'.join(df_conditions))]

                if df_result_data['available_quantity'].sum() < cancelled_quantity:
                    self.errors.append(f'Cancelled quantity more the picked quantity for picklist_number-{picklist_number}, sku_code-{sku_code}')
                    continue

                cancelled_quantity = self.validate_picklist_details_for_item(each_item, df_result_data, cancelled_quantity)

                if cancelled_quantity > 0:
                    self.errors.append(f'Cancelled quantity more the picked quantity for picklist_number-{picklist_number}, sku_code-{sku_code}')

    def validate_picklist_details_for_item(self, request_details, df_result_data, cancelled_quantity):
        '''
        1. Get picklist details for each items and update status and quantity
        2. preparing the data for updating the picklist, sos, stock and order details
        3. Update the cancelled quantity in the picklist and sos records
        4. Prepare the packing details if required
        '''

        for index, picklist_record in df_result_data.iterrows():
            json_data = picklist_record.get('json_data', {}) or {}
            status = picklist_record['status']
            json_data['cancelled_by'] = self.user.username

            if cancelled_quantity:
                assigned_quantity = min(picklist_record['available_quantity'], cancelled_quantity)
            elif status in ['picked', 'cancelled', 'short_closed']:
                assigned_quantity = picklist_record['available_quantity']
            else:
                assigned_quantity = picklist_record['reserved_quantity']

            if picklist_record['reference_model'] == 'OrderDetail':
                if picklist_record['available_quantity']:
                    continue_flag = self.update_sos_and_stock_records(picklist_record, assigned_quantity, request_details)
                    if continue_flag:
                        continue
                # order quantity updations
                order_obj = self.order_df[self.order_df['id'] == picklist_record['order_id']]
                if not order_obj.empty and order_obj['object'].iloc[0] not in self.order_update_objs:
                    order_obj = order_obj['object'].iloc[0]
                    if order_obj.suspended_quantity and order_obj.suspended_quantity == cancelled_quantity:
                        order_obj.suspended_quantity = 0
                        order_obj.cancelled_quantity += cancelled_quantity
                    else:
                        order_obj.quantity += cancelled_quantity
                        order_obj.status = 1
                    if order_obj.original_quantity == order_obj.cancelled_quantity:
                        order_obj.status = 3
                    self.order_update_objs.append(order_obj)
                    self.update_order_refs.add(order_obj.order_reference)

            self.picklist_df.loc[self.picklist_df['id'] == picklist_record['id'], 'available_quantity'] -= assigned_quantity
            picklist_record['object'].cancelled_quantity += assigned_quantity
            if picklist_record['object'].cancelled_quantity == picklist_record['object'].picked_quantity:
                picklist_record['object'].status = 'cancelled'
            picklist_record['object'].json_data = json_data

            if status in ['picked', 'cancelled', 'short_closed']:
                self.prepare_po_location_records(picklist_record, assigned_quantity)
            else:
                self.prepare_output_details(picklist_record, picklist_record['reserved_quantity'])
                picklist_record['object'].reserved_quantity = 0

            self.sku_codes.append(picklist_record['sku_code'])
            self.picklist_update_objs.append(picklist_record['object'])

            if cancelled_quantity:
                cancelled_quantity -= assigned_quantity
                if cancelled_quantity == 0:
                    break

        return cancelled_quantity


    def update_sos_and_stock_records(self, picklist_record, assigned_quantity, request_details):
        """
        Updates Seller Order Summary (SOS) and stock records when cancelling or modifying a picklist.

        This method performs the following:
        - Validates if there are sufficient SOS and stock records for the given picklist and assigned quantity.
        - Iterates through relevant SOS records, updating their status and quantities as required.
        - Updates or creates stock records based on the cancellation or modification.
        - Handles LPN (License Plate Number) specific logic if provided in the request details.
        - Updates packing details if necessary.
        - Appends updated or newly created objects to their respective lists for further processing.

        Args:
            picklist_record (dict): The picklist record containing details such as 'id' and 'picklist_number'.
            assigned_quantity (int): The quantity to be cancelled or updated.
            request_details (dict): Additional request details, may include 'from_lpn' and other relevant keys.

        Returns:
            bool: A flag indicating whether to continue further processing (True) or not (False).
        """

        continue_flag = True
        sos_records = self.sos_df[self.sos_df['picklist_id'] == picklist_record['id']]
        if sos_records.empty:
            return continue_flag
        sos_quantity = sos_records['quantity'].sum()
        if sos_quantity < assigned_quantity:
            return continue_flag
        sos_ids = sos_records['id'].tolist()
        # Filter stock records
        stock_df_filter = (self.stock_df['receipt_number'].isin(sos_ids))
        if request_details.get('from_lpn'):
            stock_df_filter &= (self.stock_df['lpn_number'] == request_details['from_lpn'])
        stock_records = self.stock_df[stock_df_filter]
        if stock_records.empty or stock_records['quantity'].sum() < assigned_quantity:
            return continue_flag
        sos_remove_qty = assigned_quantity
        for sos_obj in sos_records['object'].tolist():
            continue_flag = False
            if not sos_remove_qty:
                break
            elif sos_remove_qty == sos_obj.quantity:
                # If the entire SOS quantity is to be cancelled
                sos_obj.order_status_flag = 'cancelled'
                self.sos_update_objs.append(sos_obj)
                stock_obj = self.stock_df[self.stock_df['receipt_number'] == sos_obj.id]
                if not stock_obj.empty:
                    stock_obj = stock_obj['object'].iloc[0]
                    stock_obj_dict = stock_obj.__dict__.copy()
                    stock_obj_dict.pop('id', None)
                    stock_obj_dict.pop('_state', None)
                    stock_obj.quantity -= sos_remove_qty
                    self.stock_update_objs.append(stock_obj)
                    sos_qty = sos_remove_qty
                    sos_remove_qty = 0
                # Stock ocject creation with To LPN if provided
                if self.to_lpn:
                    stock_obj_dict['lpn_number'] = self.to_lpn
                    stock_obj_dict['json_data'].pop('picked_lpn', None)
                    self.stock_create_objs.append(StockDetail(**stock_obj_dict))
                    # Increment quantity in To LPN
                    self.update_packing_details(picklist_record['picklist_number'], self.to_lpn, stock_obj, sos_obj, sos_qty)
            else:
                sos_qty = min(sos_remove_qty, sos_obj.quantity)
                sos_obj.quantity -= sos_qty
                sos_remove_qty -= sos_qty
                sos_obj_dict = sos_obj.__dict__.copy()
                sos_obj_dict.pop('id', None)
                sos_obj_dict.pop('_state', None)
                sos_obj_dict['quantity'] = sos_qty
                sos_obj_dict['order_status_flag'] = 'cancelled'
                json_data = {
                    'cancelled_by': self.user.username,
                    'cancelled_reason': self.reason,
                    'old_sos_id': sos_obj.id
                }
                sos_obj_dict['json_data'].update(json_data)
                self.sos_create_objs.append(SellerOrderSummary(**sos_obj_dict))
                self.sos_update_objs.append(sos_obj)
                stock_obj = self.stock_df[self.stock_df['receipt_number'] == sos_obj.id]
                if not stock_obj.empty:
                    stock_obj = stock_obj['object'].iloc[0]
                    stock_obj.quantity -= sos_qty
                    self.stock_update_objs.append(stock_obj)
            if request_details.get('from_lpn'):
                # Decrement quantity in From LPN
                self.update_packing_details(picklist_record['picklist_number'], request_details['from_lpn'], stock_obj, sos_obj, stock_obj.quantity)

        return continue_flag


    def update_packing_details(self, picklist_number, lpn_number, stock_obj, sos_obj, quantity):
        """
        Updates the packing details for a given picklist and LPN (License Plate Number).

        This method updates the internal packing_data_dict with packing information for a specific picklist and LPN.
        It adds item details, including batch information and quantities, to the packing dictionary. If the picklist
        or LPN does not exist in the dictionary, it initializes the necessary structures.

        Args:
            picklist_number (str or int): The unique identifier for the picklist.
            lpn_number (str): The license plate number associated with the packed items.
            stock_obj (object): The stock object containing SKU and batch details.
            sos_obj (object): The sales order stock object containing order and transaction details.
            quantity (int or float): The quantity of items packed.
        """

        if not (stock_obj and sos_obj):
            return

        if picklist_number not in self.packing_data_dict:
            self.packing_data_dict[picklist_number] = {
                'user': self.user.username,
                'warehouse': self.warehouse.username,
                'transaction_number': str(picklist_number),
                'transaction_type': 'so_packing',
                'status': 'closed',
                'packing_dict': {},
                'json_data': {'packed_lpn': True}
            }

        if lpn_number not in self.packing_data_dict[picklist_number]['packing_dict']:
            self.packing_data_dict[picklist_number]['packing_dict'].update({
                lpn_number: {
                    'lpn_number': lpn_number,
                    'items': [],
                }
            })

        batch_no, batch_reference = '', ''
        manufactured_date, expiry_date = None, None
        if stock_obj.batch_detail:
            batch_no = stock_obj.batch_detail.batch_no
            batch_reference = stock_obj.batch_detail.batch_reference
            manufactured_date = stock_obj.batch_detail.manufactured_date
            expiry_date = stock_obj.batch_detail.expiry_date

        self.packing_data_dict[picklist_number]['packing_dict'][lpn_number]['items'].append({
            'transaction_id': sos_obj.id,
            'sku_code': stock_obj.sku.sku_code,
            'sku_desc': stock_obj.sku.sku_desc,
            'mrp': sos_obj.order.mrp,
            'unit_price': sos_obj.order.unit_price,
            'packed_quantity': quantity,
            'batch_details': {
                'batch_number': batch_no,
                'batch_reference': batch_reference,
                'manufactured_date' : get_local_date_known_timezone(self.timezone, manufactured_date, send_date=True).strftime('%Y-%m-%d') if manufactured_date else None,
                'expiry_date' : get_local_date_known_timezone(self.timezone, expiry_date, send_date=True).strftime('%Y-%m-%d') if expiry_date else None,
            },
            'json_data': {
                'pack_uom_quantity': 1,
                'packed_lpn': True,
            }
        })


    def validating_header_level_data_for_cancel(self, picklist_number, reference_number, request_details):
        '''
        Validating header level data for cancel picklist
        '''
        if picklist_number:
            df_result_data = self.picklist_df.loc[self.picklist_df['picklist_number'] == picklist_number]
        elif reference_number:
            df_result_data = self.picklist_df.loc[self.picklist_df['reference_number'] == reference_number]

        self.validate_picklist_details_for_item(request_details, df_result_data, 0)

    def prepare_output_details(self, df_record, cancelled_quantity):
        '''
        Preparing output json data
        '''
        cancelled_unique_qty = (df_record['id'], df_record['reference_id'])
        if not self.cancelled_transaction_quantity.get(cancelled_unique_qty):
            self.cancelled_transaction_quantity[cancelled_unique_qty] = cancelled_quantity
        else:
            self.cancelled_transaction_quantity[cancelled_unique_qty] += cancelled_quantity

        if df_record['picklist_number'] not in self.cancelled_picklist:
            self.cancelled_picklist.append(df_record['picklist_number'])

    def prepare_po_location_records(self, picklist_record, cancelled_quantity):
        '''
        Prepare PO Location data
        '''
        self.prepare_output_details(picklist_record, cancelled_quantity)

        po_location_dict = {
            'picklist_id' : picklist_record['id'],
            'quantity': cancelled_quantity,
            'original_quantity':cancelled_quantity,
            'status': 1,
            'location_id': picklist_record['location_id'],
            'sku_id':picklist_record['sku_id'],
            'putaway_type':'cancelled_picklist',
            'account_id': self.warehouse.userprofile.id,
            'batch_detail_id': picklist_record['object'].stock.batch_detail_id,
            'json_data': {
                'cancelled_type': self.extra_params.get('cancelled_type'),
                'return_reason': self.extra_params.get('return_reason', '') or '',
            },
            'reference_number': picklist_record['picklist_number']
        }
        if self.to_lpn:
            po_location_dict['json_data']['lpn_number'] = self.to_lpn
        self.po_loc_create_objs.append(POLocation(**po_location_dict))

    def create_and_update_cancel_picklist_data(self):
        '''
        1. Update picklist status and cancelled quantity
        2. Create pull to locate records in POLocation
        3. Update SOS, Stock and Order details
        4. Create packing details if required
        5. Update audit entries if outbound QC is enabled
        '''

        with transaction.atomic('default'):

            if self.picklist_update_objs:
                Picklist.objects.bulk_update_with_rounding(self.picklist_update_objs, ['reserved_quantity', 'cancelled_quantity', 'status', 'json_data'])

            if self.po_loc_create_objs:
                POLocation.objects.bulk_create_with_rounding(self.po_loc_create_objs, batch_size=self.batch_size)

            for po_loc in self.po_loc_create_objs:
                create_putaway_task(po_loc)

            if self.sos_update_objs:
                SellerOrderSummary.objects.bulk_update_with_rounding(self.sos_update_objs, ['quantity', 'order_status_flag', 'json_data'])
            if self.stock_update_objs:
                StockDetail.objects.bulk_update_with_rounding(self.stock_update_objs, ['quantity'])
            if self.order_update_objs:
                OrderDetail.objects.bulk_update_with_rounding(self.order_update_objs, ['quantity', 'cancelled_quantity', 'suspended_quantity', 'status'])
            if self.update_order_refs:
                update_order_header_status(self.warehouse, list(self.update_order_refs))

            if self.sos_create_objs:
                SellerOrderSummary.objects.bulk_create_with_rounding(self.sos_create_objs, batch_size=self.batch_size)
                for sos_obj in self.sos_create_objs:
                    old_sos_id = sos_obj.json_data.get('old_sos_id')
                    if not old_sos_id:
                        continue
                    stock_obj = self.stock_df[self.stock_df['receipt_number'] == old_sos_id]
                    if stock_obj.empty:
                        continue
                    stock_obj = stock_obj['object'].iloc[0]
                    stock_obj_dict = stock_obj.__dict__.copy()
                    stock_obj_dict.pop('id', None)
                    stock_obj_dict.pop('_state', None)
                    stock_obj_dict['receipt_number'] = sos_obj.id
                    stock_obj_dict['quantity'] = sos_obj.quantity
                    if self.to_lpn:
                        stock_obj_dict['json_data'].pop('picked_lpn', None)
                        stock_obj_dict['lpn_number'] = self.to_lpn
                        self.update_packing_details(stock_obj.transact_number, self.to_lpn, stock_obj, sos_obj, sos_obj.quantity)
                    self.stock_create_objs.append(StockDetail(**stock_obj_dict))

            if self.stock_create_objs:
                StockDetail.objects.bulk_create_with_rounding(self.stock_create_objs, batch_size=self.batch_size)

            if self.enable_outbound_qc:
                audit_objs = AuditEntry.objects.filter(warehouse=self.warehouse, reference_type='LPN', reference_number__in=self.picklist_numbers, audit_reference__in=self.source_lpns, status=2)
                for audit_obj in audit_objs:
                    audit_obj.status = 3
                    audit_obj.resolved_by = self.user
                    audit_obj.updation_date = timezone.now()
                if audit_objs:
                    AuditEntry.objects.bulk_update(audit_objs, ['status', 'resolved_by', 'updation_date'])

            request_dict = self.extra_params.get('request_dict', {})
            packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
            for picklist_number, packing_data in self.packing_data_dict.items():
                packing_dict = packing_data.pop('packing_dict', {})
                packing_data['packing_details'] = list(packing_dict.values())
                packing_details, packing_service_errors = packing_service_instance.create_packing(packing_data)
                log.info(f"Create packing response packing details {packing_details} and errors {packing_service_errors}")

@get_warehouse
def cancel_picklist_task(request, warehouse: User):
    """
    Cancels the picklist task based on the provided request data.
    Args:
        request (HttpRequest): The HTTP request object containing the payload with picklist details.
        warehouse (User): The user object representing the warehouse.
    Returns:
        JsonResponse: A JSON response indicating the success or failure of the operation.
    """

    try:
        request_data = json.loads(request.body)
    except:
        return JsonResponse({"error": "Invalid payload"}, status=400)

    picklist_number = request_data.get('picklist_number', '')
    picklist_ids = request_data.get('picklist_ids', [])
    send_next_task = request_data.get('send_next_task', False)

    mandatory_fields = ['picklist_number', 'picklist_ids']
    for field in mandatory_fields:
        if not request_data.get(field):
            return JsonResponse({"error": f'{field} is mandatory'}, status=400)

    # Fetch the picklist objects
    picklist_objs = Picklist.objects.filter(id__in=picklist_ids, picklist_number=picklist_number, user_id=warehouse.id, status='open')
    if not picklist_objs:
        return JsonResponse({"error": "Invalid picklist Ids"}, status=400)
    
    pick_type = ''
    pick_and_pass_objs, sub_zones, next_task_data = [], [], []
    order_id_qty_dict = {}

    # Update Picklist status
    for picklist_obj in picklist_objs:
        pick_type = picklist_obj.pick_type
        if picklist_obj.order_id:
            order_id_qty_dict.setdefault(picklist_obj.order_id, 0)
            order_id_qty_dict[picklist_obj.order_id] += picklist_obj.reserved_quantity
        json_data = picklist_obj.json_data or {}
        if json_data.get('sub_zone'):
            sub_zones.append(json_data['sub_zone'])
        picklist_obj.status = 'cancelled'
        picklist_obj.reason = 'Cancel Task'
        picklist_obj.updation_date = timezone.now()

    # Update TaskMaster status
    task_objs = TaskMaster.objects.filter(task_ref_id__in=picklist_ids, task_ref_type='Picklist', status=False)
    for task_obj in task_objs:
        task_obj.status = True
        task_obj.employee_id = None
        task_obj.updation_date = timezone.now()

    # Update OrderDetail status
    order_objs = OrderDetail.objects.filter(id__in=order_id_qty_dict, status__in=[0, 1])
    for order_obj in order_objs:
        order_obj.status = 1
        order_obj.quantity += order_id_qty_dict[order_obj.id]
        order_obj.quantity = min(order_obj.quantity, order_obj.original_quantity)

    if pick_type == 'pick_and_pass':
        # Update PickAndPassStrategy status
        pick_and_pass_objs = PickAndPassStrategy.objects.filter(reference_number=picklist_number, reference_type='so_picking', warehouse_id=warehouse.id, status='open', sub_zone__zone__in=sub_zones)
        for pick_and_pass_obj in pick_and_pass_objs:
            if not pick_and_pass_obj.lpn_number:
                pick_and_pass_obj.status = 'cancelled'

    with transaction.atomic():
        if picklist_objs:
            Picklist.objects.bulk_update_with_rounding(picklist_objs, ['status', 'reason', 'updation_date'])
        if order_objs:
            OrderDetail.objects.bulk_update_with_rounding(order_objs, ['status', 'quantity'])
        if task_objs:
            TaskMaster.objects.bulk_update(task_objs, ['status', 'employee_id', 'updation_date'])
        if pick_and_pass_objs:
            PickAndPassStrategy.objects.bulk_update(pick_and_pass_objs, ['status'])

    if send_next_task:
        # Fetch selected zones
        sub_zones = list(UserZoneMapping.objects.filter(user=request.user, warehouse=warehouse).values_list('zone__zone', flat=True))
        # Prepare request object for next task
        task_request = HttpRequest()
        task_request.headers = request.headers
        task_request.META = request.META
        task_request.user = request.user
        task_request.warehouse = warehouse
        task_request.timezone = request.timezone
        task_request.GET = {'picklist_number': picklist_number, 'group_type': ','.join(sub_zones)}

        # Get next task data
        next_task_data = get_task(task_request, internal_call=True)

    return JsonResponse({"message": "Picklist task cancelled Successfully", "next_task_data": next_task_data}, safe=False)

@get_warehouse
def end_wave(request, warehouse):
    """
    Cancel all Open Picklists for the given warehouse and wave references.
    """
    try:
        request_data = json.loads(request.body)
    except Exception as e:
        log.debug(traceback.format_exc())
        return JsonResponse({"error": "Invalid payload"}, status=400)
    wave_references = request_data.get('wave_references', [])
    if not wave_references:
        return JsonResponse({"error": "Wave reference is mandatory"}, status=400)
    for wave_reference in wave_references:
        cancel_open_wave_picklists.apply_async(args=[request.user.id, warehouse.id, wave_reference])
    return JsonResponse({"message": "Picklist cancellation process started"}, status=200)

@app.task(soft_time_limit=3500, time_limit=3600)
def cancel_open_wave_picklists(request_user_id, warehouse_id, wave_reference):
    """
    Cancel Open Picklists for the wave reference.
    """
    picklist_numbers = list(Picklist.objects.filter(user_id=warehouse_id, json_data__wave_reference=wave_reference, status='open').values_list('picklist_number', flat=True).distinct())
    if not picklist_numbers:
        return
    user = User.objects.get(id=request_user_id)
    warehouse = User.objects.get(id=warehouse_id)
    query_dict = QueryDict('', mutable=True)
    request = RequestFactory()
    request.warehouse = warehouse
    request.user = user
    query_dict.update({'picklist_numbers': ','.join(map(str,picklist_numbers)), 'key':'process'})
    request.GET = query_dict
    log.info("Cancelling picklist for wave reference %s, picklist_numbers %s" % (wave_reference, picklist_numbers))
    try:
        error = picklist_delete(request, picklist_numbers=picklist_numbers)
        log.info("picklist cancellation completed for wave reference %s, status: %s" % (wave_reference, error))
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info("Error in picklist cancellation for wave reference %s, error: %s" % (wave_reference, str(e)))


@get_warehouse
def cancel_picklist_lines(request, warehouse):

    try:
        request_data = json.loads(request.body)
    except Exception as e:
        return JsonResponse({'errors': ['Invalid payload']}, status=400)

    if not request_data:
        return JsonResponse({'errors': ['Request data is empty']}, status=400)
    if not isinstance(request_data, list):
        request_data = [request_data]

    extra_params = {
        'cancelled_type': request_data[0].get('cancelled_type', 'Picklist Line Cancellation'),
        'request_dict': {
            'request_headers': {
                'Warehouse': request.headers.get('Warehouse', ''),
                'Authorization': request.headers.get('Authorization', '')
            },
            'request_meta': {
                'HTTP_HOST': request.META.get('HTTP_HOST', '')
            }
        }
    }

    cancel_picklist_obj = CancelPicklistMixin(request.user, warehouse, request_data, extra_params)
    cancel_picklist_obj.timezone = request.timezone
    response = cancel_picklist_obj.cancel_picklist_process()
    if response.get('errors'):
        return JsonResponse({'errors': response['errors']}, status=400)
    return JsonResponse({'message': 'Picklist Lines cancelled Successfully'}, status=200)

