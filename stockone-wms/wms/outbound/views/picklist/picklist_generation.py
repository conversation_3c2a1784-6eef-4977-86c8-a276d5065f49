# package imports
import ast
import pandas as pd
import traceback

from datetime import datetime, timezone, timedelta
from copy import deepcopy
from decimal import Decimal
from collections import defaultdict

# django imports
from django.db.models import Value, When, Case, IntegerField, F, Sum, Q, CharField
from django.core.cache import cache
from django.db import transaction

# wms imports
from wms_base.wms_utils import init_logger

# lms imports
from lms.models import TaskMaster
from lms.views import lmstasks1, EmployeeMaster

# core operations imports
from core.models import SKUMaster
from core_operations.views.common.main import (
    get_exclude_zones, get_decimal_value, truncate_float, get_misc_options_list
)
from core_operations.views.configurations.cluster_picking_config import ClusterMixin
from core_operations.views.integration.integration import webhook_integration_3p

# inventory imports
from inventory.models import (
    StockDetail, CycleCount
)

# production models
from production.models import BOMMaster

# outbound imports
from outbound.models import (
    Picklist, OrderTypeZoneMapping, CustomerSKU, CustomerMaster, OrderDetail, StockAllocation
)
from outbound.views.orders.helpers import get_order_type_classification
from outbound.views.orders.utils import validate_transaction_lock
from outbound.views.picklist.drop_ship_picklist import drop_ship_generate_picklist
from wms_base.notifications import warehouse_push_notification

from .validation import get_picklist_number
from .constants import PICKLIST_GENERATION_EXTRA_FIELDS, BACKFLUSH_JO_TYPES, SHORT_PICK

log = init_logger('logs/common_picklist_generation.log')
cluster_log = init_logger('logs/cluster_picklist_generation.log')

class AllocatedOrderPicklistGeneration:
    def __init__(self, user, warehouse, reference_numbers, pick_type, reference_model, extra_params=None):
        self.user = user
        self.warehouse = warehouse

        self.reference_numbers = reference_numbers
        self.pick_type = pick_type
        self.extra_params = extra_params or {}
        self.reference_model = reference_model

    def get_allocation_data(self):
        '''
        Get allocation info using reference number
        '''
        filters = {
            'warehouse_id': self.warehouse.id,
            'reference_number__in': self.reference_numbers,
            'status': 1,
            'reference_model' : self.reference_model
        }
        self.allocated_objects = defaultdict(list)
        allocated_objects = list(StockAllocation.objects.filter(**filters))
        for allocated_object in allocated_objects:
            self.allocated_objects[allocated_object.reference_number].append(allocated_object)

    def generate_picklist_for_allocation(self, picklist_data):
        '''
        Validating and Creating the Picklist for allocation
        '''
        errors, allocated_objects = self.validate_and_prepare_allocation_filter_data(
            picklist_data)
        if errors:
            return [], [], [], errors
        reference_ids, reference_numbers, picklist_objects_list = self.create_picklist_for_allocation(allocated_objects)
        return reference_ids, reference_numbers, picklist_objects_list, []

    def validate_and_prepare_allocation_filter_data(self, picklist_data):
        '''
        Validating the allocated reference/order or not
        '''
        errors, allocated_objects = [], []

        for picklist_record in picklist_data:
            reference_number = picklist_record.get('reference_number', '')

            if reference_number not in self.allocated_objects:
                errors.append('Reference Number - %s is not allocated' % reference_number)
                continue
                
            allocated_objects.extend(self.allocated_objects[reference_number])

        return errors, allocated_objects

    def create_picklist_for_allocation(self, allocated_objects):
        '''
        Create Picklist for allocated references/order
        '''
        reference_ids, reference_numbers, allocate_ids = set(), set(), set()
        picklist_data_dict = {}
        for allocated_object in allocated_objects:
            unique_key = (allocated_object.reference_id, allocated_object.stock_id)
            json_data = allocated_object.json_data or {}
            json_data['generated_by'] = self.user.username
            if unique_key not in picklist_data_dict:
                picklist_data = {
                    'sku_id': json_data.get('sku_id', None),
                    'user_id': self.warehouse.id,
                    'reference_id': allocated_object.reference_id,
                    'reference_number': allocated_object.reference_number,
                    'picklist_quantity': allocated_object.quantity,
                    'reserved_quantity': allocated_object.quantity,
                    'picked_quantity': 0,
                    'order_type': json_data.get('order_type', ''),
                    'pick_type': self.pick_type,
                    'status': 'open',
                    'json_data' : json_data,
                    'account_id': self.warehouse.userprofile.id,
                    'reference_model': allocated_object.reference_model,
                    'classification' : 'combo' if json_data.get('combo_sku_qty') else '',
                    'stock_id' : allocated_object.stock_id,
                    'location_id' : json_data.get('location_id'),
                }
                if self.extra_params.get('is_order'):
                    picklist_data['order_id'] = allocated_object.reference_id
                
                reference_ids.add(allocated_object.reference_id)
                reference_numbers.add(allocated_object.reference_number)
                picklist_data_dict[unique_key] = picklist_data
            else:
                picklist_data_dict[unique_key]['picklist_quantity'] += allocated_object.quantity
                picklist_data_dict[unique_key]['reserved_quantity'] += allocated_object.quantity
            allocate_ids.add(allocated_object.id)
        
        picklist_objects_list = []
        for picklist_data in picklist_data_dict.values():
            picklist_objects_list.append(Picklist(**picklist_data))
        
        if allocate_ids:
            StockAllocation.objects.filter(id__in=allocate_ids, status=1).update(status=2)
        
        return reference_ids, reference_numbers, picklist_objects_list


class PicklistMixin:
    def __init__(self, user, warehouse, request_data, extra_params=None, is_order=False):
        self.user = user
        self.warehouse = warehouse

        try:
            self.employee = EmployeeMaster.objects.get(user_id=self.user.id)
        except Exception:
            self.employee = None
            log.info("Employee details not forund for user - %s" % str(self.user.username))

        self.employee = None if extra_params.get('auto_generated') else self.employee
        self.request_data = request_data
        self.extra_params = extra_params or {}
        self.skip_allocation = self.extra_params.get('skip_allocation')
        self.is_order = is_order
        self.batch_size = 500
        self.current_date = pd.Timestamp(datetime.now(timezone.utc))

        self.get_misc_values()
        self.get_decimal_limit()

    def get_misc_values(self):
        '''
        Get all picklist misc values
        '''
        self.switch_values = self.extra_params.get('switch_values', {}) or {}

        self.mrp_based_picking = []
        if self.switch_values.get('mrp_based_picking'):
            self.mrp_based_picking = self.switch_values.get('mrp_based_picking') or []

        self.mrp_tolerance = 0
        if self.mrp_based_picking and self.switch_values.get('mrp_tolerance') not in ['', None, 'false', False]:
            try:
                self.mrp_tolerance = float(self.switch_values.get('mrp_tolerance'))
            except (ValueError, TypeError):
                self.mrp_tolerance = 0

        self.is_short_close_on = False
        if self.switch_values.get('short_close_order') not in ['', None, 'false', False]:
            self.is_short_close_on = True

        self.picklist_for_out_of_stock = False
        if self.switch_values.get('no_stock_switch') not in ['', None, 'false', False]:
            self.picklist_for_out_of_stock = True

        self.zone_mandatory_for_picklist_generation = False
        if self.switch_values.get('zone_mandatory_for_picklist_generation') not in ['', None, 'false', False]:
            self.zone_mandatory_for_picklist_generation = True

        self.zone_mandatory_for_jo_picklist_generation = False
        if self.switch_values.get('zone_mandatory_for_jo_picklist_generation') not in ['', None, 'false', False]:
            self.zone_mandatory_for_jo_picklist_generation = True

        self.enable_sales_uom = False
        if self.switch_values.get('enable_sales_uom') not in ['', None, 'false', False]:
            self.enable_sales_uom = True

        self.create_cycle_count_for_short_close = False
        if self.switch_values.get('create_cycle_count_for_short_close', 'false') not in ['false', False, '', None]:
            self.create_cycle_count_for_short_close = True
        
        self.create_cycle_count_for_short_close_joborder = False
        if self.switch_values.get('create_cycle_count_for_short_close_joborder', 'false') not in ['false', False, '', None]:
            self.create_cycle_count_for_short_close_joborder = True
        
        self.create_cycle_count_for_short_close_batosa = False
        if self.switch_values.get('create_cycle_count_for_short_close_batosa', 'false') not in ['false', False, '', None]:
            self.create_cycle_count_for_short_close_batosa = True
        
        self.group_replenishment_task_count = 0
        if self.switch_values.get('group_replenishment_task_count', 'false') not in ['false', False, '', None]:
            self.group_replenishment_task_count = int(self.switch_values.get('group_replenishment_task_count', 0))

        self.stock_allocate = False
        if self.switch_values.get('stock_allocate', 'false') not in ['false', False, '', None] and not self.skip_allocation:
            self.stock_allocate = True
        
        misc_options_list = ['FIFO','FEFO','LEFO','LIFO','Get Sequence','customer_shelf_life']
        self.stock_selection_strategy_ordertypes = get_misc_options_list(misc_options_list, self.warehouse)
        self.customer_shelf_life_ordertypes = self.stock_selection_strategy_ordertypes.pop('customer_shelf_life', [])
        
        self.manual_stock_selection_for_picklist = False
        if self.switch_values.get('manual_stock_selection_for_picklist', 'false') not in ['false', False, '', None]:
            self.manual_stock_selection_for_picklist = True
        
        if self.stock_selection_strategy_ordertypes:
            for key, value in self.stock_selection_strategy_ordertypes.items():
                self.stock_selection_strategy_ordertypes[key] = ast.literal_eval(value[0])

    def get_decimal_limit(self):
        '''
        Get decimal limit from config
        '''
        self.decimal_limit = None
        config_decimal_limit = get_decimal_value(self.warehouse.id)
        if config_decimal_limit and isinstance(config_decimal_limit, str) and config_decimal_limit.isdigit():
            self.decimal_limit = int(config_decimal_limit)

    def generate_picklist_process(self):
        '''
        Start Generate picklist process
        '''
        self.timezone = self.warehouse.userprofile.timezone or 'Asia/Calcutta'
        self.errors, self.redis_cache_ids, self.reference_numbers = [], [], []
        try:
            self.request_data_validation()
            
            if self.errors:
                return {'errors': self.errors}

            #handle duplicates
            self.set_cache_ids(self.reference_numbers)
            if self.errors:
                return {'errors': self.errors}
            log.info(f"Picklist Generation started for user - {self.user.username} and warehouse - {self.warehouse.warehouse_name} for reference numbers - {self.reference_numbers}")
            #picklist generation
            self.generate_picklist()

            response_data = {
                'errors': self.errors,
                'generated_picklist_numbers': self.generated_picklist_numbers,
                'reference_picklist_details': self.reference_id_picklist_details,
                'reference_ids': self.reference_ids,
                'insufficient_stock_order_ids': self.insufficient_stock_order_ids,
                'processed_order_ids' : self.processed_order_ids,
            }
            self.delete_cache_ids(self.reference_numbers)
            # Example push notification for warehouse
            # warehouse_push_notification(self.warehouse, 'picklist', {"data": "picklist generated"})
            return response_data

        except Exception as e:
            log.debug(traceback.format_exc())
            log.info("Generate Picklist Process failed with error- %s" % str(e))
            self.errors.append('Picklist Generation Failed!')
            self.delete_cache_ids(self.reference_numbers)
            return {'errors': self.errors}

    def request_data_validation(self):
        '''
        Validating request payload
        '''
        self.validate_header_level_data()
        self.validate_and_preparing_picklist_line_level_data()

    def validate_header_level_data(self):
        '''
        Validating picklist type and data as mandatory fields
        '''
        pick_types = ['default', 'pack_while_pick', 'pick_and_pass',
                      'bulk_picking', 'sorting', 'cluster_picking']

        self.pick_type = self.request_data.get('pick_type', 'default')
        if self.pick_type not in pick_types:
            self.errors.append('Invalid pick type!')

        self.reference_details = self.request_data.get('data', []) or []
        if not self.request_data.get('data', []):
            self.errors.append(
                'Reference details are mandatory for generate picklist')

    def validate_and_preparing_picklist_line_level_data(self):
        '''
        1. Validating the reference number and reference type field as mandatory
        2. Preparing the picklist creation data using request data based on pick type and reference type
        output: list of lists, each considered as one picklist
        3. Reference model name and reference ID as mandatory fields in item level
        '''
        picklist_details_dict, self.reference_model = defaultdict(list), ''
        self.reference_numbers, self.order_skus_count_details, self.order_quantity_count_details, self.route_details, self.trip_details = set(), {}, {}, {}, {}

        for reference_record in self.reference_details:
            reference_number = reference_record.get('reference_number', '')
            reference_type = reference_record.get('reference_type', '')
            customer_id = reference_record.get('customer_id', '')
            self.route_details[reference_number] = reference_record.get('route_id', '')
            self.trip_details[reference_number] = reference_record.get('trip_id', '')
            
            # Validation
            if not reference_number:
                self.errors.append('Reference Number is mandatory for generating picklist')
                return

            # Preparing picklist creation data
            if self.pick_type in ['bulk_picking', 'sorting', 'cluster_picking'] or self.extra_params.get('bulk_picking'):
                picklist_unique_key = ('', reference_type)
            elif self.pick_type in ['pick_and_pass']:
                picklist_unique_key = (customer_id, reference_type)
                if reference_type.lower() in self.switch_values.get('manual_assignment', []) or 'all' in self.switch_values.get('manual_assignment', []):
                    self.errors.append('Manual Assignment order type is not allowed for pick and pass picklist')
            else:
                picklist_unique_key = (reference_number, reference_type)

            picklist_details_dict[picklist_unique_key].append(reference_record)
            
            self.reference_numbers.add(reference_number)
            
            # Items value validation
            items = reference_record.get('items', []) or []
            self.order_skus_count_details[reference_number] = reference_record.get('items_count') or len(items)
            self.order_quantity_count_details[reference_number] = 0
            for item_record in items:
                reference_model = item_record.get('reference_model', '')
                if not reference_model:
                    self.errors.append('Reference Model is mandatory for generating picklist')
                    return
                self.reference_model = self.reference_model or reference_model
                if self.reference_model != reference_model:
                    self.errors.append("Multiple reference models are not allowed in single picklist")
                    return
                
                reference_id = item_record.get('id', '')
                if not reference_id:
                    self.errors.append('Reference ID is mandatory for generating picklist')
                    return

                zone = item_record.get('zone', '')
                if not zone:
                    if self.zone_mandatory_for_picklist_generation and self.reference_model == 'OrderDetail':
                        self.errors.append('Zone mandatory for SO picklist generation')
                        return
                    elif self.zone_mandatory_for_jo_picklist_generation and self.reference_model == 'JOMaterial':
                        self.errors.append('Zone mandatory for JO picklist generation')
                        return

                quantity = max(0, item_record.get('original_quantity', 0) - item_record.get('cancelled_quantity', 0))
                self.order_quantity_count_details[reference_number] += quantity
        
        self.picklist_details_list = list(picklist_details_dict.values())
        self.reference_numbers = list(self.reference_numbers)

    def generate_picklist(self):
        '''
        Generating picklist
        '''
        self.reference_id_picklist_details, self.picklist_callback_data = {}, set()
        self.generated_picklist_numbers, self.cancelled_picklist_numbers, self.reference_ids, self.insufficient_stock_order_ids = [], [], [], []
        self.picklist_generated_zones, self.generated_order_types, self.processed_order_ids  = [],[], set()
        try:
            if self.stock_allocate:
                extra_params = {'is_order' : self.is_order}
                self.allocation_object = AllocatedOrderPicklistGeneration(
                    self.user, self.warehouse, self.reference_numbers, self.pick_type, self.reference_model, extra_params)
                self.allocation_object.get_allocation_data()

            extra_params = deepcopy(self.extra_params)
            extra_params.update(extra_params.pop('switch_values', {}))
            if self.picklist_details_list:
                extra_params['reference_model'] = self.picklist_details_list[0][0].get('items', [{}])[0].get('reference_model', '')
            restricted_orders_dict = validate_transaction_lock(self.warehouse, self.reference_numbers, extra_params=extra_params, transaction = 'picklist')

            # restrict hold/expired orders for picklist generation, if not wave picklist
            if restricted_orders_dict and not self.extra_params.get('wave_reference'):
                self.errors.extend(list(restricted_orders_dict.values()))
                return self.reference_id_picklist_details

            picklist_data_list = []
            for picklist_data in self.picklist_details_list:
                valid_picklist_record = []
                for picklist_record in picklist_data:
                    reference_number = picklist_record.get('reference_number', '')
                    if reference_number in restricted_orders_dict:
                        self.errors.append(restricted_orders_dict[reference_number])
                        continue
                    valid_picklist_record.append(picklist_record)
                if valid_picklist_record:
                    picklist_data_list.append(valid_picklist_record)

            for picklist_data in picklist_data_list:
                self.picklist_objects_list, self.cycle_count_stock = [], set()
                self.pick_sku_codes, self.sku_zones_dict = set(), defaultdict(list)

                # dropship order picklist generation
                is_dropship_order, picklist_number = self.prepare_dropship_picklist_generation_data(picklist_data)
                if is_dropship_order:
                    if picklist_number:
                        self.generated_picklist_numbers.append(picklist_number)
                    continue

                # allocation
                if self.stock_allocate:
                    self.generate_picklist_for_allocated(picklist_data)
                    continue

                # get available stocks
                self.prepare_stock_filter(picklist_data)
                if not self.get_stock_details():
                    self.errors.append('Insufficient stock. Cannot process the picklist')
                    # frame insufficient stock order ids
                    self.frame_insufficient_stock_order_ids_for_no_stock(picklist_data)
                    continue

                # assign stock to items
                self.assign_stock_and_prepare_picklist(picklist_data)

                # create picklist and update stock quantity
                self.insert_picklist_details()
            if self.picklist_generated_zones:
                push_data = {"zones": self.picklist_generated_zones,
                            "order_types":self.generated_order_types ,
                            "picklist_type": "picklist"}
                warehouse_push_notification(self.warehouse, 'picklist', push_data)
            
            self.picklist_generation_callback(self.picklist_callback_data)

        except Exception as e:
            log.debug(traceback.format_exc())
            log.info("Method: Generate Picklist failed with error - %s" % str(e))
            self.errors.append('Picklist Generation Failed!')

        return self.reference_id_picklist_details

    def generate_picklist_for_allocated(self, picklist_data):
        """
        
        """
        with transaction.atomic('default'):
            reference_ids, allocated_reference_numbers, self.picklist_objects_list, errors = self.allocation_object.generate_picklist_for_allocation(
                picklist_data)
            if errors:
                self.errors.extend(errors)
                return
            for picklist in self.picklist_objects_list:
                json_data = picklist.json_data or {}
                item = {
                    'id' : picklist.reference_id,
                    'pack_uom_quantity' : json_data.get('pack_uom_quantity', 0),
                }
                combo_sku_qty = json_data.get('combo_sku_qty', 0) or 0
                is_combo = (picklist.classification == 'combo')
                self.prepare_reference_qty_dict(item, picklist.reserved_quantity, is_combo, combo_sku_qty)
            self.insert_picklist_details()
            self.reference_ids.extend(reference_ids)
            self.picklist_callback_data.union(allocated_reference_numbers)

    def prepare_stock_filter(self, picklist_data):
        '''
        Preparing Stock Detail filter using request data
        '''
        self.get_unique_values_from_request_data(picklist_data)
        self.get_combo_members_details()
        self.get_order_type_zone_mapping()

        self.exclude_object = Q()

        # stock order by
        self.stock_order_by = 'location_id__pick_sequence'
        strategy_order_map = {
            'FIFO': 'receipt_date',
            'FEFO': 'batch_detail__expiry_date',
            'LEFO': '-batch_detail__expiry_date'
        }
        for strategy, order_by in strategy_order_map.items():
            if self.stock_selection_strategy_ordertypes.get(strategy, []) and self.order_types and set(self.order_types).issubset(self.stock_selection_strategy_ordertypes.get(strategy, [])):
                self.stock_order_by = order_by
                break
        

        self.sku_ids = self.sku_ids.union(self.members_skus)

        self.stock_filter, self.exclude_dict = {'sku__in': self.sku_ids}, {}

        # mrp based picking on
        if (self.mrp_based_picking) and ((self.reference_type.lower() in self.mrp_based_picking) or ('all' in  self.mrp_based_picking)) and (not self.mrp_tolerance):
            self.stock_filter['batch_detail__mrp__in'] = self.mrps

        # block expired batch picklist on
        if self.switch_values.get('block_expired_batches_picklist', 'false') == 'true':
            self.exclude_dict['batch_detail__expiry_date__lte'] = datetime.now()

        if self.zones_list:
            self.stock_filter['location__zone__in'] = self.zones_list
        elif self.zones:
            self.stock_filter['location__zone__zone__in'] = self.zones
        else:
            self.exclude_dict['location__zone__zone__in'] = get_exclude_zones(
                self.warehouse)

        #sub zone filter
        if self.sub_zones:
            self.stock_filter['location__sub_zone__zone__in'] = self.sub_zones

        #pick type as pick and pass in case of alternative picklist we are ignoring stock ids
        if self.extra_params.get('picklist_number'):
            self.exclude_short_picked_location_batches()
            if self.pick_type in ['pick_and_pass']:
                self.exclude_picked_stock_ids()

        # exclude cycle count stocks
        self.cycle_count_data()
        
        #exclude customer shelf life expired stocks
        if self.customer_shelf_life_ordertypes:
            self.customer_shelf_life_data()

        for key, value in self.exclude_dict.items():
            self.exclude_object |= Q(**{key: value})

    def exclude_picked_stock_ids(self):
        '''
        Exclude already picked stock ids
        '''
        picked_stock_ids = Picklist.objects.filter(user_id=self.warehouse.id, picklist_number=self.extra_params.get('picklist_number'), status__in=['picked', 'cancelled', 'short_closed', 'dispatched']).values_list('stock_id', flat=True)
        self.exclude_dict['id__in'] = picked_stock_ids

    def exclude_short_picked_location_batches(self):
        """
        Excludes batches from the picklist that have been short picked.

        This method filters out batches that have been either 'short_closed' or 'picked' 
        but have a picklist quantity greater than the picked quantity. It updates the 
        `exclude_object` attribute with the filtered results.

        The filtered results are distinct combinations of:
        - `sku_id`
        - `location`
        - `batch_no`

        These results are then used to update the `exclude_object` attribute with
        the appropriate query conditions.
        """

        short_picked_data = list(Picklist.objects.filter(user_id=self.warehouse.id, picklist_number=self.extra_params.get('picklist_number'),
                    sku_id__in=self.sku_ids, status__in=['short_closed', 'picked'],
                    picklist_quantity__gt=F('picked_quantity'))
            .values('sku_id', 'location__location', batch_detail__batch_no=F('stock__batch_detail__batch_no'),
                    lpn_number=F('stock__lpn_number')
            )
            .distinct()
        )

        for short_picked in short_picked_data:
            self.exclude_object |= Q(**short_picked)

    def get_unique_values_from_request_data(self, picklist_data):
        '''
        Get unique details
        '''
        self.reference_type = ''
        self.sku_ids = set()
        self.customer_ids = set()
        self.mrps = set()
        self.zones = set()
        self.locations = set()
        self.order_types = set()
        self.sub_zones = set()

        unique_values_mapping = {
            'sku_id': self.sku_ids,
            'mrp': self.mrps,
            'zone': self.zones,
            'sub_zone': self.sub_zones,
            'location': self.locations,
            'order_type': self.order_types,
        }

        for picklist_record in picklist_data:
            if not self.reference_type:
                self.reference_type = picklist_record.get('reference_type', '')

            customer_id = picklist_record.get('customer_id', '')
            if customer_id:
                self.customer_ids.add(customer_id)

            items = picklist_record.get('items', []) or []

            for each_item in items:
                for key, field_name in unique_values_mapping.items():
                    key_value = each_item.get(key, '')
                    if key == 'order_type' and key_value:
                        key_value = key_value.lower()

                    if key_value not in [None, 'null', '']:
                        field_name.add(key_value)
                self.processed_order_ids.add(each_item.get('id', ''))

        self.order_types = list(self.order_types)

    def get_combo_members_details(self):
        '''
        Get combo sku details
        '''
        self.combo_skus, self.members_skus = {}, set()
        sku_combo_objects = list(BOMMaster.objects.filter(product_sku__user=self.warehouse.id, product_sku_id__in=self.sku_ids, status=1, bom_type=1).values(
            'product_sku_id', 'material_sku_id', 'material_quantity'))
        for combo_sku in sku_combo_objects:
            parent_sku_id = combo_sku.get('product_sku_id', '')
            member_sku_id = combo_sku.get('material_sku_id', '')
            if member_sku_id:
                self.members_skus.add(member_sku_id)
            if not self.combo_skus.get(parent_sku_id):
                self.combo_skus[parent_sku_id] = {}
            self.combo_skus[parent_sku_id].update({
                member_sku_id: combo_sku.get('material_quantity', 0)
            })

    def get_order_type_zone_mapping(self):
        '''
        Get order type zone mapping details
        '''
        self.zones_list, self.zone_priority, self.stock_order_by_dict, self.stock_status_list, self.order_type_level_shelf_life = [], {}, {}, [], {}

        ordet_type_zone_mapping_objects = OrderTypeZoneMapping.objects.filter(user_id=self.warehouse.id, order_type=self.reference_type, status=1)
        if ordet_type_zone_mapping_objects.exists():
            self.stock_order_by_dict = {
                "zone_priority": Case(
                    default=Value(999999999),
                    output_field=IntegerField()
                )
            }
            for each_ordertype_zone in ordet_type_zone_mapping_objects.iterator():
                order_type = each_ordertype_zone.order_type
                if self.order_type_level_shelf_life.get(order_type) is None:
                    json_data = each_ordertype_zone.json_data or {}
                    if json_data.get('customer_shelf_life') not in [None, 0]:
                        order_level_customer_shelf_life = timedelta(float(json_data.get('customer_shelf_life', 0)))
                        self.order_type_level_shelf_life[order_type] = self.shelf_life_calculation(order_level_customer_shelf_life, istimedelta=True)

                if each_ordertype_zone.receipt_type != 'PICKLIST':
                    continue

                if each_ordertype_zone.zone:
                    self.zones_list.append(each_ordertype_zone.zone.id)
                    self.stock_order_by_dict["zone_priority"].cases.append(
                        When(location__zone=each_ordertype_zone.zone,
                            then=each_ordertype_zone.priority)
                    )
                ordertype_mapping_json_data = each_ordertype_zone.json_data or {}
                stock_status_list = ordertype_mapping_json_data.get('stock_status', []) or []
                if stock_status_list:
                    self.stock_status_list.extend(stock_status_list)

        if self.stock_status_list:
            self.extra_params['stock_status'] = self.stock_status_list

    def get_stock_details(self):
        '''
        Get available stock
        '''
        stock_base_filters = {
            'sku__user': self.warehouse.id,
            'quantity__gt': 0,
            'location__status': 1,
        }
        stock_base_filters['status__in'] = [1]
        if self.extra_params.get('stock_status'):
            stock_base_filters['status__in'] = self.extra_params.get('stock_status', [1]) or [1]

        # Get available stocks
        stock_query = StockDetail.objects.filter(**stock_base_filters, **self.stock_filter).exclude(self.exclude_object)
        order_by = [self.stock_order_by, 'location__pick_sequence', 'id']
        if self.mrp_tolerance:
            order_by.insert(0, 'batch_detail__mrp')
        if self.zones_list:
            stock_query = stock_query.annotate(**self.stock_order_by_dict)
            order_by.insert(0, 'zone_priority')
        stock_query = stock_query.order_by(*order_by)
        
        stock_data = stock_query.values('id', 'sku_id', 'location_id', 'quantity', 'lpn_number', sku_code=F('sku__sku_code'), expiry_date=F('batch_detail__expiry_date'), mrp=F(
            'batch_detail__mrp'), mrp_=F('batch_detail__mrp'), batch_number=F('batch_detail__batch_no'), batch_reference=F('batch_detail__batch_reference'), batch_id=F('batch_detail_id'),
            location_name=F('location__location'), zone=F('location__zone__zone'), sub_zone=F('location__sub_zone__zone'), zone_id = F('location__zone_id'))

        final_stock_data = []
        for stock in stock_data.iterator(chunk_size=500):
            stock_key = (stock['sku_id'], stock['location_id'], stock['batch_id'] or 0, stock['lpn_number'] or '')
            if stock_key not in self.cycle_count_stock:
                final_stock_data.append(stock)

        if not final_stock_data:
            return False

        self.stock_df = pd.DataFrame(final_stock_data)
        # exclude reserved quantity
        self.get_reserved_quantity()
        
        self.get_allocated_quantity()
        
        reserved = pd.concat([self.picklist_reserved, self.allocation_reserved])
        
        if reserved.empty:
            self.stock_df['available_quantity'] = self.stock_df['quantity']
            self.stock_df['reserved_qty'] = 0
        else:
            reserved = reserved.groupby('stock_id')['reserved_qty'].sum().reset_index()
            self.stock_df = self.stock_df.merge(
                reserved, left_on='id', right_on='stock_id', how='left')
            self.stock_df['quantity'] = self.stock_df['quantity'].fillna(0)
            self.stock_df['reserved_qty'] = self.stock_df['reserved_qty'].fillna(0)
            
            self.stock_df['available_quantity'] = (
                self.stock_df['quantity'].astype(str).apply(Decimal) - self.stock_df['reserved_qty'].astype(str).apply(Decimal)
            ).astype(float)
        
            # Drop rows where available quantity is <= 0
            self.stock_df = self.stock_df[self.stock_df['available_quantity'] > 0]

        return True

    def frame_insufficient_stock_order_ids_for_no_stock(self, picklist_data):
        """
        Identifies and appends order IDs with insufficient stock to the `insufficient_stock_order_ids` list.

        This method processes the provided `picklist_data` to determine which orders have insufficient stock.
        It checks if all items are eligible for full picking based on the `is_full_picking` flag and the
        `order_all_items_picklist_generation` switch values. If the conditions are met and the reference model
        is 'OrderDetail', it appends the item IDs to the `insufficient_stock_order_ids` list.

        Args:
            picklist_data (list): A list of dictionaries containing picklist records. Each record should have
                                    'reference_type' and 'items' keys.

        Returns:
            None
        """

        is_full_picking = self.extra_params.get('is_full_picking') or 'all' in self.switch_values.get('order_all_items_picklist_generation', [])

        for picklist_record in picklist_data:
            reference_type = picklist_record.get('reference_type', '')
            is_all_items_full_picking = is_full_picking or reference_type.lower() in self.switch_values.get('order_all_items_picklist_generation', [])
            # Check if all items are eligible for full picking and is a SO Order
            if not (is_all_items_full_picking and picklist_record.get('items', [{}])[0].get('reference_model') == 'OrderDetail'):
                continue

            for item in picklist_record.get('items', []):
                self.insufficient_stock_order_ids.append(item.get('id'))

    def cycle_count_data(self):
        '''
        Exclude cycle count location stocks
        '''
        cycle_count_types, self.cycle_count_stock = set(), set()

        cycle_type_run_type_mapping = {
            'short_cycle_count': [SHORT_PICK],
            'scheduled_cycle_count': ['scheduled', 'Scheduled'],
            'unscheduled_cycle_count': ['unscheduled', 'Unscheduled', 'UnScheduled'],
            'audit_cycle_count': ['Audit'],
            'adhoc_cycle_count': ['adhoc'],
        }

        # By default, exclude short pick cycle count records if short close is enabled
        if self.reference_type == 'BA_TO_SA':
            if self.create_cycle_count_for_short_close_batosa:
                cycle_count_types.add(SHORT_PICK)
        elif self.reference_type in BACKFLUSH_JO_TYPES:
            if self.create_cycle_count_for_short_close_joborder:
                cycle_count_types.add(SHORT_PICK)
        elif self.create_cycle_count_for_short_close:
            cycle_count_types.add(SHORT_PICK)

        restrict_stock_cycle_count_creation_types = self.switch_values.get('restrict_picklist_on_cycle_count_creation_options', '').split(',')
        restrict_stock_cycle_count_pending_approval_types = self.switch_values.get('restrict_picklist_on_cycle_count_pending_approval_options', '').split(',')

        # Get cycle count run types based on the restrict options
        for key, value in cycle_type_run_type_mapping.items():
            if key in restrict_stock_cycle_count_creation_types or key in restrict_stock_cycle_count_pending_approval_types:
                cycle_count_types.update(value)

        if not cycle_count_types:
            return

        # Fetch cycle count records based on run type filters
        self.cycle_count_stock = set(CycleCount.objects
            .filter(run_type__in= cycle_count_types, sku_id__in=self.sku_ids, status__in= [1,2])
            .values_list('sku_id', 'location_id')
            .annotate(
                batch_id=Case(When(batch_detail__isnull=False,then=F('batch_detail_id')), default=0, output_field=IntegerField()),
                lpn_number=Case(When(lpn_number__isnull=False, then=F('lpn_number')), default=Value(''), output_field=CharField())
            )
            .distinct()
        )

    def customer_shelf_life_data(self):
        '''
        Validating customer shelf life
        '''
        self.cust_sku_dict, self.sku_master_dict = {}, {}
        if self.sku_ids or self.customer_ids:
            cust_sku_df = pd.DataFrame(CustomerSKU.objects.filter(customer_shelf_life__gt=timedelta(days=0), sku__user=self.warehouse.id,
                                        customer__customer_id__in=self.customer_ids).values('customer_shelf_life', 'customer__customer_id', 'sku_id'))

            for index, cust_sku in cust_sku_df.iterrows():
                self.cust_sku_dict[(cust_sku['customer__customer_id'],cust_sku['sku_id'])] = self.shelf_life_calculation(cust_sku['customer_shelf_life'], istimedelta=True)

            self.customer_sku_shelf_life, self.customer_shelf_life = self.get_customer_shelf_life()

            sku_shelf_df = pd.DataFrame(SKUMaster.objects.filter(customer_shelf_life__gt=timedelta(days=0),
                user=self.warehouse.id, id__in=self.sku_ids).values('customer_shelf_life', sku_id=F('id')))
            
            for index, sku in sku_shelf_df.iterrows():

                self.sku_master_dict[sku['sku_id']] = self.shelf_life_calculation(sku['customer_shelf_life'], istimedelta=True)


    def get_customer_shelf_life(self):
        '''
        return customer shelf life dataframe in days if customer shelf life percentage is defined.
        returns: with customer shelf life (customer_id, sku_id) in days
        '''
        # customer get skus shelf life and customer master's shelf life days and percentage if percentage is not defined give shelf life days. else calculate shelf life in days based on percentage for each sku
        sku_shelf_life = list(SKUMaster.objects.filter(id__in=self.sku_ids, user=self.warehouse.id, shelf_life__gt=0).values('id', 'shelf_life'))
        customer_masters = list(CustomerMaster.objects.filter( (Q(customer_shelf_life__gt=0) | Q(customer_shelf_life_percentage__gt=0)), customer_id__in=self.customer_ids, user=self.warehouse.id).values('customer_id', 'customer_shelf_life', 'customer_shelf_life_percentage'))
        customer_sku_shelf_life, customer_shelf_life = {}, {}
        for customer in customer_masters:
            for sku in sku_shelf_life:
                shelf_life = customer.get('customer_shelf_life', 0) or 0
                if customer['customer_shelf_life_percentage'] > 0:
                    shelf_life = float(sku.get('shelf_life', 0)) * float((customer['customer_shelf_life_percentage']/100))
                customer_sku_shelf_life[(customer['customer_id'], sku['id'])] = self.shelf_life_calculation(shelf_life)

            shelf_life = customer.get('customer_shelf_life', 0) or 0
            customer_shelf_life[customer['customer_id']] = self.shelf_life_calculation(shelf_life)

        return customer_sku_shelf_life, customer_shelf_life

    def shelf_life_calculation(self, no_of_days, istimedelta=False):
        """
        Calculate the shelf life date based on the given number of days.

        Args:
            no_of_days (int or timedelta): The number of days or timedelta object to add to the current date.
            istimedelta (bool, optional): Flag indicating if no_of_days is a timedelta object. Defaults to False.

        Returns:
            datetime: The calculated shelf life date in UTC timezone.
        """
        if istimedelta:
            shelf_life_filter = self.current_date + no_of_days
        else:
            shelf_life_filter = self.current_date + pd.Timedelta(days=no_of_days)
        timestamp = datetime.fromisoformat(str(shelf_life_filter))
        shelf_life_date_time = timestamp.astimezone(timezone.utc)
        return shelf_life_date_time

    def get_reserved_quantity(self):
        '''
        Calculate the reserved quantity for available stock
        '''
        if self.stock_df.empty:
            return

        stock_ids = []
        if 'id' in self.stock_df.columns:
            stock_ids = list(self.stock_df['id'])
        reserved_filter = {
            'stock_id__in': stock_ids,
            'status' : 'open',
            'reserved_quantity__gt': 0,
        }
        if self.zones_list:
            reserved_filter['stock__location__zone__in'] = self.zones_list
        reserved_query = Picklist.objects.filter(**reserved_filter).values('stock_id').annotate(reserved_qty=Sum('reserved_quantity'))
        self.picklist_reserved = pd.DataFrame(reserved_query)
        
    def get_allocated_quantity(self):
        '''
        Calculate the allocated quantity for available stock
        '''
        if self.stock_df.empty:
            return

        if not self.stock_allocate:
            self.allocation_reserved = pd.DataFrame()
            return

        stock_ids = []
        if 'id' in self.stock_df.columns:
            stock_ids = list(self.stock_df['id'])
        allocated_filter = { 
            'stock_id__in': stock_ids,
            'status' : 1,
            'warehouse_id' : self.warehouse.id,
        }
        if self.zones_list:
            allocated_filter['stock__location__zone__in'] = self.zones_list
        allocated_query = StockAllocation.objects.filter(**allocated_filter).values('stock_id').annotate(reserved_qty=Sum('quantity'))
        self.allocation_reserved = pd.DataFrame(allocated_query)
        
    def assign_stock_and_prepare_picklist(self, picklist_data, allocation=False):
        '''
        1. Validating stock based on picklist generation validation
        2. Assign stock to items
        3. Preparing picklist model data for insertion
        '''
        for picklist_record in picklist_data:
            self.reference_number = picklist_record.get('reference_number')
            reference_type = picklist_record.get('reference_type', '') or ''

            # partial picking configuration
            self.is_all_items_full_picking, self.is_partial_picking = False, False
            if reference_type.lower() in self.switch_values.get('allow_partial_picklist', []) or 'all' in self.switch_values.get('allow_partial_picklist', []):
                self.is_partial_picking = True
            if self.extra_params.get('is_full_picking') or reference_type.lower() in self.switch_values.get('order_all_items_picklist_generation', []) or 'all' in self.switch_values.get('order_all_items_picklist_generation', []):
                self.is_all_items_full_picking = True
            if self.is_all_items_full_picking:
                is_valid, insufficient_stock_order_ids = self.is_all_items_availble_for_picking(
                    picklist_record)
                if not is_valid:
                    if not allocation:
                        self.insufficient_stock_order_ids.extend(insufficient_stock_order_ids)
                    self.errors.append(
                        f'Insufficient stock. Cannot process the picklist for order - {self.reference_number}')
                    continue

            self.validating_and_prepare_picklist(picklist_record)

    def validating_and_prepare_picklist(self, picklist_data):
        '''
        Validating stock and preparing picklist data
        '''
        reference_items = picklist_data.get('items', []) or []
        for item in reference_items:
            sku_id = item.get('sku_id', 0.0)
            sku_code = item.get('sku_code', '')

            if not self.combo_skus.get(sku_id, {}):
                is_valid = self.validating_sku_stock_without_combo(item, picklist_data)
                if not is_valid:
                    self.errors.append('Insufficient stock for %s'%sku_code)
            else:
                is_valid = self.validating_combo_sku_stock(item)
                if not is_valid:
                    self.errors.append('Insufficient stock for %s'%sku_code)

    def get_dataframe_conditions(self, item, picklist_record, data_frame_name='self.stock_df'):
        '''
        Preparing conditions data using request data
        '''
        DATA_FRAME_NAME = data_frame_name
        FIELD_MAPPING = {
            'batch_number': 'batch_number',
            'location': 'location_name',
            'zone': 'zone',
            'sub_zone': 'sub_zone',
            'batch_id': 'batch_id',
            'batch_reference': 'batch_reference',
            'lpn_number': 'lpn_number',
        }
        MULTI_EXCLUDE_FIELD = {
            'exclude_zones' : 'zone'
        }

        sku_id = item.get('sku_id', '')
        customer_id = picklist_record.get('customer_id', '')
        mrp = item.get('mrp', 0.0)
        data_frame_filter = {f"{DATA_FRAME_NAME}['sku_id']": f"== {sku_id}", f"{DATA_FRAME_NAME}['available_quantity']": "> 0"}
        if self.mrp_tolerance:
            max_mrp = int(mrp*(1 + self.mrp_tolerance/100)) #Rounding down the decimals
            data_frame_filter[f"{DATA_FRAME_NAME}['mrp']"] = f">= {mrp}"
            data_frame_filter[f"{DATA_FRAME_NAME}['mrp_']"] = f"<= {max_mrp}"
        elif self.reference_type.lower() in self.mrp_based_picking or 'all' in self.mrp_based_picking:
            data_frame_filter[f"{DATA_FRAME_NAME}['mrp']"] = f"== {mrp}"

        # customer shelf life
        if (not item.get('manual_location')) and picklist_record.get('reference_type') and picklist_record.get('reference_type').lower() in self.customer_shelf_life_ordertypes or 'all' in self.customer_shelf_life_ordertypes:
            data_frame_filter = self.prepare_customer_shelf_life_filter(customer_id, sku_id, picklist_record, data_frame_filter, DATA_FRAME_NAME)

        for field_name, mappend_field in FIELD_MAPPING.items():
            field_value = item.get(field_name, '')
            if field_value:
                if field_name in ['batch_id']:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{mappend_field}']"] = f"== {field_value}"
                else:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{mappend_field}']"] = f"== '{field_value}'"
            
        for field_name, mapped_field in MULTI_EXCLUDE_FIELD.items():
            field_value = item.get(field_name, '')
            if field_value:
                data_frame_filter[f"~{DATA_FRAME_NAME}['{mapped_field}']"] = f".isin({field_value})"

        conditions = [f"({column} {condition})" % {'column': column, 'condition': condition} for column, condition in data_frame_filter.items()]
        return conditions

    def prepare_customer_shelf_life_filter(self, customer_id, sku_id, picklist_record, data_frame_filter, DATA_FRAME_NAME):
        """
        Prepare customer shelf life filter for picklist generation.

        This method determines the appropriate shelf life filter based on the customer and SKU information.
        It checks various dictionaries in a specific order to find the most relevant shelf life data.

        Args:
            customer_id (str): The ID of the customer.
            sku_id (str): The ID of the SKU.
            picklist_record (dict): The picklist record containing additional information.
            data_frame_filter (dict): The current data frame filter.
            DATA_FRAME_NAME (str): The name of the data frame being used.

        Returns:
            dict: The updated data frame filter with the shelf life condition added if applicable.
        """
        if self.cust_sku_dict.get((customer_id, sku_id)):
            shelf_life_filter = self.cust_sku_dict.get((customer_id,sku_id), 0)
        elif self.customer_sku_shelf_life.get((customer_id, sku_id)):
            shelf_life_filter = self.customer_sku_shelf_life.get((customer_id, sku_id), 0)
        elif self.customer_shelf_life.get(customer_id):
            shelf_life_filter = self.customer_shelf_life.get(customer_id, 0)
        elif self.order_type_level_shelf_life.get(picklist_record.get('reference_type')):
            shelf_life_filter = self.order_type_level_shelf_life.get(picklist_record.get('reference_type'), 0)
        else:
            shelf_life_filter = self.sku_master_dict.get(sku_id, 0)
        if shelf_life_filter:
            data_frame_filter[f"{DATA_FRAME_NAME}['expiry_date']"] = f"> '{shelf_life_filter}'"
        return data_frame_filter

    def is_all_items_availble_for_picking(self, picklist_record):
        '''
        Validating reference of all items have stock availability
        '''
        items = picklist_record.get('items', []) or []
        validating_stock_df = deepcopy(self.stock_df)

        insufficient_stock_order_ids = set()

        for item in items:
            if not self.is_item_available(item, validating_stock_df, picklist_record):
                insufficient_stock_order_ids.add(item['id'])

        is_valid = True
        if insufficient_stock_order_ids:
            is_valid = False

        return is_valid, list(insufficient_stock_order_ids)

    def is_item_available(self, item, validating_stock_df, picklist_record):
        sku_id = item.get('sku_id', '')
        quantity = item.get('quantity', '')

        if not self.combo_skus.get(sku_id, {}):
            return self.is_single_sku_available(item, quantity, validating_stock_df, picklist_record)
        else:
            return self.is_combo_sku_available(sku_id, quantity, validating_stock_df)

    def is_single_sku_available(self, item, quantity, validating_stock_df, picklist_record):
        '''
        Full order picking stock validation for both pack and non pack uoms
        '''
        df_conditions = self.get_dataframe_conditions(item, picklist_record, data_frame_name='validating_stock_df')

        pack_qty = 1
        if self.enable_sales_uom and item.get('pack_uom_quantity'):
            pack_qty = item.get('pack_uom_quantity', 1)

        if validating_stock_df[eval('&'.join(df_conditions))]['available_quantity'].sum() < quantity*pack_qty:
            return False

        for index, stock in validating_stock_df[eval('&'.join(df_conditions))].iterrows():
            stock_available_quantity = stock['available_quantity']
            if self.enable_sales_uom:
                stock_available_quantity = int(stock_available_quantity/pack_qty)

            min_qty = min(stock_available_quantity, quantity)
            assigned_quantity = min_qty*pack_qty
            validating_stock_df.loc[validating_stock_df['id'] == stock['id'], 'available_quantity'] -= assigned_quantity
            quantity -= min_qty

            if quantity <= 0:
                break

        if quantity > 0:
            return False

        return True

    def is_combo_sku_available(self, sku_id, quantity, validating_stock_df):
        combo_sku_details = self.combo_skus.get(sku_id, {}) or {}

        for member_sku_id, member_quantity in combo_sku_details.items():
            combo_member_quantity = quantity * member_quantity

            if validating_stock_df[(validating_stock_df['sku_id'] == member_sku_id)]['available_quantity'].sum() < combo_member_quantity:
                return False

            for index, stock in validating_stock_df[(validating_stock_df['sku_id'] == sku_id) & (validating_stock_df['available_quantity'] > 0)].iterrows():
                assigned_quantity = min(stock['available_quantity'], combo_member_quantity)
                validating_stock_df.loc[validating_stock_df['id'] == stock['id'], 'available_quantity'] -= assigned_quantity

        return True

    def validating_sku_stock_without_combo(self, item, picklist_data):
        '''
        Validating and preparing data for request SKU
        '''
        quantity = item.get('quantity', 0.0)
        df_conditions = self.get_dataframe_conditions(item, picklist_data)

        if not self.is_valid_stock(item, df_conditions, quantity):
            return False

        if not self.assign_stock_quantities(item, df_conditions, quantity):
            return False

        return True

    def is_valid_stock(self, item, df_conditions, quantity):
        '''
        Validating stock for non combo skus
        '''
        valid_stock, total_available_quantity = True, 0

        pack_qty = 1
        if self.enable_sales_uom and item.get('pack_uom_quantity'):
            pack_qty = item.get('pack_uom_quantity', 1)

        if self.stock_df.empty:
            valid_stock = False
        else:
            if not self.enable_sales_uom:
                total_available_quantity = self.stock_df[eval('&'.join(df_conditions))]['available_quantity'].sum()
                if self.decimal_limit:
                    total_available_quantity = truncate_float(total_available_quantity, self.decimal_limit)
                if total_available_quantity < quantity:
                    valid_stock = False
            else:
                valid_stock, total_available_quantity = self.check_valid_uom_qty(df_conditions, quantity, pack_qty)

        if not valid_stock:
            if self.is_partial_picking and total_available_quantity:
                valid_stock = True
            else:
                valid_stock = False

        return valid_stock

    def check_valid_uom_qty(self, df_conditions, quantity, pack_qty):
        '''
        Validating sales uom quantity
        '''
        available_quantity = 0

        for index, stock in self.stock_df[eval('&'.join(df_conditions))].iterrows():
            stock_available_quantity = stock['available_quantity']
            no_of_sales_uom = int(stock_available_quantity/pack_qty)
            min_qty = min(no_of_sales_uom, quantity)

            quantity -= min_qty
            available_quantity += min_qty

            if quantity == 0:
                break
        if quantity:
            return False, available_quantity
        return True, available_quantity

    def assign_stock_quantities(self, item, df_conditions, quantity):
        '''
        Assigned stock to items
        '''
        picklist_quantity, pack_qty = quantity, 1
        if self.enable_sales_uom and item.get('pack_uom_quantity'):
            pack_qty = item.get('pack_uom_quantity', 1)
            picklist_quantity = quantity * pack_qty

        if not self.stock_df.empty:
            for index, stock in self.get_available_stock(df_conditions):
                if stock.get('available_quantity') <= 0:
                    continue

                assigned_qty = min(stock['available_quantity'], picklist_quantity)
                if self.enable_sales_uom and item.get('pack_uom_quantity'):
                    assigned_qty = int(assigned_qty/pack_qty)*pack_qty
                if assigned_qty == 0:
                    continue
                self.update_stock_quantity(stock['id'], assigned_qty)
                picklist_quantity -= assigned_qty
                self.prepare_picklist_data(item, stock, assigned_qty)
                if picklist_quantity == 0:
                    break

        if picklist_quantity > 0:
            return False
        return True


    def get_available_stock(self, df_conditions):
        '''
        Get filtered stock
        '''
        return self.stock_df.loc[eval('&'.join(df_conditions))].iterrows()

    def validating_combo_sku_stock(self, item):
        '''
        Validating and preparing data for combo SKUs
        '''
        sku_id = item.get('sku_id', '')
        quantity = item.get('quantity', 0.0)
        combo_sku_details = self.combo_skus.get(sku_id, {}) or {}

        is_valid_combo, available_combo_qty = self.is_valid_combo(combo_sku_details, quantity)
        if not is_valid_combo:
            return False

        if not self.stock_df.empty:
            quantity = self.assign_combo_quantities(item, combo_sku_details, quantity, available_combo_qty)

        return True

    def is_valid_combo(self, combo_sku_details, quantity):
        '''
        Validating combo sku's
        '''
        is_valid_combo, available_combo_qty = self.combo_members_qty_validation(combo_sku_details, quantity)
        if not is_valid_combo:
            is_valid_combo = self.handle_invalid_combo(available_combo_qty)
        return is_valid_combo, available_combo_qty

    def combo_members_qty_validation(self, combo_sku_details, quantity):
        '''
        Validating stock available for combo sku
        '''
        if self.stock_df.empty:
            return False, 0

        available_combo_qty = None
        for member_sku_id, member_quantity in combo_sku_details.items():
            available_quantity = self.stock_df[(self.stock_df['sku_id'] == member_sku_id)]['available_quantity'].sum()
            if self.decimal_limit:
                available_quantity = truncate_float(available_quantity, self.decimal_limit)
            no_combo_using_memeber = int(available_quantity/member_quantity)
            min_qty = min(no_combo_using_memeber, quantity)

            if available_combo_qty == None:
                available_combo_qty = min_qty

            available_combo_qty = min(available_combo_qty, min_qty)

        if available_combo_qty < quantity:
            return False, available_combo_qty

        return True, available_combo_qty

    def handle_invalid_combo(self, available_combo_qty):
        '''
        validating combo sku
        '''
        if not (self.is_partial_picking and available_combo_qty):
            return False

        return True

    def assign_combo_quantities(self, item, combo_sku_details, quantity, available_combo_qty):
        '''
        assign stock for combo sku's
        '''
        for member_sku_id, member_quantity in combo_sku_details.items():
            combo_member_quantity = member_quantity*available_combo_qty

            for index, stock in self.get_available_stock_combo(member_sku_id):
                assigned_qty = min(stock['available_quantity'], combo_member_quantity)
                self.update_stock_quantity(stock['id'], assigned_qty)
                combo_member_quantity -= assigned_qty
                self.prepare_picklist_data_with_stock(item, member_sku_id, stock, assigned_qty, combo_sku_qty = available_combo_qty)

                if member_quantity == 0:
                    break

        pending_combo_quantity = quantity - available_combo_qty

        return pending_combo_quantity

    def get_available_stock_combo(self, sku_id):
        '''
        Get available stock for combo
        '''
        return self.stock_df[(self.stock_df['sku_id'] == sku_id) & (self.stock_df['available_quantity'] > 0)].iterrows()

    def update_stock_quantity(self, stock_id, assigned_qty):
        '''
        Update stock quantity
        '''
        self.stock_df.loc[self.stock_df['id'] == stock_id, 'available_quantity'] -= assigned_qty

    def prepare_picklist_data_with_stock(self, item, sku_id, stock, assigned_qty, combo_sku_qty = 0):
        '''
        Prepare the picklist data for combo sku's
        '''
        member_item = deepcopy(item)
        member_item['sku_id'] = sku_id
        self.prepare_picklist_data(member_item, stock, assigned_qty, is_combo_sku=True, combo_sku_qty = combo_sku_qty)

    def prepare_picklist_json_data(self, item: dict = {}):
        '''
        Preparing picklist json data
        '''
        picklist_json_data = {}
        for extra_key in PICKLIST_GENERATION_EXTRA_FIELDS:
            item_value = item.get(extra_key)
            if item_value:
                picklist_json_data[extra_key] = item_value
        return picklist_json_data

    def prepare_picklist_data(self, item, stock, quantity, is_combo_sku: bool = False, combo_sku_qty: float = 0):
        '''
        Preparing picklist model data
        '''
        reference_id = item.get('id')
        picklist_json_data = self.prepare_picklist_json_data(item)
        if self.skip_allocation:
            picklist_json_data['skip_allocation'] = True
        picklist_json_data['generated_by'] = 'auto_generated' if self.extra_params.get('auto_generated') else self.user.username
        if self.extra_params.get('wave_reference'):
            picklist_json_data['wave_reference'] = self.extra_params.get('wave_reference')
        if self.decimal_limit:
            quantity = round(quantity, self.decimal_limit)

        if quantity == 0:return
        
        self.pick_sku_codes.add(stock['sku_code'])
        if stock['sku_code'] not in self.sku_zones_dict:
            self.sku_zones_dict[stock['zone_id']].append(stock['sku_code'])
        
        picklist_data = {
            'sku_id': item.get('sku_id', None),
            'user_id': self.warehouse.id,
            'reference_id': reference_id,
            'reference_number': self.reference_number,
            'picklist_quantity': quantity,
            'reserved_quantity': quantity,
            'picked_quantity': 0,
            'order_type': self.reference_type,
            'pick_type': self.pick_type,
            'status': 'open',
            'remarks': item.get('remark', ''),
            'account_id': self.warehouse.userprofile.id,
            'json_data': picklist_json_data,
            'reference_model': item.get('reference_model', ''),
            'classification' : 'combo' if is_combo_sku else '',
        }
        if self.is_order:
            picklist_data['order_id'] = item.get('id')

        try:
            picklist_data.update({
                'stock_id': stock.get('id'),
                'location_id': stock.get('location_id'),
            })
            if self.pick_type == 'pick_and_pass':
                picklist_data['json_data'].update({
                    'picklist_zone': stock.get('zone', ''),
                    'sub_zone': stock.get('sub_zone', ''),
                    'zone': stock.get('zone', ''),
                })
                
            if item.get('reference_model', '') == "OrderDetail" and ('picklist' in self.switch_values.get('warehouse_notifications', '')) and not (self.reference_type.lower() in self.switch_values.get('manual_assignment', []) or 'all' in self.switch_values.get('manual_assignment', [])):
                if stock.get('zone', '') not in self.picklist_generated_zones:
                    self.picklist_generated_zones.append(stock.get('zone', ''))
                if self.reference_type not in self.generated_order_types:
                    self.generated_order_types.append(self.reference_type)

        except Exception:
            pass
        
        self.picklist_objects_list.append(Picklist(**picklist_data))
        self.prepare_reference_qty_dict(item, quantity, is_combo_sku, combo_sku_qty)
        
    def prepare_reference_qty_dict(self, item, quantity, is_combo_sku: bool = False, combo_sku_qty: float = 0):
        '''
        Prepare reference id and quantity dict
        '''
        reference_qty = quantity
        reference_id = item.get('id')
        if self.enable_sales_uom and item.get('pack_uom_quantity'):
            pack_qty = item.get('pack_uom_quantity', 1)
            reference_qty = int(quantity/pack_qty)
        if is_combo_sku:
            reference_qty = combo_sku_qty
        if reference_id and not self.reference_id_picklist_details.get(reference_id):
            self.reference_id_picklist_details[reference_id] = reference_qty
        elif reference_id and not is_combo_sku:
            total_qty = self.reference_id_picklist_details[reference_id] + reference_qty
            if self.decimal_limit:
                total_qty = round(total_qty, self.decimal_limit)
            self.reference_id_picklist_details[reference_id] = total_qty

    def cluster_picklist_process(self):
        '''
        Cluster picklist process
        '''
        extra_params = {
            "order_skus_count_details": self.order_skus_count_details,
            "order_quantity_count_details": self.order_quantity_count_details,
            "trip_details": self.trip_details,
            "route_details": self.route_details
        }
        cluster_picklist = ClusterPicklist(self.warehouse, self.picklist_objects_list, extra_params)
        picklist_numbers, self.picklist_objects_list, error = cluster_picklist.picklist_clustering_process()
        return picklist_numbers, error

    def pick_and_pass_sub_picklist_number_generation(self):
        """
        Generates and assigns sub picklist numbers to picklist objects based on the picklist zone.
        """
        zone_wise_sub_picklist_number = {}
        for picklist_obj in self.picklist_objects_list:
            picklist_obj.picklist_number = self.picklist_number
            picklist_zone = picklist_obj.json_data.get('picklist_zone', '')
            if not zone_wise_sub_picklist_number.get(picklist_zone):
                zone_wise_sub_picklist_number[picklist_zone] = get_picklist_number(self.warehouse, picklist_type='sub_picklist') + 1
            picklist_obj.sub_picklist_number = zone_wise_sub_picklist_number[picklist_zone]

    def get_picklist_number_based_on_pick_type(self):
        '''
        Get Picklist number based on pick type
        '''
        is_alternative, error = False, ''
        if self.extra_params.get('picklist_number'):
            self.picklist_number = self.extra_params.get('picklist_number')
            is_alternative = True
        elif self.pick_type not in ['cluster_picking']:
            self.picklist_number = get_picklist_number(self.warehouse) + 1

        if self.pick_type not in ['cluster_picking'] or is_alternative:
            sub_picklist_number = get_picklist_number(self.warehouse, picklist_type='sub_picklist') + 1
            for picklist_obj in self.picklist_objects_list:
                picklist_obj.picklist_number = self.picklist_number
                picklist_obj.sub_picklist_number = sub_picklist_number
            self.picklist_numbers = [self.picklist_number]
        else:
            self.picklist_numbers, error = self.cluster_picklist_process()
        self.generated_picklist_numbers.extend(self.picklist_numbers)
        return self.picklist_objects_list, error

    def group_dicts_sequentially(self, data, grouping_count):
        for i in range(0, len(data), grouping_count):
            yield data[i:i + grouping_count]

    def calculate_total_groups(self, data, grouping_count):
        """
            Calculate the total count of groups for a given list of dictionaries and grouping count.
        """
        num_dicts = len(data)
        num_groups = num_dicts // grouping_count
        remainder = num_dicts % grouping_count
        return num_groups + (1 if remainder > 0 else 0)

    def group_picklists_by_task_count(self):
        '''
        Picklist Grouping
        '''
        sorted_picklist_objects = sorted(self.picklist_objects_list, key=lambda x: x.location.pick_sequence)

        picklist_objs = []
        #Calculates No.of Possible Groups
        total_groups = self.calculate_total_groups(
            sorted_picklist_objects, self.group_replenishment_task_count
        )

        #Getting Incremetal Picklist Number
        self.picklist_number = self.extra_params.get(
            'picklist_number', get_picklist_number(
                self.warehouse, incremental_count=total_groups
            ) + 1
        )
        #Grouping of Data
        grouped_result_generator = self.group_dicts_sequentially(
           sorted_picklist_objects, self.group_replenishment_task_count
        )

        for picklist_group in grouped_result_generator:
            for picklist_obj in picklist_group:
                picklist_obj.picklist_number = self.picklist_number
                picklist_objs.append(picklist_obj)

            self.picklist_numbers.append(self.picklist_number)
            self.picklist_number = self.picklist_number + 1

        #Bulk Creation of Picklist Objects
        picklist_created_objects = Picklist.objects.bulk_create_with_rounding(picklist_objs, batch_size=500)
        picklist_ids = []
        for picklist in picklist_created_objects:
            picklist_ids.append(picklist.id)
            self.picklist_callback_data.add(picklist.reference_number)

        self.create_task_master_data(picklist_ids)
        self.generated_picklist_numbers.append(self.picklist_number)

    def insert_picklist_details(self):
        '''
        Picklist Insertion
        '''
        self.picklist_numbers = []
        if self.picklist_objects_list:
            if self.group_replenishment_task_count > 0 and self.extra_params.get('order_type') == 'BA_TO_SA' and len(self.picklist_objects_list) > 1:
                self.group_picklists_by_task_count()
            else:
                self.picklist_objects_list, error = self.get_picklist_number_based_on_pick_type()
                if error:
                    self.errors.append(error)
                    self.update_error_order_picked_quantity()
                    return
                picklist_created_objects = Picklist.objects.bulk_create_with_rounding(
                    self.picklist_objects_list, batch_size=500)
                picklist_ids = []
                for picklist in picklist_created_objects:
                    picklist_ids.append(picklist.id)
                    self.picklist_callback_data.add(picklist.reference_number)
                self.create_task_master_data(picklist_ids)

    def update_error_order_picked_quantity(self):
        '''
        Update order picked quantity
        '''
        if self.reference_id_picklist_details:
            for reference_id, picked_quantity in self.reference_id_picklist_details.items():
                self.reference_id_picklist_details[reference_id] -= picked_quantity
                self.reference_id_picklist_details[reference_id] = max(self.reference_id_picklist_details[reference_id], 0)
                
    def create_task_master_data(self, picklist_ids):
        '''
        Create task master data
        '''

        # Fetching the original task priority for the alternative picklist generation
        self.task_priority = 2
        if self.extra_params.get('picklist_generation_type') == 'alternative' and self.switch_values.get('override_picklist_priority') == 'true' and self.extra_params.get('original_picklist_id'):
            self.task_priority = TaskMaster.objects.filter(task_ref_id=self.extra_params.get('original_picklist_id'), task_ref_type='Picklist', warehouse_id=self.warehouse.id).values_list('task_priority', flat=True).first() or 2

        created_picklist_objects = list(Picklist.objects.filter(user=self.warehouse.id, picklist_number__in=self.picklist_numbers, status='open', id__in=picklist_ids).
                                        values('id', 'order_type', 'user', 'picklist_number', 'order__trip_id', 'order__forward_time',
                                               'order__shipment_date', 'order__creation_date', 'order__estimated_dispatch_time',
                                               'location__pick_sequence', 'location__zone__zone', 'location__sub_zone__zone', 'order'))
        task_master_data = []
        for picklist in created_picklist_objects:
            task_master_data = self.prepare_task_data(picklist, picklist['order'], self.switch_values.get(
                'picklist_priority', 'false'), task_master_data)

        task_master_data_list = []
        lmsobject = lmstasks1()
        for created_task in task_master_data:
            if self.extra_params.get('picklist_generation_type') == 'alternative':
                created_task_obj = TaskMaster.objects.create(**created_task)
                if self.employee:
                    lmsobject.assign_related_tasks_to_user(created_task_obj.id, self.employee)
            else:
                task_master_data_list.append(TaskMaster(**created_task))
        if task_master_data_list:
            TaskMaster.objects.bulk_create(task_master_data_list)

    def prepare_task_data(self, picklist, order, picklist_priority, task_master_data):
        '''
        Prepare task master data creation
        '''
        if order:
            if picklist_priority in ['trip_id', 'od_creation_time']:
                priority = picklist['order__forward_time']
                task_eta = picklist['order__creation_date']
            elif picklist_priority == 'picklist_generation':
                priority = picklist['order__forward_time']
                task_eta = datetime.now()
            elif picklist_priority == 'exp_delivery':
                priority = picklist['order__forward_time']
                task_eta = picklist['order__shipment_date']
            else:
                priority = picklist['order__forward_time']
                task_eta = picklist['order__estimated_dispatch_time']
        else:
            priority = picklist['location__pick_sequence']
            task_eta = None
        zone = picklist['location__zone__zone']
        if self.switch_values.get('user_sub_zone_mapping') == 'true' and picklist['location__sub_zone__zone']:
            zone = picklist['location__sub_zone__zone']
        if not zone:
            zone = picklist['location__zone__zone']
        task_master_data.append({
            "warehouse_id": picklist['user'],
            "task_ref_type": 'Picklist',
            "task_ref_id": picklist['id'],
            "group_type": zone,
            "order_type": picklist['order_type'],
            "reference_number": picklist['picklist_number'],
            "priority": priority,
            "task_priority": self.task_priority,
            "eta": task_eta
        })
        return task_master_data
    
    def set_cache_ids(self, reference_numbers=None):
        ''' Set Cache Ids'''
        try:
            reference_numbers = reference_numbers or []
            for reference_number in reference_numbers:
                cache_status = cache.add(reference_number, "True", timeout=300)
                if not cache_status:
                    self.errors.append("Picklist Generation is in-progress, please try again! Reference Number: "+ str(reference_number))
        except Exception:
            log.debug(traceback.format_exc())
            self.errors.append("Picklist Generation is in-progress, please try again!")
    
    def delete_cache_ids(self, reference_numbers=None):
        ''' Delete Cache Ids'''
        try:
            reference_numbers = reference_numbers or []
            for reference_number in reference_numbers:
                cache.delete(reference_number)
        except Exception:
            log.debug(traceback.format_exc())

    def prepare_dropship_picklist_generation_data(self, order_data):
        """
        Prepares the data for dropship picklist generation.

        Args:
            order_data (list): List of order data.

        Returns:
            tuple: A tuple containing the following:
                - is_dropship_order (bool): Indicates if the order is a dropship order.
                - picklist_number (str): The generated picklist number for the dropship order.
        """
        is_dropship_order = False
        picklist_number = None
        error_status = ''

        order_data = order_data[0]
        # Fetch order classification based on order type
        order_classification_dict = get_order_type_classification(self.warehouse, [order_data.get('reference_type', '')])
        order_classification = order_classification_dict.get(order_data.get('reference_type', ''), '')

        if order_classification == 'DROPSHIP':
            is_dropship_order = True
            order_ids = set(item.get('id') for item in order_data.get('items', []))
            order_detail_objs = OrderDetail.objects.filter(id__in=order_ids)
            # Generate picklist for dropship order
            try:
                error_status, picklist_number = drop_ship_generate_picklist({}, order_detail_objs, self.switch_values, self.warehouse)
            except Exception as e:
                log.debug(traceback.format_exc())
                log.info(f"Failed to generate dropship picklist for order {order_data.get('order_reference', '')}: {str(e)}")
                self.errors.append('Failed to generate dropship picklist')
            if error_status and isinstance(error_status, list):
                self.errors.append('Partial picklist generated')
            elif error_status:
                self.errors.append(error_status)

        return is_dropship_order, picklist_number
    
    
    def picklist_generation_callback(self, order_references):
        if not order_references:
            return
        filters = {
            "order_references" : list(order_references),
            "sku_codes" : list(self.pick_sku_codes),
            "zones_data" : self.sku_zones_dict
        }
        webhook_integration_3p(self.warehouse.id, "picklist_generation", filters=filters)


class ClusterPicklist:
    def __init__(self, warehouse, picklist_objects_list, extra_params):
        self.warehouse = warehouse
        self.picklist_objects_list = picklist_objects_list
        self.extra_params = extra_params

    def picklist_clustering_process(self):
        '''
        Perform the picklist clustering process.

        This method generates picklists based on cluster configurations and order SKU count details.

        Returns:
            picklist_numbers (list): List of picklist numbers generated.
            picklist_objects_list (list): List of picklist objects generated.
            error_message (str): Error message, if any.
        '''
        self.picklist_numbers = []
        self.order_skus_count_details = self.extra_params.get('order_skus_count_details', {})
        self.order_quantity_count_details = self.extra_params.get('order_quantity_count_details', {})
        self.trip_details = self.extra_params.get('trip_details', {})
        self.route_details = self.extra_params.get('route_details', {})

        self.preparing_cluster_config()
        cluster_log.info(f"Cluster configuration details for warehouse {self.warehouse} : {self.custer_config_details}")
        if not self.custer_config_details:
            return [], [], "Cluster configuration is mandatory for cluster picking!"

        try:
            self.cluster_picklist_generation()
        except Exception as e:
            cluster_log.debug(traceback.format_exc())
            cluster_log.info(f"Error in cluster picklist generation: {e}")
            return [], [], "Error in cluster picklist generation!"
        return self.picklist_numbers, self.picklist_objects_list, ""

    def preparing_cluster_config(self):
        """
        Prepares the cluster configuration details for picklist generation.

        This method retrieves the cluster configuration data for the warehouse and
        populates the `custer_config_details` dictionary with the necessary information
        for each cluster. It also calls the `prepare_sub_cluster_config` method to
        prepare the sub-cluster configuration details.

        Returns:
            None
        """
        self.custer_config_details, self.criteria = {}, None
        cluster_config = ClusterMixin(self.warehouse)
        cluster_data = list(cluster_config.get_cluster_config())
        for cluster_record in cluster_data:
            self.criteria = cluster_record.get('criteria', '')
            if self.criteria in ['order_skus_count', 'order_quantity_count']:
                range_start = cluster_record.get('range_from', 0)
                range_end = cluster_record.get('range_to', 0)
                cluster_unique_key = (range_start, range_end)
            elif self.criteria in ['route', 'trip']:
                cluster_unique_key = cluster_record.get('name', '')

            if cluster_unique_key not in self.custer_config_details:
                self.custer_config_details[cluster_unique_key] = {
                    "cluster_name": cluster_record.get('name', ''),
                    "count": 0,
                    "sub_cluster_count": 0,
                    "max_count": cluster_record.get('orders_in_picklist', 0),
                    "lpn_type": cluster_record.get('lpn_type', ''),
                    "picklist_number": None,
                    "order_reference_list": [],
                    "sub_clusters": {}
                }
            self.prepare_sub_cluster_config(cluster_record, cluster_unique_key)

    def prepare_sub_cluster_config(self, cluster_record, cluster_unique_key):
        """
        Prepare the sub-cluster configuration based on the cluster record and unique key.

        Args:
            cluster_record (dict): The cluster record containing sub-cluster data.
            cluster_unique_key (str): The unique key for the cluster.

        Returns:
            None
        """
        sub_cluster_data = list(cluster_record.get('subclusters', []))
        for sub_cluster_record in sub_cluster_data:
            no_of_orders = sub_cluster_record.get('orders_in_lpn', 0)
            start_from = self.custer_config_details.get(cluster_unique_key, {}).get('sub_cluster_count', 0)
            end_at = start_from + no_of_orders
            self.custer_config_details[cluster_unique_key]['sub_cluster_count'] = end_at
            self.custer_config_details[cluster_unique_key]['sub_clusters'][(start_from, end_at)] = {
                "name": sub_cluster_record.get('name', ''),
                "lpn_type": sub_cluster_record.get('lpn_type', ''),
                "id": sub_cluster_record.get('id', '')
            }

    def cluster_picklist_generation(self):
        """
        Generates picklists based on the specified criteria.

        If the criteria is 'order_skus_count', it generates picklists for each cluster based on the order SKU count.
        If the criteria is 'route' or 'trip', it generates picklists for each cluster based on the route or trip.

        """
        if self.criteria == 'order_skus_count':
            self.picklist_generation_for_order_sku_count_wise_cluster()
        elif self.criteria in ['route', 'trip']:
            self.picklist_generation_for_route_trip_wise_cluster()
        elif self.criteria == 'order_quantity_count':
            self.picklist_generation_for_order_sku_count_wise_cluster(order_quantity_count = True)

    def picklist_generation_for_route_trip_wise_cluster(self):
        """
        Generates picklists for route or trip wise clusters.

        This method generates picklists based on the criteria of either route or trip. It assigns picklist numbers, sub-picklist numbers, and classifications to the picklist objects. It also updates the JSON data of the picklist objects with cluster details.

        Returns:
            None
        """
        order_reference_picklist_details = {}
        unique_cluster_classification = {}

        cluster_details = list(self.custer_config_details.values())[0]
        for picklist_object in self.picklist_objects_list:
            reference_number = picklist_object.reference_number
            if order_reference_picklist_details.get(reference_number):
                order_picklist_details = order_reference_picklist_details.get(reference_number)
                picklist_object.picklist_number = order_picklist_details.get('picklist_number')
                picklist_object.sub_picklist_number = order_picklist_details.get('sub_picklist_number')
                picklist_object.classification = order_picklist_details.get('classification')
                picklist_object.json_data = order_picklist_details.get('json_data')
                continue

            unique_cluster_id = ''
            if self.criteria == 'route':
                unique_cluster_id = self.route_details.get(reference_number, '')
            elif self.criteria == 'trip':
                unique_cluster_id = self.trip_details.get(reference_number, '')

            if not unique_cluster_classification.get(unique_cluster_id):
                unique_cluster_classification[unique_cluster_id] = {
                    'count': 0,
                    'order_reference_list': []
                }

            if unique_cluster_classification[unique_cluster_id]['count'] == 0:
                unique_cluster_classification[unique_cluster_id]['picklist_number'] = picklist_number = get_picklist_number(self.warehouse) + 1
                unique_cluster_classification[unique_cluster_id]['sub_picklist_number'] = sub_picklist_number = get_picklist_number(self.warehouse, picklist_type='sub_picklist') + 1
                self.picklist_numbers.append(picklist_number)
            else:
                picklist_number = unique_cluster_classification[unique_cluster_id]['picklist_number']
                sub_picklist_number = unique_cluster_classification[unique_cluster_id]['sub_picklist_number']

            if reference_number not in unique_cluster_classification[unique_cluster_id]['order_reference_list']:
                unique_cluster_classification[unique_cluster_id]['order_reference_list'].append(reference_number)
                unique_cluster_classification[unique_cluster_id]['count'] += 1

            picklist_object.picklist_number = picklist_number
            picklist_object.sub_picklist_number = sub_picklist_number
            picklist_object.classification = unique_cluster_id
            picklist_json_data = picklist_object.json_data
            picklist_json_data.update({
                'sub_cluster_name': ' '.join([self.criteria, ':', str(unique_cluster_id)]),
                'sub_cluster_lpn_type': cluster_details['lpn_type'],
                'cluster_name': cluster_details.get('cluster_name', '')
            })
            picklist_object.json_data = picklist_json_data

            if unique_cluster_classification[unique_cluster_id]['count'] == cluster_details['max_count']:
                unique_cluster_classification[unique_cluster_id]['count'] = 0
                unique_cluster_classification[unique_cluster_id]['order_reference_list'] = []

            if not order_reference_picklist_details.get(reference_number):
                order_reference_picklist_details[reference_number] = {
                    'picklist_number': picklist_number,
                    'sub_picklist_number': sub_picklist_number,
                    'classification': unique_cluster_id,
                    'json_data': picklist_json_data
                }

    def picklist_generation_for_order_sku_count_wise_cluster(self, order_quantity_count = False):
        '''
        Cluster picklist generation

        This method generates cluster picklists based on the number of order SKUs or total order quantity based on the criteria.
        It iterates through the list of picklist objects and assigns them to the appropriate cluster based on the order SKUs count.
        The picklist number and cluster details are updated accordingly.
        Sub-cluster details are also added to the picklist JSON data if applicable.
        '''
        order_reference_picklist_details, order_sub_cluster_details = {}, {}
        self.all_picklist_number, self.all_sub_picklist_number = '', ''
        for picklist_object in self.picklist_objects_list:
            reference_number = picklist_object.reference_number
            order_skus_count = (self.order_quantity_count_details if order_quantity_count else self.order_skus_count_details).get(reference_number, 0)
            is_picklist_assigned = False
            for cluster_range, cluster_details in self.custer_config_details.items():
                if cluster_range[0] <= order_skus_count <= cluster_range[1]:
                    is_picklist_assigned = True
                    if order_reference_picklist_details.get(reference_number):
                        picklist_number = order_reference_picklist_details.get(reference_number)
                    elif cluster_details['count'] == 0:
                        cluster_details['picklist_number'] = picklist_number = get_picklist_number(self.warehouse) + 1
                        cluster_details['sub_picklist_number'] = sub_picklist_number = get_picklist_number(self.warehouse, picklist_type='sub_picklist') + 1
                        self.picklist_numbers.append(picklist_number)
                    else:
                        picklist_number = cluster_details['picklist_number']
                        sub_picklist_number = cluster_details['sub_picklist_number']

                    if not order_reference_picklist_details.get(reference_number) and reference_number not in cluster_details['order_reference_list']:
                        cluster_details['order_reference_list'].append(reference_number)
                        order_reference_picklist_details[reference_number] = picklist_number
                        cluster_details['count'] += 1

                    picklist_object.picklist_number = picklist_number
                    picklist_object.sub_picklist_number = sub_picklist_number
                    picklist_object, order_sub_cluster_details = self.assign_sub_cluster_details(reference_number, cluster_details['count'], cluster_details, order_sub_cluster_details, picklist_object)

                    if cluster_details['count'] == cluster_details['max_count']:
                        cluster_details.update({
                            "count": 0,
                            "picklist_number": None
                        })
                    break
            if not is_picklist_assigned:
                if not self.all_picklist_number:
                    self.all_picklist_number = get_picklist_number(self.warehouse) + 1
                    self.all_sub_picklist_number = get_picklist_number(self.warehouse, picklist_type='sub_picklist') + 1
                    self.picklist_numbers.append(self.all_picklist_number)
                picklist_object.picklist_number = self.all_picklist_number

    def assign_sub_cluster_details(self, reference_number, count, cluster_details, order_sub_cluster_details, picklist_object):
        """
        Assigns sub-cluster details to the picklist object based on the reference number, count, and cluster details.

        Args:
            reference_number (str): The reference number associated with the picklist.
            count (int): The count of picklists.
            cluster_details (dict): The details of the cluster.
            order_sub_cluster_details (dict): The dictionary containing the sub-cluster details for each reference number.
            picklist_object (Picklist): The picklist object to be updated.

        Returns:
            tuple: A tuple containing the updated picklist object and the updated order_sub_cluster_details dictionary.
        """
        picklist_json_data = picklist_object.json_data
        picklist_json_data['cluster_name'] = cluster_details['cluster_name']

        if order_sub_cluster_details.get(reference_number):
            sub_cluster_lpn_type = order_sub_cluster_details.get(reference_number)
        else:
            sub_cluster_lpn_type = self.get_sub_cluster_details(count, cluster_details.get("sub_clusters", {}))

        if sub_cluster_lpn_type:
            order_sub_cluster_details[reference_number] = sub_cluster_lpn_type
            picklist_json_data.update({
                'sub_cluster_name': sub_cluster_lpn_type.get('name', ''),
                'sub_cluster_lpn_type': sub_cluster_lpn_type.get('lpn_type', ''),
                'cluster_name': cluster_details.get('cluster_name', '')
            })
            picklist_object.classification = str(sub_cluster_lpn_type.get('id', ''))
        picklist_object.json_data = picklist_json_data

        return picklist_object, order_sub_cluster_details


    def get_sub_cluster_details(self, current_order_count, sub_cluster_dict):
        '''
        Get sub cluster details based on the current order count.

        Args:
            current_order_count (int): The current order count.
            sub_cluster_dict (dict): A dictionary containing sub cluster ranges as keys and sub cluster LPN types as values.

        Returns:
            str or None: The sub cluster LPN type if the current order count falls within any of the sub cluster ranges, 
            otherwise None.
        '''
        for sub_cluster_range, sub_cluster_lpn_type in sub_cluster_dict.items():
            if sub_cluster_range[0] <= current_order_count <= sub_cluster_range[1]:
                return sub_cluster_lpn_type
        return None

