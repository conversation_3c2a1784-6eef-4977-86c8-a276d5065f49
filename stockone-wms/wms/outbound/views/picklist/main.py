#package imports
import traceback
import pandas as pd
from json import loads, dumps
from operator import itemgetter
from datetime import datetime
from collections import defaultdict
from copy import deepcopy

#django imports
from django.http import (
    JsonResponse, HttpResponse
)
from django.db.models import Sum, Q, F
from django.db import transaction
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.cache import cache

#wms imports
from wms_base.wms_utils import init_logger
from wms_base.models import User

#core operations imports
from core.models import UserAttributes, MiscDetailOptions
from core.models.masters import UOMDetail
from core_operations.views.common.main import (
    get_multiple_misc_values, get_warehouse,
    get_user_time_zone, get_local_date_known_timezone, WMSListView,
    get_multiple_misc_options, get_sku_pack_repr, get_misc_value
)
from core_operations.views.common.validation import update_order_header_status
from core_operations.views.integration.integration import webhook_integration_3p

#inventory imports
from inventory.models import SKUPackMaster

#outbound imports
from outbound.models import OrderDetail, Picklist, StockAllocation
from outbound.views.allocation.allocation import AllocationProcess

from .helpers import (
    get_saved_picklist_serial_number, get_picklist_tolerance,
    get_picklist_tolerance_cal_extra_quantity, get_picklist_configurations,
    fetch_serial_data
)
from .constants import PRINT_OUTBOUND_PICKLIST_HEADERS, PICKLIST_GENERATION_EXTRA_FIELDS
from .picklist_generation import PicklistMixin

log = init_logger('logs/picklist.log')
allocation_log = init_logger('logs/allocation.log')


@get_warehouse
def view_picklist(request, warehouse: User):
    show_image, use_imei = 'false', 'false'
    request_data = request.GET
    data_id = request_data.get('data_id', '')
    if not data_id:
        return HttpResponse("Picklist Number mandatory!", status=400)
    pick_group = request_data.get('pick_group', '')
    des_customer_name = request_data.get('customer', '')
    order_reference = request_data.get('order_reference', '')
    timezone = get_user_time_zone(warehouse)
    single_order = ''
    order_status = ''
    slot_from, slot_to = '',''
    picklist_filter = {'picklist_number':data_id}
    if pick_group:
        picklist_filter['sku__pick_group'] = pick_group
    headers = list(PRINT_OUTBOUND_PICKLIST_HEADERS)
    qc_items_qs = UserAttributes.objects.filter(user_id=warehouse.id,
                                                attribute_model='dispatch_qc',
                                                status=1).values_list('attribute_name', flat=True)
    qc_items = list(qc_items_qs)
    misc_dict, headers = get_picklist_misc_details(warehouse, headers)
    split_by_pick_group = misc_dict.get('split_by_pick_group','false')
    merge_picking = misc_dict.get('merge_picking','false')
    picklist_sort_by_sku_sequence = misc_dict.get('picklist_sort_by_sku_sequence','false')
    #dispensing enabled configuration
    dispensing_tolerance_enabled = False
    dispensing_tolerance_enabled_misc = misc_dict.get('dispensing_tolerance_enabled', 'false')
    if dispensing_tolerance_enabled_misc not in ['false', False, '', None]:
        dispensing_tolerance_enabled = True

    courier_name = ''
    data = []
    is_combo_picklist = False
    sku_total_quantities = 0
    pick_filters = {
        'user': warehouse.id,
        'picklist_number': data_id,
        'status': 'open',
        'reserved_quantity__gt': 0
    }
    if split_by_pick_group == 'true':
        pick_filters['sku__pick_group'] = pick_group
    
    picklists_orders_ = Picklist.objects.filter(**pick_filters).order_by("location__pick_sequence")
    if picklists_orders_.filter(classification='combo').exists():
        is_combo_picklist = True
        model_values = [
            'stock__batch_detail__batch_no','stock__batch_detail__manufactured_date','order__json_data__pack_id',
            'stock__batch_detail__mrp','sku__enable_serial_based','pick_type','order__json_data__pack_uom_quantity',
            'stock__batch_detail__expiry_date', 'order_type', 'order__customerordersummary__mrp',
            'sku__sku_code', 'sku__sku_desc', 'sku__sku_size', 'stock__location__location', 'sku__measurement_type',
            'stock__location__zone__zone', 'stock__lpn_number', 'sku__sequence','sku__sku_category',
            'stock__location__pick_sequence', 'picklist_number', 'stock__sku__sku_code', 'reference_id', 'classification',
            'stock__sku__sku_desc', 'status', 'stock__batch_detail__batch_reference', 'sku__dispensing_enabled', 'order__sku__sku_code'
        ]
    else:
        model_values = [
            'stock__batch_detail__batch_no', 'stock__batch_detail__manufactured_date','order__json_data__pack_uom_quantity',
            'stock__batch_detail__mrp', 'pick_type', 'sku__measurement_type',
            'stock__batch_detail__expiry_date', 'order_type','order__customerordersummary__mrp',
            'sku__sku_code', 'sku__sku_desc', 'sku__sku_size', 'stock__location__location', 
            'stock__location__zone__zone', 'sku__sequence','sku__sku_category', 'sku__sku_code',
            'stock__location__pick_sequence', 'picklist_number', 'sku__enable_serial_based',
            'stock__sku__sku_code','stock__sku__sku_desc', 'status', 'stock__lpn_number',
            'stock__batch_detail__batch_reference', 'sku__dispensing_enabled',
            'order__sku__sku_code', 'classification', 'order__json_data__pack_id'
        ]
    if merge_picking == 'false':
        model_values.append('reference_number')
    picklist_orders = picklists_orders_.values(*model_values).distinct().annotate(reserved_quantity=Sum('reserved_quantity'), picklist__row_id = ArrayAgg('id'), stock_ids=ArrayAgg('stock_id'))
    display_pack = False
    app_picklist = False
    pick_obj = picklists_orders_.values('order__order_type','order__trip_id','order__slot_to','order__slot_from','order__creation_date').first()
    sku_codes, sku_measurement_types = [], []
    for pick_order_record in picklist_orders:
        sku_code = pick_order_record.get('sku__sku_code', '')
        measurement_type = pick_order_record.get('sku__measurement_type', '')
        if sku_code and sku_code not in sku_codes:
            sku_codes.append(sku_code)
        if measurement_type and measurement_type not in sku_measurement_types:
            sku_measurement_types.append(measurement_type)
    sku_pack_qtys_objs = list(SKUPackMaster.objects.filter(sku__sku_code__in=sku_codes, sku__user=warehouse.id,status=1).order_by('-pack_quantity'). \
                         values('pack_quantity', 'pack_id', 'sku__sku_code','sales_uom', 'sku__measurement_type'))
    sku_pack_dict = {}
    for sku_pack_qtys_obj in sku_pack_qtys_objs:
        sku_pack_dict.setdefault(sku_pack_qtys_obj['sku__sku_code'], [])
        sku_pack_dict[sku_pack_qtys_obj['sku__sku_code']].append({
            'pack_id': sku_pack_qtys_obj['pack_id'],
            'pack_quantity': sku_pack_qtys_obj['pack_quantity'],
            'sales_uom' : sku_pack_qtys_obj['sales_uom']
        })

    serial_skus, pick_ids = set(), []
    picklist_tolerance_type, tolerance_percentage, sku_pack_details = get_picklist_tolerance(warehouse, sku_codes, misc_dict)
    for pick_order in picklist_orders:
        picklist_stock_data, display_pack, app_picklist, serial_skus = get_picklist_stock_details(timezone, pick_order, sku_pack_dict, display_pack, sku_pack_details, tolerance_percentage, picklist_tolerance_type, dispensing_tolerance_enabled, serial_skus)
        order_data = get_order_data(timezone, pick_order, pick_obj, is_combo_picklist)
        picklist_stock_data.update(order_data)

        sku_total_quantities += int(pick_order['reserved_quantity'])
        picklist_ord_ref = pick_order.get('reference_number', '')

        picklist_stock_data.update({
            'order_reference': order_reference,
            'is_combo_picklist': is_combo_picklist,
            'picklist_order_reference':picklist_ord_ref,
        })
        pick_ids.extend(picklist_stock_data['picklist_line_ids'])
        data.append(picklist_stock_data)
    
    serial_numbers_data = fetch_serial_data(warehouse, data_id, pick_ids, serial_skus)
    for picklist_data in data:
        item_key = (picklist_data['sku_code'], picklist_data['location'], picklist_data['batch_number'] or '', picklist_data['carton_no'] or '')
        picklist_data['serial_numbers'] = serial_numbers_data.get(item_key, [])

    order_type, time, date, trip_id = '', '', '', ''
    total_qty = sku_total_quantities
    if data:
        order_type = data[0].get('order_type', '')
        time = data[0].get('time', '')
        date = data[0].get('date', '')
        trip_id = data[0].get('trip_id', '')
    outbound_staging_area = False
    misc_option_keys = ['auto_invoice']
    misc_options = get_multiple_misc_options(misc_option_keys, order_type.lower(), warehouse)
    auto_invoice = misc_options.get('auto_invoice', 'false')
    if misc_dict.get("outbound_staging_area", "false") != 'false' and auto_invoice =='false':
        outbound_staging_area =True
    if data:
        order_count = list(set(map(lambda d: d.get('order_no', ''), data)))
        order_count_len = len(list(filter(lambda x: len(str(x)) > 0, order_count)))
        if order_count_len == 1:
            single_order = str(order_count[0])
        order_status = data[0]['order_status']
    try:
        if picklist_sort_by_sku_sequence == 'false':
            data = sorted(data, key=itemgetter('sequence'))
        else:
            data = sorted(data, key=itemgetter('sku_sequence'))
    except Exception:
        pass

    return HttpResponse(dumps({'data': data, 'picklist_id': data_id,
        'show_image': show_image, 'use_imei': use_imei,'order_reference': order_reference,
                                    'order_status': order_status, 'user': request.user.id,
                                    'single_order': single_order, 'total_qty':total_qty,
                                    'app_picklist':app_picklist , 'serialised_picklist': bool(serial_skus),
                                    'sku_total_quantities': sku_total_quantities, 'courier_name' : courier_name,
                                    'qc_items': qc_items, 'warehouse_id': warehouse.id, 'order_type':order_type,
                                     'slot_from': slot_from, 'slot_to': slot_to,'pick_group': pick_group,
                                    'time':time, 'date':date,'customer_name': des_customer_name, 'trip_id':trip_id,
                                    'display_pack': display_pack,'merge_picking':merge_picking,'outbound_staging_are_flag':outbound_staging_area}))

def get_picklist_misc_details(warehouse, headers):
    misc_types = ['show_image','use_imei','pallet_switch','split_by_pick_group','merge_picking','picklist_sort_by_sku_sequence',
                  'outbound_staging_area', 'dispensing_tolerance_enabled', 'picklist_tolerance_type', 'picklist_tolerance']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    show_image = misc_dict.get('show_image','false')    
    if show_image == 'true':
        headers.insert(0,'Image')
    use_imei = misc_dict.get('use_imei','false')
    if use_imei == 'true':
        headers.insert(-1, 'Serial Number')
    pallet_switch = misc_dict.get('pallet_switch','false')
    if pallet_switch == 'true':
        headers.insert(headers.index('Location') + 1, 'Pallet Code')
    return misc_dict, headers

def get_picklist_stock_details(timezone, picklist_data, sku_pack_dict, display_pack, sku_pack_details, tolerance_percentage, picklist_tolerance_type, dispensing_tolerance_enabled, serial_skus):
    picklist_data_dict = {}
    try:
        manufactured_date = get_local_date_known_timezone(timezone, picklist_data['stock__batch_detail__manufactured_date'], send_date=True)
        picklist_data_dict['manufactured_date'] = datetime.strftime(manufactured_date, "%d/%m/%Y")
    except Exception:
        picklist_data_dict['manufactured_date'] = ''
    try:
        expiry_date = get_local_date_known_timezone(timezone,picklist_data['stock__batch_detail__expiry_date'], send_date=True)
        picklist_data_dict['expiry_date'] = datetime.strftime(expiry_date, "%d/%m/%Y")
    except Exception:
        picklist_data_dict['expiry_date'] = ''

    batchno = picklist_data.get('stock__batch_detail__batch_no', '')
    batch_reference = picklist_data.get('stock__batch_detail__batch_reference', '')
    pick_type = picklist_data.get('pick_type', '')
    app_picklist = False
    if pick_type in ['pack_while_pick', 'sorting', 'cluster_picking', 'pick_and_pass']:
        app_picklist = True

    carton_no = picklist_data['stock__lpn_number'] or ""
    if sku_pack_dict:
        display_pack = True

    pack_repr, pack_id, sku_pack_data_dict = '', '', []
    sales_uom_quantity, sales_uom_enabled = 1, 0
    
    sales_uom_enabled = 1
    if picklist_data.get('order__json_data__pack_uom_quantity') not in [None, '']:
        sales_uom_quantity = picklist_data.get('order__json_data__pack_uom_quantity')
        pack_id = picklist_data.get('order__json_data__pack_id')
        sales_uom_enabled = 1
        if sales_uom_quantity and pack_id:
            sku_pack_data_dict = [{'pack_quantity': sales_uom_quantity, 'pack_id': pack_id}]
    
    if sku_pack_dict.get(picklist_data['sku__sku_code'],[]):
        pack_repr = get_sku_pack_repr(sku_pack_data_dict or sku_pack_dict.get(picklist_data['sku__sku_code'], []), picklist_data['reserved_quantity'])

    base_uom_quantity = picklist_data['reserved_quantity']
    if sales_uom_quantity and picklist_data.get('reserved_quantity', 0):
        picklist_data['reserved_quantity'] = picklist_data['reserved_quantity'] / sales_uom_quantity

    reserved_quantity = picked_quantity = picklist_data.get('reserved_quantity', 0)
    picklist_line_ids= picklist_data.get('picklist__row_id', []) or []
    is_serialized_sku = False
    if picklist_data.get('sku__enable_serial_based',0):
        is_serialized_sku = True
        serial_skus.add(picklist_data['sku__sku_code'])

    picklist_tolerance_quantity, maximum_pick_quantity = get_picklist_tolerance_details(picklist_data, reserved_quantity, sku_pack_details, tolerance_percentage, picklist_tolerance_type, dispensing_tolerance_enabled)

    if sales_uom_quantity:
        maximum_pick_quantity = maximum_pick_quantity / sales_uom_quantity

    picklist_data_dict.update({
        'stock_ids': picklist_data.get('stock_ids', []),
        'batch_number': batchno,
        'batch_reference': batch_reference,
        'sales_uom_quantity' : sales_uom_quantity,
        'base_uom_quantity' : base_uom_quantity,
        'sales_uom_enabled' : sales_uom_enabled,
        'batch_display_key': batch_reference or batchno,
        'wms_code': picklist_data.get('sku__sku_code', ''),
        'order_status': picklist_data.get('status', ''),
        'title': picklist_data.get('sku__sku_desc', ''),
        'sku_size': picklist_data.get('sku__sku_size', ''),
        'mrp': picklist_data.get('stock__batch_detail__mrp', 0),
        'order_mrp': picklist_data.get('order__customerordersummary__mrp', 0),
        'sequence': picklist_data.get('stock__location__pick_sequence'),
        'picklist_number': picklist_data.get('picklist_number', ''),
        'sku_code': picklist_data.get('sku__sku_code', ''),
        'sku_sequence': picklist_data.get('sku__sequence', ''),
        'category': picklist_data.get('sku__sku_category', ''),
        'sku_measurement_type': picklist_data.get('sku__measurement_type', ''),
        'pack_id': pack_id,
        'unfulfilled_quantity': 0,
        'location': picklist_data.get('stock__location__location', 'NO STOCK'),
        'zone': picklist_data.get('stock__location__zone__zone', 'NO STOCK'),
        'pick_type': pick_type,
        'app_picklist': app_picklist,
        'pack_repr': pack_repr,
        'carton_no': carton_no,
        'is_serialized':is_serialized_sku,
        'picked_quantity': picked_quantity,
        'picklist_line_ids':picklist_line_ids,
        'id': picklist_line_ids,
        'picklist_tolerance_quantity': picklist_tolerance_quantity,
        'reserved_quantity': reserved_quantity,
        'maximum_pick_quantity': maximum_pick_quantity,
        'sub_picklist_number': picklist_data.get('sub_picklist_number', '')
    })

    return picklist_data_dict, display_pack, app_picklist, serial_skus

def get_picklist_tolerance_details(picklist_data, reserved_quantity, sku_pack_details, tolerance_percentage, picklist_tolerance_type, dispensing_tolerance_enabled):
    picklist_tolerance_quantity, maximum_pick_quantity = 0, reserved_quantity
    if (picklist_tolerance_type in ['default']) or (not dispensing_tolerance_enabled) or (dispensing_tolerance_enabled and picklist_data.get('sku__dispensing_enabled', 0) in [1] and picklist_tolerance_type in ['pack_size_tolarance']):
        picklist_tolerance_quantity = get_picklist_tolerance_cal_extra_quantity(reserved_quantity, picklist_data['sku__sku_code'], picklist_tolerance_type,
                    tolerance_percentage, sku_pack_details=sku_pack_details, is_total=False)
    maximum_pick_quantity += picklist_tolerance_quantity
    return picklist_tolerance_quantity, maximum_pick_quantity

def get_order_data(timezone, picklist_data, pick_obj, is_combo_picklist):
    order_data_dict = {
        'slot_from': '',
        'slot_to': ''
    }
    try:
        if pick_obj:
            slot_from = pick_obj.get('order__slot_from','')
            slot_to = pick_obj.get('order__slot_to','')
            if slot_from:
                order_data_dict['slot_from'] = get_local_date_known_timezone(timezone, slot_from, True).strftime("%d %b, %I:%M %p")
            if slot_to:
                order_data_dict['slot_to'] = get_local_date_known_timezone(timezone, slot_to, True).strftime("%d %b, %I:%M %p")
    except Exception:
        pass

    parent_sku_code = ''
    if is_combo_picklist and picklist_data['classification']=='combo':
        parent_sku_code = picklist_data['order__sku__sku_code']

    order_data_dict.update({
        'order_id': str(picklist_data.get('order__order_id', '')),
        'order_type': picklist_data.get('order_type',''),
        'trip_id': pick_obj.get('order__trip_id',''),
        'bin_ref': picklist_data.get('order__bestbininfo__bin_number', ''),
        'bin_type': picklist_data.get('order__bestbininfo__bin_type', ''),
        'pallet_code':'',
        'parent_sku_code': parent_sku_code,
    })
    try:
        creation_date = get_local_date_known_timezone(timezone, pick_obj.get('order__creation_date',''), True)
        order_data_dict['date'] = creation_date.strftime("%d/%m/%Y")
        order_data_dict['time'] = creation_date.strftime("%I:%M%p")
    except Exception:
        pass
    return order_data_dict

@get_warehouse
def get_picklist_reasons(request, warehouse: User):
    filters = {
        'misc_detail__user': warehouse.id,
        'misc_detail__misc_type': 'picklist_reasons',
        'status': 1
    }
    picklist_reasons = list(MiscDetailOptions.objects.filter(**filters).values_list('misc_value', flat=True))
    reasons_available = bool(picklist_reasons)
    return HttpResponse(dumps({'picklist_reasons': picklist_reasons,
                                    'reasons_available': reasons_available,
                                    }))


class SOPicklistSet(WMSListView):
    '''
    Class to Generate Picklist For Sale Orders
    '''

    def get_request_data(self):
        """
        Sample Request Payload
            {
                "order_references": [],
                "filters": {
                    "customer_id": "1"
                },
                "pick_type": "",
                "items" : []
            }
        """
        errors = []
        request = self.request
        self.set_user_credientials()
        try:
            request_data = loads(request.body)
        except Exception:
            try:
                request_data = request.POST.get("data")
            except Exception:
                errors = ['Invalid Payload']

        if not request_data:
            errors = ['Invalid Payload']
        return request_data, errors

    def fetch_unique_values_of_request(self):
        '''
        Fetch and prepare unique values from request data
        '''
        self.unique_order_ids,self.unique_line_references = [], []
        self.order_references = self.request_data.get('order_references', [])
        self.filters = self.request_data.get('filters', {})
        self.pick_type = self.request_data.get('pick_type', '')
        self.allocation_type = self.request_data.get('allocation_type', '')
        self.extra_params = self.request_data.get('extra_params', {}) or {}
        self.full_open_order = self.extra_params.get('full_open_order', False)
        self.allocation = bool(self.allocation_type)
        self.skip_allocation = bool(self.request_data.get('skip_allocation'))
        self.extra_params['skip_allocation'] = self.skip_allocation
        self.items = self.request_data.get('items',[])
        if not (self.order_references or self.filters or self.items):
            self.errors.append("Order Reference or Items is Mandatory")

    def fetch_unique_values_of_items(self):
        ''' Fetch unique values of request items '''
        for item in self.items:
            if not (item.get('line_reference') or item.get('id')):
                self.errors.append("Either Line Reference or Order Id is Mandatory")
            else:
                if item.get('id'):
                    self.unique_order_ids.append(item.get('id'))
                if item.get('line_reference'):
                    self.unique_line_references.append(item.get('line_reference'))
            if item.get('quantity',0) <= 0:
                self.errors.append("Quantity should be greater than zero for sku code %s and line reference %s" % (str(item.get('sku_code','')),str(item.get('line_reference'))))
            batch_details = item.get('batch_details', [])
            if batch_details:
                batch_qty = 0
                for batch_detail in batch_details:
                    if not batch_detail.get('location'):
                        self.errors.append("Location is mandatory for sku code %s and line reference %s" % (str(item.get('sku_code','')),str(item.get('line_reference'))))
                        break
                    if batch_detail.get('quantity', 0) <= 0:
                        self.errors.append("Quantity should be greater than zero for sku code %s and line reference %s" % (str(item.get('sku_code','')),str(item.get('line_reference'))))
                        break
                    batch_qty += batch_detail.get('quantity', 0)
                else:
                    if batch_qty and batch_qty != item.get('quantity', 0):
                        self.errors.append("Batch quantity should be equal to item quantity for sku code %s and line reference %s" % (str(item.get('sku_code','')),str(item.get('line_reference'))))
                        break
    
    def get_orders_data(self):
        '''
        Get Order details using request data
        '''
        #preparing filters
        order_filter = {"user": self.warehouse.id}
        if not self.full_open_order:
            if self.extra_params.get('switch_values', {}).get('stock_allocate') == 'true' and not (self.allocation or self.skip_allocation):
                stock_order_filters = {
                    'warehouse_id': self.warehouse.id,
                    'status': 1
                }
                if self.order_references:
                    stock_order_filters['reference_number__in'] = self.order_references
                self.unique_order_ids.extend(list(StockAllocation.objects.filter(**stock_order_filters).values_list('reference_id', flat=True).distinct()))
            else:
                order_filter["status"] = 1
        
        if self.unique_order_ids:
            order_filter['id__in'] = self.unique_order_ids
        if self.unique_line_references:
            order_filter['line_reference__in'] = self.unique_line_references
        if self.order_references:
            order_filter['order_reference__in'] = self.order_references
        elif self.filters:
            if self.filters.get('customer_id'):
                order_filter['customer_id'] = self.filters.get('customer_id')
            if self.filters.get('order_type'):
                order_filter['order_type'] = self.filters.get('order_type')

        order_queryset = OrderDetail.objects.filter(**order_filter).select_related('sku', 'customer_identifier')
        locked_orders = order_queryset.select_for_update(skip_locked=True, of=['self'])
        self.order_detail_objects = locked_orders

        if not self.order_detail_objects and order_queryset.exists():
            self.errors.append('Picklist Generation is in-progress, please try again!')
            
        if self.full_open_order:
            self.order_references = list(order_queryset.values('order_reference').\
                annotate(total_qty = Sum('original_quantity'), open_cancelled_qty=Sum('quantity') + Sum('cancelled_quantity')).\
                filter(total_qty=F('open_cancelled_qty')).values_list('order_reference', flat=True).distinct())
            order_filter['order_reference__in'] = self.order_references
            self.order_detail_objects = OrderDetail.objects.select_related('sku', 'customer_identifier').filter(**order_filter)
        
        if not self.order_detail_objects and not order_queryset.exists():
            self.errors.append('Order Already Processed!')

        if self.items and self.errors:
            return
        #preparing dataframe to generate individual lines of picklist 
        if self.items:
            order_data = order_queryset.values(
                    'id', 'order_id', 'order_id', 'original_order_id', 'customer_id', 'customer_name', 'sku_id', 'quantity', 'original_quantity', 'status', 'shipment_date', 'creation_date', 'trip_id',
                    'cancelled_quantity', 'sku_code', 'mrp', 'unit_price', 'order_type', 'order_reference', 'json_data', 'line_reference', sku_code_=F('sku__sku_code'),sku_id_ = F('sku__id'),
                    batch_number_=F('json_data__batch_number'),location_=F('json_data__location'),zone_=F('json_data__zone'),batch_id_=F('json_data__batch_id'),batch_reference_=F('json_data__batch_reference'),
                    route_id = F('customer_identifier__route__route_id'), pick_and_sort=F('sku__pick_and_sort'))
            if self.allocation:
                order_data = order_data.order_by('id')
            self.order_detail_df = pd.DataFrame(order_data)
            self.order_detail_df['object'] = list(self.order_detail_objects)
        
    def get_generate_picklist_dataframe_conditions(self, item, data_frame_name='self.order_detail_df'):
        '''
        Preparing conditions data using request data
        '''
        DATA_FRAME_NAME = data_frame_name
        FIELD_MAPPING = {
            'id' : 'id',
            'sku_code' : 'sku_code_',
            'line_reference' : 'line_reference',
            'batch_number': 'batch_number_',
            'location': 'location_',
            'zone': 'zone_',
            'batch_id': 'batch_id_',
            'batch_reference': 'batch_reference_'
        }

        data_frame_filter = {}
       
        for field_name, mappend_field in FIELD_MAPPING.items():
            field_value = item.get(field_name, '')
            if field_value:
                if field_name in ['id','batch_id']:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{mappend_field}']"] = f"== {field_value}"
                else:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{mappend_field}']"] = f"== '{field_value}'"

        conditions = [f"({column} {condition})" % {'column': column, 'condition': condition} for column, condition in data_frame_filter.items()]
        return conditions

    def validate_and_prepare_itemlevel_picklist_data(self):
        '''
        Validating and preparing item level generate picklist request data
        '''
        self.order_objects_dict = {}
        order_picklist_data, generate_picklist_data = {}, {}
        for item in self.items:
            #prepare condition to fetch from dataframe
            df_conditions = self.get_generate_picklist_dataframe_conditions(item, data_frame_name='self.order_detail_df')
            
            #validate request data items
            order_reference = item.get('order_reference','')
            sku_code = item.get('sku_code','')
            line_reference = item.get('line_reference','')
            main_order_df = self.order_detail_df[eval('&'.join(df_conditions))]
            if main_order_df.empty:
                self.errors.append('Given Data is invalid for order reference: %s, sku code: %s and line reference: %s' % (str(order_reference),str(sku_code),str(line_reference)))
                return generate_picklist_data
            
            #prepare generate picklist data
            for index,record in self.order_detail_df[eval('&'.join(df_conditions))].iterrows():
                json_data = record.get('json_data',{})
                order_id = record.get('id','')
                order_reference = record.get('order_reference','')

                #validate request quantity
                if item.get('quantity') > record.get('quantity'):
                    self.errors.append('Given Quantity is invalid for order reference: %s, sku code: %s and line reference: %s' % (str(order_reference),str(sku_code),str(line_reference)))
                    return generate_picklist_data

                self.order_objects_dict[order_id] = record.get('object',None)

                if not order_picklist_data.get(order_reference):
                    order_picklist_data[order_reference] = {
                        'reference_number': order_reference,
                        'reference_type': record.get('order_type',''),
                        'customer_id': record.get('customer_id',''),
                        'trip_id': record.get('trip_id',''),
                        'route_id': record.get('route_id',''),
                        'items_count': 0,
                        'items': [],
                    }
                
                order_picklist_data[order_reference]['items_count'] += 1

                item_dict = {
                    'id': order_id,
                    'reference_model': 'OrderDetail',
                    'sku_id': record.get('sku_id_',''),
                    'sku_code': record.get('sku_code_',''),
                    'mrp': record.get('mrp'),
                    'unit_price': record.get('unit_price',''),
                    'pick_and_sort' : record.get('pick_and_sort',0),
                    'original_quantity': 0,
                    'cancelled_quantity': 0,
                }

                batch_details = item.get('batch_details', [])
                if batch_details:
                    order_quantity_flag = True
                    for batch_detail in batch_details:
                        batch_dict = deepcopy(item_dict)
                        for param in PICKLIST_GENERATION_EXTRA_FIELDS:
                            if batch_detail.get(param):
                                batch_dict[param] = batch_detail.get(param)

                        if json_data.get('pack_uom_quantity'):
                            batch_dict['pack_uom_quantity'] = int(json_data['pack_uom_quantity'])
                        
                        batch_dict['quantity'] = batch_detail.get('quantity', 0)
                        if order_quantity_flag:
                            batch_dict['original_quantity'] = record.get('original_quantity', 0)
                            batch_dict['cancelled_quantity'] = record.get('cancelled_quantity', 0)
                            order_quantity_flag = False
                        
                        batch_dict['manual_location'] = True
                        order_picklist_data[order_reference]['items'].append(batch_dict)
                else:
                    item_dict['quantity'] = item.get('quantity', 0)
                    item_dict['original_quantity'] = item.get('original_quantity', 0)
                    item_dict['cancelled_quantity'] = item.get('cancelled_quantity', 0)
                    for param in PICKLIST_GENERATION_EXTRA_FIELDS:
                        if json_data.get(param):
                            item_dict[param] = json_data.get(param)

                    order_picklist_data[order_reference]['items'].append(item_dict)
                break
        
        if self.pick_type == 'dynamic':
            generate_picklist_data = self.prepare_dynamic_picklist_data(order_picklist_data)
        else:
            generate_picklist_data = {
                "data": list(order_picklist_data.values())
            }
            if self.allocation:
                generate_picklist_data['allocation_type'] = self.allocation_type
            else:
                generate_picklist_data['pick_type'] = self.pick_type
        
        return generate_picklist_data

    def preparing_data_for_picklist(self):
        '''
        Preparing generate picklist request data
        '''
        self.order_objects_dict = {}
        order_picklist_data = {}
        sku_codes, pack_ids, sku_pack_dict = [], [], {}

        for order in self.order_detail_objects.iterator(chunk_size=500):
            self.order_objects_dict[order.id] = order

            if not order_picklist_data.get(order.order_reference):
                order_picklist_data[order.order_reference] = {
                    'reference_number': order.order_reference,
                    'reference_type': order.order_type,
                    'customer_id': order.customer_id,
                    'trip_id': order.trip_id,
                    'items_count': 0,
                    'items': [],
                }
                if self.pick_type in ['cluster_picking'] and order.customer_identifier.route:
                    order_picklist_data[order.order_reference]['route_id'] = order.customer_identifier.route.route_id
                
            order_picklist_data[order.order_reference]['items_count'] += 1

            item_dict = {
                'id': order.id,
                'reference_model': 'OrderDetail',
                'sku_id': order.sku.id,
                'sku_code': order.sku.sku_code,
                'quantity': order.quantity,
                'original_quantity': order.original_quantity,
                'cancelled_quantity': order.cancelled_quantity,
                'mrp': order.mrp,
                'unit_price': order.unit_price,
                'order_type' : order.order_type,
                'pick_and_sort' : order.sku.pick_and_sort,
            }
            json_data = order.json_data or {}
            sku_codes.append(order.sku.sku_code)

            for param in PICKLIST_GENERATION_EXTRA_FIELDS:
                if json_data.get(param):
                    item_dict[param] = json_data.get(param)

            if json_data.get('pack_id') and json_data.get('pack_uom_quantity'):
                pack_ids.append(json_data['pack_id'])
                sku_pack_dict[(order.sku.sku_code, json_data['pack_id'])] = json_data['pack_uom_quantity']

            order_picklist_data[order.order_reference]['items'].append(item_dict)
        
        if self.extra_params.get('switch_values', {}).get('enable_sales_uom') == 'true':
            sku_pack_data = SKUPackMaster.objects.filter(sku__sku_code__in=sku_codes, pack_id__in=pack_ids, status=1, sku__user=self.warehouse.id, sales_uom=1).values(
                'sku__sku_code', 'pack_quantity','pack_id')
            sku_pack_data_dict = {}
            for data in sku_pack_data:
                sku_pack_data_dict[(data['sku__sku_code'], data['pack_id'])] = data['pack_quantity']

            for sku_pack_key, pack_quantity in sku_pack_dict.items():
                if sku_pack_data_dict.get(sku_pack_key,0) != pack_quantity:
                    self.errors.append("Pack UOM Quantity %s is inactive/invalid for sku code %s and pack id %s" % (str(pack_quantity), str(sku_pack_key[0]),str(sku_pack_key[1])))
        if self.pick_type == 'dynamic':
            generate_picklist_data = self.prepare_dynamic_picklist_data(order_picklist_data)
        else:
            generate_picklist_data = {
                "data": list(order_picklist_data.values())
            }
            if self.allocation:
                generate_picklist_data['allocation_type'] = self.allocation_type
            else:
                generate_picklist_data['pick_type'] = self.pick_type
        return generate_picklist_data

    def prepare_dynamic_picklist_data(self, order_picklist_data):
        """
        Prepare picklist strategy wise picklist data
        """
        picklist_strategy_key_map = {
            'sorting' : 'pick_and_sort',
            'default' : ''
        }
        picklist_data = defaultdict(dict)
        for order_ref, pick_data in order_picklist_data.items():
            for pick_type in picklist_strategy_key_map:
                picklist_data[pick_type][order_ref] = deepcopy(pick_data)
                picklist_data[pick_type][order_ref]['items'] = []
            for item in pick_data['items']:
                for pick_type, pick_key in picklist_strategy_key_map.items():
                    if pick_type != 'default' and item.get(pick_key):
                        picklist_data[pick_type][order_ref]['items'].append(item)
                        break
                else:
                    picklist_data['default'][order_ref]['items'].append(item)
                
            for pick_type in picklist_strategy_key_map:
                if not picklist_data[pick_type][order_ref].get('items'):
                    picklist_data[pick_type].pop(order_ref)

        final_picklist_data = {}
        for pick_type in picklist_strategy_key_map:
            if picklist_data.get(pick_type):
                final_picklist_data[pick_type] = {
                    "data" : list(picklist_data[pick_type].values()),
                    "pick_type" : pick_type,
                }
        
        return final_picklist_data
    
    def prepare_misc_data(self):
        '''
        Fetch Extra params from request and add misc data
        Validate Trip Id mandatory config
        '''
        picklist_configurations = get_picklist_configurations(self.warehouse)
        self.is_trip_id_mandatory = picklist_configurations.pop('mandate_tripid_for_picklist', 'false')
        self.cancel_open_order_at_picklist_generation = picklist_configurations.get('cancel_open_order_at_picklist_generation', 'false')
        self.extra_params["switch_values"] = picklist_configurations

    def generate_picklist_for_order(self, generate_picklist_data):
        '''
        Generate Picklsit for orders
        '''
        picklist = PicklistMixin(self.user, self.warehouse, generate_picklist_data, is_order=True, extra_params=self.extra_params)
        picklist_response_data = picklist.generate_picklist_process()

        reference_picklist_details, reference_ids, errors, insufficient_stock_order_ids, processed_order_ids = (
            picklist_response_data.get('reference_picklist_details'),
            picklist_response_data.get('reference_ids'),
            picklist_response_data.get('errors'),
            picklist_response_data.get('insufficient_stock_order_ids') or set(),
            picklist_response_data.get('processed_order_ids') or set()
        )
        log.info(f"SO Picklist Generation Completed for Warehouse {self.warehouse.username}, Response Data {picklist_response_data}")
        self.order_detail_updates(reference_picklist_details, reference_ids, insufficient_stock_order_ids, processed_order_ids)
    
        if errors:
            picklist_numbers = picklist_response_data.get('generated_picklist_numbers', [])
            error_response = {"errors": picklist_response_data.get('errors'), "generated_picklist_numbers": picklist_numbers}
            return error_response
        return picklist_response_data
    
    def order_detail_updates(self, reference_picklist_details, reference_ids, insufficient_stock_order_ids, processed_order_ids):
        """
        Update OrderDetail picklist quantity and status
        """
        processed_order_ids = processed_order_ids - set(insufficient_stock_order_ids)
        if processed_order_ids:
            self.update_order_detail(reference_picklist_details, processed_order_ids)
        elif reference_ids:
            self.update_order_allocation_status(reference_ids)
        if insufficient_stock_order_ids:
            self.update_order_id_errors(insufficient_stock_order_ids)

    def update_order_detail(self, reference_picklist_details, processed_order_ids):
        '''
        Update OrderDetail picklist quantity and status
        '''
        full_pick_order_ids, partial_update_orders, order_cancel, cancelled_order_references, sku_codes = set(), [], False, set(), set()
        for order_id, order_obj in self.order_objects_dict.items():
            assigned_quantity = reference_picklist_details.get(order_id, 0) or 0
            cancel_order = order_obj.order_type.lower() in self.cancel_open_order_at_picklist_generation or 'all' in self.cancel_open_order_at_picklist_generation
            if order_obj.quantity == assigned_quantity:
                full_pick_order_ids.add(order_id)
            elif cancel_order and order_id in processed_order_ids:
                order_cancel = True
                order_obj.cancelled_quantity = order_obj.quantity - assigned_quantity
                order_obj.quantity = 0
                if order_obj.cancelled_quantity == order_obj.original_quantity:
                    order_obj.status = 3
                else:
                    order_obj.status = 0
                json_data = order_obj.json_data or {}
                json_data['order_cancellation_reason'] = 'Auto Cancellation due to insufficient stock'
                order_obj.json_data = json_data
                partial_update_orders.append(order_obj)
                cancelled_order_references.add(order_obj.order_reference)
                sku_codes.add(order_obj.sku.sku_code)
            elif assigned_quantity:
                order_obj.quantity -= assigned_quantity
                partial_update_orders.append(order_obj)

        if partial_update_orders:
            update_fields = ['quantity', 'cancelled_quantity', 'status', 'json_data'] if order_cancel else ['quantity']
            OrderDetail.objects.bulk_update_with_rounding(partial_update_orders, update_fields)
        if full_pick_order_ids:
            OrderDetail.objects.filter(id__in=full_pick_order_ids).update(quantity=0, status=0, updation_date=datetime.now())

        update_order_header_status(self.warehouse, self.order_references)

        if cancelled_order_references:
            integration_filters = {
                'order_references': list(cancelled_order_references),
                'sku_codes' : list(sku_codes)
            }
            webhook_integration_3p(self.warehouse.id, "cancel_order", filters=integration_filters)


    def update_order_allocation_status(self, reference_ids):
        '''
        update order detail allocation status
        '''
        OrderDetail.objects.filter(user=self.warehouse.id, id__in=reference_ids, status=20, quantity=0).update(status=0)

    def update_order_id_errors(self, insufficient_stock_order_ids):
        '''
        update order id errors
        '''
        order_detail_objs = OrderDetail.objects.select_related('sku').filter(id__in=insufficient_stock_order_ids, user=self.warehouse.id)
        order_cancel, cancelled_orders, sku_codes = False, set(), set()
        for order_detail_obj in order_detail_objs:
            json_data = order_detail_obj.json_data or {}
            json_data['insufficient_stock'] = True
            order_detail_obj.json_data = json_data
            cancel_order = order_detail_obj.order_type.lower() in self.cancel_open_order_at_picklist_generation or 'all' in self.cancel_open_order_at_picklist_generation
            if cancel_order:
                order_cancel = True
                order_detail_obj.cancelled_quantity = order_detail_obj.quantity
                order_detail_obj.quantity = 0
                order_detail_obj.status = 3 if order_detail_obj.cancelled_quantity == order_detail_obj.original_quantity else 0
                json_data['order_cancellation_reason'] = 'Auto Cancellation due to insufficient stock'
                order_detail_obj.json_data = json_data
                cancelled_orders.add(order_detail_obj.order_reference)
                sku_codes.add(order_detail_obj.sku.sku_code)
        if order_detail_objs:
            update_fields = ['json_data', 'quantity', 'cancelled_quantity', 'status'] if order_cancel else ['json_data']
            OrderDetail.objects.bulk_update(order_detail_objs, update_fields)
        
        if cancelled_orders:
            update_order_header_status(self.warehouse, cancelled_orders)

            integration_filters = {
                'order_references': list(cancelled_orders),
                'sku_codes': list(sku_codes)
            }
            webhook_integration_3p(self.warehouse.id, "cancel_order", filters=integration_filters)

    def generate_allocation_for_order(self, allocation_data):
        allocation = AllocationProcess(self.user.id, self.warehouse.id, self.user, self.warehouse, allocation_data, extra_params=self.extra_params)
        allocation_response_data = allocation.process_allocation()
        allocation_log.info(f"SO Allocation Generation Completed for Warehouse {self.warehouse.username}, Response Data {allocation_response_data}")
        reference_allocation_details = allocation_response_data.get('reference_allocation_details', {})
        self.update_order_status(reference_allocation_details)
        return allocation_response_data
    
    def update_order_status(self, reference_allocation_details):
        """
        This method is used to update the order status.
        """
        update_orders_list, order_wise_quantity = [], defaultdict(int)
        for order_id, order_obj in self.order_objects_dict.items():
            allocated_qty = reference_allocation_details.get(order_id, 0) or 0
            if not allocated_qty:
                continue
            order_obj.quantity = max(order_obj.quantity - allocated_qty, 0)
            if not order_obj.quantity:
                order_obj.status = 20
            update_orders_list.append(order_obj)
            order_wise_quantity[order_obj.order_reference] += allocated_qty
        
        if update_orders_list:
            OrderDetail.objects.bulk_update_with_rounding(update_orders_list, ['quantity', 'status'])
        update_order_header_status(self.warehouse, self.order_references, {"allocation_details": order_wise_quantity})
        
    def validate_trip_id(self):
        '''
         Validating the trip id for the picklist generation
        '''
        if str(self.is_trip_id_mandatory).strip().lower() == 'true':
            for order in self.order_detail_objects:
                if not order.trip_id:
                    self.errors.append('Trip Id is mandatory for generating picklist')
                    break

    def prepare_and_generate_picklist_process(self, request_data, warehouse_id=None, request_user_id = None):
        '''
        start generate picklist process
        '''
        with transaction.atomic():
            self.request_data = request_data
            self.errors = []
            self.order_type_wise_data = {}

            if warehouse_id:
                self.warehouse = User.objects.filter(id=warehouse_id)[0]
            if request_user_id:
                self.user = User.objects.filter(id=request_user_id)[0]

            #fetch unique values of request data
            self.fetch_unique_values_of_request()
            #prepare unique values of items
            self.fetch_unique_values_of_items()
            if self.errors:
                return {'errors': self.errors }, self.errors
            
            self.prepare_misc_data()
            if self.allocation and self.extra_params.get('switch_values', {}).get('stock_allocate') != 'true':
                self.errors.append("Stock Allocation is disabled for this warehouse")
                return {'errors': self.errors }, self.errors

            #fetch order detail data
            self.get_orders_data()
            self.validate_trip_id()
            if self.errors:
                return {'errors': self.errors }, self.errors
                    
            if self.items:
                #validate and prepare data for generating individual lines of picklist
                generate_picklist_data = self.validate_and_prepare_itemlevel_picklist_data()
            else:
                #validate and prepare data for generating full picklist
                generate_picklist_data = self.preparing_data_for_picklist()
            
            
            #handle duplicates
            cache_status = cache.add('Picklist_Generation' + str(self.warehouse.username), "True", timeout=300)
            if not cache_status:
                self.errors.append("Picklist Generation is in-progress, please try again!")

            if self.errors:
                cache.delete('Picklist_Generation' + str(self.warehouse.username))
                return {'errors': self.errors }, self.errors
            
            log.info(f"SO Picklist Generation Started for Warehouse: {self.warehouse.username}, ReqUser: {self.user.username}")

            #prepare picklist response
            if self.allocation:
                allocation_log.info(f"SO Allocation Generation Started for Warehouse: {self.warehouse.username}")
                picklist_response_data = self.generate_allocation_for_order(generate_picklist_data)
                message = "Allocated successfully"
            else:
                log.info(f"SO Picklist Generation Started for Warehouse: {self.warehouse.username}")
                if self.pick_type == 'dynamic':
                    #generate picklist for all pick types
                    picklist_response_data = {}
                    for pick_type, picklist_data in generate_picklist_data.items():
                        response_data = self.generate_picklist_for_order(picklist_data)
                    
                        picklist_response_data.setdefault('errors', []).extend(response_data.get('errors', []))
                        picklist_response_data.setdefault('generated_picklist_numbers', []).extend(response_data.get('generated_picklist_numbers', []))
                        picklist_response_data.setdefault('allocated_orders', []).extend(response_data.get('allocated_orders', []))
                else:
                    picklist_response_data = self.generate_picklist_for_order(generate_picklist_data)
                message = "Picklist generated successfully!"

            errors = picklist_response_data.get('errors', []) or []
            if errors:
                message = ''
            response_data = {
                'errors': errors,
                'message': message
            }
            if self.allocation:
                response_data['allocated_orders'] = picklist_response_data.get('allocated_orders', [])
            else:
                response_data['picklist_numbers'] = picklist_response_data.get('generated_picklist_numbers', [])
            cache.delete('Picklist_Generation' + str(self.warehouse.username))
            return response_data, errors

    def post(self, *args, **kwargs):
        self.request_data, errors = self.get_request_data()
        if errors:
            return JsonResponse({'errors': errors}, status=400)
        try:
            response_data, errors = self.prepare_and_generate_picklist_process(self.request_data)
        except Exception as e:
            cache.delete('Picklist_Generation' + str(self.warehouse.username))
            log.debug(traceback.format_exc())
            log.error(f"SO Picklist Generation Failed for Warehouse: {self.warehouse.username}, Error: {str(e)}")
            return JsonResponse({'errors': ['Picklist Generation Failed!']}, status=400)
        
        if errors:
            status = 400
            if response_data.get('picklist_numbers', ''):
                status = 207
                response_data['message'] = 'Picklist Partially Generated'
            return JsonResponse(response_data, status=status)

        return JsonResponse(response_data, status=200)

    def get_unique_values(self, request_data):
        '''
        Get unique values from request data
        '''
        picklist_numbers, reference_numbers, reference_ids, picklist_internal_ids = [], [], [], []
        unique_values_mapping = {
            'picklist_number': picklist_numbers,
            'reference_number': reference_numbers,
            'reference_id': reference_ids,
            'id': picklist_internal_ids
        }

        if not isinstance(request_data, list):
            self.errors.append('Request data should be in list of objects')
            return

        for picklist in request_data:
            picklist_number = picklist.get('picklist_number')
            if picklist_number and picklist_number not in unique_values_mapping['picklist_number']:
                unique_values_mapping['picklist_number'].append(picklist_number)

            items = picklist.get('items', [])
            for item in items:
                for key, unique_list in unique_values_mapping.items():
                    request_value = item.get(key)
                    if request_value and request_value not in unique_list:
                        unique_list.append(request_value)

        filter_data = self.get_filter_data(picklist_numbers, reference_numbers, reference_ids, picklist_internal_ids)
        return filter_data

    def get_filter_data(self, picklist_numbers, reference_numbers, reference_ids, picklist_internal_ids):
        '''
        Get picklist filter data
        '''
        filter_data = Q(picklist_number__in=picklist_numbers) | Q(reference_number__in=reference_numbers) | Q(reference_id__in=reference_ids) | Q(id__in=picklist_internal_ids)
        return filter_data

    def get_picklist_details(self, request_data):
        '''
        Get picklist details
        '''
        filter_data = self.get_unique_values(request_data)

        picklist_objects = Picklist.objects.filter(filter_data, user_id=self.warehouse.id).order_by('-id')
        self.picklist_df = pd.DataFrame(picklist_objects.values('id', 'picklist_number', 'reference_number', 'reference_id', 'reserved_quantity', 'status', 'json_data'))
        self.picklist_df['object'] = list(picklist_objects)

        if self.picklist_df.empty:
            self.errors.append('No data found!')

    def get_dataframe_conditions(self, item, picklist_number, data_frame_name='self.picklist_df'):
        '''
        Prepare dataframe conditions
        '''
        data_frame_filter = {}
        DATA_FRAME_NAME = data_frame_name

        if picklist_number:
            data_frame_filter = {f"{DATA_FRAME_NAME}['picklist_number']": f"== {picklist_number}", f"{DATA_FRAME_NAME}['status']": "== 'open'"}

        filter_fields = ['reference_id', 'reference_number', 'id']
        for field_name in filter_fields:
            field_value = item.get(field_name)
            if field_value:
                if field_name == 'id':
                    data_frame_filter[f"{DATA_FRAME_NAME}['{field_name}']"] = F"== {field_value}"
                else:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{field_name}']"] = F"== '{field_value}'"

        conditions = [f"({column} {condition})" % {'column': column, 'condition': condition} for column, condition in data_frame_filter.items()]
        return conditions

    def update_aux_data(self, picklist_number, aux_data):
        '''
        Update picklist extra details
        '''
        for index, picklist_record in self.picklist_df.loc[self.picklist_df['picklist_number'] == picklist_number].iterrows():
            picklist_json_data = picklist_record['object'].json_data
            picklist_json_data.update(aux_data)
            picklist_record['object'].json_data = picklist_json_data

    def update_picklist_quantity(self):
        '''
        update picklist quantity
        '''
        for picklist in self.request_data:
            picklist_number = picklist.get('picklist_number', '')

            if picklist_number:
                if isinstance(picklist_number, str) and not picklist_number.isdigit():
                    self.errors.append('Invalid Picklist Number!')
                    return
                picklist_number = int(picklist_number)

            aux_data = picklist.get('aux_data', {}) or {}
            if picklist_number and aux_data:
                self.update_aux_data(picklist_number, aux_data)

            for item in picklist.get('items', []):
                cancelled_quantity = item.get('quantity', 0) or 0

                df_conditions = self.get_dataframe_conditions(item, picklist_number)
                eval_df = self.picklist_df[eval('&'.join(df_conditions))]
                if eval_df.empty:
                    self.errors.append('No data found')
                    break

                self.picklist_items_quantity_updation(eval_df, cancelled_quantity)
                if self.errors:
                    break
        
        self.update_picklist_objects()

    def picklist_items_quantity_updation(self, eval_df, cancelled_quantity):
        '''
        Validating picklist items
        '''
        if eval_df['reserved_quantity'].sum() < cancelled_quantity:
            self.errors.append('Cancelled quantity more than the picklist quantity')
        
        for index, picklist_record in eval_df.iterrows():
            if not picklist_record['reserved_quantity']:
                continue
            remove_quantity = min(picklist_record['reserved_quantity'], cancelled_quantity)

            if self.cancel_picklist:
                remove_quantity = picklist_record['reserved_quantity']
                if remove_quantity > 0:
                    picklist_record['object'].status = 'cancelled' 

            self.picklist_df.loc[self.picklist_df['id'] == picklist_record['id'], 'reserved_quantity'] -= remove_quantity
            picklist_record['object'].reserved_quantity -= remove_quantity

            if picklist_record['object'].reserved_quantity <= 0:
                picklist_record['object'].status = 'cancelled'

            if picklist_record['picklist_number'] not in self.updated_picklist:
                self.updated_picklist.append(picklist_record['picklist_number'])

            cancelled_quantity -= remove_quantity
            if cancelled_quantity <= 0 and not self.cancel_picklist:
                break

            if self.cancel_picklist:
                cancelled_quantity = 0

        if cancelled_quantity > 0:
            self.errors.append('Cancelled quantity more than the picklist quantity')

    def update_picklist_objects(self):
        '''
        Updating picklist objects
        '''
        if self.picklist_df['object'].tolist():
            Picklist.objects.bulk_update_with_rounding(self.picklist_df['object'].tolist(), ['reserved_quantity', 'json_data', 'status'])

    def update_picklist_process(self, request_data, warehouse_id=None, request_user_id = None, cancel_picklist = False):
        '''
        Start picklist updation process
        '''
        self.updated_picklist, self.errors = [], []
        self.request_data = request_data
        self.cancel_picklist = cancel_picklist

        if warehouse_id:
            self.warehouse = User.objects.filter(id=warehouse_id)[0]
        if request_user_id:
            self.user = User.objects.filter(id=request_user_id)[0]

        self.get_picklist_details(request_data)
        if self.errors:
            return {'errors': self.errors}, self.errors

        self.update_picklist_quantity()
        message = ''
        if not self.errors:
            message='Picklist updated successfully'

        return {
            'errors': self.errors,
            'message': message,
            'picklist_numbers': self.updated_picklist,
        }, self.errors

    def put(self, *args, **kwargs):
        self.request_data, errors = self.get_request_data()
        if errors:
            return JsonResponse({'errors': errors}, status=400)

        response_data, errors = self.update_picklist_process(self.request_data)
        if errors:
            return JsonResponse(response_data, status=400)

        return JsonResponse(response_data, status=200)
