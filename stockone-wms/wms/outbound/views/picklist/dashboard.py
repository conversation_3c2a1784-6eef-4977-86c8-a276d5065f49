"""
This module contains the code for fetching picking dashboard data.
"""

# package imports
from collections import defaultdict
from dateutil import parser

# django imports
from django.db.models import Max, F
from django.http import JsonResponse
from django.utils import timezone

# wms imports
from wms.settings.base import reports_database
from wms_base.models import User

# core_operations imports
from core_operations.views.common.main import WMSListView, get_warehouse

# inventory imports
from inventory.models import StockDetail, LocationMaster

# lms imports
from lms.models import TaskMaster, ManualAssignment

# outbound imports
from outbound.models import Picklist, SellerOrderSummary, PickAndPassStrategy, StagingInfo, AuditEntry


class PickingDashboardView(WMSListView):
    """
    This class is used to fetch picking dashboard data.
    """

    def get(self, *args, **kwargs):
        """
        Handles the GET request to fetch picking dashboard data.

        Args:
            args: Variable length argument list.
            kwargs: Arbitrary keyword arguments.

        Returns:
            JsonResponse: The JSON response containing the picking dashboard data.
        """

        self.set_user_credientials()

        self.request_data = self.request.GET.dict()
        self.view_type = self.request_data.get('view_type', 'lpn_view')
        self.lpn_status = self.request_data.get('lpn_status', 'all')

        self.from_date = None
        self.is_count = False

        if self.request_data.get('count') == 'true':
            self.is_count = True
        if self.request_data.get('from_date'):
            self.from_date = timezone.make_aware(parser.parse(self.request_data['from_date']), timezone.get_current_timezone())

        self.total_lpns_count, self.data_count = 0, 0
        self.final_data, self.filtered_picklists = [], []
        self.header_data, self.footer_data, self.picklist_dict, self.picklist_zone_dict = {}, {}, {}, {}
        self.open_lpns_dict, self.dropped_lpns_dict, self.completed_lpns_dict = {}, {}, {}
        self.picklist_number_lpn_map = defaultdict(set)

        self.get_picking_dashboard_data()

        return JsonResponse({
            'data': self.final_data,
            'header_data': self.header_data,
            'footer_data': self.footer_data,
            'data_count': self.data_count,
        })

    def get_picking_dashboard_data(self):
        """
        Retrieves picking dashboard data
        """

        # Fetching picklist data
        self.fetch_picklist_data()
        # Fetching header details
        self.fetch_header_details()

        if self.is_count:
            return

        if self.view_type == 'zone_view':
            # Fetching zone view data
            self.fetch_zone_view_data()
        else:
            # Fetching completed LPNs data
            self.fetch_completed_lpns_data()
            # Fetching dropped LPNs data
            self.fetch_dropped_lpns_data()
            # Fetching open LPNs data
            self.fetch_open_lpns_data()

        for picklist_number, picklist_data in self.picklist_dict.items():
            picklist_number_str = str(picklist_number)
            data_dict = {
                'picklist_number': picklist_number,
                'customer_name': picklist_data['order__customer_name'],
                'customer_id': picklist_data['order__customer_id'],
                'customer_reference': picklist_data['order__customer_identifier__customer_reference'],
                'order_type': picklist_data['order_type'],
                'route_id': picklist_data['order__trip_id'],
                'route_name': picklist_data['order__customer_identifier__route__name'],
                'open_lpns_count': 0,
                'picked_lpns_count': 0,
                'total_lpns_count': 0,
                'total_zones_count': len(self.picklist_zone_dict.get(picklist_number, {}).get('zones', [])),
                'lpn_details': [],
                'zones': self.picklist_zone_dict.get(picklist_number, {}).get('zones', []),
            }

            if self.lpn_status in ['in_progress', 'all']:
                data_dict['open_lpns_count'] = len(self.open_lpns_dict.get(picklist_number_str, []))
                data_dict['total_lpns_count'] += data_dict['open_lpns_count']
                # Adding Open LPN details
                for open_lpn_number in self.open_lpns_dict.get(picklist_number_str, []):
                    data_dict['lpn_details'].append({
                        'lpn_number': open_lpn_number,
                        'status': 'open',
                    })

            if self.lpn_status in ['picked', 'all']:
                data_dict['picked_lpns_count'] = len(self.dropped_lpns_dict.get(picklist_number_str, []))
                data_dict['total_lpns_count'] += data_dict['picked_lpns_count']
                # Adding Dropped LPN details
                for dropped_lpn_number in self.dropped_lpns_dict.get(picklist_number_str, []):
                    data_dict['lpn_details'].append({
                        'lpn_number': dropped_lpn_number,
                        'status': 'picked',
                    })
            
            self.total_lpns_count += data_dict['total_lpns_count']

            self.final_data.append(data_dict)

        # Fetching footer details
        self.fetch_footer_details()


    def fetch_header_details(self):
        """
        Fetches and calculates various header details related to picklists and LPNS.
        """

        if self.from_date:
            from_date = self.from_date
        else:
            from_date = timezone.localtime().replace(hour=0, minute=0, second=0, microsecond=0)

        picklists_count, picking_lpns_count, picked_lpns_count = 0, 0, 0
        open_picklists_count, open_picklists_line_count = 0, 0
        pick_filters, stock_filters, staging_filters = {}, {}, {}

        if 'creation_date__gte' not in self.picklist_filters:
            pick_filters['creation_date__gte'] = from_date
            stock_filters['creation_date__gte'] = from_date
            staging_filters['creation_date__gte'] = from_date
        else:
            stock_filters['creation_date__gte'] = self.picklist_filters['creation_date__gte']
            staging_filters['creation_date__gte'] = self.picklist_filters['creation_date__gte']
        if 'picklist_number__in' in self.picklist_filters:
            stock_filters['transact_number__in'] = self.picklist_filters['picklist_number__in']
            staging_filters['picklist_number__in'] = self.picklist_filters['picklist_number__in']
        elif 'picklist_number' in self.picklist_filters:
            stock_filters['transact_number'] = self.picklist_filters['picklist_number']
            staging_filters['picklist_number'] = self.picklist_filters['picklist_number']

        if self.view_type == 'lpn_view':
            picklists_count = Picklist.objects.using(reports_database).filter(**self.picklist_filters, **pick_filters).exclude(status='cancelled').values('picklist_number').distinct().count()

            picking_lpns = set()
            # Fetching picklist confirmed LPNs
            stock_lpns = dict(StockDetail.objects.using(reports_database)
                .filter(receipt_type='so_picking', quantity__gt=0, sku__user=self.warehouse.id, lpn_number__isnull=False,
                        **stock_filters)
                .values_list('lpn_number', 'json_data__picked_lpn')
                .distinct()
            )
            # Fetching dropped LPNs
            dropped_lpns = set(StagingInfo.objects.using(reports_database)
                .filter(user_id=self.warehouse.id, status=0, carton_no__in=stock_lpns, **staging_filters)
                .values_list('carton_no', flat=True)
                .distinct()
            )
            for lpn_number, picked_lpn in stock_lpns.items():
                if picked_lpn:
                    picking_lpns.add(lpn_number)
                else:
                    dropped_lpns.add(lpn_number)

            if self.lpn_status in ['in_progress', 'all']:
                picking_lpns_count = len(set(picking_lpns) - set(dropped_lpns))
            if self.lpn_status in ['picked', 'all']:
                picked_lpns_count = len(dropped_lpns)

        else:
            if 'status' not in self.picklist_filters:
                pick_filters['status'] = 'open'
            # Fetching open picklists count
            open_picklists_count = Picklist.objects.using(reports_database).filter(**self.picklist_filters, **pick_filters).exclude(status='cancelled').values('picklist_number').distinct().count()
            # Fetching open picklists line count
            open_picklists_line_count = Picklist.objects.using(reports_database).filter(**self.picklist_filters, **pick_filters).exclude(status='cancelled').values('picklist_number', 'sku_id').distinct().count()

        self.header_data = {
            'picklists_count': picklists_count,
            'picking_lpns_count': picking_lpns_count,
            'picked_lpns_count': picked_lpns_count,
            'open_picklists_count': open_picklists_count,
            'open_picklists_line_count': open_picklists_line_count,
        }


    def fetch_footer_details(self):
        """
        Fetches and calculates the footer details for the picklist dashboard.

        The footer details include the total number of picklists and LPNs.
        """

        self.footer_data = {
            'picklists_count': len(self.picklist_dict),
            'total_lpns_count': self.total_lpns_count,
        }


    def fetch_picklist_data(self):
        """
        Fetches picklist data
        """

        page = int(self.request_data.get('page', 1))
        length = int(self.request_data.get('length', 10))

        offset = (int(page) - 1) * int(length)

        filters_mapping = {
            'picklist_number': 'picklist_number',
            'customer_id': 'order__customer_id',
            'customer_name': 'order__customer_name__icontains',
            'customer_reference': 'order__customer_identifier__customer_reference',
            'order_type': 'order_type',
            'order_reference': 'order__order_reference__icontains',
            'route_id': 'order__trip_id',
            'route_name': 'order__customer_identifier__route__name',
            'picklist_strategy': 'pick_type',
            'zone': 'location__zone__zone',
            'sub_zone': 'location__sub_zone__zone',
        }

        picklist_filters = {
            'user': self.warehouse,
            'order__status': 0,
        }

        pick_and_pass_filters = {
            'warehouse': self.warehouse,
            'reference_type__in': ['so_picking', 'lpn_based_picking'],
            'lpn_number__isnull': False,
        }

        for key, value in filters_mapping.items():
            if self.request_data.get(key):
                picklist_filters[value] = self.request_data[key]

        if self.view_type == 'zone_view':
            picklist_filters['status'] = 'open'
        if self.from_date:
            picklist_filters['creation_date__gte'] = self.from_date
        else:
            picklist_filters['creation_date__gte'] = timezone.localtime().replace(hour=0, minute=0, second=0, microsecond=0)

        picklist_numbers = list(Picklist.objects.using(reports_database)
            .filter(**picklist_filters)
            .exclude(status='cancelled')
            .values_list('picklist_number', flat=True)
            .distinct()
        )
        picklist_filters['picklist_number__in'] = picklist_numbers
        pick_and_pass_filters['reference_number__in'] = picklist_numbers

        if 'lpn_status' in self.request_data and self.request_data['lpn_status'] != 'all':
            self.fetch_lpn_status_based_picklists(self.request_data['lpn_status'], picklist_numbers)
            picklist_filters['picklist_number__in'] = self.filtered_picklists
            pick_and_pass_filters['reference_number__in'] = self.filtered_picklists
        if 'lpn_number' in self.request_data:
            self.fetch_filtered_lpn_picklists(self.request_data['lpn_number'], picklist_filters)
            picklist_filters['picklist_number__in'] = self.filtered_picklists
            pick_and_pass_filters['reference_number__in'] = self.filtered_picklists
        if 'picker' in self.request_data:
            self.fetch_filtered_picker_picklists(self.request_data['picker'], picklist_filters)
            picklist_filters['picklist_number__in'] = self.filtered_picklists
            pick_and_pass_filters['reference_number__in'] = self.filtered_picklists

        if self.view_type == 'lpn_view':
            if self.request_data.get('picklist_number'):
                pick_and_pass_filters['reference_number'] = self.request_data['picklist_number']
            if self.request_data.get('sub_zone'):
                pick_and_pass_filters['sub_zone__zone'] = self.request_data['sub_zone']

            picklist_numbers = list(PickAndPassStrategy.objects.using(reports_database)
                .filter(**pick_and_pass_filters)
                .exclude(status__in=['cancelled', 'auto-dropped'])
                .values_list('reference_number', flat=True)
                .distinct()
            )
            picklist_filters['picklist_number__in'] = picklist_numbers

        self.picklist_filters = picklist_filters

        if self.is_count:
            self.data_count = Picklist.objects.using(reports_database).filter(**picklist_filters).exclude(status='cancelled').values('picklist_number').distinct().count()
            return

        # Fetching picklist data based on filters
        picklist_data = list(Picklist.objects.using(reports_database)
            .filter(**picklist_filters)
            .exclude(status='cancelled')
            .values('picklist_number', 'order__customer_name', 'order__customer_id', 'order_type',
                    'order__trip_id', 'order__customer_identifier__route__name',
                    'order__customer_identifier__customer_reference')
            .annotate(picklist_date=Max('updation_date'))
            .order_by('-picklist_date')[offset:offset + length]
        )

        for picklist in picklist_data:
            self.picklist_dict[picklist['picklist_number']] = picklist

    def fetch_zone_view_data(self):
        """
        Fetches and organizes zone view data for picklists.
        Retrieves picklist numbers and their associated zones from the database
        Attributes:
            picklist_zone_dict (dict): A dictionary containing picklist numbers and their associated zones
        """

        pick_filters = {
            'user': self.warehouse,
            'picklist_number__in': self.picklist_dict,
            'status': 'open',
            'reserved_quantity__gt': 0,
        }

        if self.request_data.get('zone'):
            pick_filters['location__zone__zone'] = self.request_data['zone']
        if self.request_data.get('sub_zone'):
            pick_filters['location__sub_zone__zone'] = self.request_data['sub_zone']

        picklist_data = Picklist.objects.using(reports_database).filter(**pick_filters).values('picklist_number', 'location__zone__zone').distinct()

        for data in picklist_data:
            self.picklist_zone_dict.setdefault(data['picklist_number'], {
                'picklist_number': data['picklist_number'],
                'zones': [],
            })
            self.picklist_zone_dict[data['picklist_number']]['zones'].append(data['location__zone__zone'])


    def fetch_lpn_status_based_picklists(self, lpn_status, picklist_numbers):
        """
        Fetches picklists based on the provided LPN (License Plate Number) status.
        Args:
            lpn_status (str): The status of the LPN. Can be 'in_progress', 'picked', or 'invoiced'.
            picklist_numbers (list): A list of picklist numbers to filter.
        Sets:
            self.filtered_picklists (list): A list of picklist numbers filtered based on the LPN status.
        """

        if lpn_status == 'in_progress':
            lpn_scanned_picklists = list(PickAndPassStrategy.objects.using(reports_database)
                .filter(warehouse=self.warehouse, reference_type__in=['so_picking', 'lpn_based_picking'], status='open',
                        lpn_number__isnull=False, reference_number__in=picklist_numbers)
                .values_list('reference_number', flat=True)
                .distinct()
            )
            picklists = list(StockDetail.objects.using(reports_database)
                .filter(receipt_type='so_picking', quantity__gt=0, sku__user=self.warehouse.id,
                        json_data__picked_lpn=True, transact_number__in=picklist_numbers)
                .values_list('transact_number', flat=True)
                .distinct()
            )
            dropped_lpns = list(StagingInfo.objects.using(reports_database)
                .filter(user_id=self.warehouse.id, status=0, picklist_number__in=picklists)
                .values_list('picklist_number', flat=True)
                .distinct()
            )
            self.filtered_picklists = lpn_scanned_picklists + list(set(picklists) - set(dropped_lpns))
        elif lpn_status == 'picked':
            stock_lpns = list(StockDetail.objects.using(reports_database)
                .filter(receipt_type='so_picking', sku__user=self.warehouse.id, lpn_number__isnull=False,
                        transact_number__in=picklist_numbers)
                .exclude(lpn_number='')
                .values('lpn_number', 'transact_number', 'json_data__picked_lpn')
                .distinct()
            )

            packed_picklists, picked_lpns = [], []
            for stock_lpn in stock_lpns:
                if stock_lpn['json_data__picked_lpn']:
                    picked_lpns.append(stock_lpn['lpn_number'])
                else:
                    packed_picklists.append(stock_lpn['transact_number'])

            dropped_picklists = list(StagingInfo.objects.using(reports_database)
                .filter(user_id=self.warehouse.id, carton_no__in=picked_lpns, picklist_number__in=packed_picklists)
                .values_list('picklist_number', flat=True)
                .distinct()
            )
            self.filtered_picklists = packed_picklists + dropped_picklists
        elif lpn_status == 'invoiced':
            self.filtered_picklists = list(SellerOrderSummary.objects.using(reports_database).filter(picklist__user=self.warehouse, lpn_number__gt='', order_status_flag__in=['customer_invoices', 'delivery_challans']).exclude(order_status_flag='cancelled').exclude(lpn_number=None).values_list('picklist__picklist_number', flat=True).distinct())

    def fetch_filtered_lpn_picklists(self, lpn_number, picklist_filters):
        """
        Fetches and filters picklists based on the provided LPN number and picklist filters.

        This method retrieves picklists from the StockDetail and SellerOrderSummary models
        using the specified LPN number and additional filters. It combines the results from
        both models and stores them in the `filtered_picklists` attribute.

        Args:
            lpn_number (str): The LPN (License Plate Number) to filter the picklists.
            picklist_filters (dict): A dictionary containing filters for the picklists. 
            Expected keys include 'picklist_number__in'.

        Returns:
            None: The method updates the `filtered_picklists` attribute with the filtered picklists.
        """

        if picklist_filters.get('picklist_number__in') == []:
            return

        pick_and_pass_filters = {
            'warehouse': self.warehouse,
            'reference_type__in': ['so_picking', 'lpn_based_picking'],
            'status': 'open',
            'lpn_number': lpn_number,
        }
        stock_filters = {
            'lpn_number': lpn_number,
            'receipt_type': 'so_picking',
            'quantity__gt': 0,
            'sku__user': self.warehouse.id,
        }
        sos_filters = {
            'lpn_number': lpn_number,
            'picklist__user': self.warehouse,
            'order_status_flag__in': ['customer_invoices', 'delivery_challans'],
        }

        if picklist_filters.get('picklist_number__in'):
            pick_and_pass_filters['reference_number__in'] = picklist_filters['picklist_number__in']
            stock_filters['transact_number__in'] = picklist_filters['picklist_number__in']
            sos_filters['picklist__picklist_number__in'] = picklist_filters['picklist_number__in']

        lpn_scanned_picklists, picklists, sos_picklists = [], [], []

        if self.lpn_status in ['in_progress', 'all']:
            lpn_scanned_picklists = list(PickAndPassStrategy.objects.using(reports_database)
                .filter(**pick_and_pass_filters)
                .values_list('reference_number', flat=True)
                .distinct()
            )
        if self.lpn_status in ['picked', 'all']:
            picklists = list(StockDetail.objects.using(reports_database).filter(**stock_filters).values_list('transact_number', flat=True).distinct())
        if self.lpn_status in ['invoiced', 'all']:
            sos_picklists = list(SellerOrderSummary.objects.using(reports_database).filter(**sos_filters).exclude(order_status_flag='cancelled').values_list('picklist__picklist_number', flat=True).distinct())

        self.filtered_picklists = lpn_scanned_picklists + picklists + sos_picklists


    def fetch_filtered_picker_picklists(self, picker, picklist_filters):
        """
        Fetches and filters picklists assigned to a specific picker based on provided filters.

        This method retrieves picklists from TaskMaster and ManualAssignment models, 
        filtering them by the given picker and additional picklist filters. The filtered 
        picklists are then combined and stored in the `self.filtered_picklists` attribute.

        Args:
            picker (str): The username of the picker whose picklists are to be fetched.
            picklist_filters (dict): A dictionary containing filters for picklists. 
                Expected keys include:
                - 'picklist_number__in' (list): A list of picklist numbers to filter by.

        Returns:
            None: The method updates the `self.filtered_picklists` attribute with the 
            filtered picklists.
        """

        if picklist_filters.get('picklist_number__in') == []:
            return

        task_filters = {
            'warehouse': self.warehouse,
            'employee__user__username': picker,
            'task_ref_type': 'Picklist',
        }
        manual_task_filters = {
            'warehouse': self.warehouse,
            'employee__user__username': picker,
        }

        if picklist_filters.get('picklist_number__in'):
            task_filters['reference_number__in'] = picklist_filters['picklist_number__in']
            manual_task_filters['reference_number__in'] = picklist_filters['picklist_number__in']

        tasks_picklists = list(TaskMaster.objects.using(reports_database).filter(**task_filters).values_list('reference_number', flat=True).distinct())
        manual_tasks_picklists = list(ManualAssignment.objects.using(reports_database).filter(**manual_task_filters).exclude(status=1).values_list('reference_number', flat=True).distinct())

        self.filtered_picklists = tasks_picklists + manual_tasks_picklists

    def fetch_dropped_lpns_data(self):
        """
        Fetches dropped LPNs data
        """

        staging_info_filters = {
            'user_id': self.warehouse.id,
            'status': 0,
            'picklist_number__in': self.picklist_dict,
        }

        stock_filters = {
            'transact_number__in': self.picklist_dict,
            'receipt_type': 'so_picking',
            'lpn_number__isnull': False,
            'sku__user': self.warehouse.id,
        }

        if self.request_data.get('lpn_number'):
            staging_info_filters['carton_no'] = self.request_data['lpn_number']
            stock_filters['lpn_number'] = self.request_data['lpn_number']

        dropped_lpns_data = list(StagingInfo.objects.using(reports_database)
            .filter(**staging_info_filters)
            .values('picklist_number', 'carton_no')
            .distinct()
        )
        stock_lpns = list(StockDetail.objects.using(reports_database)
            .filter(**stock_filters)
            .exclude(lpn_number='')
            .values_list('lpn_number', flat=True)
            .distinct()
        )

        for dropped_lpn in dropped_lpns_data:
            if dropped_lpn['carton_no'] not in stock_lpns:
                continue
            picklist_number = str(dropped_lpn['picklist_number'])
            if dropped_lpn['carton_no'] in self.completed_lpns_dict.get(picklist_number, []):
                continue
            self.dropped_lpns_dict.setdefault(picklist_number, []).append(dropped_lpn['carton_no'])

    def fetch_open_lpns_data(self):
        """
        Fetches open LPNs data
        """

        pick_and_pass_filters = {
            'warehouse': self.warehouse,
            'reference_number__in': self.picklist_dict,
            'reference_type__in': ['so_picking', 'lpn_based_picking'],
            'status': 'open',
            'lpn_number__isnull': False,
        }

        stock_filters = {
            'transact_number__in': self.picklist_dict,
            'receipt_type': 'so_picking',
            'quantity__gt': 0,
            'lpn_number__isnull': False,
            'sku__user': self.warehouse.id,
        }

        if self.request_data.get('lpn_number'):
            pick_and_pass_filters['lpn_number'] = self.request_data['lpn_number']
            stock_filters['lpn_number'] = self.request_data['lpn_number']

        # Fetching scanned LPNs data
        scanned_lpns_data = list(PickAndPassStrategy.objects.using(reports_database)
            .filter(**pick_and_pass_filters)
            .values('reference_number', 'lpn_number')
            .distinct()
        )
        for scanned_lpn in scanned_lpns_data:
            self.open_lpns_dict.setdefault(scanned_lpn['reference_number'], []).append(scanned_lpn['lpn_number'])

        open_lpns_data = StockDetail.objects.using(reports_database).filter(**stock_filters).exclude(lpn_number='').values('transact_number', 'lpn_number', 'json_data__picked_lpn').distinct()
        for open_lpn in open_lpns_data:
            picklist_number = str(open_lpn['transact_number'])
            if open_lpn['lpn_number'] in self.completed_lpns_dict.get(picklist_number, []):
                continue
            if open_lpn['lpn_number'] in self.dropped_lpns_dict.get(picklist_number, []):
                continue
            if open_lpn['lpn_number'] in self.open_lpns_dict.get(picklist_number, []):
                continue
            if not open_lpn['json_data__picked_lpn']:
                self.dropped_lpns_dict.setdefault(picklist_number, []).append(open_lpn['lpn_number'])
                continue
            self.open_lpns_dict.setdefault(picklist_number, []).append(open_lpn['lpn_number'])

    def fetch_completed_lpns_data(self):
        """
        Fetches completed LPNs data
        """

        sos_filters = {
            'picklist__user': self.warehouse,
            'picklist__picklist_number__in': self.picklist_dict,
            'lpn_number__isnull': False,
            'order_status_flag__in': ['customer_invoices', 'delivery_challans'],
        }

        if self.request_data.get('lpn_number'):
            sos_filters['lpn_number'] = self.request_data['lpn_number']

        completed_lpns_data = list(SellerOrderSummary.objects.using(reports_database)
            .filter(**sos_filters)
            .exclude(lpn_number='')
            .values('picklist__picklist_number', 'lpn_number')
            .distinct()
        )
        for completed_lpn in completed_lpns_data:
            picklist_number = str(completed_lpn['picklist__picklist_number'])
            self.completed_lpns_dict.setdefault(picklist_number, []).append(completed_lpn['lpn_number'])

@get_warehouse
def get_picking_lpn_details(request, warehouse: User):
    """
    This function is used to fetch picking LPN details.
    """
    lpn_number = request.GET.get('lpn_number')
    picklist_number = request.GET.get('picklist_number')
    try:
        picklist_number = int(picklist_number)
    except Exception:
        return JsonResponse({'error': 'Invalid Picklist Number.'}, status=400)
    if not lpn_number or not picklist_number:
        return JsonResponse({'error': 'LPN number and Picklist Number is required.'}, status=400)
    pick_and_pass_objs = PickAndPassStrategy.objects.filter(warehouse_id=warehouse.id, reference_type__in=['so_picking', 'lpn_based_picking'], lpn_number=lpn_number, reference_number=picklist_number).exclude(status__in=['cancelled', 'auto-dropped']).order_by('-creation_date').values('status', 'lpn_status', 'reference_number', 'lpn_number', 'sub_zone__zone', 'employee__user__username')
    if not pick_and_pass_objs:
        # Derive LPN status
        status = 'packed'
        invoiced_lpn = SellerOrderSummary.objects.filter(picklist__picklist_number=picklist_number, lpn_number=lpn_number, picklist__user=warehouse.id, order_status_flag__in=['customer_invoices', 'delivery_challans'])
        picked_lpn = StockDetail.objects.filter(transact_number=picklist_number, receipt_type='so_picking', lpn_number=lpn_number, sku__user=warehouse.id, json_data__picked_lpn=True)
        dropped_lpn = StagingInfo.objects.filter(user_id=warehouse.id, carton_no=lpn_number, picklist_number=picklist_number, status=0)
        audit_status = AuditEntry.objects.filter(warehouse_id=warehouse.id, audit_reference=lpn_number, status__in=[1,2]).values_list('status', flat=True).first() or ''
        if audit_status:
            status = 'QC Pending' if audit_status == 1 else 'Pending Resolution'
        elif invoiced_lpn.exists():
            status = 'invoiced'
        elif picked_lpn.exists():
            if dropped_lpn.exists():
                status = 'picked'
            else:
                status = 'in_picking'

        # Frame picked item details
        item_details, unique_skus = get_sos_details(warehouse, picklist_number, lpn_number, status=status)
        final_data = {
            'picklist_number': picklist_number,
            'lpn_number': lpn_number,
            'status': status,
            'skus_count': len(unique_skus),
            'history': [{'items': []}],
        }
        for _, items in item_details.items():
            for item in items:
                final_data["history"][0]["items"].append(item)
        return JsonResponse({'data': final_data})

    pick_and_pass_obj = pick_and_pass_objs[0]
    status_ = pick_and_pass_obj.get('status')
    lpn_status = pick_and_pass_obj.get('lpn_status')
    
    sos_objs = SellerOrderSummary.objects.filter(picklist__user_id=warehouse.id, picklist__picklist_number=picklist_number, lpn_number=lpn_number, order_status_flag__in=['customer_invoices', 'delivery_challans'])
    picked_lpn = StockDetail.objects.filter(transact_number=picklist_number, receipt_type='so_picking', lpn_number=lpn_number, sku__user=warehouse.id, json_data__picked_lpn=True)

    location = ''
    if status_ == 'completed':
        
        staging_objs = StagingInfo.objects.filter(user_id=warehouse.id, carton_no=lpn_number, picklist_number=picklist_number, status=0).values_list('location__location')
        if staging_objs:
            location = staging_objs[0][0]
    audit_status = AuditEntry.objects.filter(warehouse_id=warehouse.id, audit_reference=lpn_number, status__in=[1,2]).values_list('status', flat=True).first() or ''
    if audit_status:
        status = 'QC Pending' if audit_status == 1 else 'Pending Resolution'
    else:
        status = get_lpn_status(sos_objs, location, picked_lpn)
    final_data = {
        'lpn_number': lpn_number,
        'picklist_number': picklist_number,
        'status': status,
        'lpn_status': lpn_status,
        'sub_zone': pick_and_pass_obj.get('sub_zone__zone'),
        'picker': pick_and_pass_obj.get('employee__user__username'),
        'history': [],
    }
    for pick_and_pass_obj in pick_and_pass_objs:
        final_data['history'].append({
            'sub_zone': pick_and_pass_obj.get('sub_zone__zone'),
            'picker': pick_and_pass_obj.get('employee__user__username'),
            'lpn_number': pick_and_pass_obj.get('lpn_number'),
            'pick_and_pass_status': pick_and_pass_obj.get('status'),
        })

    item_details, unique_skus = get_sos_details(warehouse, picklist_number, lpn_number)
    for item in final_data.get('history'):
        unique_key = (item.get('sub_zone'), item.get('picker'), lpn_number)
        item['items'] = item_details.pop(unique_key, [])
        item['transaction'] = get_transaction_status(item['items'], item)

    # Add other LPN items to last history
    for item in item_details.values():
        if final_data.get('history') and final_data['history'][-1].get('picker') is None:
            final_data['history'][-1]['items'].extend(item)
        else:
            final_data['history'].append({
                'lpn_number': lpn_number,
                'sub_zone': '',
                'picker': '',
                'pick_and_pass_status': '',
                'items': item,
            })

    final_data['skus_count'] = len(unique_skus)
    if not location:
        location = item_details.get((final_data.get('sub_zone'), final_data.get('picker')), [{}])[0].get('location', '')
    final_data['location'] = location
    
    return JsonResponse({'data': final_data})

def get_transaction_status(skus, item):
    '''
    return transaction status based on item details
    '''
    if skus:
        return 'pick'
    elif item.get('pick_and_pass_status') in ['open', 'hold']:
        return 'pending'
    else:
        return 'pass'


def get_lpn_status(sos_objs, location, picked_lpn):
    status = 'in_picking'
    if sos_objs.exists():
        status = 'invoiced'
    elif location:
        # Derive Picked LPN or Consolidated LPN
        if picked_lpn.exists():
            status = 'picked'
        else:
            status = 'packed'
    
    return status


def get_sos_details(warehouse, picklist_number, lpn_number, status=None):

    sos_ids = list(StockDetail.objects.filter(lpn_number=lpn_number, receipt_type='so_picking', sku__user=warehouse.id).values_list('receipt_number', flat=True))
    sos_filters = {
        'id__in': sos_ids,
        'picklist__user_id': warehouse.id,
        'picklist__picklist_number': picklist_number,
        'order_status_flag': 'processed_orders'
    }
    if status and status == 'invoiced':
        sos_filters.pop('order_status_flag')
        sos_filters['order_status_flag__in'] = ['customer_invoices', 'delivery_challans']

    sos_details = list(SellerOrderSummary.objects.filter(**sos_filters).values('picklist__picklist_number', 'picklist_id', 'quantity', 'picklist__location__sub_zone__zone', 'picklist__location__location', 'picklist__stock__batch_detail__batch_no', 'picklist__stock__batch_detail__expiry_date', 'picklist__sku__sku_desc', 'picklist__sku__sku_code', 'id'))
    picklist_location_map = {}
    picklist_ids = []
    receipt_numbers = []
    for sos_detail in sos_details:
        picklist_ids.append(sos_detail.get('picklist_id'))
        picklist_location_map[sos_detail.get('picklist_id')] = sos_detail.get('picklist__location__sub_zone__zone')
        receipt_numbers.append(sos_detail.get('id'))

    task_objs = list(TaskMaster.objects.filter(task_ref_type='Picklist', reference_number=picklist_number, task_ref_id__in=picklist_ids, warehouse_id=warehouse.id).values('task_ref_id', 'employee__user__username', 'start_time', 'updation_date', 'group_type'))
    picklist_id_user_map = {}#{task_obj.get('task_ref_id'): task_obj.get('employee__user__username') for task_obj in task_objs}
    picktime = {}
    for task_obj in task_objs:
        picktime[task_obj.get('task_ref_id')] = task_obj.get('updation_date') - task_obj.get('start_time') if task_obj.get('start_time') and task_obj.get('updation_date') else None
        picklist_id_user_map[task_obj.get('task_ref_id')] = task_obj.get('employee__user__username')
    item_details = {}
    unique_skus = set()
    for sos_detail in sos_details:
        picklist_id = sos_detail.get('picklist_id')
        unique_key = (sos_detail.get('picklist__location__sub_zone__zone'), picklist_id_user_map.get(str(picklist_id)), lpn_number)
        sku_code = sos_detail.get('picklist__sku__sku_code') or ''
        batch_no = str(sos_detail.get('picklist__stock__batch_detail__batch_no') or '')
        unique_sku = sku_code + batch_no
        unique_skus.add(unique_sku)
        if unique_key not in item_details:
            item_details[unique_key] = [{
                'sku_desc': sos_detail.get('picklist__sku__sku_desc'),
                'quantity': sos_detail.get('quantity'),
                'location': sos_detail.get('picklist__location__location'),
                'batch_no': sos_detail.get('picklist__stock__batch_detail__batch_no'),
                'expiry_date': sos_detail.get('picklist__stock__batch_detail__expiry_date'),
                'picker': picklist_id_user_map.get(str(picklist_id)),
                'sku_code': sku_code,
                'picktime': picktime.get(str(picklist_id)).total_seconds() if picktime.get(str(picklist_id)) else None,
            }]
        else:
            item_details[unique_key].append({
                'sku_desc': sos_detail.get('picklist__sku__sku_desc'),
                'quantity': sos_detail.get('quantity'),
                'location': sos_detail.get('picklist__location__location'),
                'batch_no': sos_detail.get('picklist__stock__batch_detail__batch_no'),
                'expiry_date': sos_detail.get('picklist__stock__batch_detail__expiry_date'),
                'picker': picklist_id_user_map.get(str(picklist_id)),
                'sku_code': sku_code,
                'picktime': picktime.get(str(picklist_id)).total_seconds() if picktime.get(str(picklist_id)) else None,
            })
    return item_details, unique_skus

@get_warehouse
def get_picklist_sub_zone_details(request, warehouse: User):
    """
    Retrieve details of sub-zones within a specified zone for a given picklist number.
    Args:
        request (HttpRequest): The HTTP request object containing GET parameters.
        warehouse (User): The user object representing the warehouse.
    Returns:
        JsonResponse: A JSON response containing sub-zone details or an error message.
    GET Parameters:
        picklist_number (str): The picklist number to filter the data.
        zone (str): The zone to filter the data.
    """

    request_data = request.GET.dict()
    picklist_number = request_data.get('picklist_number')
    zone = request_data.get('zone')

    if not picklist_number or not zone:
        return JsonResponse({'error': 'Picklist number and zone are required fields.'}, status=400)

    sub_zone_details = []

    # Fetch sub-zones for the specified zone
    sub_zones = dict(LocationMaster.objects.using(reports_database)
        .filter(zone__zone=zone, sub_zone__isnull=False, zone__user=warehouse.id)
        .order_by('sub_zone__get_sequence')
        .values_list('sub_zone__zone', 'sub_zone__get_sequence')
    )

    if not sub_zones:
        return JsonResponse({'data': sub_zone_details})

    picklist_data = list(Picklist.objects.using(reports_database)
        .filter(picklist_number=picklist_number, location__zone__zone=zone, reserved_quantity__gt=0,
                status='open', user=warehouse)
        .values('sku_id', 'location_id', 'stock__batch_detail_id', sub_zone=F('location__sub_zone__zone'))
        .distinct()
    )

    picklist_dict = defaultdict(set)
    for data in picklist_data:
        picklist_dict[data['sub_zone']].add(tuple(data.values()))

    # Prepare sub-zone details sub zone get sequence wise
    for sub_zone, sequence in sub_zones.items():
        sub_zone_data = {
            'sub_zone': sub_zone,
            'sequence': sequence,
            'pending_tasks': len(picklist_dict.get(sub_zone, set())),
        }
        sub_zone_details.append(sub_zone_data)

    return JsonResponse({'data': sub_zone_details})
