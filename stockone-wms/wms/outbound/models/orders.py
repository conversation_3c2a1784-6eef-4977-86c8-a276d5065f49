#django imports
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from simple_history.models import HistoricalRecords

from core.models import SKUWAC

#wms imports
from wms_base.wms_utils import (
    TenantBaseModel, ForeignKey
)
from wms_base.models import User

#outbound imports
from outbound.models import CustomerMaster

#history imports
from core_operations.history import CustomHistoricalRecords

skumaster_const = 'core.SKUMaster'

class NetworkMaster(TenantBaseModel):
    
    dest_location_code = ForeignKey(User, on_delete=models.CASCADE)
    source_location_code = ForeignKey(User, on_delete=models.CASCADE, related_name='source_location_code')
    lead_time = models.PositiveIntegerField(blank=True, null=True)
    priority = models.IntegerField(blank=True, null=True)
    status = models.IntegerField(default=1)
    network_type = models.CharField(max_length=16, default='', null=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'NETWORK_MASTER'
        unique_together = ('dest_location_code', 'source_location_code', 'network_type')

class OrderDetail(TenantBaseModel):

    history = CustomHistoricalRecords()
    history_disable = True
    user = models.PositiveIntegerField(db_index=True)
    order_id = models.DecimalField(max_digits=50, decimal_places=0)
    original_order_id = models.CharField(max_length=64, default='', db_index=True)
    customer_id = models.PositiveIntegerField(default=0, db_index=True)
    customer_name = models.CharField(max_length=256, default='', db_index=True)
    email_id = models.EmailField(max_length=64, default='') 
    address = models.TextField(max_length=1024, default='')
    telephone = models.CharField(max_length=20, default='', blank=True, null=True, db_index=True)
    sku = ForeignKey(skumaster_const, on_delete=models.CASCADE)
    title = models.CharField(max_length=256, default='')
    quantity = models.FloatField(default=0)
    original_quantity = models.FloatField(default=0)
    cancelled_quantity = models.FloatField(default=0)
    suspended_quantity = models.FloatField(default=0)
    invoice_amount = models.FloatField(default=0)
    shipment_date = models.DateTimeField()
    marketplace = models.CharField(max_length=64, default='')
    order_code = models.CharField(max_length=64, default='')
    vat_percentage = models.FloatField(default=0)
    status = models.CharField(max_length=16, db_index=True)
    sku_code = models.CharField(max_length=65, default='')
    city = models.CharField(max_length=128, default='')
    state = models.CharField(max_length=64, default='')
    pin_code = models.CharField(max_length=32, default='')
    remarks = models.CharField(max_length=128, default='')
    payment_mode = models.CharField(max_length=128, default='')
    payment_received = models.FloatField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True, db_index=True)
    updation_date = models.DateTimeField(auto_now=True)
    mrp = models.FloatField(default=0)
    unit_price = models.FloatField(default=0)
    nw_status = models.CharField(max_length=32, default='', blank=True, null=True)
    order_type = models.CharField(max_length=32, default='Normal')
    order_reference = models.CharField(max_length=64, default='', db_index=True)
    order_reference_date = models.DateField(null=True, blank=True)
    promised_time = models.CharField(max_length=32, blank=True, null=True)
    slot_from = models.DateTimeField(null=True, blank=True)
    slot_to = models.DateTimeField(null=True, blank=True)
    forward_time = models.PositiveIntegerField(default=0)
    estimated_dispatch_time = models.DateTimeField(null=True, blank=True)
    trip_id = models.CharField(max_length=64, blank=True, null=True)
    sku_avg_price = models.FloatField(default=0)
    sku_avg_price_rt = models.FloatField(default=0)
    json_data= models.JSONField(null=True, blank=True)
    stock_count = models.FloatField(null=True, blank=True, default=None)
    is_backorder = models.BooleanField(default=False)
    distributor = ForeignKey(User,on_delete=models.CASCADE, related_name='distributer', blank=True, null=True)
    network_work = ForeignKey(NetworkMaster, on_delete=models.SET_NULL, blank=True, null=True)
    line_reference = models.CharField(max_length=64, blank=True, null=True)
    customer_po_num = models.CharField(max_length=128, default=None, blank=True, null=True)
    customer_identifier = ForeignKey(CustomerMaster, on_delete=models.SET_NULL, blank=True, null=True)
    
    class Meta:
        db_table = 'ORDER_DETAIL'
        unique_together = ('sku', 'order_reference', 'user','mrp','unit_price', 'line_reference')
        index_together = (('original_order_id', 'user'), ('order_id', 'sku', 'order_code'), ('user', 'order_code'), ('id',),
                          ('customer_id', 'order_code', 'marketplace', 'original_order_id', 'order_id', 'customer_name'),
                          ('status', 'user', 'quantity'), ('user', 'sku'),('original_order_id',), ('user', ), ('creation_date', ), ('telephone',) ,('customer_id', ), ('customer_name',),
                          ('user','creation_date'),('order_reference','user','status'),
                          ('original_order_id', 'user', 'status'),
                          )
        permissions = [
            ('display_customer_phone_no', 'Display Customer Phone Number'),
        ]

    round_fields = ['quantity', 'original_quantity', 'cancelled_quantity', 'suspended_quantity']

    def __str__(self):
        return str(self.sku) + ':' + str(self.original_order_id) + ':' + str(self.mrp)

class Order(TenantBaseModel):
    warehouse = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    order_reference =  models.CharField(max_length=64, default='', db_index=True)
    order_type = models.CharField(max_length=32, default='Normal')
    customer = ForeignKey(CustomerMaster, on_delete=models.SET_NULL, blank=True, null=True)

    expiration_date = models.DateTimeField(null=True, blank=True)
    hold_date = models.DateTimeField(null=True, blank=True)
    order_date = models.DateTimeField(null=True, blank=True)

    quantity = models.FloatField(default=0)
    open_quantity = models.FloatField(default=0)
    cancelled_quantity = models.FloatField(default=0)
    invoiced_quantity = models.FloatField(default=0)
    allocated_quantity = models.FloatField(default=0)

    status = models.CharField(max_length=16, db_index=True)#1-open, 0-in progress, 20-allocated, 5-invoiced, 3-cancelled
    approval_status = models.CharField(max_length=32, default = '', null=True, blank=True)
    json_data= models.JSONField(null=True, blank=True)

    creation_date = models.DateTimeField(auto_now_add=True, db_index=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ORDER'
        index_together = (('order_reference','warehouse'), ('order_type', 'warehouse', 'status'))
        unique_together = ('order_reference','warehouse')
    
    def __str__(self):
        return str(self.order_reference) + ':' + str(self.status)

#Signals
@receiver(post_save, sender=OrderDetail)
def save_order_original_quantity(sender, instance, created, **kwargs):
    if created:
        instance.original_quantity = instance.quantity
        skuwac_objs = list(SKUWAC.objects.filter(sku_id=instance.sku.id))
        if skuwac_objs:
            instance.sku_avg_price = skuwac_objs[0].average_price
            instance.sku_avg_price_rt = skuwac_objs[0].average_price_rt
        instance.save()

class CustomerOrderSummary(TenantBaseModel):

    history = CustomHistoricalRecords()
    history_disable = True
    order = ForeignKey(OrderDetail,on_delete=models.CASCADE, db_index=True)
    discount = models.FloatField(default=0)
    vat = models.FloatField(default=0)
    mrp = models.FloatField(default=0)
    issue_type = models.CharField(max_length=64, default='')
    tax_value = models.FloatField(default=0)
    tax_type = models.CharField(max_length=64, default='')
    order_taken_by = models.CharField(max_length=128, default='')
    shipment_time_slot = models.CharField(max_length=64, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    status = models.CharField(max_length=24, default='',  db_index=True)
    consignee = models.CharField(max_length=256, default='')
    payment_terms = models.CharField(max_length=24, default='')
    dispatch_through = models.CharField(max_length=24, default='')
    invoice_date = models.DateTimeField(null=True, blank=True)
    central_remarks = models.CharField(max_length=256, default='')
    inter_state = models.IntegerField(default=0)
    cgst_tax = models.FloatField(default=0)
    sgst_tax = models.FloatField(default=0)
    igst_tax = models.FloatField(default=0)
    utgst_tax = models.FloatField(default=0)
    cess_tax = models.FloatField(default=0)
    json_data= models.JSONField(null=True, blank=True)
    invoice_type = models.CharField(max_length=64, default='Tax Invoice')
    client_name = models.CharField(max_length=64, default='')
    mode_of_transport = models.CharField(max_length=24, default='')
    payment_status = models.CharField(max_length=64, default='')
    courier_name = models.CharField(max_length=64, default='')
    vehicle_number = models.CharField(max_length=64, default='')
    street_number = models.CharField(max_length=64, default='')
    street_name = models.CharField(max_length=64, default='')
    address_line_2 = models.CharField(max_length=256, default='')
    address_line_3 = models.CharField(max_length=256, default='')

    class Meta:
        db_table = 'CUSTOMER_ORDER_SUMMARY'
        index_together = (('order',),)


class OrderFields(TenantBaseModel):
    
    user = models.PositiveIntegerField()
    original_order_id = models.CharField(max_length=128, default='')
    name = models.CharField(max_length=256, default='')
    value = models.CharField(max_length=256, default='')
    order_type = models.CharField(max_length=256, default='order')
    extra_fields = models.CharField(max_length=128, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ORDER_FIELDS'
        index_together = (('user', 'original_order_id'), ('user', 'original_order_id', 'name'),
                          ('original_order_id', 'order_type', 'user'))

    def __str__(self):
        return str(self.original_order_id)


class OrderCharges(TenantBaseModel):
    
    order_id = models.CharField(max_length=128, default='')
    user = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True)
    charge_name = models.CharField(max_length=128, default='')
    charge_amount = models.FloatField(default=0)
    charge_tax_value = models.FloatField(default = 0)
    order_type = models.CharField(max_length=256, default='order')
    prefix = models.CharField(max_length=16, default='')
    charge_percent = models.FloatField(default=0)
    extra_flag = models.CharField(max_length=32, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ORDER_CHARGES'
        index_together = (('user', 'order_id', 'charge_name'), ('user', 'order_id', 'order_type'))

class SellerMaster(TenantBaseModel):
    
    user = models.PositiveIntegerField()
    seller_id = models.PositiveIntegerField(default=0)
    name = models.CharField(max_length=256, default='')
    email_id = models.EmailField(max_length=64, default='')
    phone_number = models.CharField(max_length=32)
    address = models.CharField(max_length=256, default='')
    vat_number = models.CharField(max_length=64, default='')
    tin_number = models.CharField(max_length=64, default='')
    price_type = models.CharField(max_length=32, default='')
    margin = models.CharField(max_length=256, default=0)
    supplier = ForeignKey('inbound.SupplierMaster',on_delete=models.CASCADE, null=True, blank=True, default=None)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SELLER_MASTER'
        unique_together = ('user', 'seller_id')
        index_together = ('user', 'seller_id')

    def json(self):
        supplier_id = '' if not self.supplier else self.supplier.id
        return {
            'id': self.id,
            'seller_id': self.seller_id,
            'name': self.name,
            'email_id': self.email_id,
            'phone_number': self.phone_number,
            'address': self.address,
            'vat_number': self.vat_number,
            'tin_number': self.tin_number,
            'price_type': self.price_type,
            'margin': self.margin,
            'supplier': supplier_id,
            'status': self.status
        }

class SellerOrder(TenantBaseModel):
    
    seller = ForeignKey(SellerMaster,on_delete=models.CASCADE, blank=True, null=True)
    sor_id = models.CharField(max_length=128, default='')
    order = ForeignKey(OrderDetail,on_delete=models.CASCADE, blank=True, null=True)
    quantity = models.FloatField(default=0)
    order_status = models.CharField(max_length=64, default='')
    invoice_no = models.CharField(max_length=64, default='')
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SELLER_ORDER'
        unique_together = ('sor_id', 'order')
        index_together = (('sor_id', 'order'), ('order', 'status'))

    def __str__(self):
        return str(self.sor_id)

class IntermediateOrders(TenantBaseModel):
    
    user = ForeignKey(User,on_delete=models.CASCADE)
    customer_user = ForeignKey(User,on_delete=models.CASCADE, related_name='customer', blank=True, null=True)
    order_assigned_wh = ForeignKey(User,on_delete=models.CASCADE, related_name='warehouse', blank=True, null=True)
    interm_order_id = models.DecimalField(max_digits=50, decimal_places=0)
    order = ForeignKey(OrderDetail,on_delete=models.CASCADE, blank=True, null=True)
    sku = ForeignKey(skumaster_const,on_delete=models.CASCADE)
    alt_sku = ForeignKey(skumaster_const,on_delete=models.CASCADE, related_name='alt_sku', blank=True, null=True)
    quantity = models.FloatField(default=1)
    unit_price = models.FloatField(default=0)
    tax = models.FloatField(default=0)
    inter_state = models.IntegerField(default=0)
    cgst_tax = models.FloatField(default=0)
    sgst_tax = models.FloatField(default=0)
    igst_tax = models.FloatField(default=0)
    utgst_tax = models.FloatField(default=0)
    status = models.CharField(max_length=32, default='')
    shipment_date = models.DateTimeField()
    project_name = models.CharField(max_length=256, default='')
    remarks = models.CharField(max_length=128, default='')
    customer_id = models.PositiveIntegerField(default=0)
    customer_name = models.CharField(max_length=256, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "INTERMEDIATE_ORDERS"
        index_together = (('user', ), ('user', 'interm_order_id'), ('sku', 'interm_order_id'))

    def json(self):
        invoice_amount = self.quantity * self.sku.price
        return {
            'sku_id': self.sku.sku_code,
            'quantity': self.quantity,
            'price': self.sku.price,
            'unit_price': self.sku.price,
            'invoice_amount': invoice_amount,
            'tax': self.tax,
            'total_amount': ((invoice_amount * self.tax) / 100) + invoice_amount,
            'image_url': self.sku.image_url,
            'cgst_tax': self.cgst_tax,
            'sgst_tax': self.sgst_tax,
            'igst_tax': self.igst_tax,
            'utgst_tax': self.utgst_tax,
        }


class OrderMapping(TenantBaseModel):

    order = ForeignKey(OrderDetail,on_delete=models.CASCADE, blank=True, null=True)
    mapping_id = models.PositiveIntegerField(default=0)
    mapping_type = models.CharField(max_length=32, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ORDER_MAPPING'
        index_together = ('mapping_id', 'mapping_type')


class PaymentInfo(TenantBaseModel):
    
    paid_amount = models.FloatField(default=0)
    transaction_id = models.CharField(max_length=64, default='')
    payment_mode = models.CharField(max_length=128, default='')
    method_of_payment = models.CharField(max_length=64, default='')
    payment_date = models.DateTimeField(auto_now=True)
    aux_info = models.TextField(default='', blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PAYMENT_INFO'

class PaymentSummary(TenantBaseModel):
    
    payment_id = models.CharField(max_length=60, default='')
    order = ForeignKey(OrderDetail,on_delete=models.CASCADE, blank=True, null=True)
    payment_received = models.FloatField(default=0)
    bank = models.CharField(max_length=64, default='')
    mode_of_pay = models.CharField(max_length=64, default='')
    remarks = models.CharField(max_length=128, default='')
    entered_amount = models.FloatField(default=0)
    balance_amount = models.FloatField(default=0)
    tds_amount = models.FloatField(default=0)
    invoice_number = models.CharField(max_length=128, default='')
    payment_date = models.DateField(blank=True, null=True)
    payment_info = ForeignKey(PaymentInfo,on_delete=models.CASCADE, blank=True, null=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PAYMENT_SUMMARY'
        index_together = ('order',)


class OrderJson(TenantBaseModel):
    order = ForeignKey(OrderDetail,on_delete=models.CASCADE)
    json_data = models.TextField()
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ORDER_JSON'


class OrderType(TenantBaseModel):

    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    order_type = models.CharField(null=True, blank=True, max_length=64)
    status = models.IntegerField(default=1)
    json_data = models.JSONField(default=dict)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ORDER_TYPES'
        unique_together = ('order_type', 'warehouse')
