"""URL FOR THE MODULES ARE DEFINED HERE"""
from django.urls import re_path, include, path

from rest_framework import routers

#customer master
from outbound.views.masters.customer_master.helpers import search_customer_data

from outbound.views.masters.customer_master.datatable import get_customer_master, get_customer_master_id
from outbound.views.masters.customer_master.create_customer import CustomerMasterSet, get_customers_by_route_id, get_customer_categories

#pricing master
from outbound.views.masters.pricingmaster.main import PricingMaster

#order type master
from outbound.views.masters.order_type_master import (
    get_ordertype_wise_zone_mapping, insert_ordertype_zone_mapping, get_order_type_zones
)

#customer sku mapping
from outbound.views.masters.customer_sku_mapping import (
    insert_customer_sku, update_customer_sku_mapping, search_customer_sku_mapping
)

# route master
from outbound.views.masters.route_master import RouteMasterList
from outbound.views.masters.lpn_detail_report import LpnDetailReport

#audit
from outbound.views.audit.audit_master import AuditMasterViewSet
from outbound.views.audit.main import AuditSet, flag_lpn_for_audit
from outbound.views.audit.dashboard import AuditReport

#create order data sub apis
from outbound.views.orders.sub_apis import (
    create_orders_data, get_order_extra_fields, get_order_extra_options,
    get_customer_sku_prices, get_order_type_price_master, get_order_types,
    get_stock_allocate, get_create_order_mapping_values, create_orders_check_ean,
    get_orders_label_data
)

#order creations
from outbound.views.orders.main import OrdersDetailsSet, get_view_order_details, update_so_status, get_order_classification
from outbound.views.orders.upload import order_upload
from outbound.views.orders.orders import SellerOrderSet
from outbound.views.orders.order_data import OrdersSummary

#order trip summary
from outbound.views.orders.order_trip_summary import OrderRoutes

#cancel orders
from outbound.views.orders.cancel_orders import order_delete

#dispatch summary dashboard
from outbound.views.orders.dashboard import DispatchSummaryReport

#allocation
from outbound.views.allocation.main import AllocationSet
from outbound.views.allocation.allocation_report import AllocationReport

#picklist generation
from outbound.views.picklist.main import view_picklist, get_picklist_reasons, SOPicklistSet
from outbound.views.picklist.print_picklist import multiple_print_picklist, print_picklist_excel
from outbound.views.picklist.cancel_picklist import picklist_delete, cancel_picklist_task, end_wave, cancel_picklist_lines
from outbound.views.picklist.sub_apis import get_stock_location_quantity, PicklistViewSet
from outbound.views.picklist.pick_and_pass_strategy import PickAndPassStrategyView

#wave picking
from outbound.views.picklist.wave_picking import manual_wave
from outbound.views.picklist.helpers import get_active_waves

#picklist summary
from outbound.views.picklist.picklist_summary import PicklistSummary

#picklist confirmation
from outbound.views.picklist.picklist_confirmation import picklist_grouped_confirmation, get_staging_data

#picklist task
from outbound.views.picklist.picklist_task import (
    manual_picker_assignment, update_picker, get_picklist_assigned_pickers, PicklistPriority, SkipPickTask,
    zone_wise_manual_picker_assignment
)

#picklist dashboard
from outbound.views.picklist.dashboard import PickingDashboardView, get_picking_lpn_details, get_picklist_sub_zone_details

# rapid picklist
from outbound.views.picklist.rapid_pick_confirm import RapidPicklistConfirmation
# picklist reports
from outbound.views.picklist.picker_output_report import PickerOutputReport

#label based picking
from outbound.views.picklist.label_picking import LabelPickTask, PicklistLabelData

#invoice sub apis
from outbound.views.invoice.sub_apis import customer_invoice_data

#invoice
from outbound.views.invoice.main import SOSViewSet, InvoiceSet, InvoiceSerialScan
from outbound.views.invoice.invoice_data import generate_customer_invoice_tab, generate_customer_invoice_for_order, display_invoice_errors, print_customer_invoice, print_saved_invoice_data
from outbound.views.invoice.update import update_invoice, APIResponse, retrigger_invoice_callback, retrigger_st_asn_creation
from outbound.views.invoice.datatables import material_request_invoice_tab_headers
from outbound.views.invoice.einvoice import cancel_einvoice_irn, EInvoiceSet, CancelEinvoiceSet
from outbound.views.invoice.invoice_preview.main import InvoicePreview
from outbound.views.invoice.invoice_failure_report import InvoiceFailureReport
from outbound.views.invoice.user_invoice_history import InvoiceHistory

#staging aread
from outbound.views.staging_lanes.location_suggestions import OutboundStagingLocationSuggestions
from outbound.views.staging_lanes.staging_area import StagingArea, PostInvoiceStaging

#cancel invoice
from outbound.views.invoice.cancel_invoice import cancel_picking_packing, cancel_invoice, get_linked_picklists

#delivery challan
from outbound.views.invoice.delivery_challan import move_to_dc
from outbound.views.invoice.delivery_challan_data import generate_customer_dc_print
from outbound.views.invoice.update import update_dc

#packing
from outbound.views.packing.packing_preview import PackingPreview
from outbound.views.packing.main import PackingView, LPNView, LpnConsolidation, ScanLpn
from outbound.views.packing.packlist import Packlist
from outbound.views.packing.packing_drop import move_to_invoice
from outbound.views.packing.lpn_lookup import LPNLookUP
from outbound.views.packing.sub_apis import (
    shipment_pack_ref_decrease, shipment_pack_ref, get_outward_carton
)

#sorting
from outbound.views.sorting.main import Sorting, SecondarySortingView, PrimarySortingView
from outbound.views.sorting.outbound_staging import SortingStagingSet, StagingSet, LpnSortStatus
from outbound.views.sorting.helpers import StagingLocationDetailSet
from outbound.views.sorting.location_lpn_mapping import LocationLPNMapping

#sorting details
from outbound.views.flipkart_sorting.sorting_details import SortingDetailsView
from outbound.views.flipkart_sorting.sorting_station_master import SortingStationView
from outbound.views.flipkart_sorting.lpn_bin_mapping import LPNBinMappingView
from outbound.views.flipkart_sorting.adhoc_sorting import AdhocSortingView

#shipment
from outbound.views.shipment.main import Shipment, shipment_info, get_shipment_api
from outbound.views.shipment.shipment_invoice import get_shipment_invoice_data
from outbound.views.shipment.sub_apis import pod_upload, get_shipment_transporter_details
from outbound.views.shipment.dispatch_dashboard import DispatchDashboard
from outbound.views.shipment.shipment_report import ShipmentReport
from outbound.views.shipment.dispatch import DispatchApiView

#manifest
from outbound.views.manifest.main import Manifest

#ewaybill
from outbound.views.shipment.ewaybill import Ewaybill, cancel_eway_bill, update_cancelled_ewaybill

from outbound.views.shipment.consolidated_ewaybill import ConsolidatedEwayBill
from outbound.views.shipment.ewaybill import Ewaybill, update_ewaybill_details

#sales return
from outbound.views.sales_return.main import SalesReturn, get_sales_return_sku
from outbound.views.sales_return.sales_return_preview import SalesReturnPreview
from outbound.views.sales_return.credit_note import CreditNote
from outbound.views.sales_return.ecredit_note import ECreditNoteGeneration, ECreditNoteCancellation

#reports
from outbound.views.reports.bounce_at_picklist_creation import BounceAtPicklistCreationReportView
from outbound.views.reports.dispatch_summary  import DispatchSummaryReportView


router = routers.DefaultRouter()
router.register(r'processed_orders', SOSViewSet)

# urlpatterns = patterns('api_calls.views',
urlpatterns = [
    path(r'', include(router.urls)),

    #customer master
    re_path('customer/$', CustomerMasterSet.as_view(), name="Customer"),
    re_path('get_customer_master_id/$', get_customer_master_id, name="Customer Id View"),
    re_path('update_customer/$', CustomerMasterSet.as_view(), name="Customer Fields Updation"),
    re_path('search_customer/$', search_customer_data, name="Customer View"),
    re_path('get_customer_by_route_ids/$', get_customers_by_route_id, name="Customer By Route"),
    re_path('get_customer_categories/$', get_customer_categories, name="Customer By Category"),

    #pricing master
    re_path('pricing_master/$', PricingMaster.as_view(), name="Pricing"),

    #order type master
    re_path('order_type_master/$', insert_ordertype_zone_mapping, name="OrderType"),
    re_path('get_order_type_master/$', get_ordertype_wise_zone_mapping, name="OrderType Details View"),
    re_path('get_order_type_zones/$', get_order_type_zones, name="OrderType Zones View"),

    #customer sku mapping master
    re_path('customer_sku_mapping/$', insert_customer_sku, name="Customer SKU Mapping"),
    re_path('update_customer_sku_mapping/$', update_customer_sku_mapping, name="Customer SKU Mapping Updation"),
    re_path('search_customer_sku_mapping/$', search_customer_sku_mapping, name="Customer SKU Mapping Details"),

    #route master
    re_path('route_master/$', RouteMasterList.as_view(), name="Route Master"),
    
    #Audit
    re_path('audit_master/$', AuditMasterViewSet.as_view(), name="Audit Master"),
    re_path('audit/$', AuditSet.as_view(), name="Audit"),
    re_path('audit_report/$', AuditReport.as_view(), name="Audit Report"),
    re_path('flag_lpn_for_audit/$', flag_lpn_for_audit, name="LPN Flagging For Audit"),

    #create order data sub apis
    re_path('create_order_info/$', create_orders_data, name="Order Creation Details"),
    re_path('order_extra_fields/$', get_order_extra_fields, name="Order Extra Fields View"),
    re_path('order_extra_options/$', get_order_extra_options, name="Order Extra Options View"),
    re_path('customer_sku_prices/$', get_customer_sku_prices, name="Customer SKU Prices View"),
    re_path('order_type_price_master/$', get_order_type_price_master, name="OrderType Price View"),
    re_path('order_types/$', get_order_types, name="OrderTypes View"),
    re_path('stock_allocate/$', get_stock_allocate, name="Stock Allocate View"),
    re_path('view_order_details/$', get_view_order_details, name="Order Details View"),
    re_path('get_create_order_mapping_values/$', get_create_order_mapping_values, name="SKU Price View"),
    re_path('create_orders_check_ean/$', create_orders_check_ean, name="EAN Check While Order Creation"),

    #orders creation
    re_path('orders/$', OrdersDetailsSet.as_view(), name="Orders"),
    re_path('order_uploads/$', order_upload, name="Orders Upload"),
    re_path('order_new/$', SellerOrderSet.as_view(), name='Order creation - V2'),
    re_path('orders/label_data/$', get_orders_label_data, name="Orders Label Data"),
    
    #order approval
    re_path('update_so_status/$', update_so_status, name="SO Status Update"),

    #order trip summary
    re_path('routes/$', OrderRoutes.as_view(), name="Order Routes"),

    #orders view
    re_path('order_summary/$', OrdersSummary.as_view(), name="Order Data"),
    re_path('get_order_classification/$', get_order_classification, name="Order Classification Data"),

    #delete orders
    re_path('order_delete/$', order_delete, name="Orders Deletion"),

    #dispatch summary dashboard
    re_path('dispatch_summary/$', DispatchSummaryReport.as_view(), name="Dispatch Summary"),

    #picklist generatios
    re_path('view_picklist/$', view_picklist, name="Picklist Details"),
    re_path('print_picklist/$', multiple_print_picklist, name="Picklist Print Details"),
    re_path('print_picklist_excel/$', print_picklist_excel, name="Picklist Print Excel Details"),
    re_path('picklist_reasons/$', get_picklist_reasons, name="Picklist Reasons View"),
    re_path('picklist_delete/$', picklist_delete, name="Picklist Deletion"),
    re_path('generate_picklist/$', SOPicklistSet.as_view(), name="SO Picklist Generation"),
    re_path('allocate/$', AllocationSet.as_view(), name="SO Allocation"),
    re_path('picklist_view/$', PicklistViewSet.as_view(), name="View Picklist"),
    re_path('pick_and_pass/$', PickAndPassStrategyView.as_view(), name="Pick and Pass"),

    #Allocation Report
    re_path('allocation_report/$', AllocationReport.as_view(), name="Allocation Report"),

    #Wave picking
    re_path('active_waves/$', get_active_waves, name="Wave Picking"),
    re_path('manual_wave/$', manual_wave, name="Manual Wave Picklist Generation"),
    re_path('end_wave/$', end_wave, name="End Wave"),

    #picklist summary
    re_path('picklist_summary/$', PicklistSummary.as_view(), name="Picklist Summary"),

    #picklist confirmation
    re_path('picklist_confirmation/$', picklist_grouped_confirmation, name="Picklist Confirmation"),

    #picklist task
    re_path('assign/task/for/employee/$',manual_picker_assignment, name="Assign Task For Employee"),
    re_path('manual_picker_assignment/$',zone_wise_manual_picker_assignment, name="Assign Task For Employee"),
    re_path('update_picker/$',update_picker, name="Picker Status"),
    re_path('get_picklist_assigned_pickers/$', get_picklist_assigned_pickers, name="Get Picklist Assigned Pickers"),
    re_path('picklist_priority/$', PicklistPriority.as_view(), name="Picklist Priority"),
    re_path('cancel_picklist_task/$', cancel_picklist_task, name="Cancel Picklist Task"),
    re_path('skip_picklist_task/$', SkipPickTask.as_view(), name="Skip Picklist Task"),
    re_path('cancel_picklist_lines/$', cancel_picklist_lines, name="Cancel Picklist Lines"),

    #picklist subtask
    re_path('stock_location_quantity/$', get_stock_location_quantity, name="Stock Location Quantity View"),

    #picklist  dashboard
    re_path('picking_dashboard/$', PickingDashboardView.as_view(), name="Picking Dashboard"),
    re_path('get_picking_lpn_details/$', get_picking_lpn_details, name="Picking LPN Details"),
    re_path('get_picklist_sub_zone_details/$', get_picklist_sub_zone_details, name="Picklist Sub Zone Details"),

    # picklist reports
    re_path('picker_output_report/$', PickerOutputReport.as_view(), name="Picker Output Report"),

    #invoice sub apis
    re_path('invoice/$', InvoiceSet.as_view(), name="Invoice"),
    re_path('customer_invoice_data/$', customer_invoice_data, name="Invoice View"),
    re_path('invoice_data/$', generate_customer_invoice_tab, name="Invoice Details"),
    re_path('print_invoice/$', print_customer_invoice, name="Print Invoice"),
    re_path('update_invoice/$', update_invoice, name="invoice Update"),
    re_path('material_request_invoice_tab_headers/$', material_request_invoice_tab_headers, name="Invoice Columns"),
    re_path('invoice_preview/$', InvoicePreview.as_view(), name="Invoice Preview"),
    re_path('customer_invoice_data_for_order/$', generate_customer_invoice_for_order, name="Order Invoice Details"),
    re_path('cancel_einvoice_irn/$', cancel_einvoice_irn, name="Cancel Einvoice"),
    re_path('display_invoice_errors/$', display_invoice_errors, name="Display Invoice Errors"),
    re_path('retrigger_invoice_callback/$', retrigger_invoice_callback, name="Invoice Callback Retrigger"),
    re_path('api_response/$',APIResponse.as_view(),name="Update Invoice Callback Response"),
    re_path('print_saved_invoice_data/$', print_saved_invoice_data, name="Invoice Data"),
    re_path('einvoice/$', EInvoiceSet.as_view(), name="EInvoice View"),
    re_path('cancel_irn/$',CancelEinvoiceSet.as_view(), name='EInvoice Cancel'),
    re_path('invoice_failure_report/$', InvoiceFailureReport.as_view(), name="Invoice Failure Report"),
    re_path('invoice_serial_scan/$', InvoiceSerialScan.as_view(), name="Invoice Serial Scan"),
    re_path('retrigger_st_asn_creation/$', retrigger_st_asn_creation, name="ST ASN Creation Retrigger"),
    re_path('user_invoice_history/$', InvoiceHistory.as_view(), name="User Invoice History"),

    #staging area
    re_path('location_suggestions/$', OutboundStagingLocationSuggestions.as_view(), name="Location Suggestions"),
    re_path('staging_area/$', StagingArea.as_view(), name="Staging Area"),
    re_path('post_invoice_staging/$', PostInvoiceStaging.as_view(), name="Post Invoice Staging"),
    re_path('staging_preview/$', get_staging_data, name="Staging Preview"),

    #cancel picklist
    re_path('picking_packing_delete/$', cancel_picking_packing, name="Picking Packing Deletion"),
    re_path('invoice_delete/$', cancel_invoice, name="Invoice Deletion"),
    re_path('get_linked_picklists/$', get_linked_picklists, name="Linked Picklists"),

    #delivery challan
    re_path('delivery_challan/$', move_to_dc, name="Delivery Challan Creation"),
    re_path('print_delivery_challan/$', generate_customer_dc_print, name="Delivery Challan Details"),
    re_path('update_delivery_challan/$', update_dc, name="Delivery Challan Updation"),

    #packing
    re_path('carton_delete/$', shipment_pack_ref_decrease, name="Carton Deletion"),
    re_path('create_carton/$', shipment_pack_ref, name="Carton"),
    re_path('get_outward_carton/$', get_outward_carton, name="Outward Carton View"),
    re_path('packing_preview/$', PackingPreview.as_view(), name="Packing Preview"),
    re_path('packing/$', PackingView.as_view(), name="packing"),
    re_path('lpn/$', LPNView.as_view(), name="lpn"),
    re_path('lpn_consolidation/$', LpnConsolidation.as_view(), name="Lpn Consolidation"),
    re_path('move_to_invoice/$', move_to_invoice, name="move to invoice"),
    re_path('packlist/$', Packlist.as_view(), name="Packlist"),
    re_path('lpn_lookup/$', LPNLookUP.as_view(), name="LPN Lookup"),
    re_path('lpn_detail_view_report/', LpnDetailReport.as_view(), name="LPN Detail View Report"),
    re_path('scan_lpn/$', ScanLpn.as_view(), name="Scan LPN"),


    #sorting
    re_path('sorting/$', Sorting.as_view(), name="Sorting"),
    re_path('outbound_staging/$', SortingStagingSet.as_view(), name="Outbound Staging"),
    re_path('staging_location/$', StagingLocationDetailSet.as_view(), name="Staging Location"),
    re_path('pick/$', StagingSet.as_view(), name="Pick"),
    re_path('lpn_sort/$', LpnSortStatus.as_view(), name="Update LPN Sort Status"),
    re_path('secondary_sorting/$', SecondarySortingView.as_view(), name="Secondary Sorting"),
    re_path('primary_sorting/$', PrimarySortingView.as_view(), name="Primary Sorting"),
    re_path('location_lpn_mapping/$', LocationLPNMapping.as_view(), name="Location LPN Mapping"),

    # flipkart sorting
    re_path('sorting-details/$', SortingDetailsView.as_view(), name="Sorting Details"),
    re_path('sorting_station_master/$', SortingStationView.as_view(), name="Sorting Station Master"),
    re_path('lpn_bin_mapping/$', LPNBinMappingView.as_view(), name="LPN Bin Mapping"),
    re_path('adhoc_sorting/$', AdhocSortingView.as_view(), name="Adhoc Sorting"),

    #shipment
    re_path('create_shipment_info/$', get_shipment_invoice_data, name="Create ShipmentInfo"),
    re_path('shipment_info/$', shipment_info, name="ShipmentInfo"),
    re_path('shipment/$', Shipment.as_view(), name="Shipment"),
    re_path('get_shipments/$', get_shipment_api, name="Shipment View"),
    re_path('pod_upload/$', pod_upload, name="POD"),
    re_path('shipment/transporter_details/$', get_shipment_transporter_details, name="Shipment Transporter Details"),
    re_path('dispatch/$', DispatchApiView.as_view(), name="Dispatch"),
    re_path('dispatch_dashboard/$', DispatchDashboard.as_view(), name="Dispatch Dashboard"),
    re_path('shipment_report/$', ShipmentReport.as_view(), name="Shipment Summary Report"),

    #manifest
    re_path('manifest/$', Manifest.as_view(), name="Manifest"),

    #Ewaybill
    re_path('ewaybill/$', Ewaybill.as_view(), name="EwayBill"),
    re_path('cancel_eway_bill/$', cancel_eway_bill, name="Cancel EwayBill"),
    re_path('consolidated_ewaybill/$', ConsolidatedEwayBill.as_view(), name="Consolidated EwayBill"),
    re_path('update_ewaybill/$', update_ewaybill_details, name="Ewaybill Update"),
    re_path('update_cancelled_ewaybill/$', update_cancelled_ewaybill, name="Update Cancelled Ewaybill"),


    #sales return
    re_path('sales_return/$', SalesReturn.as_view(), name="Sales Return"),
    re_path('sales_return_preview/$', SalesReturnPreview.as_view(), name="Sales Return Preview"),
    re_path('credit_note/$', CreditNote.as_view(), name="Credit Note"),
    re_path('generate_ecreditnote/$', ECreditNoteGeneration.as_view(), name="Generate Ecreditnote"),
    re_path('cancel_ecreditnote/$', ECreditNoteCancellation.as_view(), name="Cancel Ecreditnote"),
    re_path('sales_return_sku/$', get_sales_return_sku, name="Sales Return Sku"),

    #reports
    re_path('bounce_at_picklist_creation/$', BounceAtPicklistCreationReportView.as_view(), name="Bounce At Picklist Creation"),
    re_path('dispatch_report/$', DispatchSummaryReportView.as_view(), name="Dispatch Report"),

    # rapid
    re_path('rapid/pick_confirm', RapidPicklistConfirmation.as_view(), name="Rapid Picklist Confirmation"),

    # label based picking
    re_path('label_pick_task/$', LabelPickTask.as_view(), name="Label Pick Task"),
    re_path('picklist/label_data/$', PicklistLabelData.as_view(), name="Picklist Label Data"),
]
