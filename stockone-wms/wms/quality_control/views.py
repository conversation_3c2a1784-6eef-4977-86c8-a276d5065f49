from __future__ import unicode_literals
import math
import datetime
import json

from django.db.models import Q, Count
from django.http import JsonResponse
from rest_framework import serializers

from wms_base.choices import QC_CHOICES

from core.models.common import QCConfiguration
from core_operations.views.common.main import (
    WMSListView, get_user_ip, init_logger, generate_log_message,
    get_warehouse, get_multiple_misc_values
)

from inbound.views.common.constants import INBOUND_STAGING_LANES_MAPPING
from inbound.views.quality_check.common import fetch_pending_qc_quantity

from quality_control.models import QualityControl, QualityControlSummary
from quality_control.serializers import QCConfigurationSerializer, QualityControlSerializer

#outbound imports
from outbound.models import AuditEntry

#lms models
from lms.models import TaskMaster

log = init_logger('logs/quality_control.log')

# Create your views here.

class QualityControlSet(WMSListView):

    def get(self,distinct_values=[], qc_order_by='creation_date',*args, **kwargs):
        self.set_user_credientials()
        request = self.request
        log.info('Request params for QualityControl ' + request.user.username + ' is ' + str(request.GET) + 'ip_address:' + str(get_user_ip(request)))
        search_params = self.prepare_search_params()
        page_info = {}
        if request.GET.get('limit'):
            limit = int(request.GET.get('limit',10))
            offset = int(request.GET.get('offset', 0))
            offset = offset * limit
            qc_count = QualityControl.objects.filter(**search_params).values(*distinct_values).distinct().count()
            page_info = {'page_info': {'current_page': offset/limit, 'total_pages': math.ceil(qc_count/limit), 'count': qc_count}}
            qc_list = list(QualityControl.objects.filter(**search_params).order_by(qc_order_by).values(*distinct_values).distinct()[offset : offset+limit])
        else:
            qc_list = list(QualityControl.objects.filter(**search_params).order_by(qc_order_by).values(*distinct_values).distinct())
            for each_qc_dict in qc_list:
                qc_json = each_qc_dict.get('json_data', {})
                each_qc_dict['lpn_number'] = qc_json.get('lpn_number', '')
        page_info['data'] = qc_list
        return JsonResponse({'data': page_info, 'message' : 'Success' }, status=200)

    def prepare_search_params(self):
        request_data = self.request.GET
        search_params = {'warehouse_id': self.warehouse.id}
        qc_fields = [field.name for field in QualityControl._meta.get_fields()]
        for key, value in request_data.items():
            key = key.strip()
            if not key:
                continue

            parts = key.split('__')
            base_field = parts[0]

            if base_field not in qc_fields:
                continue
            if isinstance(value, list):
                search_params['%s__in' % key] = value
            elif ',' in str(value):
                search_params['%s__in' % key] = value.split(',')
            else:
                search_params['%s' % key] = value
        return search_params

    def create_task_master(self, serialized_data):
        task_master_data = []
        for data in serialized_data:
            task_master_data.append(TaskMaster(warehouse_id=data['warehouse'],task_ref_type='QualityCheck',task_ref_id= data['id'],group_type= data['transaction_type'],order_type=data['qc_category'],reference_number= data['id']))
        TaskMaster.objects.bulk_create(task_master_data)

    def update_task_master(self, serialized_data):
        task_master_data = []
        updation_date = datetime.datetime.now()
        for data in serialized_data:
            tasks = TaskMaster.objects.filter(warehouse_id=data['warehouse'],task_ref_type='QualityCheck',task_ref_id= data['id'],group_type= data['transaction_type'],order_type=data['qc_category'],reference_number= data['id'])
            if data['status'] == 2:
                for task in tasks:
                    task.status = 1
                    task.updation_date = updation_date
                    task_master_data.append(task)
        TaskMaster.objects.bulk_update(task_master_data, ['status', 'updation_date'])

    def post(self, *args, **kwargs):
        self.set_user_credientials()
        response = {}
        request = self.request
        try:
            qc_data = json.loads(request.body)
        except:
            try:
                qc_data = request.POST.get("data")
            except:
                response['message'] = 'Invalid Payload'
                return JsonResponse(response, status=400)
        log.info('Request params for QualityControl ' + request.user.username + ' is ' + str(qc_data) + 'ip_address:' + str(get_user_ip(request)))
        serializer = QualityControlSerializer(data=qc_data, many=True)
        if serializer.is_valid():
            serializer.save()
            self.create_task_master(serializer.data)
            response['message'] = 'Success'
            return JsonResponse(response, status=200)
        response['message'] = 'Failed'
        response['error'] = serializer._errors
        log.info(generate_log_message("QCCreationFailure",
                    warehouse_name = request.user.username, payload=qc_data, error=serializer._errors))
        return JsonResponse(response, status=400)
        

    def put(self,  *args, **kwargs):
        self.set_user_credientials()
        response = {}
        request = self.request
        try:
            qc_data = json.loads(request.body)
        except:
            try:
                qc_data = request.POST.get("data")
            except:
                response['message'] = 'Invalid Payload'
                return JsonResponse(response, status=400)
        
        log.info('Request params for QualityControl ' + request.user.username + ' is ' + str(qc_data) + 'ip_address:' + str(get_user_ip(request)))
        ids = [int(data['id']) for data in qc_data]
        instances = QualityControl.objects.filter(id__in=ids)
        serializer = QualityControlSerializer(instances,data=qc_data, many=True, partial=True)
        if serializer.is_valid():
            serializer.save()
            self.update_task_master(serializer.data)
            response['message'] = 'Success'
            return JsonResponse(response, status=200)
        response['message'] = 'Failed'
        response['error'] = serializer._errors
        return JsonResponse(response, status=400)


class QCConfigurationSet(WMSListView):


    def get(self,*args, **kwargs):
        response = {}
        self.set_user_credientials()
        request = self.request
        search_params = {'warehouse_id': self.warehouse.id}
        qc_list = list(QCConfiguration.objects.filter(**search_params).values())
        used_actions = [qc['transaction_type'] for qc in qc_list] 
        response['available_actions'] = [{'name':v,'value':k} for k,v in QC_CHOICES if k not in used_actions]
        response['warehouse_id'] = self.warehouse.id
        response['data'] = qc_list
        return JsonResponse({'data': response, 'message' : 'Success' }, status=200)


    def post(self, *args, **kwargs):
        self.set_user_credientials()
        response = {}
        request = self.request
        try:
            qc_data = json.loads(request.body)
        except:
            try:
                qc_data = request.POST.get("data")
            except:
                response['message'] = 'Invalid Payload'
                return JsonResponse(response, status=400)
        if qc_data:
            qc_data[0].update({'account_id': self.request.warehouse.userprofile.id, 'warehouse': self.warehouse.id})
        serializer = QCConfigurationSerializer(data=qc_data, many=True)
        if serializer.is_valid():
            search_params = {'warehouse_id': self.warehouse.id}
            available_actions = list(QCConfiguration.objects.filter(**search_params).values_list('transaction_type', flat=True))
            for data in qc_data:
                if data['transaction_type'] in available_actions:
                    response['message'] = 'Transaction Already Exists'
                    return JsonResponse(response, status=400)
            serializer.save()
            response['message'] = 'Success'
            response['data'] = serializer.data
            return JsonResponse(response, status=200)
        return JsonResponse(serializer._errors, status=400)
    

    def put(self,  *args, **kwargs):
        self.set_user_credientials()
        response = {}
        request = self.request
        try:
            qc_data = json.loads(request.body)
        except:
            try:
                qc_data = request.POST.get("data")
            except:
                response['message'] = 'Invalid Payload'
                return JsonResponse(response, status=400)
        ids = [int(data['id']) for data in qc_data]
        instances = QCConfiguration.objects.filter(id__in=ids)
        serializer = QCConfigurationSerializer(instances,data=qc_data, many=True, partial=True)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as error:
            return JsonResponse({"error": error.detail}, status=400)

        serializer.save()
        response['message'] = 'Success'
        response['data'] = serializer.data
        return JsonResponse(response, status=200)


@get_warehouse
def aggregated_qc_count(request, warehouse):
    """
    Aggregates quality control (QC) counts for a given warehouse.
    This function fetches and aggregates the pending QC quantities for different transaction types
    (PO GRN, SR GRN, JO GRN) and returns the counts in a structured JSON response.
    Args:
        request (HttpRequest): The HTTP request object.
        warehouse (Warehouse): The warehouse object for which QC counts are to be aggregated.
    Returns:
        JsonResponse: A JSON response containing the aggregated QC counts.
    """

    staging_lanes_mapping = INBOUND_STAGING_LANES_MAPPING
    qc_staging = staging_lanes_mapping.get('qc', {}).get('name', '')
    misc_dict = get_multiple_misc_values(['inbound_staging_lanes', 'enable_outbound_qc'], warehouse.id)
    inbound_staging_lanes = misc_dict.get('inbound_staging_lanes', 'false')
    enable_outbound_qc = misc_dict.get('enable_outbound_qc', 'false') == 'true'

    qc_data = {}
    filters = {
        'warehouse_id': warehouse.id,
        'status': False,
        'employee': None,
        'task_ref_type': 'QualityCheck',
    }
    if 'qc' in inbound_staging_lanes.split(','):
        pending_jo_qc_dict = fetch_pending_qc_quantity(qc_staging, 'jo_grn', warehouse.id)
        jo_transact_ids = [each[0] for each in list(pending_jo_qc_dict.keys())]
        pending_po_qc_dict = fetch_pending_qc_quantity(qc_staging, 'po_grn', warehouse.id)
        po_transact_ids = [each[0] for each in list(pending_po_qc_dict.keys())]
        transact_ids = jo_transact_ids + po_transact_ids
        qc_ids = list(QualityControl.objects.filter(transaction_id__in=transact_ids).values_list('id', flat=True))
        filters['task_ref_id__in'] = qc_ids
    # Fetch pending QC IDs
    qc_ids = list(TaskMaster.objects.filter(**filters).values_list('task_ref_id', flat=True))
    if qc_ids:
        # Aggregate QC counts for different transaction types
        qc_data = dict(QualityControl.objects
            .filter(id__in=qc_ids, status__in=[0, 1])
            .aggregate(
                po_items=Count('transaction_id', filter=Q(transaction_type='after_grn')),
                po_grns=Count('reference_number', filter=Q(transaction_type='after_grn'), distinct=True),
                sr_items=Count('transaction_id', filter=Q(transaction_type='after_sr_grn')),
                sr_grns=Count('reference_number', filter=Q(transaction_type='after_sr_grn'), distinct=True),
                jo_items=Count('transaction_id', filter=Q(transaction_type='after_jo_grn')),
                jo_grns=Count('reference_number', filter=Q(transaction_type='after_jo_grn'), distinct=True),
            )
        )

    audit_count = 0
    if enable_outbound_qc:
        audit_count = AuditEntry.objects.filter(status__in=[1, 2], warehouse_id=warehouse.id).count()

    qc_dict = {
        "open": {
            "PO GRN": qc_data.get('po_grns', 0),
            "SR GRN": qc_data.get('sr_grns', 0),
            "JO GRN": qc_data.get('jo_grns', 0),
            "SO QC" : audit_count,
        },
        "po_grn": {
            "open": {
                "items": qc_data.get('po_items', 0),
                "orders": qc_data.get('po_grns', 0),
            }
        },
        "sr_grn": {
            "open": {
                "items": qc_data.get('sr_items', 0),
                "orders": qc_data.get('sr_grns', 0),
            }
        },
        "jo_grn": {
            "open": {
                "items": qc_data.get('jo_items', 0),
                "orders": qc_data.get('jo_grns', 0),
            }
        },
        "so_qc": {
            "open": {
                "items": audit_count,
                "orders": audit_count,
            }
        }
    }

    return JsonResponse({"quality_control": qc_dict})
