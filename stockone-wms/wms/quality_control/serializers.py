from core.models.common import QCConfiguration
from rest_framework import serializers
from quality_control.models import QualityControl


class UpdateListSerializer(serializers.ListSerializer):
    def update(self, instances, validated_data): 
        instance_hash = {index: instance for index, instance in enumerate(instances)}
        result = [
            self.child.update(instance_hash[index], attrs)
            for index, attrs in enumerate(validated_data)
        ]
        
        return result


class QualityControlSerializer(serializers.ModelSerializer):
    total_quantity = serializers.FloatField(min_value=0, required=False)
    sampled_quantity = serializers.FloatField(min_value=0, required=False)
    approved_quantity = serializers.FloatField(min_value=0, required=False)
    rejected_quantity = serializers.FloatField(min_value=0, required=False)
    remarks = serializers.CharField(allow_blank=True, required=False)
    account_id  = serializers.IntegerField(min_value=0, required=True)
    json_data = serializers.JSONField(required=False, allow_null=True)
    class Meta:
        model = QualityControl
        fields = '__all__'
        list_serializer_class = UpdateListSerializer

class QCConfigurationSerializer(serializers.ModelSerializer):
    account_id  = serializers.IntegerField(min_value=0, required=True)
    class Meta:
        model = QCConfiguration
        fields = '__all__'
        list_serializer_class = UpdateListSerializer