from django.db import models
from wms_base.models import User, TenantBaseModel
from wms_base.choices import QC_CHOICES

# Create your models here.
class QualityControl(TenantBaseModel):
    id = models.AutoField(primary_key=True)
    account = models.ForeignKey('wms_base.UserProfile', on_delete=models.CASCADE, related_name='%(class)ss_account', db_constraint= False, blank=True, null=True)
    warehouse = models.ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    custom_reference = models.PositiveIntegerField(default=0)
    transaction_type = models.CharField(max_length=64, choices=QC_CHOICES, default='', db_index=True)
    reference_number = models.CharField(max_length=64, default='', db_index=True)
    transaction_id = models.PositiveIntegerField(default=0)
    location_id = models.PositiveIntegerField(default=0)
    batch_id = models.PositiveIntegerField(default=0)
    total_quantity = models.FloatField(default=0)
    quantity = models.FloatField(default=0)
    sampled_quantity = models.FloatField(default=0)
    approved_quantity = models.FloatField(default=0)
    short_quantity = models.FloatField(default=0)
    rejected_quantity = models.FloatField(default=0)
    qc_category = models.CharField(max_length=128, default='')
    remarks = models.CharField(max_length=128, default='')
    # sample_percentage = models.PositiveIntegerField(default=0)
    status = models.IntegerField(default=0) #0-Open 1-Inprogress 2-Closed
    json_data = models.JSONField(null=True,blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'QUALITY_CONTROL'

    round_fields = ['total_quantity', 'sampled_quantity', 'approved_quantity', 'short_quantity', 'rejected_quantity']

class  QualityControlSummary(TenantBaseModel):
    id = models.AutoField(primary_key=True)
    qc_number = models.CharField(max_length=64, default='')
    account = models.ForeignKey('wms_base.UserProfile', on_delete=models.CASCADE, related_name='%(class)ss_account', db_constraint= False, blank=True, null=True)
    quality_control = models.ForeignKey(QualityControl,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    employee = models.ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    approved_quantity = models.FloatField(default=0)
    short_quantity = models.FloatField(default=0)
    rejected_quantity = models.FloatField(default=0)
    status = models.IntegerField(default=0)
    remarks = models.CharField(max_length=128, default='') # currently storing the configured reasons
    comments = models.CharField(max_length=128, default='') # storing actual remarks
    json_data = models.JSONField(null=True,blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'QUALITY_CONTROL_SUMMARY'
        indexes = [
            models.Index(fields=['employee', 'qc_number', 'quality_control']),
            models.Index(fields=['qc_number']),
        ]

    round_fields = ['approved_quantity', 'short_quantity', 'rejected_quantity']
