#package imports
import copy
import math
import qrcode
from itertools import chain
import json
import math
import csv
import secrets
from operator import itemgetter
import xlsxwriter
from xlwt import Workbook, easyxf
import os
import subprocess
from django.conf import settings
import pytz
import re
import datetime
from dateutil.relativedelta import relativedelta
from num2words import num2words
import math
import base64
import pandas as pd
import ast
import secrets
from decimal import Decimal
from random import randint
from urllib.parse import (
    urlparse, parse_qsl, urlencode
)
import operator
import requests
from PyPDF2 import PdfFileMerger
from io import BytesIO
from concurrent.futures import ThreadPoolExecutor
from dateutil import parser
from ipware import get_client_ip
import hashlib
import traceback
from collections import OrderedDict, defaultdict

from django.core.files.storage import get_storage_class
from wms.celery import app

#django imports 
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.models import Permission, Group
from django.db import transaction
from django.db.models.fields import <PERSON><PERSON><PERSON><PERSON>, IntegerField
from django.db.models.functions import Cast, Lower, Concat
from django.db.models import Q, F, Sum, Min, Max, Value, When, Case, Count
from django.views.generic import ListView
from django.core.files import File
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.template import Context, Template
from django.utils.safestring import SafeString
from django.utils import timezone
from dateutil.parser import isoparse

#wms imports
from wms.settings.base import reports_database
from wms_base.wms_utils import (DEPARTMENT_TYPES_MAPPING,
                                init_logger, USER_ROLES,
                                WMS_FIELDS)
from wms_base.auditlogs.views import AuditLogUtils, OpsUtils
from wms_base.models import (
    Integrations, Role, UserProfile, UserGroups, 
    User, AdminGroups, CompanyRoles, StaffWarehouseMapping,
    StaffMaster, CompanyMaster, UserRoleMapping
)
from wms_base.mail_server import send_mail
from wms_base.wms_utils import folder_check
from wms_base.send_message import send_sms

from oauth2_provider.models import Application
from lms.models import EmployeeMaster

from boto3.s3.transfer import S3Transfer
from botocore.config import Config
import boto3

#inventory imports
from inventory.models import(
    ZoneMaster, LocationMaster, SubZoneMapping,
    SKUDetailStats, StockDetail, CycleCount,
    SKUPackMaster, LocationType, BatchDetail
)

#inbound imports
from inbound.models import POLocation, PurchaseApprovalMails, SupplierMaster

#core imports
from core.models import (
    MiscDetail, MiscDetailOptions, IncrementalTable,
    EANNumbers, SKUAttributes, SKUFields, SizeMaster, UOMMaster,
    MasterDocs, MiscDetail, SKUMaster, TaxMaster,
    SKURelation, UserPrefixes, InvoiceForms, PurchaseApprovalConfig,
    TempJson, BarcodeSettings, MasterEmailMapping, UserAttributes, AuthorizedBins, MasterAttributes
)
from core.models.masters import UOMDetail
from core.models.common import DATETIME_CHOICES

#outbound imports
from outbound.models.orders import OrderDetail, Order
from outbound.models.picklist import Picklist

#opensearch
from wms_base.middleware.opensearch_wrapper import OpenSearchWrapper

#auth imports
from auth.serializers.roles import get_company_roles_filter

SELLABLE_CHOICES = (
    ('sellable', 'Sellable'), ('non_sellable', 'Non Sellable'),
    ('outbound_staging', 'Outbound Staging'), ('inbound_staging', 'Inbound Staging'),
    ('cross_dock', 'Cross Dock'),
    ('drop_ship', 'Drop Ship')
)
DATE_FILTERS = ['creation_date','updation_date','date']

log = init_logger('logs/common.log')
log_mail_info = init_logger('logs/mail_info.log')
date_time_format = "%d %b, %Y %I:%M %p"
DATE_FORMAT = "%Y-%m-%d"

def get_exclude_zones(warehouse: User, is_rtv: bool=False, exclude_staging:bool=True) -> list:
    exclude_zones = ['DAMAGED_ZONE', 'QC_ZONE', 'WIPZONE']
    params = {'segregation__in' : ['outbound_staging','inbound_staging','packing']}
    if not exclude_staging:
        params = {'segregation__in': []}
    if warehouse.userprofile.industry_type == 'FMCG' and not is_rtv:
        params['segregation__in'].append('non_sellable')
    if is_rtv:
        exclude_zones.remove('DAMAGED_ZONE')
    non_sellable_zones = list(ZoneMaster.objects.filter(user=warehouse.id, **params).values_list('zone',flat=True))
    exclude_zones.extend(non_sellable_zones)
    sub_zones = SubZoneMapping.objects.filter(zone__zone__in=exclude_zones, zone__user=warehouse.id).\
        values_list('sub_zone__zone', flat=True)
    if sub_zones:
        exclude_zones = list(chain(exclude_zones, sub_zones))
    return exclude_zones

def get_exclude_locations(user):
    excluded_locations = []
    excluded_locations = CycleCount.objects.filter(sku__user=user.id,status=1,run_type='short pick').values_list('location__location', flat=True).distinct()
    return excluded_locations


def get_sku_master(warehouse: User, is_list ='', instance_name=SKUMaster):
    if not is_list:
        sku_master = instance_name.objects.filter(user=warehouse.id)
    else:
        sku_master = instance_name.objects.filter(user__in=warehouse)

    sku_master_ids = sku_master.values_list('id', flat=True)
    return sku_master, sku_master_ids

def get_uom_with_multi_skus(user, sku_codes, uom_type, uom=''):
    sku_uom_dict = {}
    company_id = get_company_id(user)
    filt_dict = {'sku_code__in': sku_codes, 'company_id': company_id, 'uom_type': uom_type}
    if uom:
        filt_dict['uom'] = uom
    sku_uoms = list(UOMMaster.objects.filter(**filt_dict).values('sku_code', 'uom', 'conversion', 'base_uom'))
    for sku_uom in sku_uoms:
        sku_uom_dict[sku_uom['sku_code']] = {
            'measurement_unit': sku_uom['uom'],
            'sku_conversion': float(sku_uom['conversion']),
            'base_uom': sku_uom['base_uom']
        }
    
    return sku_uom_dict

def get_uom_with_sku_code(user, sku_code, uom_type, uom=''):
    uom_dict = {'measurement_unit': '', 'sku_conversion': 1}
    company_id = get_company_id(user)
    filt_dict = {'sku_code': sku_code, 'company_id': company_id, 'uom_type': uom_type}
    if uom:
        filt_dict['uom'] = uom
    sku_uom = UOMMaster.objects.filter(**filt_dict)
    if not sku_uom.exists() and 'uom' in filt_dict.keys():
        del filt_dict['uom']
        sku_uom = UOMMaster.objects.filter(**filt_dict)
    if sku_uom.exists():
        uom_dict['measurement_unit'] = sku_uom[0].uom
        uom_dict['sku_conversion'] = float(sku_uom[0].conversion)
        uom_dict['base_uom'] = sku_uom[0].base_uom
    return uom_dict

def get_warehouses_list(user):
    warehouses_ids = []
    warehouse_users ={}
    main_warehouses = UserGroups.objects.filter(admin_user_id=user.id)
    if main_warehouses:
        warehouses_ids = list(main_warehouses.values_list('user_id', flat=True))
        warehouse_users = User.objects.filter(id__in=warehouses_ids)
    check_zone_users = UserGroups.objects.filter(admin_user_id__in=warehouses_ids)
    if check_zone_users:
        warehouses_ids = list(check_zone_users.values_list('user_id', flat=True))
        warehouse_users = User.objects.filter(id__in=warehouses_ids)
    return warehouses_ids, warehouse_users

class WMSListView(ListView):
    def set_user_credientials(self):
        '''
        Get request user, warehouse and accessible_warehouses
        '''
        self.user = self.request.user
        self.warehouse = self.request.warehouse
        if hasattr(self, 'accessible_warehouses'):
            self.accessible_warehouses = self.request.accessible_warehouses

def get_warehouse(f):
    def wrap(request, *args, **kwargs):
        kwargs.update({
            'warehouse': request.warehouse,
        })
        return f(request, *args, **kwargs)

    return wrap

def class_get_warehouse(f):
    def wrap(class_obj, *args, **kwargs):
        kwargs['warehouse'] = class_obj.request.warehouse
        return f(class_obj, *args, **kwargs)

    wrap.__doc__ = f.__doc__
    wrap.__name__ = f.__name__
    return wrap

def get_company_admin_user(user):
    company_id = get_company_id(user)
    admin_user = UserProfile.objects.filter(warehouse_level=0, company_id=company_id)
    if not admin_user:
        admin_user = user
    else:
        admin_user =  admin_user[0].user
    return admin_user

def get_user_ip(request):
    ip_address = ''
    try:
        ip, is_routable = get_client_ip(request)
        ip_address = ip
    except Exception as e:
        log.info("Error getting IP %s" % str(e))
    return ip_address

def get_user_time_zone(warehouse: User, user_id=''):
    time_zone = 'Asia/Calcutta'
    filters = {'timezone__isnull': False}
    if user_id:
        filters['user_id'] = user_id
    else:
        filters['user_id'] = warehouse.id

    user_timezone = UserProfile.objects.filter(**filters).values_list('timezone', flat=True).first()
    if user_timezone:
        time_zone = user_timezone

    return time_zone


def get_local_date_with_time_zone(time_zone, input_date, send_date=''):
    utc_time = input_date.replace(tzinfo=pytz.timezone('UTC'))
    local_time = utc_time.astimezone(pytz.timezone(time_zone))
    if send_date:
        return local_time
    dt = local_time.strftime(date_time_format)
    return dt

def frame_datatable_column_filter(col_filters=None, date_filters=None):
    column_filters = {}
    if col_filters is None:
        col_filters = {}
    if date_filters is None:
        date_filters = []

    date_filters = date_filters + DATE_FILTERS
    for col_name, value in col_filters.items():
        if col_name in date_filters:
            try:
                column_filters[col_name + "__date"] = parser.parse(value).strftime(DATE_FORMAT)
            except Exception:
                log.debug(traceback.format_exc())
        else:
            column_filters[col_name+"__icontains"] = value
    return column_filters

def frame_datatable_header_filter(col_filters={}):
    column_filters = {}
    for col_name, value in col_filters.items():
        column_filters[col_name] = value
    return column_filters

def get_filtered_params_search(filters, data_list: list):
    filter_params1 = {}
    filter_params2 = {}
    for key, value in filters.items():
        col_num = int(key.split('_')[-1])
        if value:
            filter_params2[data_list[col_num] + '__icontains'] = value
            filter_params1[data_list[col_num] + '__istartswith'] = value
    return filter_params1, filter_params2

def get_filtered_params_search_new(filters, data_list: list):
    filter_params1 = {}
    filter_params2 = {}
    for key, value in filters.items():
        if key in ['zone']:
            key = 'zone__zone'
        col_num = data_list.index(key)
        if value:
            filter_params2[data_list[col_num] + '__icontains'] = value
            filter_params1[data_list[col_num] + '__istartswith'] = value
    return filter_params1, filter_params2

def get_filtered_params(filters, data_list: list):
    filter_params = {}
    for key, value in filters.items():
        try:
            col_num = int(key.split('_')[-1])
        except Exception:
            continue
        if col_num >= len(data_list):
            continue
        if value:
            filter_params[data_list[col_num] + '__icontains'] = value
    return filter_params

def get_company_id(warehouse: User,obj=False):
    company = warehouse.userprofile.company
    while(1):
        if not company.parent:
            break
        else:
            company = company.parent
    return company.id if not obj else company


def validate_special_characters(value, regular_expression) -> str:
    ''' function to check 
        if given value has special characters 
        (regular expression can be given as per the validation required)
    '''
    status = "Success"
    if(bool(re.match(regular_expression,value))==False):
            status = 'Found special character'
    return status

@get_warehouse
def get_user_attributes_list(request, warehouse: User):
    attr_model = request.GET.get('attr_model')
    attributes = get_user_attributes(warehouse, attr_model)
    return HttpResponse(json.dumps({'data': list(attributes), 'wms_fields': WMS_FIELDS, 'status': 1}))

def get_user_attributes(warehouse: User, attr_model='', attr_ids=[]):
    attributes = []
    values_list = [
            'id', 'attribute_model', 'attribute_name', 'show_name',
            'attribute_type','attribute_values',
            'is_mandatory', 'attribute_order'
            ]
    filters = {'user_id' : warehouse.id, 'status' : 1}
    if attr_model:
        filters['attribute_model'] = attr_model
    if attr_ids:
        filters['id__in'] = attr_ids
    
    if attr_model or attr_ids:
        attributes = UserAttributes.objects\
            .filter(**filters)\
            .values(*values_list)\
            .order_by('attribute_order', 'attribute_type', 'id')
    return attributes

def get_extra_fields_for_upload(user, upload_name):
    extra_fields = list(UserAttributes.objects.filter(user_id=user.id, attribute_model=upload_name, status=1).annotate(attr_name=Case(When(is_mandatory=True,\
        then = Concat('attribute_name', Value('*'))), default='attribute_name')).values_list('attr_name',flat=True))
    return extra_fields

def get_sister_warehouse(warehouse: User):
    warehouses = UserGroups.objects.filter(Q(admin_user=warehouse) | Q(user=warehouse))
    return warehouses

def get_warehouse_user_from_sub_user(user_id: int):
    warehouseid = None
    subuser = User.objects.get(id=user_id)
    warehouseid = subuser.current_warehouse 
    if not warehouseid: 
        permgroup = AdminGroups.objects.filter(group_id__in=subuser.groups.all().values_list('id', flat=True))
        if permgroup.exists():
            warehouseid = permgroup[0].user
    return warehouseid

def get_model_fields(model):
    return [field.name for field in model._meta.get_fields()]

def get_or_none(model_obj, search_params):
    try:
        data = model_obj.objects.get(**search_params)
    except Exception:
        data = ''
    return data

def get_misc_object(misc_type, warehouse_id):
    misc_obj = MiscDetail.objects.filter(user=warehouse_id, misc_type=misc_type)
    if misc_obj:
        return misc_obj[0]
    else:
        return ''

def get_misc_value(misc_type, warehouse_id: int, number=False,boolean=False):
    misc_value = 'false'
    if number:
        misc_value = 0
    data = MiscDetail.objects.filter(user=warehouse_id, misc_type=misc_type)
    if data:
        misc_value = data[0].misc_value
    if boolean:
        if misc_value == 'true':
            return True
        else:
            return False
    return misc_value

def get_multiple_misc_values(misc_types, warehouse_id: int, account_id: int = None):
    misc_filters = {'user': warehouse_id, 'misc_type__in': misc_types}
    if account_id:
        misc_filters['account_id'] = account_id
    misc_data = list(MiscDetail.objects.filter(**misc_filters).values('misc_type', 'misc_value'))
    misc_values= {}
    for misc in misc_data:
        misc_values[misc.get('misc_type')] = misc.get('misc_value', '')
    return misc_values

def get_multiple_misc_options_values(misc_types, warehouse_id: int, account_id: int = None):
    misc_filters = {'misc_detail__user': warehouse_id, 'misc_detail__misc_type__in': misc_types}
    if account_id:
        misc_filters['account_id'] = account_id
    misc_data = list(MiscDetailOptions.objects.filter(**misc_filters).values('misc_detail__misc_type', 'misc_value'))
    misc_values = {}
    for misc in misc_data:
        misc_type = misc.get('misc_detail__misc_type')
        misc_value = misc.get('misc_value', '')
        if misc_type not in misc_values:
            misc_values[misc_type] = []
        misc_values[misc_type].append(misc_value)
    return misc_values

def config_multiple_misc_values(misc_types, warehouse_id: int):
    misc_data = list(MiscDetail.objects.filter(user=warehouse_id, misc_type__in=misc_types).values('misc_type', 'misc_value'))
    misc_values= {}
    check_response = ['false', '', None]
    for misc in misc_data:
        if misc.get('misc_value', '') in check_response:
            misc_values[misc.get('misc_type', '')] = ""
        elif misc.get('misc_type') == 'prefill_batch_number' and misc.get('misc_value', '') in check_response:
            misc_values[misc.get('misc_type')] = "true"
        else:
            misc_values[misc.get('misc_type')] = misc.get('misc_value', '')
    return misc_values

def get_multiple_misc_value_split(misc_types, warehouse_id: int):
    # Use values_list to get misc_type and misc_value as tuples
    misc_data = set(MiscDetail.objects.filter(user=warehouse_id, misc_type__in=misc_types).values_list('misc_type', 'misc_value'))
    misc_values = {}
    check_response = ('false', '', None)  # Define check_response as a tuple
    for misc in misc_data:
        misc_type, misc_value = misc  # Unpack the tuple (misc_type, misc_value)
        if misc_value in check_response:
            misc_values[misc_type] = []
        else:
            misc_values[misc_type] = misc_value.split(',')    
    return misc_values

def get_admin(user: User):
    is_admin_exists = UserGroups.objects.filter(user=user)
    if is_admin_exists:
        admin_user = is_admin_exists[0].admin_user
    else:
        admin_user = user
    return admin_user

def get_utc_time_from_user_timezone(input_date: str):
    naive_datetime = timezone.datetime.strptime(input_date, "%Y-%m-%d %H:%M:%S")
    local_datetime = timezone.make_aware(naive_datetime, timezone.get_current_timezone())
    utc_datetime = local_datetime.astimezone(timezone.utc)
    return utc_datetime

def get_utc_time_from_user_time_input(input_date: str):
    """
    Converts an input date string to a UTC datetime object.
    
    :param input_date: A string representing the date.
    :return: A timezone-aware datetime object in UTC or None on failure.
    """
    try:
        # Parse the input date string
        input_date = parser.parse(input_date)
        
        # If naive, assign the current timezone
        if input_date.tzinfo is None:
            input_date = input_date.replace(tzinfo=timezone.get_current_timezone())
        
        # Convert to UTC
        return input_date.astimezone(timezone.utc)
    except (ValueError, parser.ParserError):
        return None

def get_local_date(warehouse: User, input_date, send_date=''):
    local_time = input_date
    try:
        utc_time = input_date.replace(tzinfo=pytz.timezone('UTC'))
        user_details = UserProfile.objects.get(user_id=warehouse.id)
        time_zone = 'Asia/Calcutta'
        if user_details.timezone:
            time_zone = user_details.timezone
        local_time = utc_time.astimezone(pytz.timezone(time_zone))
        if send_date:
            return local_time
    except Exception:
        pass
    dt = local_time.strftime(date_time_format)
    return dt

def get_local_date_known_timezone(known_zone: str, input_date, send_date=''):
    local_time = input_date
    try:
        utc_time = input_date.replace(tzinfo=pytz.timezone('UTC'))
        time_zone = 'Asia/Calcutta'
        if known_zone:
            time_zone = known_zone
        local_time = utc_time.astimezone(pytz.timezone(time_zone))
        if send_date:
            return local_time
    except Exception:
        pass
    dt = local_time.strftime(date_time_format)
    return dt

def get_utc_start_date(date_obj, timezone='Asia/Calcutta'):
    # Getting Time zone aware start time

    ist_unaware = datetime.datetime.strptime(str(date_obj.date()), DATE_FORMAT)
    ist_aware = pytz.timezone(timezone).localize(ist_unaware)
    converted_date = ist_aware.astimezone(pytz.UTC)
    return converted_date

def validate_email(email: str):
    regex = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    if not(re.fullmatch(regex, email)):
        return True
    else:
        return False

def get_multiple_misc_options(misc_keys, misc_value, user: User):
    misc_data = list(MiscDetailOptions.objects.annotate(misc_value_str=Lower('misc_value')).\
        filter(
            misc_detail__user = user.id, misc_value_str__in = [misc_value.lower(), 'all'],
            misc_key__in=misc_keys, status=1
        ).exclude(misc_value='').values_list('misc_key', flat=True))
    misc_values = {}
    for misc_key in misc_keys:
        if misc_key in misc_data:
            misc_values[misc_key] = 'true'
    return misc_values

def get_misc_options_list(misc_keys, user: User, to_lower_case: bool = True):
    misc_details = {}
    annotate_dict = {'misc_value_str': F('misc_value')}
    if to_lower_case:
        annotate_dict['misc_value_str'] = Lower('misc_value')

    misc_data_objects = list(MiscDetailOptions.objects.filter(misc_detail__user = user.id, misc_key__in=misc_keys, status=1).exclude(
        misc_value='').values('misc_key').annotate(**annotate_dict))
    for misc_record in misc_data_objects:
        misc_key = misc_record.get('misc_key', '') or ''
        misc_value = misc_record.get('misc_value_str', '') or ''
        if misc_key and misc_value:
            if not misc_details.get(misc_key, []):
                misc_details[misc_key] = []
            misc_details[misc_key].append(misc_value)

    return misc_details

def process_date(value: str):
    value = value.split('/')
    try:
        value = datetime.date(int(value[2]), int(value[0]), int(value[1])) if len(value) > 1 else ''
    except Exception:
        value = datetime.date(int(value[2]), int(value[1]), int(value[0])) if len(value) > 1 else ''
    return value

def process_datetime(value):
    value = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M')
    return value

def get_sub_users(user: User):
    sub_users = AdminGroups.objects.get(user_id=user.id).group.user_set.filter()
    return sub_users

def update_user_role(user: User, sub_user: User, position, old_position=''):
    company_id = get_company_id(user)
    company_role = CompanyRoles.objects.filter(company_id=company_id, role_name=position, group__isnull=False)
    if old_position:
        old_role = CompanyRoles.objects.filter(company_id=company_id, role_name=old_position, group__isnull=False)
        if old_role:
            sub_user.groups.remove(old_role[0].group)
    if company_role.exists():
        group = company_role[0].group
        sub_user.groups.add(group)

def get_related_users_filters(warehouse_id: int, warehouse_types='', warehouse='', company_id='', send_parent=False, exclude_company=''):
    """ this function generates all users related to a user with filters"""
    user = User.objects.get(id=warehouse_id)
    main_company_id = get_company_id(user)
    if warehouse_types:
        user_groups = UserGroups.objects.filter(user__userprofile__warehouse_type__in=warehouse_types,
                                                company_id=main_company_id)
    else:
        user_groups = UserGroups.objects.filter(company_id=main_company_id)
    if warehouse:
        user_groups = user_groups.filter(admin_user__username__in=warehouse)
    user_list1 = list(user_groups.values_list('user_id', flat=True))
    user_list2 = list(user_groups.values_list('admin_user_id', flat=True))
    if not send_parent:
        user_list2 = []
    all_users = list(set(user_list1 + user_list2))
    all_user_objs = User.objects.filter(id__in=all_users).exclude(is_active=False)
    if company_id:
        if exclude_company == 'true':
            all_user_objs = all_user_objs.exclude(userprofile__company_id=company_id)
        else:
            all_user_objs = all_user_objs.filter(userprofile__company_id=company_id)
    return all_user_objs

        
def create_new_role(role_name):
    cat_ic = [
        'view_skumaster', 'add_skumaster', 'change_skumaster', 'view_taxmaster', 'add_taxmaster', 'view_suppliermaster',
        'view_skusupplier','view_stockdetail', 'view_pricemaster', 'add_currencyexchangemaster'
    ]
    pur_ic = [
        'view_suppliermaster','add_suppliermaster','change_suppliermaster','view_skusupplier','add_skusupplier',
        'change_skusupplier','view_taxmaster','add_taxmaster','view_skumaster','add_openpo','delete_purchaseorder',
        'add_openst','add_skustock','add_stockdetail','view_purchaseorder','view_openpo','view_sellerposummary',
        'change_openpo', 'add_pendingpo', 'view_pendingpo', 'view_pricemaster', 'add_currencyexchangemaster',
    ]
    inwd_ic = [
        'view_asnsummary','add_asnsummary','view_advanced_shipment_notice',
        'view_sellerposummary','add_sellerposummary',
        'add_purchaseorder','change_purchaseorder',
        'view_returntovendor', 'add_returntovendor',
        'view_polocation', 'view_openst',
        'view_salesreturn', 'add_salesreturn'
    ]
    put_ic = ['add_polocation','change_polocation','view_polocation']
    prod_ic = ['add_bommaster','view_skumaster','add_jomaterial','add_materialpicklist','add_joborder','add_rmlocation','add_skustock','add_stockdetail','view_statustracking','view_joborder','view_materialpicklist']
    inv_ic = [
        'add_skustock','add_stockdetail','add_cyclecount','change_inventoryadjustment','add_inventoryadjustment',
        'view_skuclassification','add_skuclassification','change_skuclassification','view_cyclecount',
        'view_moveinventory','view_inventoryadjustment','view_stockdetail','add_batosa', 'change_cyclecount',
    ]
    ord_mngt_ic = [
        'add_orderdetail','add_picklist','delete_orderdetail','view_customermaster','add_customermaster',
        'change_customermaster','view_skumaster','add_taxmaster','add_orderreturns','add_stocktransfer',
        'view_orderdetail','view_staginginfo','view_orderpackaging','view_stocktransfer','view_sellerordersummary',
        'view_pricemaster', 'add_currencyexchangemaster', 'create_order', 'cancel_order', 'update_order',
        'order_hold_and_release', 'view_packing', 'delete_packing', 'view_lpn_consolidation',
        'view_sortingstationmaster', 'add_sortingstationmaster',
    ]
    out_ic = [
        'add_picklist','delete_picklist','view_orderdetail','view_shipmentinfo', 'add_picklistlocation', 'view_picklistlocation', 'view_picklist','view_sellerordersummary',
        'create_order', 'cancel_order', 'update_order', 'order_hold_and_release', 'generate_picklist',
        'confirm_picklist', 'cancel_picklist', 'print_picklist', 'release_picker', 'user_zone_assignment',
        'generate_invoice', 'create_shipment', 'create_manifest', 'close_manifest','create_manifest_mobile','close_manifest_mobile', 'generate_eway_bill', 
        'cancel_eway_bill', 'generate_consolidate_eway_bill', 'picklist_priority_override', 'view_packing', 'delete_packing', 'view_lpn_consolidation', 'print_shipment_label'
    ]
    pick_ic = ['add_picklistlocation', 'view_picklistlocation', 'confirm_picklist', 'release_picker', 'print_picklist']
    pack_ic = ['add_sellerordersummary', 'add_cartontypes','view_picklist','view_orderpackaging', 'view_packing', 'delete_packing', 'view_lpn_consolidation']
    ship_ic = [
        'add_sellerordersummary','add_shipmentinfo','view_shipmentinfo','view_orderdetail','view_ordershipment','view_sellerordersummary',
        'create_shipment', 'create_manifest', 'cancel_shipment', 'close_manifest','create_manifest_mobile','close_manifest_mobile', 'cancel_manifest', 'update_shipment',
        'generate_invoice', 'generate_eway_bill', 'cancel_eway_bill', 'generate_consolidate_eway_bill', 'print_shipment_label'
    ]
    cust_scs_ic = ['view_orderdetail','view_sellerordersummary']
    wh_ic = ['add_staffmaster','add_cyclecountschedule','add_locationmaster','view_locationmaster',
             'add_currencyexchangemaster', 'read_configurations','view_cartontypes', 'view_skupackmaster',
             'view_replenushmentmaster', 'view_customersku','view_bommaster', 'view_uomdetail',
             'view_staffmaster', 'view_ordertypezonemapping', 'view_currencyexchangemaster',
             'view_purchaseapprovalconfig', 'view_batosadetail', 'view_qualitycontrol', 'view_jomaterial',
             'view_picklistlocation', 'view_shipmentinfo', 'view_userintegrationcalls']
    qc_ic = ['add_qualitycontrol'] 
    acc_manager = ['add_miscdetail']
    po_approver = ['change_pendingpo', 'view_pendingpo']
    inv_approver = ['can_approve_inventory_adjustment','change_inventoryadjustment','add_inventoryadjustment','view_inventoryadjustment',
                'change_cyclecount','add_cyclecount','view_cyclecount']
    dispense_manager = [
        'create_dispense', 'update_dispense_checklist', 'verify_dispense_checklist', 'confirm_dispense', 'cancel_dispense',
        'create_dispenseitem', 'confirm_dispenseitem', 'verify_source_dispenseitem', 'cancel_dispenseitem',
        'verify_destination_dispenseitem'
        ]
    pick_and_pass = ['pick_and_pass']

    ba_to_sa_assignment_based_on_destination_details = ['ba_to_sa_assignment_based_on_destination_details']

    gate_manager = ['create_and_update_gate_pass','approve_gate_pass','cancel_gate_pass']
    sorting_ic = ['view_sortingstationmaster', 'add_postsortingdetails']
    ARS_INCHARGE_PERMS = [
        'view_arsreplenishmentclassificationmaster', 'add_arsreplenishmentclassificationmaster',
        'change_arsreplenishmentclassificationmaster'
    ]
    order_approver = ['can_approve_order']

    all_perms = Permission.objects.exclude(codename__in=['DASHBOARD', 'view_dashboard', 'pick_and_pass', 'ba_to_sa_assignment_based_on_destination_details']).values_list("codename", flat=True)

    PERMISSION_DICT = {
        'Catalog Incharge': cat_ic,
        'Purchase Incharge': pur_ic,
        'Inbound Incharge': inwd_ic,
        'Putaway Incharge': put_ic,
        'Production Incharge': prod_ic,
        'Inventory Incharge': inv_ic,
        'Order Management Incharge': ord_mngt_ic,
        'Outbound Incharge': out_ic,
        'Picking Incharge': pick_ic,
        'Packing Incharge': pack_ic,
        'Shipment Incharge': ship_ic,
        'Customer Success Incharge': cust_scs_ic,
        'QC Incharge' : qc_ic,
        'Warehouse Manager' : wh_ic + list(set(cat_ic + pur_ic + inwd_ic + put_ic + prod_ic + inv_ic + ord_mngt_ic + out_ic + pick_ic + pack_ic + ship_ic + cust_scs_ic + acc_manager + gate_manager + sorting_ic)),
        'Account Manager': acc_manager,
        'PO Approver': po_approver,
        'Inventory Approver': inv_approver,
        'Dispense Manager' : dispense_manager,
        'Pick and Pass' : pick_and_pass,
        'Gate Manager' : gate_manager,
        'BA to SA Assignment Based on Destination Zone/Subzone': ba_to_sa_assignment_based_on_destination_details,
        'Order Approver': order_approver,
        'Sorting Incharge': sorting_ic,
        'ARS Incharge': ARS_INCHARGE_PERMS,
        'STOCKONE_ALL_PERMS': all_perms
    }
    role, created = Role.objects.get_or_create(name=role_name, code_name=role_name, remarks=0)
    for perm in PERMISSION_DICT.get(role_name, []):
        permission_obj = Permission.objects.filter(codename=perm)
        if permission_obj:
            role.permissions.add(permission_obj[0])
        else:
            new_perm = Permission.objects.create(name=perm, codename=perm, content_type_id=2)
            role.permissions.add(new_perm)

    return role

def get_user_role_ids(user: User):
    role_ids = list(user.roles.values_list('id', flat=True))
    return role_ids

def get_warehouse_roles(warehouse: User):
    company  = warehouse.userprofile.company
    role_params = {}
    if company.custom_roles:
        role_params['company'] = company
    else:
        role_params['company__isnull']  = True
        
    roles = Role.objects.filter(**role_params)
    return roles

def get_all_company_roles(warehouse: User):

    company = warehouse.userprofile.company
    role_params = Q()

    if company.custom_roles:
        role_params |= Q(company=company)
        if company.default_roles:
            role_params |= Q(company__isnull=True)
    else:
        role_params = Q(company__isnull=True)

    roles = Role.objects.filter(role_params)
    return roles

def convert_roles_ids(role_ids: list, warehouse: User):
    try:
        role_ids = [int(role_id) for role_id in role_ids]
        available_roles = get_all_company_roles(warehouse)
        opted_roles = available_roles.filter(id__in=role_ids)
        if len(opted_roles) != len(role_ids):
            return True, "One or More Roles are Invalid"

        return False, role_ids
    except ValueError:
        return True, "Invalid role IDs"

def update_user_roles(request, sub_users, selected_list, warehouse: User=None):
    roles = get_all_company_roles(warehouse)
    selected_roles = list(roles.filter(name__in=selected_list))
    for sub_user in sub_users:
        sub_user.roles.set(selected_roles)

def update_user_roles_id(request, sub_users, selected_list, warehouse: User=None):
    roles = get_all_company_roles(warehouse)
    selected_roles = list(roles.filter(id__in=selected_list))
    for sub_user in sub_users:
        sub_user.roles.set(selected_roles)


def add_warehouse_sub_user(user_dict, user):
    user_exists = User.objects.filter(username=user_dict['username'])
    if user_exists.exists():
        return 'Username already exists'

    user_emails = User.objects.filter(email=user_dict['email'])
    if user_emails.exists():
        return 'Email already exists'

    user_type = 'sub_user'
    new_user = User.objects.create_user(**user_dict)
    newEmp = EmployeeMaster(
        user=new_user,
        location=user,
        availability=True,
        status=True
    )
    newEmp.save()
    up, created = UserProfile.objects.get_or_create(user_id=new_user.id)
    up.user_type = user_type
    if created:
        up.company = new_user.current_warehouse.userprofile.company
    up.save()
    new_app = Application(
        client_type='confidential',
        authorization_grant_type='client-credentials',
        name=new_user.username,
        user=new_user,
        skip_authorization=0
    )
    new_app.save()
    admin_group = AdminGroups.objects.filter(user_id=user.id)
    if admin_group:
        group = admin_group[0].group
    else:
        group, created = Group.objects.get_or_create(name=user.username)
        admin_dict = {'group_id': group.id, 'user_id': user.id}
        admin_group = AdminGroups(**admin_dict)
        admin_group.save()
        user.groups.add(group)
    new_user.groups.add(group)
    #new_user.groups.add(group)
    return 'Added Successfully'



def get_incremental(warehouse: User, type_name, default_val='', is_invoice_format=False, increment=1, filters=None):
    # custom sku counter
    increment_data = {}
    with transaction.atomic('default'):
        if not default_val:
            default = 1001
        else:
            default = default_val
        inc_filters = {'user': warehouse.id, 'type_name': type_name}
        if filters:
            inc_filters.update(filters)
        inc_ids = list(IncrementalTable.objects.filter(**inc_filters).values_list('id', flat=True))
        data = IncrementalTable.objects.using('default').filter(id__in = inc_ids).select_for_update()
        if data:
            data = data[0]
            count = data.value + 1
            data.value = data.value + increment
            increment_data = {'prefix': data.prefix, 'interfix': data.interfix, 'date_type':data.date_type, 'suffix': data.suffix, 'delimiter': data.delimiter, 'max_length': data.max_length}
            data.save()
        else:
            IncrementalTable.objects.create(user_id=warehouse.id, type_name=type_name, value=default + increment - 1, account=warehouse.userprofile)
            count = default
        if is_invoice_format:
            return count, increment_data
        return count

def get_decremental(user, type_name, old_pack_ref_no=0):
    # custom sku counter
    data = IncrementalTable.objects.filter(user=user.id, type_name=type_name)
    if data:
        data = data[0]
        old_pack_ref_no_list = re.findall(r'\d+',old_pack_ref_no)
        if old_pack_ref_no_list:
            old_pack_ref_no = old_pack_ref_no_list[0]
        if int(data.value) == int(old_pack_ref_no) :
            data.value = data.value - 1
        data.save()
        return 'Success'
    else:
        return 'Fail'

def update_staff_warehouse_mapping(staff_obj: StaffMaster, warehouse_obj: User, status: int):
    staff_warehouse, created = StaffWarehouseMapping.objects.get_or_create(staff=staff_obj,  warehouse=warehouse_obj)
    staff_warehouse.status = status
    staff_warehouse.save()
    
def add_custom_user_permissions(request, user_obj: User, perm_list: list, user=None):
    ''' Adds a Permission to user if not available it creates and add the permission'''
    available_perms = Permission.objects.values_list("codename", flat=True)
    for perm in perm_list:
        if perm not in available_perms:
            new_perm = Permission.objects.create(name=perm, codename=perm, content_type_id=2)
            user_obj.user_permissions.add(new_perm)
        else:
            exiting_perm = Permission.objects.filter(codename=perm)
            user_obj.user_permissions.add(exiting_perm[0])

def get_search_params(request, user=''):
    """
    Zone Code is (NORTH, EAST, WEST, SOUTH)
    Zone Id is Warehouse Zone.
    """
    search_params = {}
    filter_params = {}
    headers = []
    date_fields = ['from_date', 'to_date','invoice_date','creation_date', 'grn_to_date', 'grn_from_date','delivery_from_date', 'delivery_to_date']
    datetime_fields = ['from_datetime', 'to_datetime','exp_to_datetime','exp_from_datetime']
    data_mapping = {'start': 'start', 'length': 'length', 'draw': 'draw', 'search[value]': 'search_term',
                    'order[0][dir]': 'order_term', 'order_index': 'order_index', 'global_search': 'search_term',
                    'sort_col' : 'order_index', 'sort_dir' : 'order_term',
                    'order[0][column]': 'order_index', 'from_date': 'from_date', 'to_date': 'to_date',
                    'wms_code': 'wms_code','status':'status', 'sku_brand':'sku_brand', 'manufacturer':'manufacturer',
                    'searchable':'searchable','bundle':'bundle',
                    'supplier': 'supplier', 'sku_code': 'sku_code', 'category': 'sku_category', 'sub_category': 'sub_category',
                    'sku_category': 'sku_category', 'sku_type': 'sku_type','sister_warehouse':'sister_warehouse',
                    'class': 'sku_class', 'zone_id': 'zone', 'location': 'location', 'open_po': 'open_po',
                    'marketplace': 'marketplace','central_order_id':'central_order_id',
                    'marketplace': 'marketplace','source_location':'source_location','destination_location':'destination_location',
                    'special_key': 'special_key', 'brand': 'sku_brand', 'stage': 'stage', 'jo_code': 'jo_code',
                    'sku_class': 'sku_class', 'sku_size': 'sku_size','order_reference':'order_reference',
                    'customer_name': 'customer_name', 'picker_name':'picker_name','picked_from':'picked_from',
                    'order_report_status': 'order_report_status', 'customer_id': 'customer_id','customers':'customers',
                    'imei_number': 'imei_number','creation_date':'creation_date','warehouse_id':'warehouse_id',
                    'order_id': 'order_id', 'job_code': 'job_code', 'jo_reference': 'jo_reference', 'rm_sku_code': 'rm_sku_code',
                    'rm_sku_batch': 'rm_sku_batch', 'rm_serial_number': 'rm_serial_number', 'job_order_code': 'job_order_code',
                    'fg_sku_code': 'fg_sku_code','warehouse_level':'warehouse_level','invoice':'invoice',
                    'rm_sku_code': 'rm_sku_code', 'pallet': 'pallet','invoice_date':'invoice_date',
                    'staff_id': 'id', 'ean': 'ean', 'invoice_number': 'invoice_number', 'dc_number': 'challan_number',
                    'zone_code': 'zone_code', 'distributor_code': 'distributor_code', 'reseller_code': 'reseller_code',
                    'supplier_id': 'supplier_id', 'rtv_number': 'rtv_number', 'corporate_name': 'corporate_name',
                    'enquiry_number': 'enquiry_number', 'enquiry_status': 'enquiry_status','discrepancy_number':'discrepancy_number',
                    'aging_period': 'aging_period', 'source_sku_code': 'source_sku_code',
                    'destination_sku_code': 'destination_sku_code',
                    'make': 'make', 'model': 'model', 'chassis_number': 'chassis_number',
                    'grn_from_date':'grn_from_date','grn_to_date':'grn_to_date', 'po_type': 'po_type',
                    'destination_sku_category': 'destination_sku_category','warehouse':'warehouse','carton_id':'carton_id',
                    'source_sku_category': 'source_sku_category', 'level': 'level', 'project_name':'project_name',
                    'customer':'customer', 'plant_code':'plant_code','product_category':'product_category', 'final_status':'final_status',
                    'priority_type': 'priority_type','pr_number': 'pr_number', 'po_number': 'po_number', 'po_status': 'po_status', 'grn_number':'grn_number',
                    'plant_name': 'plant_name', 'year': 'year', 'month_no': 'month_no', 'shipmentinfo':'shipmentinfo','from_datetime': 'from_datetime','exp_from_datetime':'exp_from_datetime','exp_to_datetime':'exp_to_datetime',
                    'to_datetime': 'to_datetime', 'picklist_number': 'picklist_number', 'order_type': 'order_type', 'receipt_type': 'receipt_type','asn_number': 'asn_number','asn_type' : 'asn_type',
                    'credit_note_number':'credit_note_number','return_id':'return_id', 'return_reference':'return_reference',
                    'chief_id': 'chief_id', 'partner_id': 'partner_id', 'cycle': 'cycle', 'from_confirmation': 'from_datetime','shipment_type': 'shipment_type',
                    'to_confirmation': 'to_datetime', 'po_name': 'po_name', 'search_key': 'search_key', 'search_value': 'search_value', 'return_type': 'return_type', 'tally_report':'tally_report','pdf':'pdf','extra_attributes' : 'extra_attributes',
                    'po_jo_sr_number': 'po_jo_sr_number', 'delivery_from_date': 'delivery_from_date', 'delivery_to_date': 'delivery_to_date', 'grn_type': 'grn_type','invoice_reference':'invoice_reference',
                    'po_jo_sr_reference': 'po_jo_sr_reference',
                    }
    int_params = ['start', 'length', 'draw', 'order[0][column]', 'order_index']
    filter_mapping = {'search0': 'search_0', 'search1': 'search_1',
                      'search2': 'search_2', 'search3': 'search_3',
                      'search4': 'search_4', 'search5': 'search_5',
                      'search6': 'search_6', 'search7': 'search_7',
                      'search8': 'search_8', 'search9': 'search_9',
                      'search10': 'search_10', 'search11': 'search_11',
                      'search12': 'search_12', 'search13': 'search_13',
                      'search14': 'search_14', 'search15': 'search_15',
                      'search16': 'search_16', 'search17': 'search_17',
                      'search18': 'search_18', 'search19': 'search_19',
                      'search20': 'search_20', 'search21': 'search_21',
                      'search22': 'search_22', 'search23': 'search_23',
                      'search24': 'search_24', 'search25': 'search_25',
                      'search26': 'search_26',
                      'cancel_invoice':'cancel_invoice', }
    request_data = request.POST
    if not request_data:
        request_data = request.GET
    for key, value in request_data.items():
        if '[data]' in key:
            headers.append(value)

        if key in data_mapping and value:
            if key in int_params:
                if value.isdigit():
                    value = int(value)
                else:
                    value = 0

            if key in date_fields:
                value = process_date(value)
            if key in datetime_fields:
                value = process_datetime(value)
            search_params[data_mapping[key]] = value
        elif key in filter_mapping:
            filter_params[filter_mapping[key]] = value
        elif key == 'special_key':
            search_params[data_mapping[key]] = value
        elif key == 'shipment_type':
            search_params[data_mapping[key]] = value
    #pos extra headers
    if user:
        headers.extend(["Billing Address" ,"Shipping Address"])
        headers.extend(["Order Taken By", "Payment Cash", "Payment Card","Payment PhonePe","Payment GooglePay","Payment Paytm"])
        extra_fields_obj = MiscDetail.objects.filter(user=user.id, misc_type__icontains="pos_extra_fields")
        for field in extra_fields_obj:
            tmp = field.misc_value.split(',')
            for i in tmp:
                headers.append(str(i))

    return headers, search_params, filter_params

def filter_or_none(model_obj, search_params):
    data = model_obj.objects.filter(**search_params)
    return data

def get_or_none(model_obj, search_params):
    try:
        data = model_obj.objects.get(**search_params)
    except Exception:
        data = ''
    return data

def get_related_users(warehouse_id: int, level=0, company_id=''):
    """ this function generates all users related to a user """
    user = User.objects.get(id=warehouse_id)
    main_company_id = get_company_id(user)
    if not level:
        user_groups = UserGroups.objects.filter(company_id=main_company_id)
    else:
        user_groups = UserGroups.objects.filter(Q(admin_user__userprofile__warehouse_level=level) |
                                                Q(user__userprofile__warehouse_level=level), company_id=main_company_id)
    user_list1 = list(user_groups.values_list('user_id', flat=True))
    user_list2 = list(user_groups.values_list('admin_user_id', flat=True))
    all_users = list(set(user_list1 + user_list2))
    if company_id:
        all_users = list(User.objects.filter(userprofile__company_id=company_id, id__in=all_users). \
                         values_list('id', flat=True))
    # log.info("all users %s" % all_users)
    return all_users

def save_json_data(user: User, data_dict: dict):
    try:
        if data_dict['json_data']:
            data = data_dict['json_data']
            if data.get('created_by',''):
                data['updated_by'] = user.username
            else:
                data['created_by'] = user.username
        else:
            data_dict['json_data']={'created_by':user.username}
    except Exception:
        data_dict['json_data']={'created_by':user.username}
    return data_dict

def update_volume(obj):
    try:
        obj.volume = float(obj.length) * float(obj.breadth) * float(obj.height)
        obj.save()
    except Exception:
        pass

def update_sku_attributes(data, request, user: User):
    for key, value in request.items():
        if 'attr_' not in key:
            continue
        if ',' in value:
            allow_multiple = True
        else:
            allow_multiple = False
        key = key.replace('attr_', '')
        exist_attributes = list(SKUAttributes.objects.filter(sku_id=data.id,
                                                                 attribute_name=key). \
                                    values_list('attribute_value', flat=True))
        attribute_type = list(UserAttributes.objects.filter(user = data.user ,attribute_model = "sku",attribute_name = key).values_list('attribute_type',flat= True))
        rem_list = set(exist_attributes) - set(value.split(','))
        if rem_list:
            SKUAttributes.objects.filter(sku_id=data.id, attribute_name=key,
                                         attribute_value__in=rem_list).delete()
        
        '''' if attribute type == Textarea then don't split the value, else split value if comma exixst '''
        if attribute_type[0] in ["Textarea","Text Field Input","Dropdown","Multi Input"]:
            update_sku_attributes_data(data,key,value, user)
        else:
            for val in value.split(','):
                update_sku_attributes_data(data, key, val, user, allow_multiple=allow_multiple)

def update_sku_attributes_data(data, key, value, user, is_bulk_create=False, create_sku_attrs=None,
                               sku_attr_mapping=None, allow_multiple=False,remove_existing=False,
                               remove_attr_ids=None):
    if value != '' or remove_existing:
        sku_attr_filter = {'sku_id': data.id, 'attribute_name': key}
        if allow_multiple:
            sku_attr_filter['attribute_value'] = value
        sku_attr_obj = SKUAttributes.objects.filter(**sku_attr_filter)
        if remove_existing:
            remove_attr_ids.extend(list(sku_attr_obj.values_list('id', flat=True)))
            sku_attr_obj = []
        if not sku_attr_obj and value:
            if not is_bulk_create:
                SKUAttributes.objects.create(sku_id=data.id, attribute_name=key, attribute_value=value,
                                             creation_date=datetime.datetime.now(), account_id=user.userprofile.id)
            else:
                if allow_multiple:
                    grp_key = '%s:%s:%s' % (str(data.id), str(key), str(value))
                else:
                    grp_key = '%s:%s' % (str(data.id), str(key))
                if grp_key not in sku_attr_mapping:
                    create_sku_attrs.append(SKUAttributes(**{'sku_id': data.id, 'attribute_name': key, 'attribute_value': value,
                                                 'creation_date': datetime.datetime.now(), 'account_id':user.userprofile.id}))
                    sku_attr_mapping.append(grp_key)
        elif sku_attr_obj and sku_attr_obj[0].attribute_value != value:
            sku_attr_obj.update(attribute_value=value)
    return create_sku_attrs, sku_attr_mapping, remove_attr_ids

def create_or_update_master_attributes(attribute_references, attribute_model, data_list, warehouse):
    try:
        # Fetch user-specific attribute data
        user_attr = list(UserAttributes.objects.filter(
            user_id=warehouse.id, 
            attribute_model=attribute_model, 
            status=1
        ).values_list('attribute_name', flat=True))
        
        # Fetch existing objects for quick lookup
        existing_objs = MasterAttributes.objects.filter(
            attribute_model=attribute_model,
            warehouse=warehouse.id,
            attribute_reference__in=attribute_references
        )
        
        # Create a dictionary of existing data for quick lookup
        existing_data = {
            (obj.attribute_reference, obj.attribute_model, obj.attribute_name): obj
            for obj in existing_objs
        }
        # Initialize lists for bulk operations
        master_attributes_to_create = []
        master_attributes_to_update = []

        # Process incoming data
        for item in data_list:
            for attribute_reference, attributes in item.items(): 
                for attribute_name, attribute_value in attributes.items():
                    process_attribute_data(
                        attribute_reference, 
                        attribute_name, 
                        attribute_value, 
                        attribute_model, 
                        warehouse, 
                        user_attr, 
                        existing_data, 
                        master_attributes_to_create, 
                        master_attributes_to_update
                    )                   
        # Perform bulk operations
        if master_attributes_to_create:
            MasterAttributes.objects.bulk_create(master_attributes_to_create, batch_size=100)
        if master_attributes_to_update:
            MasterAttributes.objects.bulk_update(master_attributes_to_update, ['attribute_value'], batch_size=100)

        return {"status": "success", "message": "Attributes processed successfully"}
    except Exception as e:
        log.error("Error during adding attributes", exc_info=True)
        return f"Error during adding attributes: {e}"
    
def process_attribute_data(attribute_reference, attribute_name, attribute_value, attribute_model, warehouse, user_attr, existing_data, create_list, update_list):
    """Helper function to process a single attribute."""
    if not user_attr or attribute_name not in user_attr:
        return
    
    key = (attribute_reference, attribute_model, attribute_name)

    # Handle creation of new attributes
    if key not in existing_data:
        payload = {
            'attribute_name': attribute_name,
            'attribute_value': attribute_value,
            'attribute_reference': attribute_reference,
            'attribute_model': attribute_model,
            'warehouse_id': warehouse.id,
            'account_id': warehouse.userprofile.id
        }
        create_list.append(MasterAttributes(**payload))
    else:
        # Handle updates for existing attributes
        existing_obj = existing_data[key]
        existing_obj.attribute_value = attribute_value
        update_list.append(existing_obj)

def get_master_attributes(attribute_references, attribute_model):
    try:
        query_results = MasterAttributes.objects.filter(
            attribute_model=attribute_model,
            attribute_reference__in=attribute_references
        ).values('attribute_name', 'attribute_value', 'attribute_reference')
        
        result = {}
        for item in query_results:
            reference = item['attribute_reference']
            attribute = {
                'attribute_name': item['attribute_name'],
                'attribute_value': item['attribute_value']
            }
            if reference not in result:
                result[reference] = []
            result[reference].append(attribute)

        return result
        
    except Exception as e:
        log.error(f"Error fetching attributes: {e}", exc_info=True)
        return {}

def get_extra_attributes(attribute_references, attribute_model):
    try:
        query_results = MasterAttributes.objects.filter(
            attribute_model=attribute_model,
            attribute_reference__in=attribute_references
        ).values('attribute_name', 'attribute_value', 'attribute_reference')
        result = {}
        for item in query_results:
            reference = item['attribute_reference']
            attribute_name = item['attribute_name']
            attribute_value = item['attribute_value']
            
            result.setdefault(reference, {})[attribute_name] = attribute_value
        return result
        
    except Exception as e:
        log.error(f"Error fetching attributes: {e}", exc_info=True)
        return {}


def upload_sku_image(request, warehouse: User, master_id, mastertype):
    #Bulk Upload
    master_doc_filters = {'master_id' : master_id, 'master_type' : mastertype}
    sku_master_docs = MasterDocs.objects.filter(**master_doc_filters)
    existed_doc_names = list(sku_master_docs.values_list('extra_flag', flat=True))
    bulk_update_docs, bulk_create_list = {}, []
    for doc_name, master_file in request.items():
        #Framing Dict to Update Master Docs if Already Exists
        if doc_name in existed_doc_names:
            bulk_update_docs[doc_name] = master_file
        else:
            master_dict = {
                'master_id':str(master_id.id),
                'master_type':mastertype,
                'extra_flag': doc_name,
                'uploaded_file' : master_file,
                'user_id' : warehouse.id,
                'account_id': warehouse.userprofile.id
                }
            bulk_create_list.append(MasterDocs(**master_dict))

    #Bulk Creation of Master Docs
    MasterDocs.objects.bulk_create_with_rounding(bulk_create_list)

    #Bulk Updation of Master Docs
    sku_master_docs = sku_master_docs.filter(extra_flag__in=list(bulk_update_docs.keys()))
    for master_obj in sku_master_docs:
        master_obj.uploaded_file = bulk_update_docs.get(master_obj.extra_flag)
    MasterDocs.objects.bulk_update_with_rounding(sku_master_docs, ['uploaded_file'])
    return "Success"

def check_update_size_type(data, value):
    sku_fields = SKUFields.objects.filter(sku_id=data.id, field_type='size_type')
    size_master = SizeMaster.objects.filter(user=data.user, size_name=value)
    if not size_master:
        return
    size_master = size_master[0]
    _value = size_master.size_name
    if not sku_fields:
        SKUFields.objects.create(sku_id=data.id, field_id=size_master.id, field_type='size_type', field_value=_value,
                                 creation_date=datetime.datetime.now())
    else:
        sku_fields[0].field_value = _value
        sku_fields[0].field_id = size_master.id
        sku_fields[0].save()

def check_update_hot_release(data, value):
    sku_fields = SKUFields.objects.filter(sku_id=data.id, field_type='hot_release')
    if not sku_fields:
        SKUFields.objects.create(sku_id=data.id, field_type='hot_release', field_value=value,
                                 creation_date=datetime.datetime.now())
    else:
        if sku_fields[0].field_value != value:
            sku_fields[0].field_value = value
            sku_fields[0].save()

def update_uom_master(user: User, data_dict={}, data=''):
    base_uom_name = ''
    company_id = get_company_id(user)
    for i in range(len(data_dict.get('uom_type', []))):
        uom_type = data_dict['uom_type'][i]
        uom_name = str(data_dict['uom_name'][i]).lower()
        conversion = data_dict['conversion'][i]
        uom_id = data_dict['uom_id'][i]
        if uom_type.lower() == 'base':
            base_uom_name = uom_name
            continue
        if isinstance(conversion, (float)):
            try:
                conversion = str(int(conversion))
            except Exception:
                conversion = str(conversion)
        else:
            conversion = str(conversion)
        name = '%s-%s' % (uom_name, conversion)
        if uom_id:
            uom_master = UOMMaster.objects.filter(id=uom_id)
            uom_master.update(name=name, conversion=conversion, uom=uom_name, base_uom=base_uom_name)
        else:
            uom_master = UOMMaster.objects.filter(company_id=company_id, sku_code=data.sku_code, name=name,
                                                  base_uom=base_uom_name, uom_type=uom_type, uom=uom_name)
            if uom_master:
                uom_master.update(conversion=conversion)
            else:
                UOMMaster.objects.create(company_id=company_id, sku_code=data.sku_code, name=name,
                                         base_uom=base_uom_name,
                                         uom_type=uom_type, uom=uom_name, conversion=conversion)

def update_ean_sku_mapping(user: User, ean_numbers: list, data, remove_existing=False):
    ean_status = 'Success'
    exist_ean_list = list(data.eannumbers_set.filter().annotate(str_eans=Cast('ean_number', CharField())).\
                          values_list('str_eans', flat=True))
    rem_ean_list = []
    if remove_existing:
        rem_ean_list = list(set(exist_ean_list) - set(ean_numbers))
    for ean_number in ean_numbers:
        if ean_number :
            ean_dict = {'ean_number': ean_number, 'sku_id': data.id, 'account': user.userprofile}
            sku_ean_check= EANNumbers.objects.filter(**ean_dict,sku__user = user.id)
            if not sku_ean_check:
                EANNumbers.objects.create(**ean_dict)
    for rem_ean in rem_ean_list:
        if str(data.ean_number) == str(rem_ean):
            data.ean_number = ''
            data.save()
        else:
            EANNumbers.objects.filter(sku_id=data.id, ean_number=rem_ean).delete()
    return ean_status

def create_default_zones(user, zone, location, sequence, segregation='sellable', storage_type = ''):
    try:
        locations = None
        creation_dict = {'user': user.id, 'zone': zone, 'segregation' : segregation, 'account_id': user.userprofile.id}
        if storage_type:
            creation_dict.update({'storage_type': storage_type})
        new_zone = ZoneMaster.objects.filter(**creation_dict).first()
        if not new_zone:
            new_zone = ZoneMaster.objects.create(**creation_dict)

        location_obj = LocationMaster.objects.filter(zone__zone=zone, zone__user = user.id, location = location)
        if not location_obj.exists():
            locations = LocationMaster.objects.create(
                location=location, max_capacity=100000,
                fill_sequence=sequence, pick_sequence=sequence, status=1,
                zone_id=new_zone.id, account_id = user.userprofile.id
            )
        else:
            locations = location_obj[0]
            if segregation == 'outbound_staging' and locations.status == 0:
                locations.status = 1
                locations.save()

        log.info('%s created for user %s' % (zone, user.username))
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info(e)
        return []
    return [locations]

def get_uploaded_files_data(request, filter_params, exclude_dict={}, return_type='dict'):
    ''' Returns Uploaded Files data from Master Docs '''
    url_path = request.META.get('HTTP_REFERER')
    master_docs = MasterDocs.objects.filter(**filter_params).exclude(**exclude_dict)
    final_data_dict = {}
    final_data_list = []
    for master_obj in master_docs:
        file_url, file_name = '', ''
        if master_obj.uploaded_file:
            file_url = master_obj.uploaded_file.url
            file_name =  master_obj.uploaded_file.name.split('/')[-1]
        data_dict = {
            'id': master_obj.id,
            'file_name': file_name,
            'file_url': file_url,
            'extra_flag': master_obj.extra_flag,
            'master_id': master_obj.master_id,
            'master_type': master_obj.master_type,
            'document_url': master_obj.document_url
        }
        if data_dict['file_url'] and not str(data_dict['file_url']).startswith('http'):
            # Appending Base URL for FileStorage based uploaded files
            data_dict['file_url'] = url_path + master_obj.uploaded_file.name
        if not data_dict['file_url'] and data_dict['document_url']:
            data_dict['file_url'] = data_dict['document_url']

        final_data_dict[master_obj.master_id] = data_dict
        final_data_list.append(data_dict)

    if return_type == 'list':
        return final_data_list
    return final_data_dict

def xcode(text, encoding='utf8', mode='strict'):
    try:
        if not isinstance(text, str):
            text = text.encode(encoding, mode)
        elif isinstance(text, SafeString):
            text = str.__str__(text)
    except Exception:
        pass
    return text

def send_data_url(excel_data, full_file_path=""):
    if getattr(settings, 'USE_S3', None):
        #have all the variables populated which are required below
        access_key = os.environ.get('AWS_ID', None)
        secret_key = os.environ.get('AWS_KEY', None)
        bucket_name = os.environ.get('AWS_STORAGE_BUCKET_NAME', "stockone-prod1")
        signature_version = os.environ.get('SIGNATURE_VERSION', 's3v4')
        region_name = os.environ.get('AWS_S3_REGION_NAME', "ap-south-1")
        if signature_version:
            client = boto3.client('s3', aws_access_key_id=access_key,
                    aws_secret_access_key=secret_key, config=Config(signature_version=signature_version),region_name = region_name)
        else:
            client = boto3.client('s3', aws_access_key_id=access_key,aws_secret_access_key=secret_key)
        transfer = S3Transfer(client)
        if full_file_path:
            transfer.upload_file(excel_data, bucket_name, full_file_path)
            excel_data = full_file_path
        transfer.upload_file(excel_data, bucket_name, excel_data)
        response= client.generate_presigned_url('get_object',
                                                Params={'Bucket': bucket_name,
                                                        'Key': excel_data},
                                                ExpiresIn=3600)
        return response

    return '../' + excel_data

def get_work_sheet(sheet_name, sheet_headers,user='',f_name='', headers_index=0, Additional_sheet_name = '', Additional_sheet_headers = '', additional_sheet = False):
    if '.xlsx' in f_name:
        wb = xlsxwriter.Workbook(f_name)
        ws = wb.add_worksheet(sheet_name)
        header_style = wb.add_format({'bold': True})
    else:
        wb = Workbook()
        ws = wb.add_sheet(sheet_name)
        header_style = easyxf('font: bold on')
        if sheet_name == 'returns':
            sales_return_extra_fields = dict(UserAttributes.objects.filter(user=user.id, attribute_model='sales_return', status=1).values_list('attribute_name', 'is_mandatory'))
            if sales_return_extra_fields:
                for field, is_mandatory in sales_return_extra_fields.items():
                    value = field
                    if is_mandatory:
                        value += '*'
                    if value not in sheet_headers:
                        sheet_headers.append(value)
        for count, header in enumerate(sheet_headers):
            ws.write(headers_index, count, header, header_style)
        if additional_sheet:
            ws = wb.add_sheet(Additional_sheet_name)
            for count, header in enumerate(Additional_sheet_headers):
                ws.write(headers_index, count, header, header_style)
    return wb, ws


def print_excel(request, temp_data, headers, warehouse: User, excel_name='', file_type='', tally_report=0):
    excel_headers = ''
    if not excel_headers:
        excel_headers = list(headers)
    if tally_report ==1:
        excel_headers = headers
    if not excel_name:
        excel_name = request.POST.get('serialize_data', '')
    if excel_name:
        file_name = "%s.%s" % (warehouse.username, excel_name.split('=')[-1])
    path = ('static/excel_files/%s.%s') % (file_name, file_type)
    if not os.path.exists('static/excel_files/'):
        os.makedirs('static/excel_files/')
    if file_type == 'csv':
        with open(path, 'w') as mycsvfile:
            thedatawriter = csv.writer(mycsvfile, delimiter=',')
            counter = 0
            try:
                thedatawriter.writerow(itemgetter(*excel_headers)(headers))
            except Exception:
                thedatawriter.writerow(excel_headers)
            for data in temp_data['aaData']:
                temp_csv_list = []
                for key, value in data.items():
                    if key in excel_headers:
                        temp_csv_list.append(str(xcode(value)))
                thedatawriter.writerow(temp_csv_list)
                counter += 1
    path= send_data_url(str(path))
    return path #HttpResponse(path_to_file)

@get_warehouse
def search_wms_codes(request, warehouse: User):
    data_id = request.GET.get('q', '')
    instance_name = SKUMaster
    sku_master, sku_master_ids = get_sku_master(warehouse, instance_name=instance_name)
    data = sku_master.filter(Q(wms_code__icontains=data_id) | Q(sku_desc__icontains=data_id), user=warehouse.id) \
                     .order_by('wms_code')
    wms_codes = [str(wms.wms_code) for wms in data[:10]]  # Limit to 10 results
    return HttpResponse(json.dumps(wms_codes))

def get_related_user_objs(user_id, level=0):
    user_ids = get_related_users(user_id, level=level)
    users = User.objects.filter(id__in=user_ids)
    return users

# @get_admin_multi_user
def check_and_get_plants(request, req_users, users=''):
    if users:
        req_users = users
    else:
        req_users = User.objects.filter(id__in=req_users)
    return req_users

def bulk_upload_master_file(request, user, master_id, master_type):
    #Bulk Upload
    master_doc_filters = {'master_id' : master_id, 'master_type' : master_type}
    master_docs = MasterDocs.objects.filter(**master_doc_filters)

    #existing file names
    existed_file_names = []
    uploaded_files = list(master_docs.values_list('uploaded_file', flat=True))
    for each_file in uploaded_files:
        file_name = each_file.split('/')[-1]
        if file_name and file_name not in existed_file_names:
            existed_file_names.append(file_name)

    bulk_create_list = []
    if request.FILES:
        for doc_name, file in request.FILES.items():
            #Framing Dict to Update Master Docs if Already Exists
            master_files = request.FILES.getlist(doc_name)
            for master_file in master_files:
                if master_file.name in existed_file_names:
                    continue
                else:
                    master_dict = {
                        'master_id':master_id,
                        'master_type':master_type,
                        'extra_flag': doc_name,
                        'uploaded_file' : master_file,
                        'user_id' : user.id,
                        'account_id' : user.userprofile.id
                        }
                    bulk_create_list.append(MasterDocs(**master_dict))

        #Bulk Creation of Master Docs
        MasterDocs.objects.bulk_create_with_rounding(bulk_create_list)
    else:
        return "No Files Found"
    return "Uploaded Successfully"

def get_uom_data(warehouse: User, sku_code, sku_measurement_type, uom_type, company_id):
    sku_uom = UOMMaster.objects.filter(sku_code=sku_code, uom_type=uom_type, company_id=company_id).first()
    sku_conversion = float(sku_uom.conversion) if sku_uom else 0
    measurement_unit = sku_uom.uom if sku_uom else sku_measurement_type
    base_uom = sku_uom.base_uom if sku_uom else ''
    return sku_conversion, measurement_unit, base_uom

def get_purchase_uom_for_sku(warehouse: User, sku_code):
    ''' Fetches Purchase UOM for SKU '''
    pack_id, pack_quantity = '', 1
    sku_pack_obj = SKUPackMaster.objects.filter(sku__sku_code=sku_code, sku__user=warehouse.id, purchase_uom=1, status=1).first()

    if sku_pack_obj:
        pack_id = sku_pack_obj.pack_id
        pack_quantity = sku_pack_obj.pack_quantity

    return pack_id, pack_quantity

def get_sku_data(warehouse: User, data):
    sku_dict = data.__dict__
    sku_code = sku_dict.get("sku_code")
    hsn_code = sku_dict.get("hsn_code") if sku_dict.get("hsn_code", "") else ""
    sku_measurement_type = sku_dict.get("measurement_type")
    company_id = get_company_id(warehouse)
    enable_purchase_uom = get_misc_value('enable_purchase_uom', warehouse.id)
    sku_uom = UOMMaster.objects.filter(sku_code=sku_code, uom_type='Purchase', company_id=company_id)
    ccf, cuom, c_base_uom = get_uom_data(warehouse, sku_code, sku_measurement_type, 'consumption', company_id)
    sku_conversion = 0
    ean_number = ''
    if sku_uom.exists():
        measurement_unit = sku_uom[0].uom
        sku_conversion = float(sku_uom[0].conversion)
    else:
        measurement_unit = sku_dict.get("measurement_type")
        sku_conversion = 0
    #Get packmaster changes
    is_purchase_uom = False
    base_uom_qty = 1
    if enable_purchase_uom == 'true':
        po_uom, base_uom_qty = get_purchase_uom_for_sku(warehouse, sku_code)
        if po_uom:
            measurement_unit = po_uom
            is_purchase_uom = True
    ean_numbers = dict(EANNumbers.objects.filter(sku__sku_code=sku_code, sku__user=warehouse.id).values_list("sku__sku_code", "ean_number"))
    if ean_numbers:
        ean_number = ean_numbers.get(sku_code, "") or ""

    tax_values = TaxMaster.objects.filter(product_type=hsn_code, user=warehouse.id).values()
    temp_tax = 0
    temp_cess_tax = 0
    if tax_values.exists():
        temp_tax = tax_values[0]['igst_tax'] + tax_values[0]['sgst_tax'] + tax_values[0]['cgst_tax']
        temp_cess_tax = tax_values[0]['cess_tax']

    data = {'wms_code': sku_dict.get("wms_code"),'sku_desc': sku_dict.get("sku_desc"),'ean_number': ean_number,'sku_size': sku_dict.get("sku_size"),
        'measurement_unit': measurement_unit,'mrp': sku_dict.get("mrp"),'sku_class': sku_dict.get("sku_class"),'style_name': sku_dict.get("style_name"),
        'conversion': sku_conversion,'base_uom': sku_measurement_type,'enable_serial_based': sku_dict.get("enable_serial_based"),
        'sku_brand': sku_dict.get("sku_brand"),'hsn_code': hsn_code, 'base_uom_qty': base_uom_qty, 'uom_qty': base_uom_qty,
        "temp_tax": temp_tax,"temp_cess_tax": temp_cess_tax,"ccf": ccf,"cuom": cuom, 'is_purchase_uom': is_purchase_uom, 'batch_based' : sku_dict.get("batch_based"),
        "sku_display_key": sku_dict.get("wms_code") + " " + sku_dict.get("sku_desc")
        }
    return data

def get_filtered_sku_pack_data(user, sku_codes, measurement_units, uom_type='purchase'):
    """
    Common function to get filtered SKU pack data based on SKU and measurement unit pairs
    Args:
        user: User object (warehouse or dest_user)
        sku_codes: List of SKU codes
        measurement_units: List of measurement units
        sku_wise_measurement_units: Dict mapping SKU codes to their measurement units
        uom_type: Type of UOM to filter ('purchase' or 'sales')
    """
    sku_pack_objs = SKUPackMaster.objects.filter(
        sku__sku_code__in=sku_codes, 
        pack_id__in=measurement_units,
        sku__user=user.id,
        status=1,
        **{f'{uom_type}_uom': 1}
    ).order_by('-pack_quantity').values('sku__sku_code', 'pack_quantity', 'pack_id')
    
    filtered_dict = {}
    for each_row in sku_pack_objs:
        sku_code = each_row['sku__sku_code']
        pack_id = each_row['pack_id']
        pack_quantity = each_row['pack_quantity']
        if sku_code not in filtered_dict:
            filtered_dict[sku_code] = {}

        if pack_id not in filtered_dict[sku_code]:
            filtered_dict[sku_code][pack_id] = pack_quantity
        
    return filtered_dict

def get_sku_pack_details_across_warehouses(warehouse_ids, sku_codes, measurement_units, uom_type='purchase'):
    """
    Get SKU pack details across multiple warehouses.

    Args:
        warehouse_ids (list): List of warehouse IDs.
        sku_codes (list): List of SKU codes.
        measurement_units (list): List of measurement units.
        uom_type (str, optional): UOM type. Defaults to 'purchase'.

    Returns:
        dict: Dictionary containing SKU pack details across warehouses.
    """
    filters = {
        "sku__user__in": warehouse_ids,
        "status": 1,
        f"{uom_type}_uom": 1
    }
    if sku_codes:
        filters["sku__sku_code__in"] = sku_codes
    if measurement_units:
        filters["pack_id__in"] = measurement_units

    sku_pack_objs = list(SKUPackMaster.objects.filter(**filters).order_by(
            '-pack_quantity').values('sku__sku_code', 'sku__user', 'pack_quantity', 'pack_id'))

    result_data = {}
    for each_row in sku_pack_objs:
        user_id = each_row.get('sku__user')
        if not result_data.get(user_id):
            result_data[user_id] = {}

        sku_code = each_row.get('sku__sku_code')
        if not result_data.get(user_id, {}).get(sku_code):
            result_data[user_id][sku_code] = {}

        pack_id = each_row.get('pack_id')
        if pack_id and not result_data[user_id][sku_code].get(pack_id.lower()):
            result_data[user_id][sku_code][pack_id.lower()] = each_row.get('pack_quantity')

    return result_data

def build_search_data(warehouse: User, to_data, from_data, limit):
    if len(to_data) >= limit:
        return to_data

    for data in from_data:
        data_dict = get_sku_data(warehouse, data)

        status = True
        for item in to_data:
            if item['wms_code'] == data.wms_code:
                status = False
                break

        if status:
            to_data.append(data_dict)

        if len(to_data) >= limit:
            break

    return to_data

def get_sku_master_data(master_data):
    master_data_dict, sku_code_list, sku_ids_list = {}, [], []
    master_data = master_data.values('id','sku_code', 'hsn_code', 'measurement_type', 'sku_desc',
                                    'sku_size', 'mrp', 'sku_class', 'style_name', 'sku_brand',
                                    'batch_based', 'sku_desc', 'enable_serial_based')
    for data in master_data:
        sku_code_list.append(data['sku_code'])
        sku_ids_list.append(data['id'])
        master_data_dict[data['sku_code']] = data
    return master_data_dict, sku_code_list, sku_ids_list

def get_sku_pack_uom_data(sku_ids_list, warehouse):
    sku_pack_dict = defaultdict(dict)

    sku_pack_objs = SKUPackMaster.objects.filter(
        sku_id__in=sku_ids_list,
        sku__user=warehouse.id,
        purchase_uom=1,
        status=1
    ).values_list('sku__sku_code', 'pack_id', 'pack_quantity').order_by('sku__sku_code', '-pack_quantity')

    for sku_code, pack_id, pack_quantity in sku_pack_objs:
        sku_pack_dict[sku_code][pack_id] = pack_quantity

    sku_pack_dict = dict(sku_pack_dict)
    return sku_pack_dict

def get_sku_wise_ean_data(sku_code_list, warehouse):
    ean_numbers = EANNumbers.objects.filter(
                    sku__sku_code__in=sku_code_list,
                    sku__user=warehouse.id
                ).values('sku__sku_code', 'ean_number')
    ean_dict = {}
    for ean in ean_numbers:
        ean_dict[ean['sku__sku_code']] = ean['ean_number']
    return ean_dict
        

def prepare_sku_search_data(master_data, warehouse, total_data):
    sku_master_data_values, sku_code_list, sku_ids_list = get_sku_master_data(master_data)
    purchase_uom = get_misc_value('enable_purchase_uom', warehouse.id)
    uom_data = get_sku_pack_uom_data(sku_ids_list, warehouse)
    ean_data = get_sku_wise_ean_data(sku_code_list, warehouse)
    for sku_code, data in sku_master_data_values.items():
        sku_conversion = 0
        pack_data = uom_data.get(sku_code, {})
        if isinstance(pack_data, dict) and pack_data:
            pack_id = list(pack_data.keys())[0]
            pack_quantity = list(pack_data.values())[0]
        else:
            pack_id = ''
            pack_quantity = 1
        if purchase_uom == 'true':
            measurement_unit = pack_id
        else:
            measurement_unit = pack_id or data.get("measurement_type")
        data={
            'wms_code': data.get("sku_code"),
            'sku_desc': data.get("sku_desc"),
            'ean_number': ean_data.get(sku_code, "") or "",
            'sku_size': data.get("sku_size"),
            'measurement_unit': measurement_unit,
            'mrp': data.get("mrp"),
            'sku_class': data.get("sku_class"),
            'style_name': data.get("style_name"),
            'conversion': sku_conversion,
            'base_uom': data.get("measurement_type"),
            'uom_data' : uom_data,
            'enable_serial_based': data.get("enable_serial_based"),
            'sku_brand': data.get("sku_brand"),
            'hsn_code': data.get('hsn_code'),
            'base_uom_qty': pack_quantity,
            'uom_qty': pack_quantity,
            'is_purchase_uom': True if pack_id else False,
            'batch_based': data.get("batch_based"),
            "sku_display_key": data.get("sku_code")+" "+data.get("sku_desc")
            }
        
        total_data.append(data)
    return total_data


@get_warehouse
def search_wms_data(request, warehouse: User):
    search_key = request.GET.get('q', '')
    total_data = []
    limit = 10
    if request.GET.get('limit'):
        limit = int(request.GET.get('limit'))

    if not search_key:
        return HttpResponse(json.dumps(total_data))
    
    master_data = SKUMaster.objects.filter(
        Q(sku_code__icontains=search_key) | Q(sku_desc__icontains=search_key) | Q(ean_number=search_key),status=1, user=warehouse.id
    )[0:limit]

    total_data = prepare_sku_search_data(master_data, warehouse, total_data)

    return HttpResponse(json.dumps(total_data))

def get_pagination_info(request_data):
    '''
    Get pagination details
    '''
    limit = request_data.get('limit', 10) or 10
    offset = request_data.get('offset', 0) or 0


    start_index = offset
    end_index = offset + limit

    page_info = {
        "start_index": start_index,
        "end_index": end_index,
        "limit": limit,
        "offset": offset
    }
    return page_info

def separate_path_and_params(full_url):
    '''
    Get path and params from full url
    '''
    parsed_url = urlparse(full_url)
    params = dict(parse_qsl(parsed_url.query))  # Convert query string to dictionary
    return params

def get_page_url(request, offset, limit):
    '''
    Get page url
    '''
    url = request.build_absolute_uri()
    params = separate_path_and_params(url)
    api_path = url.split('?')[0]
    params['limit'] = limit
    params['offset'] = offset
    return api_path + '?' + urlencode(params)

def get_paging_data(request, request_data, page_count):
    prev_url, next_url = None, None

    limit = request_data.get('limit', 10)
    offset = request_data.get('offset', 0)

    #generate previous page url
    if offset != 0:
        previous_page_offset = max(0, offset - limit)
        prev_url = get_page_url(request, previous_page_offset, limit)

    #generate next page url
    if page_count == limit:
        next_url = get_page_url(request, offset + limit, limit)

    paging = {"prev": prev_url, "next": next_url}
    return paging

def scroll_data(request, obj_lists, limit='', request_type='POST', page_num=1):
    items = 10
    if limit:
        items = limit
    if request_type == 'body':
        if request.body:
            request_data = json.loads(request.body)
        else:
            request_data = {}
    elif request_type == 'POST':
        request_data = request.POST.dict()
    else:
        request_data = request.GET.dict()
    if 'items' in request_data.keys():
        items = request_data.get('items', items)
    if 'pagenum' in request_data.keys():
        page_num = int(request_data.get('pagenum', page_num))
    paginator = Paginator(obj_lists, items)

    if items:
        try:
            records = paginator.page(page_num)
        except PageNotAnInteger:
            page_num = 1
            records = paginator.page(page_num)
        except EmptyPage:
            page_info = {'page_info': {'current_page': page_num, 'total_pages': paginator.num_pages}}
            page_info.update({'data': []})
            return page_info
    else:
        records = []
    page_info = {'page_info': {'current_page': page_num, 'total_pages': paginator.num_pages}}
    page_info.update({'data': records.object_list})
    return page_info

def create_mail_attachments(f_name, html_data):
    attachments = []
    try:
        if not isinstance(html_data, list):
            html_data = [html_data]
        for data in html_data:
            temp_name = f_name + str(secrets.randbelow(9999))
            file_name = '%s.html' % temp_name
            pdf_file = '%s.pdf' % temp_name
            path = 'static/temp_files/'
            folder_check(path)
            file = open(path + file_name, "w+b")
            #file.write(xcode(data))
            file.write(data.encode('utf8','strict'))
            file.close()
            os.system(
                "./phantom/bin/phantomjs ./phantom/examples/rasterize.js ./%s ./%s A4" % (path + file_name, path + pdf_file))
            attachments.append({'path': path + pdf_file, 'name': pdf_file})
    except Exception as e:
        log_mail_info.debug(traceback.format_exc())
        log_mail_info.info('Create Mail attachment failed for ' + str(xcode(html_data)) + ' error statement is ' + str(e))
    return attachments

def get_permission_based_sub_users_emails(warehouse: User, permission_name):
    """
        Get the list of user emails who have the provided permission.

        Parameters:
            warehouse (User): The warehouse object for whom the users are to be retrieved.
            permission (str): The permission for which the users are to be retrieved.

        Returns:
            list: A list of user emails who have the provided permission.
    """
    user_emails = list(User.objects.filter(
        is_active = True,
        staff_warehouse__staffwarehousemapping__status = 1,
        userprofile__user_type="sub_user",
        roles__permissions__codename=permission_name,
        staff_warehouse__staffwarehousemapping__warehouse_id=warehouse.id
    ).values_list('email', flat=True).distinct())

    return user_emails

def get_priceband_admin_user(warehouse: User):
    if isinstance(warehouse, int):
        price_band_flag = get_misc_value('priceband_sync', warehouse)
    else:
        price_band_flag = get_misc_value('priceband_sync', warehouse.id)
    if price_band_flag == 'true':
        admin_user = get_admin(warehouse)
    else:
        admin_user = None
    return admin_user

def validate_date(date):
    regex = re.compile("[0-9]{4}\-[0-9]{2}\-[0-9]{2}")
    match = re.match(regex, date)
    if (match):
        try:
            date = datetime.datetime.strptime(date,DATE_FORMAT)
            return date
        except Exception:
            return 0
    else:
       return 0

def update_error_message(failed_status, error_code, error_message, field_value, field_key='OrderId'):
    if 'errors' in  failed_status:
        failed_status["errors"].append(
            {
                field_key: field_value,
                "status": error_code,
                "message": error_message
            }
        )
    else:
        failed_status["errors"] = [
            {
                field_key: field_value,
                "status": error_code,
                "message": error_message
            }
        ]

def update_batch_error_message(failed_status, error_code, error_message, field_value, field_key='OrderId', order_reference=''):
    if 'errors' in  failed_status:
        failed_status["errors"].append(
            {
                "order_reference": order_reference,
                field_key: field_value,
                "status": error_code,
                "message": error_message
            }
        )
    else:
        failed_status["errors"] = [
            {
                "order_reference": order_reference,
                field_key: field_value,
                "status": error_code,
                "message": error_message
            }
        ]

def sku_relations_ratio(user,parent_sku):
    sku_relations = SKURelation.objects.filter(parent_sku__user=user.id,parent_sku=parent_sku).values('parent_sku','member_sku','quantity')
    sku_relation_dict = {}
    for each_rel in sku_relations:
        sku_key = (str(each_rel['parent_sku']),str(each_rel['member_sku']))
        if sku_key not in each_rel:
            sku_relation_dict[sku_key] = each_rel.get('quantity',0)
    return sku_relation_dict

def get_financial_year(date, delimiter = '-'):
    # It will return financial period

    date = date.date()
    year_of_date = date.year
    financial_year_start_date = datetime.datetime.strptime(str(year_of_date) + "-04-01", "%Y-%m-%d").date()
    if date < financial_year_start_date:
        return f'{delimiter}'.join([str(financial_year_start_date.year - 1)[2:], str(financial_year_start_date.year)[2:]])
    else:
        return f'{delimiter}'.join([str(financial_year_start_date.year)[2:], str(financial_year_start_date.year + 1)[2:]])

def get_user_role_names(warehouse: User):
    company = warehouse.userprofile.company
    roles_params = get_company_roles_filter(company)
    roles_list = list(Role.objects.filter(roles_params).values_list("name", flat=True))
    return roles_list

@get_warehouse
def get_warehouse_list(request, warehouse: User):
    warehouses = get_related_user_objs(warehouse.id, level=warehouse.userprofile.warehouse_level)
    if not request.user.is_staff:
        wh_ids = list(warehouses.values_list('id', flat=True))
        warehouses = check_and_get_plants(request, wh_ids)

    warehouse_list = []
    for wh in warehouses:
        if wh.id == warehouse.id:
            continue
        warehouse_list.append(
            {'warehouse_id': wh.id, 'warehouse_name': wh.username, 'warehouse_first_name': wh.first_name})
    return HttpResponse(json.dumps({'warehouses': warehouse_list}))

def get_dictionary_query(data_dict={}):
    queries = [Q(**{key: value}) for key, value in data_dict.items()]
    if queries:
        query = queries.pop()
        for item in queries:
            query |= item
    else:
        query = Q()
    return query

def build_search_term_query(columns, search_term):
    filter_params = OrderedDict()
    query = Q
    if not search_term:
        return Q()
    for col in columns:
        if not 'date' in col:
            filter_params[col + '__icontains'] = search_term
        else:
            filter_params[col + '__regex'] = search_term
    query = get_dictionary_query(data_dict=filter_params)
    return query


@get_warehouse
def get_user_roles_list(request, warehouse: User):
    total_groups = get_user_role_names(warehouse)
    return HttpResponse(json.dumps({'groups': total_groups}))

@get_warehouse
def get_company_roles(request, warehouse: User):
    company = warehouse.userprofile.company
    roles_params = get_company_roles_filter(company)
    roles_list = list(Role.objects.filter(roles_params).values("id", "name", "remarks"))
    return JsonResponse(roles_list, safe=False)

@get_warehouse
def get_user_roles(request, warehouse: User):
    user = request.user
    roles = dict(user.roles.values_list("id", "name", "remarks"))
    return JsonResponse(roles, safe=True)

def update_jsondata(instance,data):
    if instance.json_data:
        instance.json_data.update(data)
    else:
        instance.json_data = data
    instance.save()

def make_data_to_acceptable_params(list_of_items):
    from django.http import QueryDict
    constructSting = ''
    for row in list_of_items:
        for key, value in row.items():
            if '&' in str(value):
                value = value.replace('&', '#<>#')
            constructSting += '%s=%s&' % (key, value)
    return QueryDict(constructSting)

def get_product_category_from_sku(user, sku_code):
    sku = SKUMaster.objects.get(user=user.id, sku_code=sku_code)
    product_category = 'Kits&Consumables'
    return sku, product_category

def get_user_prefix_incremental(user, type_name, sku_code, dept_code='', is_check_prefix=False, full_user_prefix='', job_order_ref = False, create_default="", length=0):
    count = 0
    prefix, full_prefix, full_number, incr_type_name = '', '', '', ''
    if not full_user_prefix:
        user_prefix, inc_status = prepare_user_prefix_details(user, sku_code, type_name, create_default)
    if not is_check_prefix:
        if full_user_prefix:
            prefix = full_user_prefix
        else:
            if create_default and not user_prefix.exists():
                user_prefix = [UserPrefixes.objects.create(user_id=user.id, type_name=type_name, product_category='',
                                                        sku_category='', prefix=create_default, account=user.userprofile)]
            if not inc_status or create_default:
                user_prefix = user_prefix[0]
                prefix = user_prefix.prefix
        if prefix:
            full_number, count = prepare_incremental_prefix_details(user, prefix, type_name, job_order_ref, dept_code, length)
    elif user_prefix.exists():
        full_prefix = user_prefix[0].prefix
        
    return count, full_prefix, full_number, incr_type_name, inc_status

def prepare_user_prefix_details(user, sku_code, type_name, create_default):
    product_category, sku_category, inc_status = "", "", ""
    if sku_code:
        sku, product_category = get_product_category_from_sku(user, sku_code)
        sku_category = sku.sku_category
        if not sku_category:
            sku_category = 'Default'
    user_prefix = UserPrefixes.objects.filter(user=user.id, type_name=type_name, product_category=product_category,
                                sku_category=sku_category)
    if not user_prefix.exists():
        user_prefix = UserPrefixes.objects.filter(user=user.id, type_name=type_name, product_category=product_category,
                                                sku_category='Default')
    if not user_prefix.exists():
        user_prefix = UserPrefixes.objects.filter(user=user.id, type_name=type_name, product_category='',
                                                sku_category='')
    if not user_prefix.exists() and not create_default:
        inc_status = 'Prefix not defined'

    return user_prefix, inc_status

def prepare_incremental_prefix_details(user, prefix, type_name, job_order_ref, dept_code, length):
    full_prefix = prefix
    incr_type_name = '%s_%s' % (str(type_name), str(prefix))
    count, increment_data = 0, {}
    naming_series = False
    if type_name  in ['po_prefix','asn_prefix','grn_prefix','st_grn_prefix','sales_return_grn']:
        inc_data = IncrementalTable.objects.filter(user=user.id, type_name=type_name)
        if inc_data.exists():
            naming_series = True

    if naming_series :
        count, increment_data = get_incremental(user, type_name, default_val=1, is_invoice_format=True)
    elif type_name not in ['invoice', 'shipment_prefix', 'credit_note_prefix']:
        count = get_incremental(user, incr_type_name, default_val=1)
    else:
        count, increment_data = get_incremental(user, type_name, default_val=1, is_invoice_format=True)
    userprofile = user.userprofile
    store_code = userprofile.stockone_code
    if not dept_code:
        dept_code = '0000'
    if userprofile.warehouse_type == 'DEPT' and type_name in ['pr_prefix', 'po_prefix']:
        admin_user = get_admin(user)
        store_code = admin_user.userprofile.stockone_code
        dept_code = userprofile.stockone_code
    if type_name in ['invoice', 'shipment_prefix','credit_note_prefix']:
        full_number, count = prepare_prefix_details(increment_data,job_order_ref, count, prefix)
    elif naming_series and  increment_data.get('prefix'):
        full_number, count = prepare_prefix_details(increment_data,job_order_ref, count, prefix)
    else:
        full_prefix = '%s-%s-%s' % (prefix, store_code, dept_code)
        full_number = '%s%s' % (full_prefix, str(count).zfill(5))
        if job_order_ref:
            full_number =  '%s%s' % (prefix, str(count))
        if length:
            full_number =  '%s%s' % (prefix, str(count).zfill(length))
    
    return full_number, count

def preapre_incremental_prefix_details(increment_data, count, prefix):
    max_char = increment_data.get('max_length', 15) or 15
    prefix = increment_data.get('prefix') or prefix
    delimiter = increment_data.get('delimiter', '') or ''
    date_type = increment_data.get('date_type', 0)
    suffix = increment_data.get('suffix','') or '' 
    if date_type:
        interfix = get_incremental_datetype_format(date_type, delimiter)
    else:
        interfix = increment_data.get('interfix')
    order_list = [prefix, interfix, suffix]
    order_list = [item for item in order_list if item]
    full_number = f"{delimiter}".join(order_list)
    chars = (len(full_number)+1, len(full_number))[delimiter=='']
    full_number = f"{delimiter}".join([full_number, str(count).zfill(max_char - chars)])
    return full_number, count


def prepare_prefix_details(increment_data,job_order_ref, count, prefix):
    prefix = increment_data.get('prefix') or prefix
    date_type = increment_data.get('date_type')
    if date_type:
        interfix = get_incremental_datetype_format(date_type)
    else:
        interfix = increment_data.get('interfix')
    if interfix:
        full_number = '-'.join([prefix, interfix, str(count).zfill(7)])
    else:
        full_number = '-'.join([prefix, str(count).zfill(7)])
    if job_order_ref:
        full_number =  '%s%s' % (prefix, str(count))

    return full_number, count

@get_warehouse
def get_incremental_number(request, warehouse: User):
    request_data = json.loads(request.body)
    dept_code = request_data.get('dept_code', "")
    default_code = request_data.get('default_code', "")
    prefix = request_data.get('prefix')

    receipt_number, grn_prefix, inc_number, check_prefix, inc_status = \
            get_user_prefix_incremental(warehouse, prefix, "", dept_code=dept_code, create_default=default_code)
    if inc_number:
        return JsonResponse({"incremental_number": inc_number}, status=200)
    else:
        return JsonResponse({"message" : "Prefix Not Available"}, status=400)
    
def get_incremental_datetype_format(date_type, delimiter=''):
    now = datetime.datetime.now()
    if date_type == 1:
        date_str = get_financial_year(now, delimiter=delimiter)
    else:
        date_str = get_month_year(now, delimiter=delimiter)
    return date_str

def get_month_year(date, delimiter='-'):
    return f'{delimiter}'.join([str(date.month).zfill(2), str(date.year)[2:]])

def get_decimal_value(user_id ,price = ''):
    misc_data = ['decimal_limit_price','decimal_limit']
    misc_dict = get_multiple_misc_values(misc_data, user_id)
    decimal_limit_price = misc_dict.get('decimal_limit_price', 'false')
    decimal_limit = misc_dict.get('decimal_limit', 0)
    if price and decimal_limit_price != 'false':
        decimal_limit = decimal_limit_price
    return decimal_limit

def get_decimal_limit(user_id, value,price =''):
    decimal_limit = get_decimal_value(user_id,price)
    return truncate_float(value, decimal_limit)

def truncate_float(value, decimal_limit):
    if isinstance(decimal_limit, str):
        decimal_limit = int(decimal_limit)
    if value in [None,'']:
        return value
    return round(value, decimal_limit)

def truncate_to_fixed_decimal(value, decimal_limit):
    if isinstance(decimal_limit, str):
        decimal_limit = int(decimal_limit)
    multiplier = 10 ** decimal_limit
    return int(Decimal(str(value)) * multiplier) / multiplier

# def truncate_float(value, decimal_limit):
#     if not decimal_limit:
#         return value
#     return float(("%." + str(decimal_limit) + "f") % (value))

def save_sku_stats(user, sku_id, transact_id, transact_type, quantity, stock_detail=None, stock_stats_objs=None,
                   bulk_insert=False, transact_date=None):
    try:
        stats_dict = {'sku_id': sku_id, 'transact_id': transact_id, 'transact_type': transact_type,
                                  'quantity': quantity, 'creation_date': datetime.datetime.now(),
                      'stock_detail': stock_detail, 'account_id': user.userprofile.id}
        if bulk_insert:
            stock_stats_objs.append(SKUDetailStats(**stats_dict))
            return stock_stats_objs
        else:
            sku_stat = SKUDetailStats.objects.create(**stats_dict)
            if transact_date:
                SKUDetailStats.objects.filter(id=sku_stat.id).update(creation_date=transact_date)
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info('Save SKU Detail Stats failed for %s and sku id is %s and error statement is %s' % (
            str(user.username), str(sku_id), str(e)))


def check_for_muliple_mfg_and_exp(skus_with_batches, user):
    pending_putaway_dict = {}
    pending_putaway_values = POLocation.objects.filter(status=1, purchase_order__open_po__sku__user=user.id,
                purchase_order__open_po__sku__sku_code__in = skus_with_batches,
                quantity__gt=0,seller_po_summary__status=0)\
                .exclude(seller_po_summary__batch_detail__batch_no__isnull = True)\
                .exclude(seller_po_summary__batch_detail__batch_no = '')\
                .exclude(seller_po_summary__batch_detail__batch_no = None)\
                .values(sku_code = F('purchase_order__open_po__sku__sku_code'),
                        batch_no = F('seller_po_summary__batch_detail__batch_no'),
                        mfg_date = F('seller_po_summary__batch_detail__manufactured_date'),
                        exp_date = F('seller_po_summary__batch_detail__expiry_date')).distinct()
    for val in pending_putaway_values:
        unique_batch_sku_key = (val['sku_code'], val['batch_no'])
        if unique_batch_sku_key not in pending_putaway_dict:
            pending_putaway_dict.update({unique_batch_sku_key : {"mfg_date" : val['mfg_date'], "exp_date" : val['exp_date']}})

    stock_detail_dict = {}
    stock_detail_data = StockDetail.objects.filter(status=1,sku__user=user.id,
                sku__sku_code__in = skus_with_batches)\
                .exclude(batch_detail__batch_no__isnull = True).exclude(batch_detail__batch_no = '')\
                .exclude(batch_detail__batch_no = None)\
                .values(sku_code = F('sku__sku_code'),
                        batch_no = F('batch_detail__batch_no'),
                        mfg_date = F('batch_detail__manufactured_date'),
                        exp_date = F('batch_detail__expiry_date')).distinct()
    for val in stock_detail_data:
        unique_batch_sku_key = (val['sku_code'], val['batch_no'])
        if unique_batch_sku_key not in stock_detail_dict:
            stock_detail_dict.update({unique_batch_sku_key : {"mfg_date" : val['mfg_date'],
                "exp_date": val['exp_date']}})
    return pending_putaway_dict, stock_detail_dict

def get_companies_list(user, send_parent=False):
    company_id = get_company_id(user)
    company_list = list(CompanyMaster.objects.filter(parent_id=company_id).\
                            values('id', 'company_name'))
    if send_parent:
        parent_company = list(CompanyMaster.objects.filter(id=company_id).values('id', 'company_name'))
        company_list = list(chain(parent_company, company_list))
    return company_list

def get_invoice_formats(request, warehouse: User):
    invoice_data, invoice_types = {}, {}
    invoice_forms_data = list(InvoiceForms.objects.filter(Q(warehouse_id__isnull=True)|Q(warehouse_id_id=warehouse.id), status=1).values())
    for invoice_form in invoice_forms_data:
        invoice_format = invoice_form.get('invoice_format', '')
        invoice_types[invoice_format] = invoice_format
        output_data = invoice_form.get('output_data', '')
        json_data = invoice_form.get('json_data', '') if invoice_form.get('json_data', '') else {}
        output = get_rendered_invoice_data(output_data, json_data.get('sample_data', {}))
        invoice_data[invoice_format] = {
            'html_data': invoice_form.get('output_data', ''),
            'rendered_html': output,
            'status': invoice_form.get('status', ''),
            'warehouse_id': invoice_form.get('warehouse_id_id', '')
        }
    final_data = {
        'data': invoice_data,
        'invoice_formats': invoice_types
    }
    return final_data

def get_rendered_invoice_data(output_data, json_data):
    try:
        template_data = Template(output_data)
        json_data= json_data if json_data else {}
        context_data = Context(json_data)
        output = template_data.render(context_data)
    except Exception:
        template_data = Template("<html></html>")
        data= {}
        context_data = Context(data)
        output=template_data.render(context_data)
    return output

@get_warehouse
def get_marketplaces_list(request, warehouse:User):
    # status_type = request.GET.get('status', '')
    # marketplace = get_marketplace_names(warehouse, status_type)
    marketplace = []
    segregation_options = OrderedDict(SELLABLE_CHOICES)
    sku_data = SKUMaster.objects.filter(user=warehouse.id).only(
        'sku_category', 'sku_class'
    )
    categories, classes = set(), set()
    for sku in sku_data:
        if sku.sku_category:
            categories.add(sku.sku_category)
        if sku.sku_class:
            classes.add(sku.sku_class)
    location_types = list(LocationType.objects.filter(
        status=1, warehouse=warehouse).values_list('location_type', flat=True).distinct())
    return HttpResponse(json.dumps({
        'marketplaces': marketplace,
        'segregation_options': segregation_options,
        'categories': sorted(list(categories)),
        'classes': sorted(list(classes)),
        'location_types' : location_types
        }))

def get_marketplace_names(warehouse: User, status_type):
    userIds = [warehouse.id]
    if status_type == 'picked':
        marketplace = list(
            Picklist.objects.exclude(order__marketplace='').filter(picked_quantity__gt=0, order__user__in=userIds). \
            values_list('order__marketplace', flat=True).distinct())
    elif status_type == 'all_marketplaces':
        marketplace = list(OrderDetail.objects.exclude(marketplace='').filter(user__in=userIds, quantity__gt=0). \
                           values_list('marketplace', flat=True).distinct())
    else:
        marketplace = list(OrderDetail.objects.exclude(marketplace='').filter(status=1, user__in=userIds, quantity__gt=0). \
                           values_list('marketplace', flat=True).distinct())
    return marketplace

def newGenerateHashCodeForMail(prObj, mailId):
    hash_code = hashlib.sha256(('%s:%s' % (prObj.id, mailId)).encode()).hexdigest()
    prApprovalMailsMap = {
                    'pr_approval': prObj,
                    'email': mailId,
                    'hash_code': hash_code,
                    'account_id' : prObj.account_id,
                }
    mailObj = PurchaseApprovalMails(**prApprovalMailsMap)
    mailObj.save()
    return hash_code

def check_unique_batch(sku_id, location_id, batch_no):
    stock_excl_locs = StockDetail.objects.exclude(batch_detail__batch_no=batch_no).\
    filter(
        sku_id=sku_id, location__zone__unique_batch=1,quantity__gt=0, location_id=location_id
    )  
    check = 0
    if stock_excl_locs.exists():
        check = 1
    return check

def update_filled_capacity(locations, user_id):
    location_masters = LocationMaster.objects.filter(location__in=locations, zone__user=user_id)
    location_ids = list(location_masters.values_list('id', flat=True))
    location_stocks = StockDetail.objects.filter(location_id__in=location_ids, sku__user=user_id,
                                                 quantity__gt=0).values('location_id').distinct().annotate(
        total_filled=Sum('quantity'))
    loc_mast_ids = [d['location_id'] for d in location_stocks]
    loc_quantities = [d['total_filled'] for d in location_stocks]
    for location in location_masters:
        filled_capacity = 0
        if location.id in loc_mast_ids:
            filled_capacity = loc_quantities[loc_mast_ids.index(location.id)]
        location.filled_capacity = filled_capacity
        location.save()

def get_sku_ean_numbers(sku_codes, user):
    sku_ean_dict = {}
    if not isinstance(user, list):
       user =  [user.id]
    sku_ean_values = EANNumbers.objects.filter(sku__sku_code__in = sku_codes, sku__user__in = user)\
                     .values_list('sku__sku_code', 'ean_number').distinct()

    for sku_code, ean_number in sku_ean_values:
        if sku_code in sku_ean_dict:
            sku_ean_dict[sku_code].append(ean_number)
        else:
            sku_ean_dict[sku_code] = [ean_number]
    return sku_ean_dict

def get_batch_key_dict(warehouse, batch_nos):
    """
    Returns a dictionary containing batch numbers as keys and a list of batch identifiers as values.

    Args:
        warehouse (int): The ID of the warehouse.
        batch_nos (list): A list of batch numbers.

    Returns:
        dict: A dictionary where each key is a batch number and the corresponding value is a list of batch identifiers.
    """

    batch_key_dict = {}
    batch_objs = list(BatchDetail.objects.filter(sku__user=warehouse.id, batch_no__in=batch_nos).values('batch_no', 'batch_identifier__batch_identifier'))
    for batch_record in batch_objs:
        batch_no = batch_record.get('batch_no')
        batch_identifier = batch_record.get('batch_identifier__batch_identifier')
        if batch_no and not batch_key_dict.get(batch_no):
            batch_key_dict[batch_no] = []
        if batch_identifier and batch_identifier not in batch_key_dict.get(batch_no, []):
            batch_key_dict[batch_no].append(batch_identifier)
    return batch_key_dict

def upload_master_file(request, user, master_id, master_type, master_file=None, extra_flag=''):
    if not master_file:
        try:
            master_file = request.FILES.get('master_file', '')
        except Exception:
            return 'No Files'
    if not master_file and master_id and master_type:
        return 'Fields are missing.'
    upload_doc_dict = {
        'master_id': master_id, 'master_type': master_type,
        'uploaded_file': master_file, 'user_id': user.id, 'extra_flag': extra_flag,
        'account_id' : user.userprofile.id,
        }
    master_doc = MasterDocs.objects.filter(**upload_doc_dict)
    if not master_doc:
        master_doc = MasterDocs(**upload_doc_dict)
        master_doc.save()
    else:
        master_doc = master_doc[0]
    return master_doc.uploaded_file

def get_sku_pack_repr(sku_pack_qtys, location_quantity):
    pack_repr_lis = []
    for sku_pack in sku_pack_qtys:
        if not location_quantity or not sku_pack['pack_quantity']:
            break
        if sku_pack['pack_quantity'] <= location_quantity:
            alloted = int(location_quantity/sku_pack['pack_quantity'])
            if alloted:
                alloted_qty = alloted * sku_pack['pack_quantity']
                pack_repr_lis.append('%s %s' % (str(alloted), sku_pack['pack_id']))
                location_quantity -= alloted_qty
    else:
        if location_quantity:
            pack_repr_lis.append('%s %s' % (str(int(location_quantity)), 'Ea'))
    pack_repr = ','.join(pack_repr_lis)
    return pack_repr

def get_internal_mails_list(user=""):
    send_mail_list = []
    internal_mail = MiscDetail.objects.filter(user=user.id, misc_type='Internal Emails')
    misc_internal_mail = MiscDetail.objects.filter(user=user.id, misc_type='internal_mail',
                                                   misc_value='true')
    if misc_internal_mail and internal_mail:
        internal_mail = internal_mail[0].misc_value.split(",")
        send_mail_list.extend(internal_mail)
    return send_mail_list

def mail_not_availble_skus(mail_list, sku_list, order_reference):
    mail_report = {
            "subject": f"Order Creation Failed : {order_reference}"
            }
    sku_html = ""
    for sku in sku_list:
        sku_html = sku_html+f" <li><b>{sku}</b></li> "
    html = """\
            <html>
            <body>
            <p>Hi,</p>
            <p>Order creation failed for the below SKU’s in the Order ID : <b>%s</b></p>
            <ol>%s</ol>
            </body>
            </html>
            """ % (str(order_reference), sku_html)
    try:
        send_mail(mail_list, mail_report["subject"], html, True, False)
    except Exception:
        pass

def check_and_add_dict(grouping_key, key_name, adding_dat, final_data_dict={}, is_list=False):
    final_data_dict.setdefault(grouping_key, {})
    final_data_dict[grouping_key].setdefault(key_name, {})
    if is_list:
        final_data_dict[grouping_key].setdefault(key_name, [])
        final_data_dict[grouping_key][key_name] = copy.deepcopy(
            list(chain(final_data_dict[grouping_key][key_name], adding_dat)))

    elif grouping_key in final_data_dict.keys() and final_data_dict[grouping_key][key_name].get('quantity'):
        final_data_dict[grouping_key][key_name]['quantity'] = final_data_dict[grouping_key][key_name]['quantity'] + \
                                                              adding_dat.get('quantity', 0)
        final_data_dict[grouping_key][key_name]['invoice_amount'] = final_data_dict[grouping_key][key_name][
                                                                  'invoice_amount'] + \
                                                              adding_dat.get('invoice_amount', 0)
    else:
        final_data_dict[grouping_key][key_name] = copy.deepcopy(adding_dat)

    return final_data_dict

def get_sku_stock(sku, sku_stocks, user, val_dict, sku_id_stocks='', add_mrp_filter=False, needed_mrp_filter=0, picked_quantity_dict={}):
    temp_dict = val_dict
    data_dict = {'sku_id': sku.id, 'quantity__gt': 0}
    if add_mrp_filter and needed_mrp_filter:
        data_dict['batch_detail__mrp'] = needed_mrp_filter
    if val_dict.get('batch_detail__batch_no', ''):
        data_dict['batch_detail__batch_no'] = val_dict.get('batch_detail__batch_no', '')
    stock_detail = sku_stocks.filter(**data_dict)
    stock_count = 0
    stock_totals = list(val_dict['stock_totals'])
    pick_res_ids = list(val_dict['pic_res_ids'])
    pic_res_quans = list(val_dict['pic_res_quans'])
    sku_ids = list(val_dict['sku_ids'])
    if sku.id in sku_ids:
        indices = [i for i, x in enumerate(sku_id_stocks) if x['sku_id'] == sku.id]
        for index in indices:
            stock_count += stock_totals[index]
        if sku.id in pick_res_ids:
            pic_res_id_index = pick_res_ids.index(sku.id)
            stock_count = stock_count - pic_res_quans[pic_res_id_index]

    #stock_count = get_decimal_limit(user.id, stock_count)
    stock_count -= picked_quantity_dict.get(sku.id, 0)
    stock_count = float(stock_count)
    return stock_detail, stock_count, sku.wms_code

@get_warehouse
def order_management_toggle(request, warehouse: User):
    toggle = request.GET.get('order_manage', '')
    if toggle:
        config_obj, created = MiscDetail.objects.get_or_create(user=warehouse.id, misc_type="order_manage")
        config_obj.misc_value = toggle
        config_obj.save()

        integrations_obj, is_created = Integrations.objects.get_or_create(user=warehouse.id, name='retailone',
                                                                          api_instance='EasyopsAPI')
        integrations_obj.status = 1 if toggle == 'true' else 0
        integrations_obj.save()
    return HttpResponse(toggle)


def check_version_number(request):
    return_response = ''
    ui_version = request.GET.get('prod_version', '')
    if ui_version:
        version_number = get_git_current_version_number()
        if not version_number == ui_version:
            return_response = 'mismatch'
    else:
        return_response = 'input_mismatch'
    return HttpResponse(return_response)

def get_git_current_version_number():
    version_number= ""
    version_number =  os.environ.get('VERSION_NUMBER', "")
    if version_number:
        return version_number
    try:
        current_path= os.getcwd()
        current_path= current_path.split("/API_WMS/miebach")
        git_path=""
        if current_path:
            git_path= current_path[0]
        else:
            git_path= current_path
        repo=git.Repo(git_path)
        try:
            git_version_str= subprocess.Popen(["git", "describe", "--tags", "--abbrev=0"], stdout=subprocess.PIPE).communicate()
        except Exception:
            git_version_str= subprocess.Popen(["/usr/bin/git", "describe", "--tags", "--abbrev=0"], stdout=subprocess.PIPE).communicate()
        if git_version_str:
            version_number = str(git_version_str[0][:-1]).split("'")[1]
    except Exception:
        pass
    return version_number

def get_custom_html_format(filter_dict, data_dict, user_id):
    rendered_data, form_data = '', {}
    document_type = filter_dict.get('document_type', '')
    custom_format = filter_dict.get('invoice_format', '')
    if custom_format not in ['', None, 'null', 'None', 'false']:
        custom_forms = InvoiceForms.objects.filter(document_type=document_type, invoice_format=custom_format, status=1)
        if custom_forms.exists():
            warehouse_forms = custom_forms.filter(warehouse_id= user_id)
            if warehouse_forms.exists():
                form_data = warehouse_forms.values('output_data')[0]
            else:
                default_forms = custom_forms.filter(warehouse_id__isnull=True)
                if default_forms.exists():
                    form_data = default_forms.values('output_data')[0]
            rendered_data = get_rendered_invoice_data(form_data.get('output_data'), data_dict)
    return rendered_data

def prepare_grn_ext_data(user):
    sku_category_list_pf, sku_category_list_ba = [], []
    sku_category_pf = get_misc_value('sku_category_list_pf', user.id)
    sku_category_ba = get_misc_value('sku_category_list_ba', user.id)
    if sku_category_pf and sku_category_pf != 'false':
        sku_category_list_pf = sku_category_pf.split(',')
        sku_category_list_pf = list(map(lambda x: x.lower(), sku_category_list_pf))
    if sku_category_ba and sku_category_ba != 'false':
        sku_category_list_ba = sku_category_ba.split(',')
        sku_category_list_ba = list(map(lambda x: x.lower(), sku_category_list_ba))
    pick_zones_list = get_misc_value('pick_zones_list', user.id)
    if pick_zones_list not in ['false', '']:
        pick_zones_list = pick_zones_list.split(',')
    extra_dat = {'sku_category_list_pf': sku_category_list_pf, 'sku_category_list_ba': sku_category_list_ba,
                 'pick_zones_list': pick_zones_list}
    return extra_dat

@get_warehouse
def get_sku_list(request, warehouse: User):
    skus = list(SKUMaster.objects.filter(user=warehouse.id).values('sku_code','sku_desc'))
    categories = list(SKUMaster.objects.filter(user=warehouse.id).values_list('sku_category', flat=True).distinct())
    company_id = get_company_id(warehouse)
    uoms = list(UOMMaster.objects.filter(company_id=company_id).values_list('uom_type', flat=True).\
                    exclude(uom_type__in=['purchase', 'sale']))
    uoms.append('Base')
    location_types = list(LocationType.objects.filter(
        status=1, warehouse=warehouse).values_list('location_type', flat=True).distinct())

    return HttpResponse(json.dumps({
        'skus': skus,
        'categories': categories,
        'uoms': uoms,
        'location_types' : location_types
        }))

@get_warehouse
def get_category_and_location_type_data(request, warehouse: User):
    categories = list(SKUMaster.objects.filter(user=warehouse.id).values_list('sku_category', flat=True).distinct())
    location_types = list(LocationType.objects.filter(
        status=1, warehouse=warehouse).values_list('location_type', flat=True).distinct())
    
    return HttpResponse(json.dumps({
        'categories': categories,
        'location_types' : location_types
        }))
    
def get_sku_ean_list(sku, order_by_val=''):
    eans_list = []
    if sku.ean_number not in ['', '0']:
        eans_list.append(str(sku.ean_number))
    multi_eans = sku.eannumbers_set.filter().annotate(str_eans=Cast('ean_number', CharField())).\
                    values_list('str_eans', flat=True)
    if order_by_val and multi_eans:
        order_by_field = 'creation_date'
        if order_by_val == 'desc':
            order_by_field = '-creation_date'
        multi_eans = multi_eans.order_by(order_by_field)
    if multi_eans:
        eans_list = list(chain(eans_list, multi_eans))
    return eans_list

def check_and_update_incremetal_type_val(table_value, user, type_name):
    table_value = int(table_value)
    data = IncrementalTable.objects.filter(user=user.id, type_name=type_name)
    if data and int(data[0].value) == table_value:
        IncrementalTable.objects.filter(user=user.id, type_name=type_name).update(value=table_value-1)

def get_purchase_company_address(profile):
    """ Returns Company address for purchase order"""

    company_address = profile.address
    if profile.wh_address:
        address = profile.wh_address
    else:
        address = profile.address
    if not (address and company_address):
        return '', ''
    if profile.user.email:
        address = ("%s, Email:%s") % (address, profile.user.email)
        company_address = ("%s, Email:%s") % (company_address, profile.user.email)
    if profile.phone_number:
        #address = ("%s, Phone:%s") % (address, profile.phone_number)
        company_address = ("%s, Phone:%s") % (company_address, profile.phone_number)
    if profile.gst_number:
        #address = ("%s, GSTINo:%s") % (address, profile.gst_number)
        company_address = ("%s, GSTINo:%s") % (company_address, profile.gst_number)
    return address, company_address

def number_in_words(value):
    try:
        value = (num2words(int(round(value)), lang='en_IN').replace(',', '').replace('-', ' ')).capitalize()
    except:
        value = f"Rupees {value} only"
    return value

def get_po_company_logo(user, IMAGE_PATH_DICT, request):
    logo_path = ""
    try:
        logo_name = IMAGE_PATH_DICT.get(user.username, '')
        if logo_name:
            logo_path = f"{request.scheme}://{request.get_host()}/static/company_logos/{logo_name}"
    except Exception:
        pass
    return logo_path

def convert_sku(sku_code):
    if type(sku_code) == float:
        sku_code = int(sku_code)
    return str(sku_code)

def get_decimal_data(cell_data, index_status, count, user):
    if get_misc_value('float_switch', user.id) == 'false':
        try:
            cell_data = float(cell_data)
            frac, whole = math.modf(cell_data)
            if frac:
                index_status.setdefault(count, set()).add('Decimal Not Allowed In Qty')
        except Exception:
            index_status.setdefault(count, set()).add('Invalid Number')
    return index_status

def get_sku_weight(sku):
    """ Returns SKU Weight"""
    weight = ''
    weight_obj = sku.skuattributes_set.filter(attribute_name='weight')
    if weight_obj.exists():
        weight = weight_obj[0].attribute_value
    return weight

def update_incremental_value(table_value, user, type_name):
    table_value = int(table_value)
    data = IncrementalTable.objects.filter(user=user.id, type_name=type_name).select_for_update()
    if data and table_value:
        data[0].value = table_value
        data[0].save()
        return "Success"
    else:
        return "Fail"

def get_all_zones(user, zones=None):
    """ Send Zones under the mentioned Zones"""
    zone_filter = {'user': user.id}
    all_zones = []
    if not zones:
        zones = []
    if zones:
        zone_filter['zone__in'] = zones
    zone_master = ZoneMaster.objects.filter(**zone_filter)
    all_zones = list(zone_master.values_list('zone', flat=True))
    sub_zones = SubZoneMapping.objects.filter(zone__user=user.id, zone__zone__in=all_zones).\
                                        values_list('sub_zone__zone', flat=True)
    if sub_zones.exists():
        all_zones = list(set(chain(all_zones, list(sub_zones))))
    return all_zones

def get_warehouse_admin(user):
    """ Check and Return Admin user of current """

    is_admin_exists = UserGroups.objects.filter(Q(user=user) | Q(admin_user=user))
    if is_admin_exists:
        admin_user = is_admin_exists[0].admin_user
    else:
        admin_user = user
    return admin_user

def get_all_warehouses(user):
    user_list = []
    admin_user = UserGroups.objects.filter(
        Q(admin_user__username__iexact=user.username) | Q(user__username__iexact=user.username)). \
        values_list('admin_user_id', flat=True)
    user_groups = UserGroups.objects.filter(admin_user_id__in=admin_user).values('user__username',
                                                                                 'admin_user__username')
    for users in user_groups:
        for key, value in users.items():
            if user.username != value and value not in user_list:
                user_list.append(value)
    return user_list

def get_exclude_locations(user):
    excluded_locations = []
    excluded_locations = CycleCount.objects.filter(sku__user=user.id,status=1,run_type='short pick').values_list('location__location', flat=True).distinct()
    return excluded_locations

@get_warehouse
def delete_temp_json(request, warehouse: User):
    model_name = request.POST.get('model_name', '')
    json_id = request.POST.get('json_id', '')
    if json_id and model_name:
        temp_json_obj = TempJson.objects.filter(id=json_id, model_name=model_name)
        if temp_json_obj.exists():
            temp_json_obj.delete()
    return HttpResponse(json.dumps({'message': 'deleted'}))

def get_format_types(request):
    format_types = {}
    for i in BarcodeSettings.objects.filter(user=request.warehouse).order_by('-format_type'):
        if i.size:
            try:
                size = "%sX%s" % ast.literal_eval(i.size)
            except Exception:
                size = i.size
            format_t = "_".join([i.format_type, size])
            format_types.update({format_t: i.format_type})

    return HttpResponse(json.dumps({'data': format_types}))

@get_warehouse
def get_warehouses(request, warehouse: User):
    user = warehouse
    status = {'status': 200, 'message': 'Success', 'data': []}
    request_data = request.body
    search_param = {}
    if request_data:
        try:
            request_data = json.loads(request_data)
        except Exception:
            request_data = {}
        search_param['user__userprofile__multi_level_system'] = 1
        if request_data.get('level'):
            search_param['user__userprofile__warehouse_level'] = request_data['level']
        if request_data.get('warehouse_name'):
            search_param['user__username'] = request_data['warehouse_name']
        if request_data.get('warehouse_id'):
            search_param['user_id'] = request_data['warehouse_id']
    warehouse = get_sister_warehouse(user)
    user_data = warehouse.filter(**search_param).values(warehouse_id=F('user_id'), warehouse_name=F('user__username'),
                                                        warehouse_first_name=F('user__first_name'),
                                                        level=F('user__userprofile__warehouse_level'),
                                                        min_order_value=F('user__userprofile__min_order_val'),
                                                        email=F('user__email'),
                                                        city=F('user__userprofile__city'),
                                                        zone=F('user__userprofile__zone'))
    status['data'] = list(user_data)
    return JsonResponse(status, status=200)

def get_decimal_data(cell_data, index_status, count, user):
    if get_misc_value('float_switch', user.id) == 'false':
        try:
            cell_data = float(cell_data)
            frac, whole = math.modf(cell_data)
            if frac:
                index_status.setdefault(count, set()).add('Decimal Not Allowed In Qty')
        except Exception:
            index_status.setdefault(count, set()).add('Invalid Number')
    return index_status

def process_ranges_and_emails(requested_data, existing_config_data, ranges_id_dict, level_emails_dict):
    new_ranges = []
    error_message = ''
    creating_emails, updating_emails = {}, {}
    for po_type in requested_data.get('po_types', []):
        ranges_validation = []
        for config_data in requested_data.get('ranges', []):
            for each_level, each_level_emails in config_data.get('mail_ids', {}).items():
                # ranges validations
                if config_data['max_Amt'] <= config_data['min_Amt']:
                    error_message = "max Amt must be greater than min Amt - %s" % str((config_data['min_Amt'], config_data['max_Amt']))
                    return '','','', error_message
                if ranges_validation and each_level == 'level1':
                    if config_data['min_Amt'] - ranges_validation[-1][1] != 1:
                        error_message = "Min amt - %s should be greater than previous range Max amt" % config_data['min_Amt']
                        return '','','', error_message
                if (config_data['min_Amt'], config_data['max_Amt']) not in ranges_validation:
                    ranges_validation.append((config_data['min_Amt'], config_data['max_Amt']))
                range_dict = {
                    'min_Amt': config_data['min_Amt'],
                    'max_Amt': config_data['max_Amt'],
                    'level': each_level,
                    'po_type': po_type,
                }
                range_set = tuple(range_dict.values())
                # Segregating new ranges or new emails or updating emails
                if range_set in existing_config_data:
                    range_id = str(ranges_id_dict[range_set])
                    existing_config_data.remove(range_set)
                    if level_emails_dict[range_id] == each_level_emails:
                        del level_emails_dict[range_id]
                    else:
                        updating_emails[range_id] = each_level_emails
                else:
                    new_ranges.append(range_dict)
                    creating_emails[range_set] = each_level_emails
            if not config_data.get('mail_ids', {}):
                error_message = "Emails are mandatory for price ranges"
                return '','','', error_message
        if not requested_data.get('ranges', []):
            error_message = "Price ranges are mandatory"
            return '','','', error_message
    if not requested_data.get('po_types', []):
        error_message = "PO types are mandatory"
        return '','','', error_message

    return new_ranges, creating_emails, updating_emails, error_message

def create_new_range_email_mapping(master_type, new_ranges, creating_emails, name, warehouse, approval_type, purchase_type, status, emails_insertion_list):
    # Creating new ranges or levels
    new_emails = []
    ranges_insertion_list = []
    ranges_insertion_filter = Q()
    for new_range in new_ranges:
        new_emails.append(creating_emails[tuple(new_range.values())])
        new_range.update({
            'name': name,
            'user_id': warehouse.id,
            'approval_type': approval_type,
            'purchase_type': purchase_type,
            'status': status,
            'account_id' : warehouse.userprofile.id,
        })
        ranges_insertion_filter |= Q(**new_range)
        ranges_insertion_list.append(PurchaseApprovalConfig(**new_range))
    PurchaseApprovalConfig.objects.bulk_create_with_rounding(ranges_insertion_list)
    if ranges_insertion_filter:
        # Mapping emails for newly created ranges
        inserted_ranges_ids = list(PurchaseApprovalConfig.objects.filter(ranges_insertion_filter).values_list('id', flat=True))
        for ind in range(len(inserted_ranges_ids)):
            master_data = {
                'user_id': warehouse.id,
                'master_type': master_type,
                'master_id': inserted_ranges_ids[ind],
                'account_id' : warehouse.userprofile.id,
            }
            for each_mail in new_emails[ind]:
                master_data['email_id'] = each_mail
                emails_insertion_list.append(MasterEmailMapping(**master_data))

    return emails_insertion_list

def update_or_remove_emails(updating_emails, level_emails_dict, emails_insertion_list, warehouse, master_type):
    deleting_emails_filter = Q()

    for master_id, level_emails in updating_emails.items():
        exist_emails = level_emails_dict[master_id]
        deleting_emails = list(set(exist_emails) - set(level_emails))
        update_emails = list(set(level_emails) - set(exist_emails))

        master_data = {
            'user_id': warehouse.id,
            'master_id': master_id,
            'master_type': master_type,
            'account_id': warehouse.userprofile.id,
        }

        for each_mail in update_emails:
            master_data['email_id'] = each_mail
            emails_insertion_list.append(MasterEmailMapping(**master_data))

        for each_mail in deleting_emails:
            master_data['email_id'] = each_mail
            deleting_emails_filter |= Q(**master_data)

        del level_emails_dict[master_id]

    if deleting_emails_filter:
        # Deleting removed emails
        MasterEmailMapping.objects.filter(deleting_emails_filter).delete()

    return emails_insertion_list

@get_warehouse
def new_add_update_po_approval_config(request, warehouse: User):
    try:
        master_type = 'pr_approvals_conf_data'
        purchase_type = 'PO'
        approval_type = 'PO'

        requested_data = json.loads(request.body)
        log.info('Request for create or update po approval config data username %s, IP Address %s, params are %s' %(request.user.username, str(get_user_ip(request)), str(requested_data)))
        name = requested_data.get('name', '')
        status = requested_data.get('status', 1)
        po_types = requested_data.get('po_types', [])
        create_config = requested_data.get('create_config', False)

        filter_params = {'purchase_type': purchase_type, 'approval_type':approval_type, 'user_id': warehouse.id}
        purchase_config_data = PurchaseApprovalConfig.objects.filter(**filter_params, name=name)
        if not purchase_config_data.exists():
            if not create_config:
                return JsonResponse({"message": "Invalid Config name"}, status=400)
        elif create_config:
            return JsonResponse({"message": "Config name already exists"}, status=400)
        config_data_po_types = list(PurchaseApprovalConfig.objects.filter(**filter_params, po_type__in=po_types) \
                                            .exclude(name=name).values_list('po_type',flat=True).distinct())
        if config_data_po_types:
            return JsonResponse({"message": "PO types - %s are already mapped" % (','.join(config_data_po_types))}, status=400)

        existing_config_data = list(purchase_config_data.values_list('min_Amt', 'max_Amt', 'level', 'po_type').order_by('id'))
        master_ids = list(purchase_config_data.values_list('id', flat=True).order_by('id'))
        ranges_id_dict = dict(zip(existing_config_data, master_ids))
        master_emails = list(MasterEmailMapping.objects.filter(user_id=warehouse.id, master_id__in=master_ids, \
                                                master_type=master_type).values('master_id', 'email_id'))

        level_emails_dict = {}
        for email_data in master_emails:
            if email_data['master_id'] in level_emails_dict:
                level_emails_dict[email_data['master_id']].append(email_data['email_id'])
            else:
                level_emails_dict[email_data['master_id']] = [email_data['email_id']]

        new_ranges, creating_emails, updating_emails, error_message = process_ranges_and_emails(requested_data, existing_config_data, ranges_id_dict, level_emails_dict)
        if error_message:
            return JsonResponse({"message":error_message}, status=400)

        value_keys = ['min_Amt', 'max_Amt', 'level', 'po_type']
        emails_insertion_list = []
        if new_ranges:
            emails_insertion_list = create_new_range_email_mapping(master_type, new_ranges, creating_emails, name, warehouse, approval_type, purchase_type, status, emails_insertion_list)
        if updating_emails:
            # Updating or removing emails from existing
            emails_insertion_list = update_or_remove_emails(updating_emails, level_emails_dict, emails_insertion_list, warehouse, master_type)

        MasterEmailMapping.objects.bulk_create_with_rounding(emails_insertion_list)

        if existing_config_data:
            # Deleting removed ranges
            exist_range_filter = Q()
            for exist_data in existing_config_data:
                exist_range = dict(zip(value_keys, list(exist_data)))
                exist_range_filter |= Q(**exist_range)
            if exist_range_filter:
                deletion_data = purchase_config_data.filter(exist_range_filter).delete()

        if level_emails_dict:
            # Deleting emails of removed ranges
            deletion_data = MasterEmailMapping.objects.filter(user_id=warehouse.id, master_id__in=level_emails_dict.keys(), \
                                                    master_type=master_type).delete()
        updation_data = purchase_config_data.update(status=status)
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info('create or update po approval config data failed for %s and params are %s and error statement is %s' % (
        str(request.user.username), str(request.body), str(e)))
        return JsonResponse({"message": "Create or Update PO approval config failed"}, status=400)

    return_status =  "Created" if create_config else "Updated"
    return JsonResponse({"message": "%s Successfully" % return_status})

@get_warehouse
def new_get_purchase_config_data(request, warehouse: User):
    # Returns Approval Matrix of a config
    name = request.GET.get('name', '')
    purchase_type = request.GET.get('purchase_type', '')
    master_type = 'pr_approvals_conf_data'
    approval_type = 'PO'

    purchase_config_data = list(PurchaseApprovalConfig.objects.filter(user_id=warehouse.id, name=name, purchase_type=purchase_type, \
                                        approval_type=approval_type) \
                                        .values('id', 'min_Amt', 'max_Amt', 'level', 'po_type', 'status').order_by('min_Amt'))
    if not purchase_config_data:
        return JsonResponse({"message": "Invalid Config name"}, status=400)
    master_ids = [config['id'] for config in purchase_config_data]
    master_emails = list(MasterEmailMapping.objects.filter(user_id=warehouse.id, master_id__in=master_ids, \
                                master_type=master_type).values('master_id','email_id'))

    level_emails_dict = {}
    for email_data in master_emails:
        if email_data['master_id'] in level_emails_dict:
            level_emails_dict[email_data['master_id']].append(email_data['email_id'])
        else:
            level_emails_dict[email_data['master_id']] = [email_data['email_id']]

    config_dict = {'name': name, 'status': purchase_config_data[0]['status']}
    po_types = []
    ranges_data_dict = {}
    for config_data in purchase_config_data:
        ranges_dict = {
            'min_Amt': config_data['min_Amt'],
            'max_Amt': config_data['max_Amt'],
        }
        price_ranges = (config_data['min_Amt'], config_data['max_Amt'])
        mail_ids = {config_data['level']: level_emails_dict.get(str(config_data['id']))}
        if price_ranges in ranges_data_dict:
            ranges_data_dict[price_ranges].update(ranges_dict)
            ranges_data_dict[price_ranges]['mail_ids'].update(mail_ids)
        else:
            ranges_dict['mail_ids'] = mail_ids
            ranges_data_dict[price_ranges] = ranges_dict
        if config_data['po_type'] not in po_types:
            po_types.append(config_data['po_type'])
    config_dict['ranges'] = list(ranges_data_dict.values())
    config_dict['po_types'] = po_types

    return JsonResponse({'data': config_dict})


def user_company_name(user):
    company_name = ''
    company_qs = UserGroups.objects.filter(user=user.id).values_list('company__company_name', flat=True)
    if company_qs.exists():
        company_name = company_qs[0]
    return company_name

@get_warehouse
def get_all_wh_user_with_permission(request, warehouse: User):
    key= request.GET.get('key','')
    perm_dict = {'Picker':'add_picklistlocation'}
    perm_name = perm_dict.get(key,'')
    users_list = []
    if not perm_name:
        return HttpResponse(json.dumps(users_list))
    sub_users = list(StaffWarehouseMapping.objects.filter(warehouse__id=warehouse.id,status=1).values_list('staff__user',flat=True))
    if not sub_users:
        return HttpResponse(json.dumps(users_list))
    perm  = Permission.objects.get(codename=perm_name)
    users_list = list(User.objects.filter(Q(roles__permissions=perm) | Q(user_permissions=perm),id__in=sub_users).values_list('username',flat=True).distinct())
    return HttpResponse(json.dumps(users_list))

def get_supplier_info(request):
    supplier_user = ''
    supplier = ''
    supplier_parent = ''
    profile = UserProfile.objects.get(user=request.user)
    if profile.user_type == 'supplier':
        supplier_data = UserRoleMapping.objects.get(user=request.user, role_type='supplier')
        supplier = SupplierMaster.objects.get(id = supplier_data.role_id)
        supplier_parent = User.objects.get(id = supplier.user)
        return True, supplier_data, supplier, supplier_parent
    return False, supplier_user, supplier, supplier_parent

def resize_image(url, user):
    try:
        path = 'static/images/resized/'
        folder = str(user.id)

        height = 193
        width = 258

        if url:
            new_file_name = url.split("/")[-1].split(".")[0] + "-" + str(width) + "x" + str(height) + "." + url.split(".")[
                1]
            file_name = url.split(".")
            file_name = "%s-%sx%s.%s" % (file_name[0], width, height, file_name[1])

            if not os.path.exists(path + folder):
                os.makedirs(path + folder)

            # if os.path.exists(path+folder+"/"+new_file_name):
            #    return "/"+path+folder+"/"+new_file_name;

            try:
                from PIL import Image
                temp_url = url[1:]
                image = Image.open(temp_url)
                if image.size[0] == image.size[1]:
                    height = width = 250
                imageresize = image.resize((height, width), Image.ANTIALIAS)
                imageresize.save(path + folder + "/" + new_file_name, 'JPEG', quality=75)
                url = "/" + path + folder + "/" + new_file_name
                return url
            except Exception:
                log.debug(traceback.format_exc())
                log.info('Issue for ' + url)
                return url
        else:
            return url
    except Exception:
        return ''

def update_temp_order_detail_status(order_objs):
    for order_obj in order_objs:
        order_obj.status = 1
        order_obj.save()

def get_invoice_html_data(invoice_data):
    show_mrp = invoice_data.get('show_mrp', 'false')
    show_sno = invoice_data.get('show_sno', 'false')
    sku_packs_invoice = invoice_data.get('sku_packs_invoice', 'false')
    data = {'totals_data': {'label_width': 6, 'value_width': 6}, 'columns': 11, 'emty_tds': [], 'hsn_summary_span': 3}
    if show_mrp == 'true':
        data['columns'] += 1
    if show_sno == 'true' :
        data['columns'] += 1
    if sku_packs_invoice :
        data['columns'] +=1
    if invoice_data.get('is_cess_tax_flag', '') == 'false':
        data['columns'] -= 1
    if invoice_data.get('invoice_remarks', '') not in ['false', '']:
        data['totals_data']['label_width'] = 4
        data['totals_data']['value_width'] = 8

    if invoice_data.get('show_disc_invoice', '') == 'true':
        data['columns'] += 1
        data['hsn_summary_span'] = 4
    data['empty_tds'] = [i for i in range(data['columns'])]
    return data

def dynamic_script_api(request):
    data = script_helper_function(request.warehouse)
    return JsonResponse({'data': data})


def script_helper_function(warehouse: User):
    return "Dynamic Script Helper Function"


# @get_admin_all_wh
def check_and_get_plants_depts(request, req_users, users=''):
    warehouse_type= request.user.userprofile.warehouse_type if request.user.userprofile else ""
    if users and warehouse_type in ["STORE", "ST_HUB", "ADMIN"]:
        req_users = users
    else:
        req_users = User.objects.filter(id__in=req_users)
    return req_users

def update_stock_count(order_ids, user):
    for order in order_ids:
        orderObj = OrderDetail.objects.get(id=order)
        sku = orderObj.sku
        stock_count = StockDetail.objects.filter(sku_id=sku.id).aggregate(Sum('quantity'))['quantity__sum']
        if stock_count:
            orderObj.stock_count = stock_count
            orderObj.save()

@app.task
def update_stock_count_async(order_ids, user_id):
    try:
        user = User.objects.get(id=user_id)
        update_stock_count(order_ids, user)
    except Exception:
        pass

def get_auth_signature(request, warehouse, inv_date=None):
    auth_signature = ''
    master_docs_obj = MasterDocs.objects.filter(master_type='auth_sign_copy', user_id=warehouse.id).order_by('creation_date')
    if master_docs_obj.exists():
        # url = request.META.get('wsgi.url_scheme')+'://'+request.META.get('HTTP_HOST')
        url = request.build_absolute_uri('/')
        # url = request.META.get('HTTP_HOST')
        # url = request.META.get('HTTP_REFERER')[:-1]
        if len(master_docs_obj) > 1 and inv_date:
            for auth_sign in master_docs_obj:
                if inv_date > auth_sign.creation_date:
                    auth_signature = url+auth_sign.uploaded_file.url
                    break
                else:
                    auth_signature = url+auth_sign.uploaded_file.url
        elif len(master_docs_obj) == 1 or (not auth_signature):
            auth_signature = url+master_docs_obj[0].uploaded_file.url
        else:
            auth_signature = url+master_docs_obj[0].uploaded_file.url
    return auth_signature

@get_warehouse
def get_users(request, warehouse: User):
    admin_user = warehouse
    filter_params = {}
    if request.GET.get('user_type', None):
        filter_params['userprofile__user_type'] = request.GET.get('user_type', None)
    users = Group.objects.get(name=warehouse.username).user_set.exclude(username=warehouse).filter(**filter_params)
    res = []
    for row in users:
        res.append({
            'username': row.username,
            'firstname': row.first_name,
            'id': row.id,
            'user_type': row.userprofile.user_type
        })
    return { 'data': res }


def convert_qr_code_image(user, qr_code_data, transact_id, url, transact_type):
    qr_code_image = ''
    qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_H,
                box_size=10,
                border=4)
    qr.add_data(qr_code_data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white").convert('RGB')
    image_name = str(user.id) + transact_id.replace('/', '-') + f"{transact_type}.png"
    file_name = "%s/static/qrcodes/"%(settings.BASE_DIR)
    folder_check(file_name)
    file_name = file_name+image_name
    img.save(file_name)
    file_name = 'static/qrcodes/'+image_name
    qr_code_image = url+file_name
    try:
        # base64 conversion of qrcode image
        with open(file_name, 'rb') as f:
            qr_code_image = "data:image/png;base64," + base64.b64encode(f.read()).decode('utf-8')
        os.remove(file_name)
    except Exception:
        pass
    return qr_code_image

def get_auto_misc_value(misc_type, user, number=False,boolean=False):
    misc_value = ''
    if number:
        misc_value = 0
    data = MiscDetail.objects.filter(user=user, misc_type=misc_type)
    if data:
        misc_value = data[0].misc_value
    if boolean:
        if misc_value == 'true':
            return True
        else:
            return False
    return misc_value

def get_full_sequence_number(user_type_sequence, creation_date):
    inv_num_lis = []
    if user_type_sequence.prefix:
        inv_num_lis.append(user_type_sequence.prefix)
    if user_type_sequence.date_type:
        if user_type_sequence.date_type == 'financial':
            inv_num_lis.append(get_financial_year(creation_date))
        elif user_type_sequence.date_type == 'month_year':
            inv_num_lis.append(creation_date.strftime('%m%y'))
    if user_type_sequence.interfix:
        inv_num_lis.append(user_type_sequence.interfix)
    inv_num_lis.append(str(user_type_sequence.value).zfill(3))
    sequence_number = '/'.join(['%s'] * len(inv_num_lis)) % tuple(inv_num_lis)
    return sequence_number

def get_company_logo(user, IMAGE_PATH_DICT):
    try:
        logo_name = IMAGE_PATH_DICT.get(user.username, '')
        logo_path = 'static/company_logos/' + logo_name
        image = logo_path
        with open(logo_path, "rb") as image_file:
            image = base64.b64encode(image_file.read())
    except Exception:
        image = ""
    return image

def get_parent_company(companyObj):
    if companyObj.parent:
        return get_parent_company(companyObj.parent)
    else:
        return companyObj

@get_warehouse
def delete_user_attribute(request, warehouse: User):
    attr_id = request.GET.get('data_id', '')
    if attr_id:
        UserAttributes.objects.filter(id=attr_id).update(status=0)
        return JsonResponse({'message': 'Updated Successfully'}, status=200)
    else:
        return JsonResponse({'message': 'Attribute id should not be empty'}, status=400)

@get_warehouse
def bulk_files_upload(request, warehouse: User):
    success_data = []
    uploaded_file_dict = {}
    master_type = request.POST.get('master_type', '')
    master_id = request.POST.get('master_id', '')
    extra_flag = request.POST.get('extra_flag', '')
    if not (master_id and master_type):
        return HttpResponse('Fields are missing.')
    filter_dict = {'master_id': master_id, 'master_type': master_type, 'user_id': warehouse.id, 'extra_flag': extra_flag}
    MasterDocs.objects.filter(**filter_dict).delete()
    for i in request.FILES:
        master_file = request.FILES.get(i, '')
        if master_file:
            upload_doc_dict = {
                'master_id': master_id, 'master_type': master_type,
                'uploaded_file': master_file, 'user_id': warehouse.id, 'extra_flag': extra_flag,
                'account_id' : warehouse.userprofile.id,
                }
            master_doc = MasterDocs(**upload_doc_dict)
            master_doc.save()
            uploaded_file_dict = {'file_name': master_doc.uploaded_file.name.split('/')[-1], 'id': master_doc.id,
                                      'file_url': '/' + master_doc.uploaded_file.name}
    return HttpResponse(json.dumps(uploaded_file_dict))

@get_warehouse
def get_sku_mrp(request , warehouse: User):
    mrp = 0
    purchase_uom = ''
    wms_code  = json.loads(request.POST.get('wms_code',''))
    sku_mrp = SKUMaster.objects.filter(wms_code=wms_code, user=warehouse.id).values('mrp', 'measurement_type')
    if sku_mrp.exists():
        mrp = sku_mrp[0]['mrp']
        purchase_uom = sku_mrp[0]['measurement_type']
    po_uom_obj = SKUPackMaster.objects.filter(sku__sku_code=wms_code, sku__user=warehouse.id, purchase_uom='1').values('pack_id')
    if po_uom_obj:
        purchase_uom = po_uom_obj[0]['pack_id']
    return HttpResponse(json.dumps({'mrp':mrp, 'purchase_uom': purchase_uom}))

def bulk_create_in_batches(model_obj, data_objs):
    last_batch = 0
    for cur_batch in range(0, len(data_objs)+1000, 1000):
        batch_data_objs = data_objs[last_batch:cur_batch]
        last_batch = cur_batch
        model_obj.objects.bulk_create(batch_data_objs,ignore_conflicts=True)

def update_error_message(failed_status, error_code, error_message, field_value, field_key='OrderId', reference=''):
    """Common function to frame error dict with error message and status code """
    error_dict = {
        field_key: field_value,
        "status": error_code,
        "message": error_message
    }
    if reference:
        error_dict['po_reference'] = reference

    if 'errors' in  failed_status:
        failed_status["errors"].append(error_dict)
    else:
        failed_status["errors"] = [error_dict]

''' function to validate- sku attributes value with attribute type = drop down'''
def validate_drop_down_values(attribute_name,attribute_value,user):
    status = "Success"
    attribute_value = attribute_value.split(',')
    drop_down_data = dict(UserAttributes.objects.filter(user = user.id,attribute_type="Dropdown").values_list('attribute_name','attribute_values'))
    if attribute_name in drop_down_data.keys():
        drop_down_values = drop_down_data.get(attribute_name)
        if drop_down_values != '' or drop_down_values != None:
            for att_val in attribute_value:
                if str(att_val) not in drop_down_values:
                    status = ("Drop down data not found:-"  +att_val)
    return status

@get_warehouse
def get_department_list(request, warehouse: User):
    department_list = copy.deepcopy(DEPARTMENT_TYPES_MAPPING)
    return HttpResponse(json.dumps({'department_list': department_list}))

@get_warehouse
def get_company_warehouses(request, warehouse: User):
    company_id = request.GET.get('company_id', '')
    warehouse_types = request.GET.get('warehouse_type', '')
    if warehouse_types:
        warehouse_types = warehouse_types.split(',')
    warehouse_user = request.GET.get('warehouse', '')
    exclude_company = request.GET.get('exclude_company', '')
    if warehouse_user:
        warehouse_user = warehouse_user.split(',')
    else:
        warehouse_user = []
    parent_company_id = get_company_id(warehouse)
    if str(parent_company_id) == str(company_id):
        company_id = ''
    wh_objs = get_related_users_filters(warehouse.id, warehouse_types=warehouse_types, warehouse=warehouse_user,
                                        company_id=company_id, send_parent=False, exclude_company=exclude_company)
    warehouse_list = []
    wh_list = wh_objs.values('id', 'username', 'userprofile__stockone_code', 'first_name', 'last_name')
    for wh in wh_list:
        name = ' '.join([wh['first_name'], wh['last_name']])
        wh_dict = {'id': wh['id'], 'username': wh['username'], 'stockone_code': wh['userprofile__stockone_code'],
                   'name': name}
        warehouse_list.append(wh_dict)
    return HttpResponse(json.dumps({'warehouse_list': warehouse_list}))

@get_warehouse
def get_categories_list(request, warehouse: User):
    sku_master, sku_master_ids = get_sku_master(warehouse)
    brand = request.GET.get('brand', '')
    filter_params = {'user': warehouse.id}
    if brand:
        filter_params['sku_brand__in'] = brand.split('<<>>')
    categories_list = sku_master.filter(**filter_params).exclude(sku_category='').values_list('sku_category',flat=True).distinct()
    return HttpResponse(json.dumps(list(categories_list)))

@get_warehouse
def get_sku_details_for_ean_number(request, warehouse: User):
    search_term = request.GET.get('search_term')
    return_status = {}
    if search_term:
        sku_code = SKUMaster.objects.filter(user=warehouse.id, sku_code__iexact=search_term).\
                    values('sku_code','sku_desc','sku_brand','sku_size','image_url','batch_based').distinct()
        if sku_code.exists():
            return_status['sku_code'] = sku_code[0].get('sku_code','')
            return_status['sku_desc'] = sku_code[0].get('sku_desc','')
            return_status['sku_brand'] = sku_code[0].get('sku_brand','')
            return_status['sku_size'] = sku_code[0].get('sku_size','')
            return_status['batch_based'] = sku_code[0].get('batch_based','')
            image_url = sku_code[0].get('image_url','')
            if image_url and "http" not in image_url:
                image_url = request.build_absolute_uri('/') + image_url
            image_url = image_url.replace("https://udaan.azureedge.net/products", "https://ud-img.azureedge.net/w_120/u/products")
            return_status['image_url'] = image_url
        else:
            ean_number = EANNumbers.objects.filter(sku__user=warehouse.id, ean_number=search_term).\
                        values('sku__sku_code','sku__sku_desc','sku__sku_brand','sku__sku_size','sku__image_url','sku__batch_based').distinct()
            if ean_number.exists():
                return_status['sku_code'] = ean_number[0].get('sku__sku_code','')
                return_status['sku_desc'] = ean_number[0].get('sku__sku_desc','')
                return_status['sku_brand'] = ean_number[0].get('sku__sku_brand','')
                return_status['sku_size'] = ean_number[0].get('sku__sku_size','')
                return_status['batch_based'] = ean_number[0].get('sku__batch_based','')
                image_url = ean_number[0].get('sku__image_url','')
                if image_url and "http" not in image_url:
                    image_url = request.build_absolute_uri('/') + image_url
                image_url = image_url.replace("https://udaan.azureedge.net/products", "https://ud-img.azureedge.net/w_120/u/products")
                return_status['image_url'] = image_url
    if return_status:
        return_status['ean_numbers'] = []
        sku_code = return_status.get('sku_code')
        ean_numbers = list(EANNumbers.objects.filter(sku__user=warehouse.id, sku__sku_code = sku_code).values_list('ean_number',flat=True))
        if ean_numbers:
            return_status['ean_numbers'] = ean_numbers
        return JsonResponse({'sku': return_status}, status=200)
    else:
        return JsonResponse({'error': 'No SKU/EAN Found'}, status=400)


@get_warehouse
def get_available_cartons(request, warehouse: User):
    cartons = []
    cartons = (list(AuthorizedBins.objects.filter(user = warehouse.id).values_list('bin_number', flat=True)))
    return JsonResponse({'cartons_list': cartons})

def save_models_objs(objs):
    try:
        for obj in objs:
            obj.save()
    except Exception:
        pass
    
def get_sequence_data(warehouse, filters=None):
    '''
    Get the sequence data for the warehouse
    '''
    if not filters:
        filters = {}
    sequence_data = IncrementalTable.objects.filter(user_id=warehouse.id, **filters).values('prefix', 'interfix', 'date_type', 'type_name', 'suffix')
    return list(sequence_data)

@get_warehouse
def get_sequence(request, warehouse: User):
    ''' Retrieve Invoice Sequence Details '''
    
    try:
        response_data = get_sequence_data(warehouse)
    except Exception as e:
        log.error('Failed to fetch Invoice Sequence for %s. Error: %s', warehouse.username, str(e), exc_info=True)
        response_data = {
            'status': 'Failed to Retrieve Invoice Sequence'
        }
    
    return JsonResponse({"data":response_data})

@get_warehouse
def update_sequence(request, warehouse: User):
    ''' Create or Update Invoice Sequences '''

    log.info('Request Params for Update Invoice Sequences for %s is %s by %s' % (warehouse.username, str(request.GET.dict()),(request.user.username)))
    status = ''
    try:
        data = json.loads(request.body)
        inc_prefix = data.get('prefix', '')
        inc_interfix = data.get('interfix', '')
        inc_date_type = data.get('date_type', '')
        inc_type_name = data.get('type_name','')
        choice_mapping = dict(DATETIME_CHOICES)
        if not inc_type_name or not inc_prefix:
            status = "Prefix/Type is mandatory"
        if inc_date_type:
            inc_date_type = int(inc_date_type) if int(inc_date_type) in list(choice_mapping.keys()) else None
        if not status:
            sequence_ids = list(IncrementalTable.objects.filter(user_id=warehouse.id, type_name = inc_type_name).values_list('id',flat=True))
            if sequence_ids:
                IncrementalTable.objects.filter(id__in = sequence_ids).update(prefix=inc_prefix,interfix=inc_interfix,date_type=inc_date_type)
            else:
                IncrementalTable.objects.create(prefix=inc_prefix, value=1,
                                                interfix=inc_interfix,
                                                date_type=inc_date_type,
                                                user_id=warehouse.id,
                                                type_name = inc_type_name,
                                                account_id=warehouse.userprofile.id)
            status = 'Success'
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info('Update Sequence failed for %s and params are %s and error statement is %s' %
                 (str(warehouse.username), str(data), str(e)))
        status = 'Update  Sequence Failed'
    return JsonResponse({'message': status})


def get_audit_logs_data(start_idx, stop_idx, temp_data, search_term, order_term, col_num, request, warehouse: User, filters):
    '''
        Function that fetches data from either clickhouse or opensearch 

    '''
    request_data = request.GET
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "request_time"
    sort_type = request_data.get('sort_type', 0) or 0
    status_dict = {200:"Success", 400:"Invalid Request", 500:"Failed", 401:"Invalid Request"}
    master_data = []

    # Check the environment variable to determine which data source to use
    logger_flag = getattr(settings, 'OPENSEARCH_AUDIT_LOGGING', False)
    if logger_flag :
        data_source = 'opensearch'
    else:
        data_source = 'clickhouse'

    #Set the sort type parameter based on data_source and sort_key 
    if sort_type == '1':
        ascending = "ASC" if data_source == 'clickhouse' else "asc"
    else:
        ascending = "DESC" if data_source == 'clickhouse' else "desc"

    #Limites and Index
    start_index = int(request_data.get('start', 0))
    limit = int(request_data.get('length', 10))
    stop_index = start_index + limit

    if data_source == 'clickhouse':
        if request.GET.get('pdf') == 'true':
            stop_index = None
        column_headers.update({"warehouse_id":warehouse.id,"limit": stop_index, "offset": start_index, "order_by": sort_by_column, "sort_type": ascending})
        try:
            auditlog_instance = AuditLogUtils()
            if column_headers:
                auditlog_instance.request_params = column_headers
            queryset = auditlog_instance.get_clickhouse_data()

            if not queryset.empty:
                master_data = queryset.to_dict('records')
        except Exception:
            log.debug(traceback.format_exc())
            return [{"detail" : "Error"}]
    else:
        column_headers.update({"warehouse_id":warehouse.id,"limit": stop_index, "offset": start_index, "order_by": sort_by_column, "sort_type": ascending})
        try:
            auditlog_instance = OpsUtils()
            if column_headers:
                auditlog_instance.request_params = column_headers
            queryset = auditlog_instance.get_os_data()
            if queryset:
                master_data = queryset['hits']['hits']
        except Exception:
            log.debug(traceback.format_exc())
            return [{"detail" : "Error"}]

    #Total Count
    temp_data['recordsTotal'] = len(master_data)
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    ist_timezone = pytz.timezone('Asia/Kolkata')
    for data in master_data[start_index:stop_index]:
        if data_source == 'clickhouse':
            request_time = data.get('request_time').astimezone(ist_timezone).strftime('%Y-%m-%d %H:%M:%S') if data.get('request_time') else ""
            audit_status = status_dict.get(data.get('status_code'), '') if data.get('status_code') else ""
            temp_data['aaData'].append(
                            OrderedDict((
                            ('id', str(data.get('id', ''))),
                            ('warehouse', data.get('warehouse', "")),
                            ('username', data.get('username', "")),
                            ('request_path', data.get('request_path', '')),
                            ('request_time', request_time),
                            ('status_code', audit_status),
                            ('module_name', data.get('module_name', '')),
                            ('request_method', data.get('request_method', '')),
                            ('header_referer', data.get('header_referer', '')),
                            ('request_id', data.get('request_id', '')))))
        else:
            # Assuming `data['_source'].get('request_time')` gives the datetime string like '2024-11-27T05:21:16.207150+00:00'
            request_time_str = data['_source'].get('timestamp', '')
            # Parse the datetime string into a datetime object
            if request_time_str:
                request_time = isoparse(request_time_str)  # This converts the ISO format string to a datetime object
                # Convert to 'Asia/Kolkata' timezone
                ist_timezone = pytz.timezone('Asia/Kolkata')
                request_time_ist = request_time.astimezone(ist_timezone)  # Now you can call astimezone
                # Format the datetime into your desired string format
                formatted_time = request_time_ist.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_time = ""
            
            audit_status = status_dict.get(data['_source'].get('status_code'), '') if data['_source'].get('status_code') else ""
            temp_data['aaData'].append(
                            OrderedDict((
                            ('id', str(data.get('_id', ''))),
                            ('warehouse', data['_source'].get('warehouse', "")),
                            ('username', data['_source'].get('username', "")),
                            ('request_path', data['_source'].get('request_path', '')),
                            ('request_time', formatted_time),
                            ('status_code', audit_status),
                            ('module_name', data['_source'].get('module_name', '')),
                            ('request_method', data['_source'].get('request_method', '')),
                            ('header_referer', data['_source'].get('header_referer', '')),
                            ('request_id', data['_source'].get('request_id', '')))))
            

@get_warehouse
def get_uom_types_list(request, warehouse:User):
    uoms_list = []
    company_id = get_company_id(warehouse)
    uoms_list = list(UOMDetail.objects.filter(company_id = company_id).values_list("uom_code", flat=True))
    return JsonResponse({"uom_types":uoms_list})

def get_uom_decimals(company_id, uom_codes=list()):
    ''' Return a dict with uom code and its allowed decimal places'''
    filter_dict = {"company_id": company_id}
    if uom_codes:
        filter_dict['uom_code__in'] = uom_codes
    uom_decimals = dict(UOMDetail.objects.filter(
        **filter_dict
    ).values_list('uom_code').annotate(uom_decimal = Cast('decimals', IntegerField()))
    )
    return uom_decimals

def get_company_details(user_id):
    ''' Returns Company details of current Warehouse '''
    company_dict = {}
    company_data = CompanyMaster.objects.filter(userprofile__user_id=user_id)
    if company_data:
        company_data = company_data[0]
        company_dict = dict(company_data.__dict__)
        company_dict.pop('_state', '')
        if company_data.logo:
            company_dict['logo'] = company_data.logo.url
            company_dict['logo_name'] = company_data.logo.name
    return company_dict

def convert_html_to_pdf(user, f_name, folder_name, html_data):
    '''
    Function to convert html to pdf
    '''
    attachments = []
    try:
        if not isinstance(html_data, list):
            html_data = [html_data]
        for data in html_data:
            temp_name = f_name + str(secrets.randbelow(9999))
            file_name = '%s.html' % temp_name
            pdf_file = '%s.pdf' % temp_name
            path = 'static/master_docs/%s/%s/' % (user.id,folder_name)
            folder_check(path)
            file = open(path + file_name, "w+b")
            file.write(xcode(data))
            file.close()
            os.system(
                "./phantom/bin/phantomjs ./phantom/examples/rasterize.js ./%s ./%s A4" % (path + file_name, path + pdf_file))
            attachments.append({'path': path + pdf_file, 'name': pdf_file,'html': path + file_name})
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info('PDF file genrations failed for ' + str(xcode(html_data)) + ' error statement is ' + str(e))
    return attachments

def generate_log_message(keyword, **kwargs):
    params = ', '.join(f'{name}: {value}' for name, value in kwargs.items())
    log_message = f"{keyword} -> {params}"
    return log_message

def create_update_user(full_name, email, phone_number, password, username, role_name='customer'):
    """
    Creating a new Customer User
    """
    new_user_id = ''
    if username and password:
        user = User.objects.filter(username=username)
        if user:
            status = "User already exists"
        else:
            user = User.objects.create_user(username=username, email=email, password=password, first_name=full_name,
                                            last_login=datetime.now())
            user.save()
            new_user_id = user.id
            hash_code = hashlib.sha256(b'%s:%s' % (user.id, email)).hexdigest()
            if user:
                prefix = re.sub('[^A-Za-z0-9]+', '', user.username)[:3].upper()
                user_profile = UserProfile.objects.create(phone_number=phone_number, user_id=user.id,
                                                          api_hash=hash_code, prefix=prefix, user_type=role_name)
                user_profile.save()
            status = 'New Customer Added'

    return status, new_user_id

#update 
def password_notification_message(username, password, name, to, role_name='Customer'):
    """ Send SMS for password modification """
    arguments = "%s -- %s -- %s -- %s" % (username, password, name, to)
    log.info(arguments)
    try:

        data = " Dear %s, Your credentials for %s %s Portal are as follows: \n Username: %s \n Password: %s" % (
                        role_name, role_name, name, username, password)
        send_sms(to, data)
    except Exception as e:
        log.info(("message sending failed with exception") % str(e))

def update_user_password(data_name, data_email, phone_number, password, cur_user_id, user, role_name):
    cur_user = User.objects.get(id=cur_user_id)
    if password:
        cur_user.set_password(password)
    cur_user.email = data_email
    cur_user.first_name = data_name
    cur_user.save()
    if user.first_name:
        name = user.first_name
    else:
        name = user.username
    if password:
        password_notification_message(cur_user.username, password, name, phone_number, role_name)


def sla_detail_validation(user, key_type, value, name):
    """
    This function validates the sla configuration.

    Parameters:
    name (str): The name of the sla.
    key_type (str): The key type of the MiscDetail. Should be 'Red', 'Yellow', or 'Green'.
    value (str): The value of the MiscDetail.
    user (User object): The user object to filter on.

    Returns:
    str: A string indicating the result of the validation.
    """
    if not name:
        return 'Name is mandatory'
    if key_type not in ['Red','Yellow','Green'] :
        return 'Invalid Color'
    if not value and key_type!='Green'  :
        return 'Invalid Payload'
    else :
        format_flag = is_hour_min_format(value)
        if not format_flag :
            return 'Invalid Time Format'
    if key_type == 'Yellow':
        red_entry = filter_or_none(MiscDetailOptions,{'misc_detail__misc_type':'sla','misc_key':'Red','misc_detail__user':user.id,'status':1})
        if not red_entry.exists():
            return 'Red Entry Sholud be defined before yellow'
        else:
            entry_sts = sla_time_validation('Red','Yellow',red_entry[0].misc_value,value)
            if entry_sts:
                return entry_sts
    elif key_type=='Green':
        yellow_entry = filter_or_none(MiscDetailOptions,{'misc_detail__misc_type':'sla','misc_key':'Yellow','misc_detail__user':user.id,'status':1})
        if yellow_entry.exists():
            entry_sts = sla_time_validation('Yellow','Green', yellow_entry[0].misc_value, value)
            if entry_sts:
                return entry_sts
        else:
            red_entry = filter_or_none(MiscDetailOptions,{'misc_detail__misc_type':'sla','misc_key':'Red', 'misc_detail__user':user.id, 'status':1})
            if red_entry.exists():
                entry_sts=sla_time_validation('Red', 'Green', red_entry[0].misc_value, value)
                if entry_sts:
                    return entry_sts

    return ''

def is_hour_min_format(input_time):
    try:
        # If the input string is in the "HH:MM" format, this will succeed
        parts = input_time.split(':')
        if len(parts) == 2 and float(parts[0]) >= 0  and float(parts[1]) >= 0:
            return True
        else:
            return False
    except ValueError:
        return False

def sla_time_validation(first_sla, second_sla, first_time, second_time):
    if first_time and second_time:
        first_value = first_time.split(':')
        second_value = second_time.split(':')
        if int(second_value[0]) < int(first_value[0]) or (int(first_value[0]) == int(second_value[0]) and int(second_value[1]) < int(first_value[1])):
            return second_sla +' Time should be greater than ' + first_sla + ' Time'
    return ''

def delete_sla_entries(user,misc_type, misc_key):
    misc_entries = MiscDetailOptions.objects.filter(misc_detail__user=user.id, misc_detail__misc_type=misc_type, misc_key=misc_key)
    if misc_entries.exists():
        misc_entries.delete()


def get_sla_misc_details(user,api=False):
    """
    This function return sla details.

    Parameters:
    name (object): warehouse name.

    Returns:
    dict: sla color_code, time dict sorted in descending order.
    dict: sla name, color code dict.
    dict: color code and time dict.
    """
    vals = ['misc_key','misc_value','json_data', 'id']
    misc_data = list(MiscDetailOptions.objects.filter(misc_detail__user=user.id,\
                    misc_detail__misc_type='sla',misc_detail__misc_value='true',status=1).\
                    values(*vals))
    if api:
        return misc_data,{},{}
    sla_dict = {}
    color_name_dict = {}
    for sla in misc_data:
        sla_dict[sla['misc_key']] = sla['misc_value']
        if sla['json_data']:
            color_name_dict[sla['json_data'].get('name','')] = sla['misc_key']
    sla_time_dict = copy.deepcopy(sla_dict)
    return hour_min_to_seconds(sla_dict),color_name_dict,sla_time_dict

def hour_min_to_seconds(misc_data):
    for key, value in misc_data.items():
        # Split the time string by the colon
        hours, minutes = map(int, value.split(':'))

        # Convert the hours and minutes to seconds
        seconds = hours * 3600 + minutes * 60

        misc_data[key] = seconds
    sorted_dict = dict(sorted(misc_data.items(), key=operator.itemgetter(1), reverse=True))
    return sorted_dict


def get_ship_status_msg(shipment_date, sla_data, time_zone):
    """
    This function calculates the difference between the maximum ship date and the current date and time,
    and determines the ship status and SLA message based on this difference.

    Parameters:
    sla_data (dict): A dictionary where the keys are color codes and the values are time ranges in seconds.

    Returns:
    tuple: A tuple containing the ship status (a string) and the SLA message (a string).
    """
    ship_status, sla_msg='',''
    diff_time = date_diff_hour_min_format(shipment_date, datetime.datetime.now().astimezone(pytz.timezone(time_zone)))
    if diff_time <0:
        ship_status='Red'
        sla_msg = 'SLA BREACHED'
    else:
        for color,time_range in sla_data.items():
            if diff_time < time_range:
                ship_status = color
                if  ship_status == 'Red':
                    sla_msg = 'SLA BREACHED'
                else:
                    hr_left = int(diff_time/3600)
                    sla_msg = str(hr_left)+ ' Hrs Left'
        if not ship_status:
            ship_status = 'Green'
            hr_left = int(diff_time/3600)
            sla_msg = str(hr_left)+ ' Hrs Left'
    return ship_status,sla_msg


def date_diff_hour_min_format(datetime1,datetime2):
    # Calculate the difference
    diff = datetime1 - datetime2

    # Convert the difference to seconds
    total_seconds = diff.total_seconds()

    return total_seconds
def get_uom_level_for_transaction_type(warehouse_id, transaction_type):
    '''
    Retrieves the level value for the given transaction type from MiscDetailOptions.
    '''
    misc_value = MiscDetailOptions.objects.filter(
        misc_detail__user=warehouse_id,
        misc_detail__misc_type='uom_transactions',
        status=1,
        misc_key=transaction_type
    ).values_list('misc_value', flat=True).first()

    # Check if misc_value is not None or any falsey value
    level = misc_value if misc_value not in [None, 'null', 'None'] else ''

    return level


def merge_pdf_urls(request):
    """
    Merge multiple PDFs from the provided URLs.

    Args:
        request (HttpRequest): The HTTP request object.

    Returns:
        JsonResponse: A JSON response containing the merged PDF as base64 encoded string.

    Raises:
        JsonResponse: If the request is invalid or if there are no PDF URLs provided.
        JsonResponse: If there is an error while fetching or merging the PDFs.
    """
    try:
        request_data = json.loads(request.body)
    except json.JSONDecodeError:
        return JsonResponse({'message': 'Invalid Request'}, status=400)
    try:
        pdf_urls = request_data.get("pdf_urls", []) or []
        if not pdf_urls:
            return JsonResponse({'message': 'No PDF urls provided'}, status=400)

        pdf_merger = PdfFileMerger(strict=False)

        def download_and_append(pdf_url, index):
            try:
                response = requests.get(pdf_url, stream=True)
                response.raise_for_status()
                pdf_bytes = BytesIO()
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        pdf_bytes.write(chunk)
                pdf_bytes.seek(0)  # Reset the stream position to the beginning
                return (index, pdf_bytes)
            except Exception as e:
                log.debug(traceback.format_exc())
                return JsonResponse({'message': f"Failed to fetch or merge PDF from {pdf_url}: {e}", 'index': index},
                                    status=500)

        with ThreadPoolExecutor(max_workers=5) as executor:
            results = list(executor.map(download_and_append, pdf_urls, range(len(pdf_urls))))
            results.sort(key=lambda x: x['index'] if isinstance(x, dict) else x[0])
            for result in results:
                if isinstance(result, dict):
                    return JsonResponse(result)
                else:
                    index, pdf_bytes = result
                    pdf_merger.append(pdf_bytes, import_bookmarks=False)

        merged_pdf_bytes = BytesIO()
        pdf_merger.write(merged_pdf_bytes)
        merged_pdf_bytes.seek(0)  # Reset the stream position to the beginning
        pdf_merger.close()

        merged_pdf_base64 = base64.b64encode(merged_pdf_bytes.read()).decode('utf-8')
        return JsonResponse({'merged_pdf': merged_pdf_base64})

    except Exception as e:
        log.debug(traceback.format_exc())
        return JsonResponse({'message': f'Merge PDF urls Failed for {request_data} error: {e}'}, status=500)

def get_custom_sku_attributes_and_values(sku_filter_dict):
    sku_attributes_dict = defaultdict(dict)
    sku_attributes = SKUAttributes.objects.filter(**sku_filter_dict)\
                                .values("sku__sku_code", 'attribute_value', 'attribute_name', 'sku__user')
    for sku_attr in sku_attributes:
        sku_code = sku_attr['sku__sku_code']
        attribute_name = sku_attr['attribute_name']
        attribute_value = sku_attr['attribute_value']
        
        sku_attributes_dict[sku_code][attribute_name] = attribute_value
    
    return dict(sku_attributes_dict)

@get_warehouse
def get_billing_details(request, warehouse: User):
    request_data = request.GET
    company_str = request_data.get('company_names', '')
    company_names = [ company.strip() for company in company_str.split(',')]
    from_billing_date = request_data.get('from_billing_date', '')
    to_billing_date = request_data.get('to_billing_date', '')
    if not company_str or not from_billing_date or not to_billing_date:
        return JsonResponse({'message': 'Company Names, From Billing Date and To Billing Date are mandatory'}, status=400)
    
    try:
        start_date = datetime.datetime.strptime(f'{from_billing_date}', '%m-%Y')
        to_date = datetime.datetime.strptime(f'{to_billing_date}', '%m-%Y')
        to_date = to_date + relativedelta(months=1)
        end_date = start_date + relativedelta(months=1)
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info('Failed to parse dates for billing details. Error: %s', str(e))
        return JsonResponse({'message': 'Invalid Dates'}, status=400)
    
    # No of Active users, Warehouses and Sale Orders
    final_data = defaultdict(dict)
    while to_date > start_date:
        company_ids_list = list(CompanyMaster.objects.using(reports_database).filter(company_name__in=company_names).values('company_name').annotate(parent_id=Case(When(parent__isnull=True,then=F('id')),default=F('parent_id'), output_field=IntegerField()),child_id=Case(When(parent__isnull=True,then=F('companymaster')),default=F('id'), output_field=IntegerField())))
        parent_company_ids, child_company_ids = {}, {}
        for company in company_ids_list:
            parent_company_ids[company['company_name']] = company['parent_id']
            child_company_ids[company['company_name']] = company['child_id']
        
        active_users = dict(StaffWarehouseMapping.objects.using(reports_database).filter(Q(status=1) | Q(updation_date__gte=start_date),staff__company__in=parent_company_ids.values(), staff__user__userprofile__user_type='sub_user', staff__user__date_joined__lt=end_date).exclude(staff__user__email__icontains='@shipsy.io').exclude(staff__user__email__icontains='@stockone.com').values_list('staff__company_id').annotate(Count('staff__user_id', distinct=True)))
        
        # No of Active Warehouses
        active_warehouses =dict(User.objects.using(reports_database).filter(userprofile__company__in=child_company_ids.values(), userprofile__user_type='warehouse_user',userprofile__warehouse_level=3,is_active=True, date_joined__lt=end_date).values_list('userprofile__company_id').annotate(Count('id')))
        
        # No of Sale Orders
        orders = dict(Order.objects.using(reports_database).filter(creation_date__gte=start_date, creation_date__lt=end_date, warehouse__userprofile__company__in=child_company_ids.values()).exclude(order_type='StockTransfer').values_list('warehouse__userprofile__company_id').annotate(Count('id')))
        
        billing_date = start_date.strftime('%m-%Y')
        for company in company_names:
            parent_company_id = parent_company_ids.get(company,0)
            child_company_id = child_company_ids.get(company,0)
            final_data[company][billing_date] = {
                'billing_date' : billing_date,
                'active_users': active_users.get(parent_company_id, 0),
                'active_warehouses': active_warehouses.get(child_company_id, 0),
                'orders': orders.get(child_company_id, 0)
            }
        
        start_date = end_date
        end_date = start_date + relativedelta(months=1)

    return JsonResponse(final_data)

@get_warehouse
def get_approval_users(request, warehouse: User):
    """
    Retrieve users with specific approval permissions for a given transaction type in a warehouse.
    Args:
        request (HttpRequest): The HTTP request object containing query parameters.
        warehouse (User): The warehouse user object for which approval users are to be fetched.
    Returns:
        JsonResponse: A JSON response containing a list of approval users' emails if the transaction type is valid,
                      or an error message with status 400 if the transaction type is invalid.
    """
    
    permission_mapping = {
        'so': 'can_approve_order',
    }
    users_with_permission = []
    transaction_type = request.GET.get('transaction_type','')
    if transaction_type not in permission_mapping:
        return JsonResponse({'message': 'Invalid Transaction Type'}, status=400)
    # Get the permission key for the transaction type
    permission_key = permission_mapping.get(transaction_type, '')
    if permission_key:
        users_with_permission = get_permission_based_sub_users_emails(
            warehouse, permission_key
        )
    return JsonResponse({'approval_users': users_with_permission},status=200)

def get_ars_replenishment_strategy(misc_type: str, warehouse: User):
    """
    Retrieve the ARS replenishment strategy for a given warehouse.
    Args:
        request (HttpRequest): The HTTP request object.
        warehouse (User): The warehouse user object for which the ARS replenishment strategy is to be fetched.
    Returns:
        JsonResponse: A JSON response containing the ARS replenishment strategy if it exists,
                      or an empty list if it does not exist.
    """

    ars_replenishment_strategy_objs = list(MiscDetailOptions.objects.filter(
        misc_detail__user=warehouse.id,
        misc_detail__misc_type=misc_type,
        status=1
    ).values(
         'misc_key', 'misc_value', 'json_data', 'id', 'misc_detail__misc_type'
    ))

    ars_rep_classifications = []
    for ars_misc_data in ars_replenishment_strategy_objs :
        ars_rep_strategy_data = ars_misc_data['json_data'] or {}
        ars_rep_strategy_data.update({
            'id': ars_misc_data['id'],
            'misc_key': ars_misc_data['misc_key'],
            'misc_value': ars_misc_data['misc_value']
        })
        ars_rep_classifications.append(ars_rep_strategy_data)

    return {
        'ars_rep_classifications': ars_rep_classifications
    }
