

#static fieldsreserved_quantity
created_by = "Created By"
creation_date = "Creation Date"
updation_date = "Updation Date"
updated_by = "Updated By"
phone_number = "Phone Number"
sku_code = "SKU Code"
supplier_id =  "Supplier ID"
supplier_name = "Supplier Name"
customer_name = "Customer Name"
order_type = "Order Type"
po_number_ref = "PO Number/Ref"
total_quantity = "Total Quantity"
sku_description = "SKU Description"
reserved_quantity = "Reserved Quantity"
batch_reference_or_number = "Batch Ref/Number"
invoice_number = "Invoice Number"
invoice_date = "Invoice Date"
po_reference = "PO Reference"
challan_number = "Challan Number"
challan_date = "Challan Date"
po_currency = "PO Currency"
purchase_order_id = "Purchase Order ID"
po_number = "PO Number"
po_date = "PO Date"
to_date = "To Date"
total_amount = "Total Amount"
grn_number = "GRN Number"
customer_id = "Customer ID"
updated_user = "Updated User"
return_date = "Return Date"
order_reference = "Order Reference"
return_reference = "Return Reference"
picked_quantity = "Picked Quantity"
picklist_id =  "Picklist ID"
from_date = "From Date"
order_date_and_time = "Order Date&Time"
financial_year = "Financial Year"
invoice_date_and_time = "Invoice Date&Time"
invoice_quantity = "Invoice Quantity"
invoice_id = "Invoice ID"
order_quantity = "Order Quantity"
manufactured_date = "Manufactured Date"
expiry_date = "Expiry Date"
jo_display_key_const = 'Job Code/Ref'
po_type = "PO Type"
rtv_number = "RTV Number"
invoice_or_challan = "Invoice Number/Challan Number"
shipped_quantity = "Shipped Quantity"
shipment_status = "Shipment Status"
shipment_number = "Shipment Number"
shipment_date = "Shipment Date"
awb_number = "AWB Number"
transporter_name = "Transporter"
transporter_id = "Transporter ID"
manifest_number = "Manifest Number"
vehicle_number = "Vehicle Number"
pack_size_qty = 'Pack Size Qty'
source_location_const = "Source Location"
destination_location_const = "Destination Location"
sku_size_const = 'SKU Size'
storage_type_const = 'Storage Type'
warehouse_id = 'Warehouse Id'
return_id = 'Return ID'
sku_category = "SKU Category"
source_zone = "Source Zone"
destination_zone = "Destination Zone"
cancelled_quantity = "Cancelled Quantity"
lpn_number = "LPN Number"
sub_zone_const = 'Sub Zone'

#data table headers
sku_master = [{"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "sku_desc", "header": "Product Description", "editable": True, "priority": 2, "sortable": True, "isBoolean": False, "isDownload": True, "isViewable": True},
              {"field": "sku_group", "header": "SKU Group", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "sku_type", "header": "SKU Type", "editable": True, "priority": 3, "sortable": True, "isBoolean": False, "isDownload": True, "isViewable": True}, 
              {"field": "sku_category", "header": sku_category, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "sku_class", "header": "SKU Class", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "sku_brand", "header": "SKU Brand", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "style_name", "header": "SKU Style", "editable": True, "priority": 5, "sortable": True, "isBoolean": False, "isDownload": True, "isViewable": True}, 
              {"field": "sku_size", "header": sku_size_const, "editable": True, "priority": 5, "sortable": True, "isBoolean": False, "isDownload": True, "isViewable": False}, 
              {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "cost_price", "header": "Cost Price", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "price", "header": "Selling Price", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "mrp", "header": "MRP", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "image_url", "header": "Image Url", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "min_norm_quantity", "header": "Min Norm Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "max_norm_quantity", "header": "Max Norm Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "measurement_type", "header": "UOM", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
              {"field": "color", "header": "Color", "editable": False, "priority": 4, "sortable": True, "isBoolean": False, "isDownload": True, "isViewable": True}, 
              {"field": "ean_number", "header": "Ean Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "hsn_code", "header": "HSN Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "sub_category", "header": "Sub Category", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "combo_flag", "header": "Combo Flag", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "block_options", "header": "Block For PO", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "gl_code", "header": "GL Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "batch_based", "header": "Batch Based", "editable": True, "sortable": True, "isDownload": True, "isViewable": False}, 
              {"field": "dispensing_enabled", "header": "Dispense", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
              {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
              {"field": "length", "header": "Length", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "breadth", "header": "Breadth", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "height", "header": "Height", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "weight", "header": "Weight", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "tax", "header": "Tax Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "product_shelf_life", "header": "Product Shelf Life", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "customer_shelf_life", "header": "Customer Shelf Life", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "sku_reference", "header": "SKU Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "scan_picking", "header": "Scan Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "minimum_shelf_life", "header": "Minimum Shelf Life", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "make_or_by", "header": "Make Or By", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "serialized", "header": "Serialized", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "qc_required", "header": "QC Required", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "receipt_tolerance", "header": "Receipt Tolerance", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},
              {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
              {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
location_master = [
    {"field": "zone__zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sub_zone__zone", "header": "SubZone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "zone__segregation", "header": "Segregation", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone__storage_type", "header": storage_type_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "max_capacity", "header": "Capacity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "fill_sequence", "header": "Put Sequence", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "pick_sequence", "header": "Get Sequence", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_mapping", "header": "SKU Mapping", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "restrict_one_location_to_one_sku", "header": "Restrict one Location To One Sku", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "json_data__created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "json_data__updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
staging_routing = [
    {"field": "transaction", "header": "Transaction", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "attribute_name", "header": "Attribute Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "attribute_value", "header": "Attribute Value", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "stage", "header": "Stage", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "put_zone__zone", "header": "Put Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "staging_location__location", "header": "Staging Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
zone_master = [
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "segregation", "header": "Segregation", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "storage_type", "header": storage_type_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "restrict_one_location_to_one_sku", "header": "Restrict One Location To One Sku", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
subzone_master = [
    {"field": "zone", "header": sub_zone_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "segregation", "header": "Segregation", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "storage_type", "header": storage_type_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "restrict_one_location_to_one_sku", "header": "Restrict One Location To One Sku", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "put_sequence", "header": "Put Sequence", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "get_sequence", "header": "Get Sequence", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
supplier_master = [
    {"field": "supplier_id", "header":supplier_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "address", "header": "Address", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "phone_number", "header": phone_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "email_id", "header": "Email", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "created_by__username", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updated_by__username", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
lpn_master = [{"field": "bin_number", "header": "Carton Number", "editable": True, "priority": 1, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True}, {"field": "bin_type", "header": "Carton Type", "editable": True, "priority": 2, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "create_user", "header": created_by, "editable": True, "priority": 3, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "priority": 4, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "priority": 5, "sortable": True, "isDownload": True, "isViewable": True}]
supplier_sku_mapping_master = [
    {"field": "supplierid", "header":supplier_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "supplier_code", "header": "Supplier's SKU Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "costing_type", "header": "Costing Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "currency", "header": "Currency", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "price", "header": "Price", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "margin_percentage", "header": "Margin Percentage", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "markup_percentage", "header": "Mark Up Percentage", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "mrp", "header": "MRP", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "lead_time", "header": "Lead Time", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "moq", "header": "MOQ", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "preference", "header": "Priority", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "gatekeeper_margin", "header": "GATE KEEPER MARGIN (%)", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "purchase_uom", "header": "UOM", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}

]

inventory_replenishment_master = [{"field": "classification", "header": "Classification", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "size", "header": "Size", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "min_days", "header": "SA Min Days", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "max_days", "header": "SA Max Days", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
min_max_replenishment_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "from_zone", "header": source_zone, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "from_location", "header": source_location_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone", "header": destination_zone, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": destination_location_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "min_qty", "header": "Minimum Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "max_qty", "header": "Maximum Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
nte_replenishment_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "sku_category", "header": sku_category, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "from_zone", "header": source_zone, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "from_location", "header": source_location_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone", "header": destination_zone, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": destination_location_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]

ARS_REPLENISHMENT_CLASSIFICATION_MASTER = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "classification", "header": "Classification", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "avg_sales_day", "header": "Avg Sales/Day", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "avg_sales_day_value", "header": "Avg Sales Value/Day", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cumulative_contribution", "header": "Cumulative Contribution", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "min_stock_qty", "header": "Min Stock Qty", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "max_stock_qty", "header": "Max Stock Qty", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "replenishment_qty", "header": "Replenishment Qty", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "suggested_qty", "header": "Suggested Qty", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_avail_qty", "header": "SKU Available Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "sku_pen_po_qty", "header": "SKU Pending PO Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "sku_pen_putaway_qty", "header": "SKU Pending Putaway Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]

customer_master = [{"field": "customer_id", "header":customer_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_reference", "header": "Customer Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "name", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "email_id", "header": "Email", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "latitude", "header": "Latitude", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "longitude", "header": "Longitude", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "phone_number", "header": phone_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "billing_address", "header": "Address", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}]
customer_sku_mapping_master = [{"field": "customer_id", "header": customer_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_name", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_sku_code", "header": "Customer SKU Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_shelf_life", "header": "Customer Shelf Life", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
bom_master = [{"field": "product_sku__sku_code", "header": "Product SKU Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},{"field": "product_sku__sku_desc", "header": "Product SKU Description", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},{"field": "material_sku_code", "header": "Material SKU Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},{"field": "material_sku_desc", "header": "Material SKU Description", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},{"field": "material_quantity", "header": "Material Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},{"field": "wastage_percentage", "header": "Wastage Percentage", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},{"field": "uom", "header": "UOM", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},{"field": "bom_type", "header": "BOM Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": False},{"field": "creation_date_only", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date_only", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "json_data__created_by", "header": created_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "json_data__updated_by", "header": updated_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}]
pricing_master = [{"field": "price_id", "header": "Selling Price List", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "currency_code", "header": "Currency Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "mrp_required", "header": "MRP Required", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "price_description", "header": "Price Description", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "price_list_type", "header": "Price List Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
tax_master = [{"field": "product_type", "header": "Tax Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}]
user_access_control_master = [{"field": "staff_code", "header": "User Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "name", "header": "Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "user_name", "header": "Username", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "email_id", "header": "Email", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "phone_number", "header": phone_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "groups", "header": "Roles", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "last_login", "header": "Last Login", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "last_logout", "header": "Last Logout", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "added_on", "header": "Added On", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
role_master = [{"field": "name", "header": "Role Name", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "remarks", "header": "Managed", "editable": False, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "created_by", "header": "Created By", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "creation_date", "header": "Created Date", "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "filterType": "date"}, {"field": "updation_date", "header": "Updation Date", "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "filterType": "date"}]
order_type_zone_mapping_master = [{"field": "order_type", "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "price_id", "header": "Price ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "last_updated_by", "header": "Last Updated By", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
currency_exchange_master = [{"field": "from_currency", "header": "From Currency", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "to_currency", "header": "To Currency", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
po_approval_config_master = [{"field": "name", "header": "Config Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
sku_pack_master = [{"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
                   {"field": "pack_id", "header": "Pack Id / UOM", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
                   {"field": "pack_quantity", "header": "Pack Quantity / UOM Conversion", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
                   {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
                   {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
                   {"field": "updation_date", "header": "Updation Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
                   {"field": "created_user", "header": created_by, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
                   {"field": "updated_user", "header": updated_by, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}]
uom_master = [{"field": "uom_code", "header": "UOM Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "uom_description", "header": "UOM Description", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "uom_class", "header": "UOM Class", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "base_uom", "header": "Base UOM", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "conversion_factor", "header": "Conversion Factor", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "decimals", "header": "Decimals", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
inventory_approval_config_master = [{"field": "name", "header": "Config Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]

purchase_order_master = [
    {"field": "po_display_key", "header": po_number_ref, "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "po_type", "header": po_type, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "open_po_number", "header": "Open PO Number", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "supplier_id", "header":supplier_id, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "supplier_name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "total_quantity", "header": "Total Qty", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "received_quantity", "header": "Received Qty", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "asn_quantity", "header": "ASN Qty", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "creation_date", "header": "Order Date", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "delivery_date", "header": "Expected Date", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "po_status", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
    ]
reecive_po_beta_master = [
    {"field": "po_display_key", "header": po_number_ref, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "order_quantity", "header": total_quantity, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "supplier_id", "header":supplier_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "supplier_name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "po_type", "header": po_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
    ]

asn_data_beta_master = [
    {"field": "asn_number", "header": "ASN Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "po_display_key", "header": po_number_ref, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "invoice_date", "header": invoice_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "asn_qty", "header": total_quantity, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "received_quantity", "header": "Received Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "wh_name", "header": "Store", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "asn_type", "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
    ]

grn_approval_pending = [
    {"field": "asn_number", "header": "ASN Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "po_display_key", "header": po_number_ref, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "invoice_date", "header": invoice_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "asn_qty", "header": total_quantity, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "received_quantity", "header": "Received Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "wh_name", "header": "Store", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "asn_type", "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "sku_count", "header": "Line Count", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "lpn_number", "header": "LPN Number", "editable": True, "sortable": False, "isDownload": True, "isViewable": True, 'type': 'Multi'},
    {"field": "last_submitted_by", "header": "Last Submitted By", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "last_submitted_on", "header": "Last Submitted On", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    ]

grn_pending_datatable = [
    {"field": "grn_number", "header": "GRN Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "asn_reference", "header": 'ASN Reference', "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "supplier_name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "invoice_date", "header": invoice_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "invoice_value", "header": 'Invoice Value', "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
    ]

sales_return_grn_beta_master = [
    {"field": "return_date", "header": return_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "return_id", "header": return_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "return_reference", "header": return_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "order_reference", "header": order_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "total_sr_qty", "header": total_quantity, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sr_quantity", "header": "Received Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_id", "header": customer_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_name", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "reference_type", "header": "Reference Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "return_type", "header": "Return Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
sales_return_beta_master = [
    {"field": "return_date", "header": return_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "return_id", "header": return_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "return_reference", "header": return_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "reference_type", "header": "Reference Document", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "return_type", "header": "Return Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_reference", "header": "Customer Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_name", "header": customer_name, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "line_item_count", "header": "SKU Count", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "total_sr_qty", "header": "Return Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "order_reference", "header": 'Order Reference', "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "total_return_value", "header": "Return Value", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "sales_return_status", "header": "Return Status", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    ]
po_putaway_master = [
    {"field": "grn_date", "header": "GRN Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "grn_display_key", "header": "GRN Ref/No", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "pending_quantity", "header": "GRN Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "po_display_key", "header": "PO Ref/No", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "supplier_id", "header": "Supplier ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "supplier_name", "header": "Supplier Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "po_type", "header": "PO Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    ]
pull_to_locate_master = [
    {"field": "order_reference", "header": "Order Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "picklist_number", "header": "Picklist Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cancelled_quantity", "header": cancelled_quantity, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "pending_quantity", "header": "Pending Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cancelled_date", "header": "Cancelled Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    ]
po_quality_control_master = [
    {"field": warehouse_id, "header": warehouse_id, "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "creation_date", "header": "GRN Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "grn_display_key", "header": "GRN Ref/No", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "po_display_key", "header": "PO Ref/No", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "grn_number", "header": grn_number, "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "grn_reference", "header": "GRN Reference", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "purchase_order__po_number", "header": "PO Number", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "purchase_order__open_po__po_name", "header": "Reference No", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "purchase_order__open_po__supplier__supplier_id", "header": "Supplier Id", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "purchase_order__open_po__supplier__name", "header": "Supplier Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "purchase_order__open_po__order_type", "header": "Transation Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
sr_quality_control_master = [
    {"field": warehouse_id, "header": warehouse_id, "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "creation_date", "header": "GRN Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "grn_number", "header": grn_number, "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "grn_reference", "header": "GRN Reference", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "grn_display_key", "header": "GRN Ref/No", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sales_return__return_id", "header": return_id, "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "sales_return__return_reference", "header": "Reference No", "editable": False, "sortable": True, "isDownload": True, "isViewable": False},
    {"field": "sales_return__customer__customer_id", "header": "Customer Id", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sales_return__customer__name", "header": "Customer Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sales_return__return_type", "header": "Transation Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}

]
cycle_count_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "checkBoxes": True}, 
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "batch_display_key", "header": batch_reference_or_number, "editable": True, "sortable": False, "isDownload": False, "isViewable": True}, 
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "sub_zone", "header": sub_zone_const, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "lpn_number", "header": "LPN Number", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "quantity", "header": "Quantity", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "stock_status", "header": "Status", "editable": True, "sortable": False, "isDownload": False, "isViewable": True}
]
confirm_cycle_count_master = [
    {"field": "schedule_id", "header": "schedule ID", "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True, "table_configs": {"hoverButtons": [{"check_types": [{"icon": "fi fi-rr-circle-xmark", "type": "remove_cycle_assignee", "preventDoubleClick": True, "field": "assigned_to", "tooltip": "Remove Assigne", "check_type": "exist"}]}]}},
    {"field": "cycle_count_id", "header": "Cycle Count ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "cycle_type", "header": "Cycle Count Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "generation_source", "header": "Generation Source", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "wms_code", "header": sku_code, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_size", "header": "SKU Size", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sub_zone", "header": sub_zone_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": "Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "assigned_to", "header": "Assigned To", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
empty_location_cycle_count_master = [
    {"field": "cycle_count_id", "header": "Cycle Count ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "cycle_type", "header": "Cycle Count Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sub_zone", "header": sub_zone_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": "Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "generated_user", "header": "Created By", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}
]
scheduled_cycle_count_master = [{"field": "id", "header": "ID", "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True}, {"field": "cycle_type", "header": "Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "cycle_value", "header": "Value", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "frequency", "header": "Frequency", "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
batch_level_stock_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_category", "header": sku_category, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "batch_display_key", "header": batch_reference_or_number, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "manufactured_date", "header": manufactured_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "expiry_date", "header": expiry_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "quantity", "header": "Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "pack_repr", "header": pack_size_qty, "editable": False, "sortable": False, "isDownload": False, "isViewable": True}, 
    {"field": "mrp", "header": "MRP", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "price", "header": "Price", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "weight", "header": "Weight", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "reserved_quantity", "header": "Reserved Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}
]
inventory_adjustment_master = [
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True},
    {"field": "sub_zone", "header": sub_zone_const, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_size", "header": "SKU Size", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "batch_display_key", "header": batch_reference_or_number, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "mrp", "header": "MRP", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "batch_wac", "header": "Batch WAC", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "cost_price", "header": "Cost Price", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "stock_status", "header": "Stock Status", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "adjustment_type", "header": "Adjustment Type", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "total_quantity", "header": total_quantity, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "physical_quantity", "header": "Physical Quantity", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "discrepancy_qty", "header": "Discrepancy Quantity", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "reason", "header": "Reason", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "serial_numbers", "type" :"columnClick", "field_view": True, "header": "Serial Numbers", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
move_inventory_master = [{"field": "reference_number","header": "Transaction Number","editable": True,"sortable": True,"isDownload": True,"isViewable": True},
                         {"field": "sku_code","header": sku_code,"editable": True,"sortable": False,"isDownload": True, "isViewable": True },
                         {"field": "sku_desc", "header": sku_description,"editable": True,"sortable": False, "isDownload": True, "isViewable": True},
                         {"field": "source_loc","header": source_location_const, "editable": True,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "batch_display_key","header": "Batch Number/ref","editable": True,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "lpn_number","header": "Source LPN Number","editable": True,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "dest_loc","header": destination_location_const,"editable": True,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "move_quantity","header": 'Quantity',"editable": True,"sortable": True,"isDownload": True,"isViewable": True},
                         {"field": "picked_quantity","header": 'Picked Quantity',"editable": False,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "dropped_quantity","header": "Dropped Quantity","editable": False,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "picked_by","header": "Picked By","editable": False,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "picked_at","header": "Picked At","editable": False,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "carton_id","header": "LPN","editable": True,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "status","header": "Status","editable": True,"sortable": False,"isDownload": True,"isViewable": True},
                         {"field": "serial_numbers", "header": "Serial Numbers","editable": False,"sortable": False,"isDownload": True,"isViewable": False}]
stock_summary_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "total_qty", "header": total_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}
]

closing_stock_report_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "sub_category", "header": "Sub Category", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "sku_brand", "header": "SKU Brand", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "batch_no", "header": "Batch Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "vendor_batch_number", "header": "Vendor Batch Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "batch_reference", "header": "Batch Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "inspection_lot_number", "header": "Inspection Lot Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "mfg_date", "header": manufactured_date, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "exp_date", "header": expiry_date, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "retest_date", "header": "Retest Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "reevaluation_date", "header": "Reevaluation Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "best_before_date", "header": "Best Before Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "mrp", "header": "MRP", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "stock_qty", "header": "Stock Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "uom_type", "header": "UOM Type", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "weight", "header": "Weight", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone_type", "header": "Zone Type", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "storage_type", "header": storage_type_const, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "closing_date", "header": "Closing Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True}
]
location_stock_summary_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, 
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, 
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, 
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "total_qty", "header": total_quantity, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "reserved_qty", "header": reserved_quantity, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "pack_repr", "header": pack_size_qty, "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
]
batch_stock_summary_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, 
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "batch_display_key", "header": "Batch Number/Ref", "editable": True, "sortable": False, "isDownload": False, "isViewable": True}, 
    {"field": "total_qty", "header": total_quantity, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "reserved_qty", "header": reserved_quantity, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "pack_repr", "header": pack_size_qty, "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
]

location_batch_stock_summary_master = [
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "batch_display_key", "header": "Batch Number/Ref", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "manufactured_date", "header": manufactured_date, "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "expiry_date", "header": expiry_date, "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "total_qty", "header": total_quantity, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "mrp", "header": "MRP", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "buy_price", "header": "Price", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "weight", "header": "Weight", "editable": True, "sortable": False, "isDownload": False, "isViewable": True}, 
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "subzone", "header": "Sub Zone", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, 
    {"type": "columnClick", "field": "lpn_number", "header": lpn_number, "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "reserved_qty", "header": reserved_quantity, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "pack_repr", "header": pack_size_qty, "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"type": "columnClick", "field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "is_cycle", "header": "Is Cycle", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"type": "columnClick", "field_view": True, "field": "serial_numbers", "header": "Serial Numbers", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
]

multi_warehouse_stock_summary_master = [
    {"field": "warehouse", "header": "Warehouse", "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "converter": "dropListConverter", "optionLabel": "label", "optionValue": "value", "filterType": "multiselect", "table_configs": {"FiltersOptionsFromSession": [{"modelKey": "warehouse", "filterKey": "warehouse", "modelType": "object", "optionsKey": "warehouses", "searchValue": "data___parent___userName"}]}},
    {"field": "batch_detail__batch_no", "header": "Batch No", "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": "location__location", "header": "Location", "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": "location__sub_zone__zone", "header": "SubZone", "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": "location__zone__zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": False, "isViewable": False}
] + stock_summary_master

multi_warehouse_stock_detail_master = [
    {"field": "warehouse", "header": "Warehouse", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
] + location_batch_stock_summary_master

ba_to_sa_detail_master = [
    {"field": "picklist_no", "header": "Replenishment ID", "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True, "table_configs": {"hoverButtons": [{"check_types": [{"icon": "fi fi-rr-circle-xmark", "type": "remove_replenishment_assign", "preventDoubleClick": True, "field": "assigned_to", "tooltip": "Remove Assigne", "check_type": "exist"}]}]}},
    {"field": "generation_time", "header": "Generation Time", "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "rep_type", "header": "Replenishment Type", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "sku_code", "header": sku_code, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": sku_description, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "min_qty", "header": "Min Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "max_qty", "header": "Max Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "rep_qty", "header": "Replenishment Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "source_zone", "header": source_zone, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "source_sub_zone", "header": "Source Sub Zone", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "source_loc", "header": source_location_const, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "batch_no", "header": "Batch Number", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "mfg_date", "header": manufactured_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, 
    {"field": "exp_date", "header": "Expiry Date", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "picklist_quantity", "header": "Picklist Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "picked_qty", "header": "Picked Qty", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "putaway_qty", "header": "Putaway Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "dest_zone", "header": destination_zone, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "dest_sub_zone", "header": "Destination Sub Zone", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "dest_loc", "header": destination_location_const, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "available_qty", "header": "Destination Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "assigned_to", "header": "Pick User", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "reference", "header": "Transaction ID", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
]
job_order_master = [{"field": "jo_display_key", "header": jo_display_key_const, "editable": True, "sortable": False, "isDownload": False, "isViewable": True}, {"field": "order_type", "header": order_type, "editable": False, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}]
job_order_dispense_master = [{"field": "transaction_type", "header": "Transaction Type", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "reference_number", "header": "Reference Number", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},{"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
raw_material_picklist_master = [{"field": "jo_display_key", "header": jo_display_key_const, "editable": True, "sortable": False, "checkBoxes": True, "isDownload": True, "isViewable": True}, {"field": "order_type", "header": "JO Type", "options": ["Standard JO", "Non Standard JO"], "editable": True, "sortable": False, "converter": "", "filterType": "dropdown", "isDownload": True, "isViewable": True, "type_of_field": "string", "time_inputType": "", "params_inputType": "input", "api_url_inputType": "input", "options_inputType": "chip", "converter_inputType": "dropdown", "field_path_inputType": "input", "hourFormat_inputType": "", "optionLabel_inputType": "input", "optionValue_inputType": "input", "showSeconds_inputType": "", "type_of_field_inputType": "dropdown", "default_from_range_inputType": ""}, 
                                {"field": "product_quantity", "header": "Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "pending_quantity", "header": "Pending Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "picked_quantity", "header": "Picked Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "received_quantity", "header": "Received Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"time": False, "field": "creation_date", "header": "Creation Date", "editable": True, "sortable": True, "filterType": "date", "hourFormat": 24, "isDownload": False, "isViewable": True, "showSeconds": False, "time_inputType": "dropdown", "params_inputType": "", "api_url_inputType": "", "options_inputType": "", "default_from_range": 30, "converter_inputType": "", "field_path_inputType": "", "hourFormat_inputType": "dropdown", "optionLabel_inputType": "", "optionValue_inputType": "", "showSeconds_inputType": "dropdown", "type_of_field_inputType": "", "default_from_range_inputType": "input"}]


order_view_sku_level_master = [{"field": "sku_code", "header": "Sku Code", "editable": False, "sortable": False, "isDownload": True, "isViewable": True,"checkBoxes":True}, {"field": "sku_description", "header": "Sku Desc", "editable": False, "sortable": False, "isDownload": False, "isViewable": True}, {"field": "order_quantity", "header": "Order Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "open_quantity", "header": "Open Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "picked_quantity", "header": "Picked Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "dispatched_quantity", "header": "Dispatched Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "cancelled_quantity", "header": cancelled_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "allocated_quantity", "header": "Allocated Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "unit_price", "header": "Unit Price", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "mrp", "header": "Mrp", "editable": False, "sortable": False, "isDownload": False, "isViewable": True}]
open_orders_master = [{"field": "picklist_id", "header":picklist_id, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True}, {"field": "order_display_key", "header": "Order Ref/ID", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "customer", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "picklist_status", "header": "Status", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "reserved_quantity", "header": reserved_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "slot_from", "header": "Slot From", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"}, {"field": "slot_to",  "header": "Slot To", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"}, {"field": "forward_time", "header": "Forward Time", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"}, {"field": "shipment_date", "header": "Exp Delivery Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"}, {"field": "dispatch_date", "header": "Dispatch Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},{"field": "order_type", "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "zones", "header": "ZONE", "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "type" : "Multi"}, {"field": "picker_name", "header": "Picker Name", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "trip_id", "header": "Trip ID", "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "date", "header": "Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "city", "header": "City", "editable": True, "sortable": False, "checkBoxes": False, "isDownload": True, "isViewable": True}]
order_view_master = [{'field': 'customer_name', 'header': customer_name, 'editable': True, 'sortable': True, 'isDownload': False, 'isViewable': True, 'checkBoxes': True}, {'field': 'order_reference', 'header': 'Order Ref/ID', 'editable': True, 'sortable': False, 'isDownload': False, 'isViewable': True}, {'field': 'order_status', 'header': 'Order Status', 'editable': True, 'sortable': False, 'isDownload': False, 'isViewable': False}, {"field": "order_date_and_time", "header": "Order Date & Time", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"}, {"field": "order_date", "header": "Order Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"}, {'field': 'address', 'header': 'Address', 'editable': True, 'sortable': True, 'isDownload': True, 'isViewable': True}, {'field': 'total_quantity', 'header': 'total_quantity', 'editable': False, 'sortable': False, 'isDownload': True, 'isViewable': True}, {'field': 'total_open_quantity', 'header': 'Open Quantity', 'editable': False, 'sortable': False, 'isDownload': True, 'isViewable': True}, {'field': 'cancelled_quantity', 'header': 'Cancelled Quantity', 'editable': False, 'sortable': False, 'isDownload': True, 'isViewable': True}, {'field': 'order_type', 'header': order_type, 'editable': True, 'sortable': True, 'isDownload': True, 'isViewable': True}, {'field': 'status', 'header': 'Status', 'editable': False, 'sortable': True, 'isDownload': True, 'isViewable': True}, {'field': 'order_taken_by', 'header': 'Order Taken By', 'editable': True, 'sortable': True, 'isDownload': True, 'isViewable': True}, {'field': 'creation_date__gte', 'header': 'From Date', 'editable': True, 'sortable': False, 'isDownload': False, 'isViewable': False, 'filterType': 'date'}, {'field': 'creation_date__lte', 'header': 'To Date', 'editable': True, 'sortable': False, 'isDownload': False, 'isViewable': False, 'filterType': 'date'}, {'field': 'zone', 'header': 'ZONE', 'editable': False, 'sortable': False, 'isDownload': True, 'isViewable': True, 'type': 'Multi'}, {'field': 'shipment_date', 'header': 'Exp Delivery Date', 'editable': False, 'sortable': True, 'isDownload': True, 'isViewable': True, 'filterType': 'date'}, {'field': 'city', 'header': 'City', 'editable': True, 'sortable': True, 'isDownload': True, 'isViewable': True}]
job_order_open_orders_master = [{"field": "picklist_id", "header":picklist_id, "editable": True, "sortable": True, "checkBoxes": True, "isDownload": True, "isViewable": True}, {"field": "order_display_key", "header": "JO Code/Ref", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "picklist_status", "header": "Status", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "date", "header": "Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "order_type", "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "reserved_quantity", "header": reserved_quantity, "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "picker_name", "header": "Picker Name", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}]
credit_note_master = [{"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "credit_note_number", "header": "Credit Note Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True}, {"field": "return_id", "header": "Return Id", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "return_reference", "header": return_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "document_type", "header": "Document Type", "editable": True, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "customer_id", "header": customer_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "customer_name", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "order_reference", "header": order_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "credit_note_amount", "header": "Credit Note Amount", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "return_date", "header": return_date, "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updated_user", "header": updated_user, "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "isDoubleLine" : True}, {"field": "irn_number", "header": "IRN Number", "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "isDoubleLine": True}, {"field": "ack_number", "header": "ACK Number", "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "isDoubleLine": True}, {"field": "ack_date", "header": "ACK Date", "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "isDoubleLine": True}]

user_integrations_master = [{"field": "username", "header": "User", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "name", "header": "Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "auth_type", "header": "Auth Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
user_integrations_apis_master = [{"field": "user", "header": "User", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "user_integration_name", "header": "Integration Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "api_url", "header": "API Url", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "send_token", "header": "Send Token", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "is_token_url", "header": "Is Token Url", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "trigger", "header": "Trigger", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "data_format", "header": "Data Format", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "api_method", "header": "API Method", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "data_params", "header": "Data Params", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]
user_integration_calls_master = [{"field": "id", "header": "Id", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "user", "header": "User", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "user_integration_name", "header": "User Integration Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "api_url", "header": "API Url", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "reference_number", "header": "Reference Number", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "api_method", "header": "API Method", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "trigger", "header": "Trigger", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "data_format", "header": "Data Format", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "retry_attempts", "header": "Retry Attempts", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "retry_completed", "header": "Retry Completed", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}]

audit_logs_master = [{"field": "warehouse", "header": "Warehouse", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "username", "header": "Username", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "request_path", "header": "Transaction Type", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "request_method", "header": "Method Type", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "request_time", "header": "Request Time", "editable": False, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "status_code", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "module_name", "header": "Module Name", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"type": "input", "field": "header_referer", "header": "Internal/External", "editable": True, "sortable": False, "isDownload": False, "isViewable": False}, {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "request", "header": "Request", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},{"field": "response", "header": "Response", "editable": True, "sortable": False, "isDownload": False, "isViewable": False}]
labeldata_master = [{"field": "id", "header": "Id", "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "checkBoxes": True},{"field": "label_name", "header": "Label Name", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "created_by", "header": created_by, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "updated_by", "header": updated_by, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "count", "header": "Count", "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}, {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}]

checklist_master = [
    {"field": "attribute_model", "header": "Checklist Type", "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "checkBoxes": False},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True}
    ]

return_to_vendor_master = [
    {"field": "from_date", "header": "From Date *", "filterType": "date", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "filterType": "date", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": supplier_id, "header": supplier_id, "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "purchase_order__open_po__supplier__supplier_id", "header": supplier_id, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": supplier_name, "header": supplier_name, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "purchase_order__open_po__supplier__name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": po_number, "header": po_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "purchase_order__po_number", "header": po_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": po_reference, "header": po_reference, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "purchase_order__open_po__po_name", "header": po_reference, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": po_date, "header": po_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": grn_number, "header": grn_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "grn_number", "header": grn_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": 'asn_number', "header": "ASN No", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": invoice_number, "header": invoice_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": invoice_date, "header": invoice_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": challan_number, "header": challan_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "challan_number", "header": challan_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": challan_date, "header": challan_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": total_quantity, "header": total_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": po_currency, "header": po_currency, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": total_amount, "header": total_amount, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}
    ]

saved_rtv_master = [
    {"field": "from_date", "header": "From Date *", "filterType": "date", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "filterType": "date", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": supplier_id, "header": supplier_id, "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "seller_po_summary__purchase_order__open_po__supplier__supplier_id", "header": supplier_id, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": supplier_name, "header": supplier_name, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__purchase_order__open_po__supplier__name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": po_number, "header": po_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__purchase_order__po_number", "header": po_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": po_reference, "header": po_reference, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__purchase_order__open_po__po_name", "header": po_reference, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": po_date, "header": po_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": grn_number, "header": grn_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "rtv_number", "header": rtv_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__grn_number", "header": grn_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": "ASN No" ,"header":"ASN No" ,"editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "seller_po_summary__asn__asn_number", "header": "ASN No", "editable": False, "sortable": False, "isDownload": True, "isViewable": False},
    {"field": invoice_number, "header": invoice_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": invoice_date, "header": invoice_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, 
    {"field": challan_number, "header": challan_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, 
    {"field": "seller_po_summary__challan_number", "header": challan_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": challan_date, "header": challan_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": total_quantity, "header": total_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, 
    {"field": po_currency, "header": po_currency, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, 
    {"field": total_amount, "header": total_amount, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}]



closed_rtv_master = [
    {"field": "from_date", "header": "From Date *", "filterType": "date", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "filterType": "date", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "rtv_number", "header": "RTV Ref/No ", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "rtv_date", "header": "RTV Date", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'grn_display_key', "header": "GRN Ref/No", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'grn_date', "header": "GRN Date", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'asn_number', "header": "ASN No", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__asn__asn_number", "header": "ASN No", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": 'asn_date', "header": "ASN Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": 'po_display_key', "header": po_number_ref, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'po_date', "header": po_date, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'supplier_id', "header": supplier_id, "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "seller_po_summary__purchase_order__open_po__supplier__supplier_id", "header": supplier_id, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": 'supplier_name', "header": supplier_name, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__purchase_order__open_po__supplier__name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": 'invoice_number', "header": invoice_number, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "seller_po_summary__invoice_number", "header": invoice_number, "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": 'invoice_date', "header": "Invoice Date", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'total_quantity', "header": total_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'total_value', "header": total_amount, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": 'debit_note_number', "header": "Debit Note NO", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'created_by', "header": "Created By", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": 'created_on', "header": "Created On", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
]



processed_orders_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field":picklist_id, "header":picklist_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": order_reference, "header": order_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": customer_name, "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": customer_id, "header": customer_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": picked_quantity, "header": picked_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "Packed Quantity", "header": "Packed Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": order_type, "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zones", "header": "ZONE", "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "type" : "Multi"},
    {"field": order_date_and_time, "header": order_date_and_time, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "Staging Lane", "header": "Staging Lane", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "LPN Number", "header": lpn_number, "editable": True, "sortable": False, "isDownload": True, "isViewable": False}
    ]
customer_invoices_tab_master = [{"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},{"field": invoice_id, "header": invoice_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True}, {"field":picklist_id, "header":picklist_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": financial_year, "header": financial_year, "editable": False, "sortable": False, "filterType": "date", "isDownload": True, "isViewable": True}, {"field": order_reference, "header": order_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": customer_name, "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": order_quantity, "header": order_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": invoice_quantity, "header": invoice_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": order_type, "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": invoice_date_and_time, "header": invoice_date_and_time, "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "Einvoice", "header": "Einvoice", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}]
delivery_challans_master = [{"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},{"field": "Challan ID", "header": "Challan ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True}, {"field": order_reference, "header": order_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field":picklist_id, "header":picklist_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": customer_name, "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": order_quantity, "header": order_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": picked_quantity, "header": picked_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": order_date_and_time, "header": order_date_and_time, "editable": False, "sortable": True, "isDownload": True, "isViewable": True}]
cancelled_invoices_master = [{"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},{"field": invoice_id, "header": invoice_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True}, {"field":picklist_id, "header":picklist_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": financial_year, "header": financial_year, "editable": False, "sortable": False, "filterType": "date", "isDownload": True, "isViewable": True}, {"field": order_reference, "header": order_reference, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": customer_name, "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": order_quantity, "header": order_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": "Invoice/Picklist Quantity", "header": "Invoice/Picklist Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": order_type, "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": invoice_date_and_time, "header": invoice_date_and_time, "editable": False, "sortable": True, "isDownload": True, "isViewable": True}, {"field": "Einvoice", "header": "Einvoice", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}]
stock_transfer_invoice_tab_master = [{"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},{"field": order_reference, "header": "Material Request ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},{"field":"Invoice ID", "header": invoice_or_challan, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field":picklist_id, "header":picklist_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": customer_name, "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, {"field": order_quantity, "header": order_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": invoice_quantity, "header": invoice_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, {"field": invoice_date_and_time, "header": invoice_date_and_time, "editable": False, "sortable": True, "isDownload": True, "isViewable": True}]

receive_jo_master = [
    {"field": "jo_reference", "header": jo_display_key_const, "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "product_code__sku_code", "header": "Product Code", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "product_code__sku_desc", "header": "Product Desc", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "receive_status", "header": "Receive Status", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "quantity", "header": "Quantity", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
]

invoice_shipment_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "reference_number", "header": invoice_or_challan, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "customer_reference", "header": "Customer Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_name", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_address", "header": "Customer Address", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "order_type", "header": order_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "route_id", "header": "Route ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "name", "header": "Route Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "Quantity", "header": "Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": shipped_quantity, "header": shipped_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": shipment_status, "header": shipment_status, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "shipment_type", "header": "Shipment Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": shipment_number, "header": shipment_number, "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "invoice_date_and_time", "header": invoice_date_and_time, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
]

rtv_shipment_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False}, 
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": 'reference_number', "header": rtv_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True}, 
    {"field": "rtv_date", "header": "RTV Date", "editable": False, "sortable": False, "filterType": "date", "isDownload": True, "isViewable": True}, 
    {"field": "supplier_id", "header": supplier_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "supplier_name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "supplier_address", "header": "Supplier Address", "editable": False, "sortable": False, "isDownload": True, "isViewable": True}, 
    {"field": "po_type", "header": po_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "Quantity", "header": "Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": shipped_quantity, "header": shipped_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": shipment_status, "header": shipment_status, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
]

invoice_shipment_data_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "shipment_number", "header": shipment_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "shipment_date", "header": shipment_date, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "awb_number", "header": awb_number, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "transporter_name", "header": transporter_name, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "transporter_id", "header": transporter_id, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "reference_number", "header": invoice_or_challan, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_name", "header": customer_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "order_type", "header": "Order Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "total_quantity", "header": total_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "manifest_number", "header": manifest_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "ewaybill_data", "header": "ewaybill_data", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "ewaybill_", "header": "ewaybill_", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "ewaybill_err", "header": "ewaybill_err", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "vehicle_number", "header": vehicle_number, "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "route_id", "header": "Route_Id", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_reference", "header": "Customer Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]
rtv_shipment_data_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": shipment_number, "header": shipment_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": shipment_date, "header": shipment_date, "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": awb_number, "header": awb_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": transporter_name, "header": transporter_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": transporter_id, "header": transporter_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": rtv_number, "header": rtv_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": supplier_name, "header": supplier_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": po_type, "header": po_type, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": total_quantity, "header": total_quantity, "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": manifest_number, "header": manifest_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "ewaybill_data", "header": "ewaybill_data", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "ewaybill_", "header": "ewaybill_", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "ewaybill_err", "header": "ewaybill_err", "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": vehicle_number, "header": vehicle_number, "editable": False, "sortable": False, "isDownload": False, "isViewable": False},
]

manifest_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "manifest_number", "header": manifest_number, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "checkBoxes": True},
    {"field": "manifest_date", "header": "Manifest Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "transporter_name", "header": transporter_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "total_shipments", "header": "Total Shipments", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Manifest Status", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "vehicle_number", "header": "Vehicle Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "total_lpns", "header": "Lpns Count", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "route_id", "header": "Route ID", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "invoice_reference", "header": "Invoice Reference", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
]

putaway_mapping = [
    {'field': 'transaction_type', 'header': 'Transaction Type', 'editable': True, 'sortable': False, 'isDownload': False, 'isViewable': True, "checkBoxes" : True},
    {'field': 'quantity_type', 'header': 'Quantity Type', 'editable': True, 'sortable': False, 'isDownload': False, 'isViewable': True},
    {"field": "putaway_strategy", "header": "Strategy", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "process_type", "header": "Return Type", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "reason", "header": "Reason", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "sku_code", "header": "SKU Code", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "sku_category", "header": "SKU Category", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "zone", "header": "Zone", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "priority", "header": "Priority", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "filterType": "date",},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "filterType": "date",}]

location_type_master = [
    {"field": "location_type", "header": "Location Type", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "length", "header": "Length", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "breadth", "header": "Quantity", "editable": False, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "height", "header": "Height", "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "status", "header": "Status", "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "storage_type", "header": "Storage Type", "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "volume_utilization", "header": "Volume Utilization", "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "weight_utilization", "header": "Weight utilization", "editable": False, "sortable": True, "isDownload": False, "isViewable": True},
]

supplier_csv_mapping = [
    {"field": "supplier_id", "header": supplier_id, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "supplier_name", "header": supplier_name, "editable": True, "sortable": True, "isDownload": True, "isViewable": True}, 
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date",},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date",},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True}
]

route_master = [
    {"field": "route_id", "header": "Route ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "name", "header": "Route Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "description", "header": "Description", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "priority", "header": "Priority", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "json_data__created_by", "header": created_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "json_data__updated_by", "header": updated_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
]

gate_master = [
    {"field": "gate_id", "header": "Gate ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "gate_name", "header": "Gate Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "gate_type", "header": "Gate Type", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
]

gate_pass_master = [
    {"field": "from_date", "header": from_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": to_date, "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "gate_pass_id", "header": "Gate Pass ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "gate_name", "header": "Gate Name", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "gate_pass_type", "header": "Gate Pass Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "gate_pass_sub_type", "header": "Gate Pass Sub Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "lr_number", "header": "LR No", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "lr_no", "header": "LR No", "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
    {"field": "lr_date", "header": "LR Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "transporter", "header": "Transporter", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "vehicle_no", "header": "Vehicle No", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "driver_name", "header": "Driver Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "driver_mobile_no", "header": "Driver Mobile Number", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "driver_mob_no", "header": "Driver Mobile Number", "editable": True, "sortable": False, "isDownload": False, "isViewable": False},
    {"field": "status", "header": "Status", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True,"filterType": "date"},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "created_by", "header": "Created BY", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "created_by__username", "header": "Created BY", "editable": True, "sortable": True, "isDownload": False, "isViewable": False},
]

appointment_detail_master = [
    {"field": "from_date", "header": "from_date", "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "to_date", "header": "to_date", "editable": True, "sortable": False, "filterType": "date", "isDownload": False, "isViewable": False},
    {"field": "appointment_id", "header": "appointment_id", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "appointment_date", "header": "appointment_date", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "appointment_type", "header": "appointment_type", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "dock_name", "header": "dock_name", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "status", "header": "status", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "function_type", "header": "function_type", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "handling_unit", "header": "handling_unit", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "no_of_boxes", "header": "no_of_boxes", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "start_time", "header": "start_time", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "end_time", "header": "end_time", "editable": True, "sortable": False, "isDownload": False, "isViewable": True},
    {"field": "created_by", "header": "created_by", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "updated_by", "header": "updated_by", "editable": True, "sortable": True, "isDownload": False, "isViewable": True},
    {"field": "creation_date", "header": "creation_date", "editable": True, "sortable": True, "isDownload": False, "isViewable": True, "filterType": "date"}
]

seller_usage_data_master = [
    {"field": "seller_name", "header": "Seller ID", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_code", "header": "SKU Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cost_type", "header": "Transaction Type", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cost_reference", "header": "Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "uom", "header": "UOM", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cost_quantity", "header": "Quantity", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "unit_cost", "header": "Unit Cost", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "conversion_factor", "header": "Conversion Factor", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cost_amount", "header": "Cost", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cost_category", "header": "Cost Category", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "cost_date", "header": "Transaction Date", "editable": False, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "from_cost_date", "header": "From Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": False, "filterType": "date"},
    {"field": "to_cost_date", "header": "TO Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": False, "filterType": "date"},
]

seller_usage_cost_master = [
    {"field": "seller_id", "header": "Seller Id", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_cost_category", "header": "SKU Cost Category", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "storage_cost_unit", "header": "Storage Cost Unit", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "storage_cost", "header": "Storage Cost", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "grn_cost", "header": "GRN Cost", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "rtv_cost", "header": "RTV Cost", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sale_orders_cost", "header": "Sale Orders Cost", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": "Created Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "updation_date", "header": "Updated Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
]

sorting_station_master = [
    {"field": "station", "header": "Station", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "bin", "header": "Bin", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_reference", "header": "Customer Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "vertical_group", "header": "Vertical Group", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sequence", "header": "Sequence", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "json_data__created_by", "header": created_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "json_data__updated_by", "header": updated_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
]

audit_master = [
    {"field": "reference_type", "header": "Reference Type", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "reference_number", "header": "Reference Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "audit_type", "header": "QC Type", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "audit_value", "header": "QC Value", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "current_counter", "header": "Total Flagged LPNs", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "total_count", "header": "Total Matched LPNs", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "counter_reset_days", "header": "Counter Reset Days", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "last_reset_date", "header": "Last Reset Date", "editable": False, "sortable": False, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "status", "header": "Status", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "creation_date", "header": creation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "updation_date", "header": updation_date, "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
    {"field": "json_data__created_by", "header": created_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "json_data__updated_by", "header": updated_by, "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
]

audit_view = [
    {"field": "picklist_number", "header": "Picklist Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "lpn_number", "header": "LPN Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_reference", "header": "Customer Reference", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "status", "header": "Status", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "auditor", "header": "Auditor", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "audit_date", "header": "Audit Date", "editable": True, "sortable": True, "isDownload": True, "isViewable": True, "filterType": "date"},
]

skipped_tasks_view = [
    {"field": "picklist_number", "header": "Picklist Number", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "order_reference", "header": "Order Reference", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "customer_name", "header": "Customer Name", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_code", "header": "SKU Code", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "sku_desc", "header": "SKU Description", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "batch_no", "header": "Batch No", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "location", "header": "Location", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "zone", "header": "Zone", "editable": True, "sortable": True, "isDownload": True, "isViewable": True},
    {"field": "quantity", "header": "Quantity", "editable": False, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "reason", "header": "Reason", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "picker", "header": "Picker", "editable": True, "sortable": False, "isDownload": True, "isViewable": True},
    {"field": "date", "header": "Date", "editable": True, "sortable": False, "isDownload": True, "isViewable": True, "filterType": "date"},
]

DATA_TABLE = {
    #masters
    "sku_master": {"sku_master": sku_master},
    "location_master": {"location_master": location_master},
    "staging_routing": {"staging_routing": staging_routing},
    "zone_master": {"zone_master": zone_master},
    "subzone_master": {"subzone_master": subzone_master},
    "location_type_master" : {"location_type_master": location_type_master},
    "supplier_master": {"supplier_master":supplier_master},
    "tax_master": {"tax_master": tax_master},
    "lpn_master" : {"lpn_master": lpn_master},
    "supplier_sku_mapping_master": {"supplier_sku_mapping_master":supplier_sku_mapping_master},
    "inventory_replenishment_master": {"inventory_replenishment_master": inventory_replenishment_master},
    "min_max_replenishment_master": {"min_max_replenishment_master":min_max_replenishment_master},
    "nte_replenishment_master": {"nte_replenishment_master":nte_replenishment_master},
    "ars_replenishment_classification_master": {"ars_replenishment_classification_master": ARS_REPLENISHMENT_CLASSIFICATION_MASTER},
    "customer_master": {"customer_master":customer_master},
    "customer_sku_mapping_master":{"customer_sku_mapping_master": customer_sku_mapping_master},
    "bom_master": {"bom_master": bom_master},
    "pricing_master": {"pricing_master": pricing_master},
    "user_access_control_master": {"user_access_control_master": user_access_control_master},
    "role_master": {"role_master": role_master},
    "order_type_zone_mapping_master": {"order_type_zone_mapping_master": order_type_zone_mapping_master},
    "currency_exchange_master": {"currency_exchange_master": currency_exchange_master},
    "po_approval_config_master": {"po_approval_config_master": po_approval_config_master},
    "sku_pack_master": {"sku_pack_master": sku_pack_master},
    "uom_master": {"uom_master": uom_master},
    "inventory_approval_config_master": {"inventory_approval_config_master": inventory_approval_config_master},
    "putaway_mapping" : {"putaway_mapping" : putaway_mapping},
    "supplier_csv_mapping": {"supplier_csv_mapping": supplier_csv_mapping},
    "route_master" : {"route_master" : route_master},
    "sorting_station_master" : {"sorting_station_master" : sorting_station_master},

    #material receipt beta
    "purchase_order_master": {"purchase_order_master": purchase_order_master},
    "reecive_po_beta_master": {"reecive_po_beta_master": reecive_po_beta_master},
    "asn_data_beta_master": {"asn_data_beta_master": asn_data_beta_master},
    "grn_approval_pending_master": {"grn_approval_pending": grn_approval_pending},
    "grn_pending_master": {"grn_pending_datatable": grn_pending_datatable},
    "po_putaway_master": {"po_putaway_master": po_putaway_master},
    "pull_to_locate_master": {"pull_to_locate_master": pull_to_locate_master},
    "sales_return_grn_beta_master": {"sales_return_grn_beta_master": sales_return_grn_beta_master},

    #quality control
    "po_quality_control_master" : {"po_quality_control_master": po_quality_control_master},
    "sr_quality_control_master": {"sr_quality_control_master": sr_quality_control_master},
    
    #inventory datatables
    "cycle_count_master": {"cycle_count_master": cycle_count_master},
    "confirm_cycle_count_master": {"confirm_cycle_count_master": confirm_cycle_count_master},
    "scheduled_cycle_count_master": {"scheduled_cycle_count_master": scheduled_cycle_count_master},
    "empty_location_cycle_count_master": {"empty_location_cycle_count_master": empty_location_cycle_count_master},
    "batch_level_stock_master": {"batch_level_stock_master": batch_level_stock_master},
    "inventory_adjustment_master": {"inventory_adjustment_master": inventory_adjustment_master},
    "move_inventory_master": {"move_inventory_master": move_inventory_master},
    "stock_summary_master": {"stock_summary_master": stock_summary_master},
    "closing_stock_report_master": {"closing_stock_report_master": closing_stock_report_master},
    "batch_stock_summary_master": {"batch_stock_summary_master": batch_stock_summary_master},
    "location_stock_summary_master": {"location_stock_summary_master": location_stock_summary_master},
    "location_batch_stock_summary_master": {"location_batch_stock_summary_master": location_batch_stock_summary_master},
    "multi_warehouse_stock_summary_master": {"multi_warehouse_stock_summary_master": multi_warehouse_stock_summary_master},
    "multi_warehouse_stock_detail_master": {"multi_warehouse_stock_detail_master": multi_warehouse_stock_detail_master},
    "job_order_master": {"job_order_master": job_order_master},
    "job_order_dispense_master": {"job_order_dispense_master": job_order_dispense_master},
    "job_order_open_orders_master": {"job_order_open_orders_master": job_order_open_orders_master},
    "raw_material_picklist_master": {"raw_material_picklist_master": raw_material_picklist_master},
    "receive_jo_master": {"receive_jo_master": receive_jo_master},
    "seller_usage_data_master": {"seller_usage_data_master": seller_usage_data_master},
    "seller_usage_cost_master": {"seller_usage_cost_master": seller_usage_cost_master},


    #rtv datatables
    "return_to_vendor_master": {"return_to_vendor_master": return_to_vendor_master},
    "saved_rtv_master": {"saved_rtv_master": saved_rtv_master},
    "closed_rtv_master": {"closed_rtv_master": closed_rtv_master},

    #outbound datatables
    "order_view_master": {"order_view_master": order_view_master},
    "open_orders_master": {"open_orders_master": open_orders_master},
    "sales_return_beta_master": {"sales_return_beta_master": sales_return_beta_master},
    "credit_note_master" : {"credit_note_master" : credit_note_master},
    "processed_orders_master" : {"processed_orders_master" : processed_orders_master},
    "customer_invoices_tab_master" : {"customer_invoices_tab_master" : customer_invoices_tab_master},
    "delivery_challans_master" : {"delivery_challans_master" : delivery_challans_master},
    "cancelled_invoices_master" : {"cancelled_invoices_master" : cancelled_invoices_master},
    "stock_transfer_invoice_tab_master" : {"stock_transfer_invoice_tab_master" : stock_transfer_invoice_tab_master},
    "invoice_shipment_master" : {"invoice_shipment_master" : invoice_shipment_master},
    "rtv_shipment_master" : {"rtv_shipment_master" : rtv_shipment_master},
    "invoice_shipment_data_master" : {"invoice_shipment_data_master" : invoice_shipment_data_master},
    "rtv_shipment_data_master" : {"rtv_shipment_data_master" : rtv_shipment_data_master},
    "manifest_master" : {"manifest_master" : manifest_master},
    "order_view_sku_level_master": {"order_view_sku_level_master": order_view_sku_level_master},
    "skipped_tasks_view": {"skipped_tasks_view": skipped_tasks_view},

    #integration datatables
    "user_integrations_master": {"user_integrations_master": user_integrations_master},
    "user_integrations_apis_master": {"user_integrations_apis_master": user_integrations_apis_master},
    "user_integration_calls_master": {"user_integration_calls_master": user_integration_calls_master},
    "ba_to_sa_detail_master": {"ba_to_sa_detail_master": ba_to_sa_detail_master},

    #audit logs datatable
    "audit_logs_master": {"audit_logs_master": audit_logs_master},
    
    #labeldata datatable
    "labeldata_master": {"labeldata_master": labeldata_master},
    
    #checklist datatable
    "checklist_master": {"checklist_master": checklist_master},

    #gate management datatable
    "gate_master": {"gate_master": gate_master},

    "gate_pass_master": {"gate_pass_master": gate_pass_master},
    "appointment_detail_master": {"appointment_detail_master": appointment_detail_master},
    "audit_master": {"audit_master": audit_master},
    "audit_view": {"audit_view": audit_view},
}