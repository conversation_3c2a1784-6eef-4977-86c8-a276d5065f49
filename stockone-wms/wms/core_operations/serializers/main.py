import datetime
from rest_framework import serializers

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/serializers' + today + '.log')
log_err = init_logger('logs/serializers.log')


class UpdateListSerializer(serializers.ListSerializer):
    def update(self, instances, validated_data): 
        instance_hash = {index: instance for index, instance in enumerate(instances)}
        result = [
            self.child.update(instance_hash[index], attrs)
            for index, attrs in enumerate(validated_data)
        ]
        return result

def get_serializer_error_lists(value):
    required_fields, blank_fields, invalid_fields = [],[],[]
    invalid_choices, minvalue_fields, null_fields = [],[], []
    for item_key, item_value in value.items():
        if item_value[0].code == 'required':
            required_fields.append(item_key)
        if item_value[0].code == 'blank':
            blank_fields.append(item_key)
        elif item_value[0].code == 'invalid':
            invalid_fields.append(item_key)
        elif item_value[0].code == 'invalid_choice':
            invalid_choices.append(item_value[0])
        elif item_value[0].code == 'min_value':
            minvalue_fields.append(item_key)
        elif item_value[0].code == 'null':
            null_fields.append(item_key)
    return (required_fields, blank_fields, invalid_fields, invalid_choices, minvalue_fields, null_fields)

def prepare_final_error_messages_dict(key_wise_error_dict, error_show_dict):
    '''Prepare Final Error Messages'''
    for error_key, error_item in key_wise_error_dict.items():
        error_message = []
        required_fields, blank_fields, invalid_fields, invalid_choices, minvalue_fields, null_fields = error_item
        if required_fields:
            tail_message = ' is required' if len(required_fields)==1 else ' are required'
            error_message.append(', '.join([error_key for error_key in required_fields]) + tail_message)
        if blank_fields:
            tail_message = ' cannot be blank'
            error_message.append(', '.join([error_key for error_key in blank_fields]) + tail_message)
        if null_fields:
            tail_message = ' cannot be null'
            error_message.append(', '.join([error_key for error_key in null_fields]) + tail_message)
        if invalid_fields:
            tail_message = ' is invalid' if len(invalid_fields)==1 else ' are invalid'
            error_message.append(', '.join([error_key for error_key in invalid_fields]) + tail_message)
        if invalid_choices:
            error_message.append(invalid_choices)
        if minvalue_fields:
            tail_message = ' cannot be lessthan or equals to 0'
            error_message.append(', '.join([error_key for error_key in minvalue_fields]) + tail_message)

        error_show_dict.update({ str(error_key) : error_message})
    
    return error_show_dict

def get_serializer_error_message(request_data ,error_dict):
    key_wise_error_dict = {}
    error_show_dict = {}
    try:
        log.info("Serializer Validation Error, data "+str(request_data) + "error  data"+ str(error_dict))
        line_item = error_dict.pop('items')  if ("items" in error_dict) else {}
        if not line_item:
            line_item = error_dict.pop('rates')  if ("rates" in error_dict) else {}
    except Exception as e:
        print(e)
    if error_dict:
        errors_tuple = get_serializer_error_lists(error_dict)
        if errors_tuple:
            key_wise_error_dict['header'] = errors_tuple
    
    for key, value in line_item.items():
        if value:
            errors_tuple = get_serializer_error_lists(value)
            if request_data.get('items'):
                sku_code = request_data.get('items')[key].get('sku_code','')
                index_sku_key = str(request_data.get('items')[key].get('index_key', key)) +'_'+str(sku_code)
            elif request_data.get('rates'):
                rate = request_data.get('rates')[key].get('rate','')
                index_sku_key = str(request_data.get('rates')[key].get('index_key', key))+'_'+str(rate)
            key_wise_error_dict[index_sku_key] = errors_tuple

    error_show_dict = prepare_final_error_messages_dict(key_wise_error_dict, error_show_dict)

    return error_show_dict
