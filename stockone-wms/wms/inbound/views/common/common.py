import datetime
from django.db.models import F
from collections import defaultdict
#Model Imports
from inbound.models import (
    SKUSupplier, PurchaseOrder, ASNSummary, SellerPOSummary, POLocation
    )
from quality_control.models import QualityControl

from outbound.models import SellerOrderSummary
from core.models.masters import (
    SKUMaster, SKUAttributes, EANNumbers,
    )
from inventory.models import (
    SKUPackMaster,
    SerialNumberTransactionMapping,
    StockDetail
    )

#Method Imports
from wms_base.send_message import send_sms
from core_operations.views.common.main import (
    get_user_attributes, generate_log_message
    )

from .fetch_query import get_data_from_custom_params
from .constants import MSG_CONSTANT, AMT_CONSTANT

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/inbound_common' + today + '.log')

def frame_filters_with_request_dict(request_data, filter_keys):
    filters = {}
    excluded_filter_keys = [
        'limit', 'offset', 'flat', 'header', 'from_date',
        'to_date', 'updated_at_gte', 'updated_at_lte'
    ]
    for key, value in request_data.items():
        if key in excluded_filter_keys:
            continue
        if key in filter_keys:
            if key == 'status':
                filters['status__in'] = value.split(',')
            else:
                if isinstance(value, str) and len(value.split(',')) > 1:
                    filters[str(key) + '__in'] = value.split(',')
                elif isinstance(value, list):
                    filters[str(key) + '__in'] = value
                else:
                    filters[key] = value
        else:
            return 'Invalid Filters', filters, []
    return '', filters, []


def get_po_data_with_po_number(po_numbers, warehouse, open_po=False, extra_data={}):
    ''' Fetching Purchase Order, SKU Details with PO Number'''
    return_type = extra_data.get('return_type', 'query_objects')
    value_key_list = extra_data.get('value_key_list')
    filters = extra_data.get('filters')
    po_excludes = extra_data.get('excludes', {})
    aggregates = extra_data.get('aggregates', {})
    order_by = extra_data.get('order_by', [])
    grn_inspect_skus = extra_data.get('grn_inspect_skus', [])

    po_filters = {'open_po__sku__user' : warehouse.id}
    if grn_inspect_skus:
        po_filters['open_po__sku_id__in'] = grn_inspect_skus

    q_filters = [{'po_number__in': po_numbers, 'open_po__po_name__in': po_numbers}]
    if open_po:
        po_excludes.update({'status__in' : ['location-assigned', 'stock-transfer']})
        q_filters.extend([{'open_po__order_quantity__gt' : F('received_quantity'), 'open_po__free_quantity__gt' : 0}])
    
    if filters:
        po_filters.update(filters)
    distincts = ['po_id']
    if not value_key_list:
        value_key_list = [
            'po_number', 'order_id', 'remainder_mail', 'remarks',
            'po_id', 'po_name', 'po_type', 'purchaseorder_date', 'po_creation_date', 'expected_date',
            'order_quantity', 'po_quantity', 'po_received_quantity', 'saved_quantity', 
            'supplier', 'supplier_id', 'supplier_name', 'supplier_address', 'po_line_reference',
            'sku_id', 'sku_code', 'sku_desc', 'sku_brand', 'sku_weight', 'sku_size', 'image_url',
            'sku_type', 'sku_put_zone', 'put_zone', 'sku_uom', 'pcf', 'base_uom', 'style_name',
            'sku_reference', 'receipt_tolerance', 'batch_based', 'sku_shelf_life', 'minimum_shelf_life',
            'enable_serial_based', 'mrp', 'buy_price', 'price', 'put_zone_id', 'unit', 'json_data',
            'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'cess_tax', 'apmc_tax', 'po_reference',
        'cancelled_quantity', 'delivery_date', 'free_quantity', 'received_free_quantity', 'saved_free_quantity',
        'sku_tax_type', 'supplier_tax_type', 'sku_category', 'sku_length', 'sku_breadth', 'sku_height', 'sku_weight'
        ]
    
    #Fetch Supplier Master Data
    extra_params = {
        'filters' : po_filters, 'excludes' : po_excludes,
        'distincts' : distincts, 'aggregates' : aggregates,
        'value_keys' : value_key_list, 'return_type' : return_type,
        'q_filters' : q_filters, 'order_by' : order_by
        }
    master_data = \
        get_data_from_custom_params(PurchaseOrder,'purchase_order', extra_params)
    return master_data

def get_grn_data_with_grn_number(grn_numbers, warehouse, extra_data={}):
    '''Fetching GRN Details with GRN Number'''
    filters = extra_data.get('filters')
    return_type = extra_data.get('return_type', 'query_objects')
    value_key_list = extra_data.get('value_key_list', [])
    excludes = extra_data.get('excludes', {})
    aggregateds = {}

    grn_filters = {'user_id' : warehouse.id, 'grn_number__in' : grn_numbers}
    if filters:
        grn_filters.update(filters)

    if not value_key_list:
        value_key_list = [
            'sku_desc', 'order_quantity', 'received_quantity', 'uom', 'price',
            'sku_buy_price', 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'amount', 
            'batch_reference', 'sku_batch_no', 'batch_display_key', 'expiry_date', 'manufactured_date', 'mrp',
            'invoice_number', 'invoice_date', 'grn_reference', 'creation_date',
            ]
    if 'batch_key' in value_key_list:    
        aggregateds = {
            'batch_key' : 'StringAggr'
        }

    #Fetch Supplier Master Data
    extra_params = {
        'filters' : grn_filters,
        'distincts' : ['id'],
        'value_keys' : value_key_list, 'return_type' : return_type,
        'aggregateds' : aggregateds,
        'excludes': excludes
        }
    master_data = \
        get_data_from_custom_params(SellerPOSummary, 'grn', extra_params)
    return master_data

def get_po_location_data_with_grn_number(grn_numbers, warehouse, extra_data={}, pol_ids = None, grn_ids=None, cp_putaway=False):
    '''Fetching GRN Details with GRN Number'''
    filters = extra_data.get('filters')
    return_type = extra_data.get('return_type', 'query_objects')
    value_key_list = extra_data.get('value_key_list', [])
    aggregateds = {}

    grn_filters = {'sku__user' : warehouse.id, 'status': 1}
    if pol_ids:
        grn_filters['id__in'] = pol_ids
    else:
        grn_filters['seller_po_summary__grn_number__in'] = grn_numbers
    if grn_ids:
        grn_filters['seller_po_summary__id__in'] = grn_ids
    if filters:
        grn_filters.update(filters)

    if not value_key_list:
        value_key_list = [
            'sku_desc', 'order_quantity', 'received_quantity', 'uom', 'price',
            'sku_buy_price', 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'amount', 
            'batch_reference', 'sku_batch_no', 'batch_display_key', 'expiry_date', 'manufactured_date', 'mrp',
            'invoice_number', 'invoice_date', 'grn_reference', 'creation_date',
            ]
    usage = 'po_putaway'
    if cp_putaway:
        usage = 'cp_putaway'
    if 'batch_key' in value_key_list:    
        aggregateds = {
            'batch_key' : 'StringAggr'
        }

    #Fetch Supplier Master Data
    extra_params = {
        'filters' : grn_filters,
        'distincts' : ['id'],
        'value_keys' : value_key_list, 'return_type' : return_type,
        'aggregateds' : aggregateds
        }
    master_data = \
        get_data_from_custom_params(POLocation, usage, extra_params)
    return master_data

def get_sps_data_from_quality_control(grn_numbers, warehouse, extra_data={}, grn_ids=None):
    """
    Fetches Quality Control (QC) and corresponding SellerPOSummary (SPS) details
    for given GRN numbers in a warehouse.

    Args:
        grn_numbers (list): List of GRN reference numbers.
        warehouse (object): Warehouse instance.
        extra_data (dict): Optional data for filters and return type.
        grn_ids (list, optional): List of GRN IDs to filter by.

    Returns:
        list: Merged QC and SPS data records.
    """
    # Extract filters and return type from extra_data
    filters = extra_data.get('filters', {})
    return_type = extra_data.get('return_type', 'values_list')

    # Define keys to extract from QualityControl
    qc_value_keys = [
        'id', 'grn_number', 'quantity',
        'sps_id', 'json_data', 'creation_date',
        'lpn_number',
    ]

    # Prepare QC filters
    qc_filters = {
        'warehouse_id': warehouse.id,
        'reference_number__in': grn_numbers
    }
    qc_filters.update(filters)
    if grn_ids:
        qc_filters['transaction_id__in'] = grn_ids

    # Prepare params for QC data fetch
    qc_params = {
        'filters': qc_filters,
        'excludes': {},
        'distincts': ['id'],
        'value_keys': qc_value_keys,
        'return_type': return_type,
    }

    # Fetch QC data
    master_data = get_data_from_custom_params(QualityControl, 'quality_control', qc_params)

    # Extract unique SPS IDs from QC records
    sps_ids = [data['sps_id'] for data in master_data if data.get('sps_id')]

    # Prepare filters and fields for SPS fetch
    sps_filters = {
        'id__in': sps_ids,
        'user_id': warehouse.id
    }
    sps_value_keys = [
        'price', 'sps_id', 'sku_id', 'sku_code', 'sku_desc', 'batch_based',
        'is_barcode_required', 'batch_detail_id', 'batch_no', 'batch_reference',
        'manufactured_date', 'expiry_date', 'mrp', 'vendor_batch_number',
        'inspection_lot_number', 'retest_date', 'batch_key'
    ]
    sps_params = {
        'filters': sps_filters,
        'return_type': return_type,
        'value_keys': sps_value_keys
    }

    # Fetch SPS data and create a lookup dictionary
    sps_data = get_data_from_custom_params(SellerPOSummary, 'grn', sps_params)
    sps_lookup = {sps['sps_id']: sps for sps in sps_data}

    # Merge QC data with corresponding SPS data
    for record in master_data:
        sps_id = record.get('sps_id')
        if sps_id and sps_id in sps_lookup:
            record.update(sps_lookup[sps_id])

    return master_data

def get_asn_data_with_asn_number(asn_numbers, warehouse, extra_data={}):
    '''Fetching ASN Details with ASN Number'''
    filters = extra_data.get('filters')
    return_type = extra_data.get('return_type', 'query_objects')
    value_key_list = extra_data.get('value_key_list', [])
    exclude_dict = extra_data.get('exclude_dict', {})

    asn_filters = {'asn_user_id' : warehouse.id, 'asn_number__in' : asn_numbers,}
    if filters:
        asn_filters.update(filters)
    if not value_key_list:
        value_key_list = [
            'id', 'original_quantity', 'quantity', 'rejected_quantity', 'accepted_quantity',
            'reason', 'remarks', 'purchase_order_id', 'invoice_quantity', 'invoice_value',
            'discount_percent', 'overall_discount', 'price', 'tcs_value', 'po_number', 'asn_number',
            'invoice_number', 'invoice_date', 'json_data', 'receive_quantity', 'dc_number',
            'dc_date', 'tcs', 'manufactured_date', 'expiry_date', 'batch_no', 'batch_reference',
            'weight', 'mrp', 'carton_number', 'vendor_batch_number', 'retest_date', 'reevaluation_date',
            'inspection_lot_number', 'best_before_date', 'batchdetail_id',
            'sku_shelf_life', 'minimum_shelf_life', 'asn_status',
            'line_invoice_quantity', 'invoice_free_quantity', 'line_invoice_free_quantity',
            'value_with_tax', 'value_without_tax', 'scheduled_percent', 'scheduled_amount', 'cash_discount_percent', 
            'cash_discount_amount', 'sku_tax_type', 'supplier_tax_type', 'gatekeeper_margin', 'cn_amount', 'tcs_value',
            'additional_cost', 'discount_amount', 'margin', 'net_amount', 'net_gross_amount', 'tax_amt',
            'taxable_amount',  'gross_amount', 'effective_rate', 'additional_margin' , 'tax_amount', 'mrp_without_tax',
            'supplier_gst_number', 'eff_rate', 'tcs_value', 'scanned_qr_lpns', 'supplier_invoice_tax', 'sku_code', 'asn_reference',
            'po_type','po_uom', 'sku_length', 'sku_breadth', 'sku_height', 'sku_weight'
            ]
    #Fetch Supplier Master Data
    excludes = {'purchase_order__status' : 'location-assigned'}
    if exclude_dict:
        excludes.update(exclude_dict)
    extra_params = {
        'filters' : asn_filters,
        'excludes' : exclude_dict,
        'distincts' : ['id'],
        'value_keys' : value_key_list, 'return_type' : return_type,
        }
    
    master_data = \
        get_data_from_custom_params(ASNSummary, 'asn', extra_params)
    
    return master_data

def get_sr_grn_data_with_grn_number(grn_numbers, warehouse, extra_data={}):
    '''Fetching SR GRN Details with GRN Number'''
    filters = extra_data.get('filters')
    return_type = extra_data.get('return_type', 'query_objects')
    value_key_list = extra_data.get('value_key_list', [])

    sr_grn_filters = {'user_id' : warehouse.id, 'grn_number__in' : grn_numbers, 'grn_type': 'SR'}
    if filters:
        sr_grn_filters.update(filters)

    if not value_key_list:
        value_key_list = [
            'sku_code', 'sku_desc', 'sr_line_aux_data', 'remarks',
            'return_id', 'return_reference', 'sr_reference_number', 'sales_return_quantity', 
            'sr_grn_number', 'sr_grn_reference', 'grn_json_data', 'grn_quantity',
            'buy_price', 'batch_no', 'batch_reference', 'manufactured_date', 'sr_batch_aux_data',
            'expiry_date', 'vendor_batch_number', 'sr_damaged_quantity', 'sr_cancelled_quantity', 'sr_accepted_quantity',
            'sr_reason', 'grn_status', 'sr_grn_reference', 'customer_id', 'customer_name', 'customer_reference',
            'sr_document_type', 'sr_return_type', 'sr_aux_data', 'uom', 'grn_date', 'sos_id',
            'sps_id', 'customer_type', 'credit_note_number', 'accepted_quantity', 'rejected_quantity', 'pack_id', 'pack_uom_quantity'

        ]
    #Fetch Supplier Master Data
    extra_params = {
        'filters' : sr_grn_filters,
        'distincts' : ['id'],
        'value_keys' : value_key_list, 'return_type' : return_type,
        }
    master_data = \
        get_data_from_custom_params(SellerPOSummary, 'sr_grn', extra_params)
    # add sos details to master_data
    # 'invoice_number', 'challan_number', 'sales_uom_quantity', 'sos_json_data'
    sos_ids = []
    for each_row in master_data:
        sos_id = each_row.get('sos_id')
        if sos_id and sos_id not in sos_ids:
            sos_ids.append(sos_id)
    if sos_ids:
        sos_data = list(SellerOrderSummary.objects.filter(id__in=sos_ids, order_status_flag__in=['customer_invoices', 'delivery_challans']).values('id', 'invoice_reference', 'challan_number', 'json_data'))
        sos_dict = {}
        for each_row in sos_data:
            sos_dict[each_row['id']] = each_row
        for each_row in master_data:
            sos_id = each_row.get('sos_id')
            if sos_id in sos_dict:
                each_row['invoice_number'] = sos_dict[sos_id].get('invoice_reference')
                each_row['challan_number'] = sos_dict[sos_id].get('challan_number')
                each_row['sos_json_data'] = sos_dict[sos_id].get('json_data')
                each_row['sales_uom_quantity'] = each_row.get('json_data', {}).get('pack_uom_quantity', 0)
    
    return master_data

def frame_header_line_level_data(data_list, header_key, key_dict={}):
    header_keys = key_dict.get('header_keys', [])
    line_keys = key_dict.get('line_keys', [])
    batch_keys = key_dict.get('batch_keys', [])
    
    data_dict = {}
    for each_row in data_list:
        if each_row[header_key] in data_dict:
            data_dict[each_row[header_key]].append(each_row)
        else:
            data_dict[each_row[header_key]] = [each_row]
    
    final_data_list = []
    for header_key, data in data_dict.items():
        each_dict = {}
        header_data = data[0]
        for header_key in header_keys:
            each_dict[header_key] = header_data[header_key]
        
        line_item_data = []
        for sku_data in data:
            line_item_dict = {}
            for sku_key in line_keys:
                line_item_dict[sku_key] = sku_data[sku_key]
            
            batch_dict = {}
            if line_item_dict.get('batch_based'):
                for batch_key in batch_keys:
                    batch_dict[batch_key] = sku_data[batch_key]
            
            line_item_dict['batch_details'] = batch_dict
            line_item_data.append(line_item_dict)
        
        each_dict['items'] = line_item_data
        final_data_list.append(each_dict)
    return final_data_list

def get_sku_data_with_sku_ids(sku_ids, warehouse):
    '''Fetching SKU Details with SKU Ids'''
    sku_values = ['id', 'sku_code', 'wms_code', 'sku_desc', 'color', 'sku_class', 'sku_brand', 
        'sku_category', 'image_url', 'load_unit_handle', 'shelf_life', 'minimum_shelf_life', 'weight']
    sku_results = list(SKUMaster.objects.filter(id__in=sku_ids, user=warehouse.id).values(*sku_values))
    sku_detail_dict = {}
    for each_sku in sku_results:
        each_sku['minimum_shelf_life'] = each_sku['minimum_shelf_life'] / datetime.timedelta(1)
        sku_detail_dict[each_sku['id']] = each_sku
    return sku_detail_dict

def get_sku_attributes_with_sku_ids(sku_ids, warehouse):
    sku_attr_dict = {}
    attributes = get_user_attributes(warehouse, 'sku')
    attr_list = [sku_attr.get('attribute_name') for sku_attr in attributes]
    sku_attribute_objs = list(SKUAttributes.objects.filter(sku_id__in=sku_ids, attribute_name__in = attr_list
        ).values_list('sku_id', 'attribute_name', 'attribute_value'))
    for sku_id, attribute_name, attribute_value in sku_attribute_objs:
        if sku_id not in sku_attr_dict:
            sku_attr_dict[sku_id] = {attribute_name: attribute_value}
        else:
            if attribute_name in sku_attr_dict[sku_id]:
                attr_value_str = sku_attr_dict[sku_id][attribute_name]
                sku_attr_dict[sku_id][attribute_name] = attr_value_str+","+attribute_value
            else:
                sku_attr_dict[sku_id].update({attribute_name: attribute_value})
    return sku_attr_dict

def get_ean_numbers_with_sku_ids(sku_ids, warehouse):
    '''Fetching EANNumbers for SKUs with SKU Ids'''
    sku_ean_dict = {}
    ean_number_objs = list(EANNumbers.objects.filter(
        sku__in=sku_ids, sku__user=warehouse.id).values('sku', 'ean_number'))
    for each_row in ean_number_objs:
        if each_row['sku'] in sku_ean_dict:
            sku_ean_dict[each_row['sku']].append(each_row['ean_number'])
        else:
            sku_ean_dict[each_row['sku']] = [each_row['ean_number']]
    return sku_ean_dict

def get_sku_pack_data_with_sku_ids(sku_ids, warehouse='', warehouse_id=''):
    '''Fetching SKUPack Details with SKU ids'''
    if warehouse:
        warehouse_id = warehouse.id
    sku_pack_dict = {}
    sku_packs = list(SKUPackMaster.objects.filter(
        sku_id__in=sku_ids, sku__user=warehouse_id, status=1).order_by('-pack_quantity'
            ).values('sku_id', 'pack_quantity', 'pack_id'))
    for each_pack in sku_packs:
        if each_pack['sku_id'] not in sku_pack_dict:
            sku_pack_dict[each_pack['sku_id']] = each_pack
    return sku_pack_dict

def get_sku_pack_size_with_sku_codes(sku_codes, warehouse_id):
    '''Return list of pack sizes for each sku'''
    sku_pack_objs = (
        SKUPackMaster.objects.filter(
            sku__sku_code__in=sku_codes,
            sku__user=warehouse_id,
            purchase_uom=1,
            status=1
        )
        .order_by('sku__sku_code', '-pack_quantity')
        .distinct('sku__sku_code')
        .values_list('sku__sku_code', 'pack_id', 'pack_quantity')
    )

    sku_pack_dict = {
        sku_code: {'pack_id': pack_id, 'pack_quantity': pack_quantity}
        for sku_code, pack_id, pack_quantity in sku_pack_objs
    }
    return sku_pack_dict

def get_max_pack_uom_quantity(warehouse, sku_codes, misc_dict):
    """
    Get SKU UOM dictionary for purchase order
    :param warehouse: Warehouse object
    :param sku_codes: List of SKU codes
    :param misc_dict: Miscellaneous dictionary
    :param validate_sku_wise_measurement_units: SKU wise measurement units
    :return: SKU UOM dictionary, SKU wise measurement units, and list of measurement units
    """
    sku_pack_mappping = {}
    measurement_units = set()

    if misc_dict.get('enable_purchase_uom', 'false') == 'true':
        sku_pack_objs = SKUPackMaster.objects.filter(
            sku__sku_code__in=sku_codes,
            sku__user=warehouse.id,
            purchase_uom=1,
            status=1
        ).order_by('sku__sku_code', '-pack_quantity').values('sku__sku_code', 'pack_id', 'pack_quantity')

        for obj in sku_pack_objs:
            sku_code = obj['sku__sku_code']
            if sku_code not in sku_pack_mappping:
                sku_pack_mappping[sku_code] = {obj['pack_id']: obj['pack_quantity']}
            else:
                sku_pack_mappping[sku_code].update({obj['pack_id']: obj['pack_quantity']})
            measurement_units.add(obj['pack_id'])

    return sku_pack_mappping, list(measurement_units)

def get_serial_numbers_with_reference_number(filters, warehouse_id, return_type='query_objects', value_keys=[], batch_based=False):
    alias_names = {
        'serial_number' : 'serial__serial_number',
        'sku_code' : 'serial__sku__sku_code',
        'batch_no' : 'serial__batch_detail__batch_no',
        'putaway_zone' : 'stock__location__zone__zone'
        }
    filters['warehouse_id'] = warehouse_id
    #Framing Query Values
    values_dict = {}
    values_list = []
    for each_key in value_keys:
        if each_key in alias_names:
            values_dict[each_key] = F(alias_names[each_key])
        else:
            values_list.append(each_key)
    
    
    
    serial_number_objs = SerialNumberTransactionMapping.objects.filter(**filters).values(*values_list, **values_dict)
    if return_type == "query_objects":
        return serial_number_objs
    
    serial_dict, serial_zone_dict = {}, {}
    for each_row in serial_number_objs:
        key = (each_row.get('reference_number'), each_row.get('sku_code'))
        if batch_based:
            batch_no = each_row.get('batch_no', '') if each_row.get('batch_no') else ''
            key += (batch_no,)
        if key not in serial_dict:
            serial_dict[key] = [each_row.get('serial_number')]
        else:
            serial_dict[key].append(each_row.get('serial_number'))
        serial_zone_dict[each_row.get('serial_number')] = each_row.get('putaway_zone')
    serial_dict['serial_zone_dict'] = serial_zone_dict
    return serial_dict

def check_margin_percentage(sku_id, supplier_id, warehouse):
    status = ''
    if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
        sku_attr = SKUAttributes.objects.filter(sku__user=warehouse.id, sku_id=sku_id, attribute_name='Vendor')
        if sku_attr.exists():
            if str(sku_attr[0].attribute_value.lower()) == 'daily procurement':
                return status
    if not SKUSupplier.objects.filter(supplier_id=supplier_id, sku_id=sku_id,
                                      costing_type='Margin Based').exists():
        status = "No Margin Cost Found"
    else:
        if not SKUSupplier.objects.filter(supplier_id=supplier_id, sku_id=sku_id,
                                          costing_type='Margin Based', margin_percentage__gt=0):
            status = "Margin Percentage Should be Greater than Zero"
    return status

def jo_message(po_data, phone_no, user_name, f_name, order_date):
    data = 'Dear Vendor, Please find the Job Order details for Job Code %s on dated %s from %s' % (
    f_name, order_date, user_name)
    total_quantity = 0
    for po in po_data.get('material_data', []):
        data += '\nSKU: %s, Qty: %s' % (po['SKU Code'], po['Quantity'])
        total_quantity += int(po['Quantity'])
    data += '\nTotal Qty: %s' % (total_quantity)
    send_sms(phone_no, data)

def po_message(po_data, phone_no, user_name, f_name, order_date, ean_flag, table_headers=None, supplier_currency='INR'):
    data = '%s Orders for %s dated %s' % (user_name, f_name, order_date)
    total_quantity = 0
    total_amount = 0
    if ean_flag:
        for po in po_data:
            data += MSG_CONSTANT % (po[2], po[5])
            if table_headers:
                total_quantity += int(po[table_headers.index('Qty')])
                amt_name = AMT_CONSTANT.format(supplier_currency) if AMT_CONSTANT.format(supplier_currency) in table_headers else 'Amt'
                total_amount += float(po[table_headers.index(amt_name)])
            else:
                total_quantity += int(po[4])
                total_amount += float(po[7])
    else:
        for po in po_data:
            data += MSG_CONSTANT % (po[1], po[4])
            if table_headers:
                total_quantity += int(po[table_headers.index('Qty')])
                amt_name = AMT_CONSTANT.format(supplier_currency) if AMT_CONSTANT.format(supplier_currency) in table_headers else 'Amt'
                total_amount += float(po[table_headers.index(amt_name)])
            else:
                total_quantity += int(po[3])
                total_amount += float(po[6])
    data += '\nTotal Qty: %s, Total Amount: %s\nPlease check WhatsApp for Images' % (total_quantity, total_amount)
    send_sms(phone_no, data)

def grn_message(po_data, phone_no, user_name, f_name, order_date):
    data = 'Dear Supplier,\n%s received the goods against PO NO. %s on dated %s' % (user_name, f_name, order_date)
    total_quantity = 0
    total_amount = 0
    for po in po_data:
        data += MSG_CONSTANT % (po[0], po[5])
        total_quantity += int(po[5])
        total_amount += int(po[6])
    data += '\nTotal Qty: %s, Total Amount: %s' % (total_quantity, total_amount)
    send_sms(phone_no, data)

def get_serial_numbers(request, warehouse, filter_params = {}):
    search_params = request.GET
    reference_number = search_params.get('reference_number', '')
    reference_type = search_params.get('reference_type', '')
    sku_code = search_params.get('sku_code', '')
    location = search_params.get('location', '')
    warehouse  = warehouse.username
    sku_serial_number_dict = {}
    data_dict = {}
    if not reference_number:
        return {'message':"Please give Reference Number"}
    # serial number default filters
    serial_filters = {
        'reference_number' : reference_number,
        'reference_type' : reference_type,
        'status' : 0,
        'warehouse_id': warehouse.id
        }
    if location:
        serial_filters.update({'stock__location__location': location})
    if sku_code:
        serial_filters.update({'serial__sku__sku_code' : sku_code})

    # To get all Serial Numbers for dynamic filters
    for key, value in filter_params.items():
        serial_filters.update({key:value})

    serial_number_objs = SerialNumberTransactionMapping.objects.filter(**serial_filters)
    all_serial_numbers = serial_number_objs.values(
        sku_code=F('serial__sku__sku_code'), sku_desc = F('serial__sku__sku_desc'), sku_id=F('serial__sku_id'),
        serial_number = F('serial__serial_number'), batch_no = F('serial__batch_detail__batch_no'))
    sku_serial_map = {}
    for serial_data in all_serial_numbers:
        sku_serial_map.setdefault(serial_data['sku_code'], []).append({'suggested':serial_data['serial_number'], 'updated':serial_data['serial_number'], 'batch_no': serial_data['batch_no']})
    for sku_data in all_serial_numbers:
        sku_details = {}
        if sku_data['sku_code'] not in sku_serial_number_dict:
            sku_details['sku_code'] = sku_data.get('sku_code','')
            sku_details['sku_desc'] = sku_data.get('sku_desc', '')
            sku_details['sku_id'] = sku_data.get('sku_id', '')
            sku_details['show'] = False
            sku_details['count'] = len(sku_serial_map.get(sku_data['sku_code'], []))
            sku_details['serial_numbers'] = sku_serial_map.get(sku_data['sku_code'], [])
            sku_serial_number_dict[sku_data['sku_code']] = sku_details
    data_dict['warehouse'] = warehouse
    data_dict['reference_number'] = reference_number
    data_dict['items'] = list(sku_serial_number_dict.values())
    return data_dict



def preload_stock_details_for_locations(warehouse_id, locations):
    """
    Preloads stock details for the specified locations in a warehouse.

    Args:
        warehouse_id (int): The ID of the warehouse to filter stock details.
        locations (list of str): A list of location names to fetch stock details for.

    Returns:
        dict: A dictionary (`stock_map`) where each key is a location name (str), and the value is a dictionary with the following structure:
            - 'skus' (set): A set of SKU codes (str) present in the location.
            - 'batches' (dict): A dictionary where keys are SKU codes (str) and values are sets of batch numbers (str) associated with the SKU.
            - 'unique_sku' (bool): Indicates if the location is restricted to a single SKU.
            - 'unique_batch' (bool): Indicates if the location is restricted to unique batches.
    """
    stock_qs = StockDetail.objects.filter(
        location__location__in=locations,
        sku__user=warehouse_id,
        quantity__gt=0
    ).select_related('sku', 'batch_detail', 'location', 'location__zone')

    stock_map = {}
    for stock in stock_qs:
        loc = stock.location.location
        unique_sku = stock.location.zone.restrict_one_location_to_one_sku
        unique_batch = stock.location.zone.unique_batch
        sku_code = stock.sku.sku_code
        batch_no = stock.batch_detail.batch_no if stock.batch_detail else None
        
        if loc not in stock_map:
            stock_map[loc] = {'skus': set(), 'batches': {}, 'unique_sku': unique_sku, 'unique_batch': unique_batch}
        if unique_sku:
            stock_map[loc]['skus'].add(sku_code)
        
        if sku_code not in stock_map[loc]['batches'] and unique_batch:
            stock_map[loc]['batches'][sku_code] = set()
        if batch_no and unique_batch:
            stock_map[loc]['batches'][sku_code].add(batch_no)
    
    return stock_map

def sku_batch_mixing_validation(stock_map, location, sku_code, batch_number, putaway_location_sku_batch_map, zone_restrictions):
    """
    Validates if the location is compatible with the SKU and batch number.
    
    Args:
        stock_map (dict): A dictionary containing stock details for locations.
        location (str): The location to validate.
        sku_code (str): The SKU code to validate.
        batch_number (str): The batch number to validate.
        putaway_location_sku_batch_map (dict): A dictionary containing SKU and batch information for the location.
    
    Returns:
        tuple: (bool, str)
            - First element: True if the location is compatible, False otherwise.
            - Second element: An error message if validation fails, empty string otherwise.
    """
    # Check if the location is in the stock map
    if location not in stock_map:
        # No existing stock in location
        location_skus = putaway_location_sku_batch_map[location]['skus']
        location_batches = putaway_location_sku_batch_map[location]['batches']
        if location in zone_restrictions.get('restrict_one_location_to_one_sku', set()):
        # If unique SKU is enabled and multiple SKUs are being added
            if len(location_skus) > 0 and sku_code not in location_skus:
                return False, f"Location {location} is restricted to one SKU. Cannot add multiple SKUs."
        if location in zone_restrictions.get('unique_batch', set()):
            # If unique batch is enabled and multiple batches are being added
            if location_batches and batch_number and batch_number not in location_batches:
                return False, f"Location {location} is restricted to one batch. Cannot add multiple batches."
        
        return True, ""
    
    loc_data = stock_map[location]
    # Check for different SKUs
    if loc_data['unique_sku']:
        if sku_code not in loc_data['skus']:
            return False, f"Location {location} already contains a different SKU."
    
    # Check batch compatibility if batch_number provided
    if batch_number and loc_data['unique_batch']:
        batches_for_sku = loc_data['batches'].get(sku_code, set())
        if batches_for_sku and batch_number not in batches_for_sku:
            return False, f"Location {location} already contains a different batch for SKU {sku_code}."
        
    return True, ""