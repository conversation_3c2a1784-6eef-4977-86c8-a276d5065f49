import datetime, copy
import traceback
import json
from datetime import timezone
from django.db.models import Sum
from django.db import transaction
from django.http import HttpRequest
from django.test.client import RequestFactory


from core_operations.views.common.main import (
    get_multiple_misc_values, get_user_attributes,
    get_user_prefix_incremental, prepare_grn_ext_data,
    create_default_zones, WMSListView, get_user_time_zone,
    generate_log_message, truncate_float, get_local_date_known_timezone,
    get_custom_sku_attributes_and_values,
    ) 
from core_operations.views.services.packing_service import PackingService
from inventory.views.locator.stock_detail import get_or_create_batch
from inventory.views.locator.packing import update_packing_status_to_complete
from quality_control.views import QualityControlSet

from outbound.models import OrderDetail, SalesReturnBatchLevel, SalesReturnLineLevel
from outbound.views.sales_return.credit_note import CreditNoteGeneration
from outbound.views.picklist.drop_ship_picklist import drop_ship_generate_picklist


from wms_base.models import User
from wms.celery import app as celery_app

from core.models import (
    SKUMaster, AuthorizedBins, MasterDocs, TempJson, MasterEmailMapping
    )
from inbound.models import ( 
    OpenPO, PurchaseOrder, ASNSummary, SellerPOSummary,
    QualityCheck, POLocation)

from inventory.views.move.staging_lanes import StagingLaneSet
from inventory.views.move.move_inventory import create_destination_stocks
from inventory.views.masters.location_master import (
    get_staging_route_location_details, get_next_suggested_staging_location
)
from inventory.models import LocationMaster, ZoneMaster, StockDetail, BatchDetail
from core_operations.views.integration.integration import webhook_integration_3p
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

from inbound.views.common.constants import INBOUND_STAGING_LANES_MAPPING
from inbound.views.common.mail_pdf import write_and_mail_pdf
from inbound.views.common.common import(
    get_po_location_data_with_grn_number,
    get_sps_data_from_quality_control
)
from inbound.views.putaway.suggestions import save_suggested_po_location, fetch_default_locations,putaway_suggestions_for_grn

from .discrepancy import generate_discrepancy_data
from .combo import ChildSKUBatchSet
from .constants import GRN_ERROR_MESSAGE
from .common import save_grn_extra_fields

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/grn_creation' + today + '.log')



def create_stocks_in_staging_area_after_grn(warehouse, sps_data, grn_number, cp_putaway=None, transaction_type='', qc_data=None, is_qc=False):
    '''
        Creating Stock in Staging Area after GRN
    '''
    try:
        log.info(generate_log_message(
            "StagingStockCreationforPackingRequest", username=warehouse.username,
            sps_data = sps_data, grn_number=grn_number)
            )
        grn_number_ = ''
        stock_creation_list = []
        lpn_wise_put_zones = {}
        pnd_location_id = fetch_default_locations('pnd', 'inbound_staging', warehouse)[0].id
        misc_dict = get_multiple_misc_values(['staging_location_routing', ], warehouse.id)

        qc_data = qc_data or []
        # Process each data item in sps_data to prepare stock creation details.
        sps_data.extend(qc_data)
        for data in sps_data:
            json_data = data.get('json_data', {}) or {}
            if json_data.get('remarks', '') == 'shortage':
                continue
            to_location_id = json_data.get('to_location_id')
            # Set 'to_location_id' to the pending location if not provided.
            if not to_location_id:
                to_location_id = pnd_location_id
            lpn_number = json_data.get('lpn_number')
            grn_number_ = data['grn_number'] if data.get('grn_number') else grn_number
            destination_details = {
                'sku_id' : data.get('sku_id'),
                'receipt_type' : "grn_packing",
                'receipt_number': data.get('id'),
                'grn_number' : grn_number_,
                'status' : 0,
                'dest_loc_id' : to_location_id,
                'batch_detail_id' : data.get('batch_detail_id'),
                'quantity' :  data.get('quantity'),
                'lpn_number': lpn_number,
                'is_barcode_required': data.get('is_barcode_required')
            }
            # If an LPN number is present, update putaway zone details.
            if lpn_number:
                if lpn_number not in lpn_wise_put_zones:
                    lpn_wise_put_zones[lpn_number] = data.get('putaway_zone', '')
                    destination_details['putaway_zone'] = data.get('putaway_zone', '')
                else:
                    destination_details['putaway_zone'] = lpn_wise_put_zones[lpn_number]
            stock_creation_list.append(destination_details)

        # Check if staging location routing includes 'PO'
        if transaction_type.lower() in misc_dict.get('staging_location_routing', '').lower().split(',') and not cp_putaway and not is_qc:
            # Retrieve staging route data and a default receiving location.
            staging_route_data = get_staging_route_location_details(warehouse, [], [transaction_type], current_stage = None)
            rec_loc = None
            lpn_wise_rec_loc = {}
            # Assign suggested staging locations for each stock item.
            for each_data in stock_creation_list:
                putaway_zone = each_data.get('putaway_zone')
                if not putaway_zone:
                    putaway_zone = None
                barcode_required = 'yes' if each_data.get('is_barcode_required') else 'no'
                # Suggest next staging location or default staging zone if next lane doesn't exists.
                if each_data.get('lpn_number') not in lpn_wise_rec_loc:
                    next_lane = get_next_suggested_staging_location(staging_route_data, (putaway_zone, barcode_required, '', transaction_type))
                    if not next_lane and not rec_loc:
                        rec_loc = create_default_zones(warehouse, 'REC_ZONE1', 'REC_LOC1', 99999, 'inbound_staging', storage_type='REC')
                        rec_loc = rec_loc[0].id
                    each_data['dest_loc_id'] = next_lane.get('location_id') if next_lane else rec_loc
                    lpn_wise_rec_loc[each_data.get('lpn_number')] = each_data['dest_loc_id']
                else:
                    each_data['dest_loc_id'] = lpn_wise_rec_loc[each_data.get('lpn_number')]

        # Create destination stocks based on the prepared stock creation list.
        create_destination_stocks(warehouse, stock_creation_list, staging_stock=True)

        log.info(generate_log_message(
            "StagingStockCreationforPackingResponse", username=warehouse.username,
            sps_data = sps_data, grn_number=grn_number)
            )
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(generate_log_message(
            "StagingStockCreationforPackingFailure", username=warehouse.username,
            sps_data = sps_data, grn_number=grn_number, error=str(e))
            )
    return grn_number_

def prepare_batch_key_dict(sps_data, warehouse_id):
    sku_code_list = [each.get('sku_code') for each in sps_data]
    batch_no_list = [each.get('batch_no') for each in sps_data]
    batch_key_data = BatchDetail.objects.filter(
        batch_no__in = batch_no_list, sku__sku_code__in = sku_code_list, sku__user = warehouse_id, batch_identifier__json_data__created_by = "system_generated"
        ).values_list('sku__sku_code', 'batch_no', 'batch_identifier__batch_identifier')
    sku_batch_batch_key_dict = {}
    for each in batch_key_data:
        sku_code, batch_no, batch_key = each
        sku_batch_batch_key_dict[(sku_code, batch_no)] = batch_key
    return sku_batch_batch_key_dict

# @celery_app.task
def create_grn_lpn_transactions(user_id, warehouse_id, grn_number='', pol_ids = None, extra_params={}, grn_ids=None, grn_type='', is_qc=False):
    try:
        warehouse = User.objects.get(id=warehouse_id)
        values_list = [
            'id', 'grn_number', 'grn_reference', 'quantity',
            'unit_price', 'json_data', 'creation_date',
            'sku_id','sku_code', 'sku_desc', 'batch_based',
            'batch_detail_id', 'batch_no', 'batch_reference', 'manufactured_date', 'expiry_date', 'mrp',
            'vendor_batch_number', 'inspection_lot_number', 'retest_date', 'batch_key', 'lpn_number',
            'putaway_zone', 'is_barcode_required'
            ]
        
        grn_extra_data = {
            'return_type': 'values_list', 'value_key_list' : values_list
                }
        transaction_type = extra_params.get('transaction_type', '')
        cp_putaway = True if (pol_ids and not is_qc) else False
        sps_data = get_po_location_data_with_grn_number([grn_number], warehouse, extra_data = grn_extra_data, pol_ids=pol_ids, grn_ids=grn_ids, cp_putaway=cp_putaway)
        qc_data = get_sps_data_from_quality_control([grn_number], warehouse, extra_data = grn_extra_data, grn_ids=grn_ids)
        grn_number_ = create_stocks_in_staging_area_after_grn(warehouse, sps_data, grn_number, cp_putaway=cp_putaway, transaction_type=transaction_type, qc_data=qc_data, is_qc=is_qc)
        sku_code_list = [each.get('sku_code') for each in sps_data]
        sku_attributes = get_custom_sku_attributes_and_values({'sku__user':warehouse_id, 'sku__sku_code__in' : sku_code_list})
        sku_batch_system_generated_batch_key_dict = prepare_batch_key_dict(sps_data, warehouse_id)
        lpn_wise_dict = {}
        for each_sps in sps_data:
            sps_json_data = each_sps.get('json_data', {})
            created_by = sps_json_data.get('request_user', '')
            carton_type = sps_json_data.get('carton_type')
            if each_sps.get('remarks') == 'Shortage':
                continue

            lpn_number = each_sps.get('lpn_number', '')
            if not lpn_number:
                continue
            #Framing Batch Details
            batch_details = None
            batch_key = sku_batch_system_generated_batch_key_dict.get(((each_sps.get('sku_code'), each_sps.get('batch_no'))))
            if each_sps.get('batch_based'):
                mfg_date, exp_date = '', ''
                timezone = get_user_time_zone(warehouse_id, warehouse_id)
                if each_sps.get('manufactured_date'):
                    mfg_date = get_local_date_known_timezone(
                        timezone, each_sps.get('manufactured_date'), send_date=True).strftime("%Y-%m-%d")
                if each_sps.get('expiry_date'):
                    exp_date = get_local_date_known_timezone(
                        timezone, each_sps.get('expiry_date'), send_date=True).strftime("%Y-%m-%d")
                batch_details = {
                    "batch_number": each_sps.get('batch_no'),
                    "batch_reference": each_sps.get('batch_reference'),
                    "manufactured_date": mfg_date,
                    "expiry_date": exp_date,
                    "mrp": each_sps.get('mrp'),
                    "vendor_batch_number": each_sps.get('vendor_batch_number'),
                    "inspection_lot_number": each_sps.get('inspection_lot_number'),
                    "retest_date": each_sps.get('retest_date'),
                    "batch_keys" : [batch_key] if batch_key else []
                }

            lpn_item = {
                "transaction_id": each_sps.get('id'),
                "sku_code": each_sps.get('sku_code'),
                "sku_description": each_sps.get('sku_desc'),
                "carton_type": carton_type,
                "mrp": each_sps.get('mrp'),
                "unit_price": each_sps.get('price'),
                "packed_quantity": each_sps.get('quantity'),
                "max_packable_quantity": each_sps.get('quantity'),
                "batch_details": batch_details,
                "json_data": {'sku_attributes': sku_attributes.get(each_sps.get('sku_code'), {})},
                "unique_id" : each_sps.get('id')
                }

            if lpn_number not in lpn_wise_dict:
                lpn_wise_dict[lpn_number] = {
                    "lpn_number": lpn_number,
                    "carton_type": carton_type,
                    "weight": 10,
                    "uom": "KG",
                    "json_data": {
                        "created_by": created_by
                    },
                    "items": [lpn_item]
                    }
            else:
                lpn_wise_dict[lpn_number]['items'].append(lpn_item)

        if lpn_wise_dict:
            transaction_type = 'grn_packing'
            if grn_type == 'SR':
                transaction_type = 'sr_grn'
            if cp_putaway:
                transaction_type = 'cancelled_putaway'
            packing_dict = {
                "warehouse": warehouse.username,
                "user": created_by,
                "status": "closed",
                "transaction_number": str(grn_number_),
                "transaction_type": transaction_type,
                "allow_multiple_transactions" : True,
                "packing_details": list(lpn_wise_dict.values()),
                "json_data": {}
                }

            log.info(generate_log_message("GRNPackingCreationRequest", warehouse_name = warehouse.username,
                    grn_number=grn_number, request=packing_dict))
            user = User.objects.get(id=user_id)
            request_dict = {
                "request_headers": extra_params.get('headers', {}),
                "request_meta": extra_params.get('request_meta', {}),
                "request_scheme": extra_params.get('request_scheme', ''),
            }
            packing_service_instance = PackingService(request_dict, user, warehouse)
            packing_response, packing_service_errors = packing_service_instance.create_packing(packing_dict)
            if packing_service_errors:
                log.info(generate_log_message("GRNPackingErrors", warehouse_name = warehouse.username,
                    grn_number=grn_number, error=packing_service_errors))
            else:
                log.info(generate_log_message("GRNPackingCreationResponse", warehouse_name = warehouse.username,
                    grn_number=grn_number, reponse=packing_response))
        else:
            log.info(generate_log_message("NoGRNpackingTransactionsFound", warehouse_name = warehouse.username,
                    grn_number=grn_number))
    except Exception as e:
        log.info(generate_log_message("GRNPackingTransactionFailure", warehouse_name = warehouse.username,
                    grn_number=grn_number, error=str(e)))
      
class CreateGRN(WMSListView):
    """
        GRN Creation Process

        It handles both Purchase Order(Normal,Import and Stock Transfer) and SalesReturn GRN Creation.
        Generates GRN Number, Serial Numbers (If required) and  Credit Note Number for SR GRN.
        This is the Entry Point for the Batches, and Assigns to SPS.
        Putaway Suggestions will be generated right After SPS Creation.
        Creates QC Records, Instead of Putaway Flow Based on Configuration.
        Rejected SKUs goes to Damaged Zone.
        Wrong SKUs goes to Wrong Zone and Short SKUs goes to Short Zone.
        Keeps the Stock into the Staing Lanes, Based on Configuration.
        Updates the WAC value in the SKU Master for the Received SKUs.
    """

    def get_configurations(self):
        """
        Fetches configurations from the database and sets the necessary attributes.
        """
        
        #Fetching Configurations
        misc_list = [
            'express_putaway', 'location_sku_mapping', 'receive_po_mandatory_fields',
            'return_po_qty', 'expense_item_putaway', 'inbound_packing', 'inbound_staging_lanes', 
            'restrict_location_to_one_sku', 'restrict_sku_to_one_location', 'putaway_strategy',
            'dynamic_bin_mapping_level', 'blind_grn', 'sr_grn_packing', 'hold_grn_po_types',
            'location_capacity_calculation', 'enable_inbound_staging_lanes',
            'allow_asn_creation_without_batch', 'old_putaway_suggestions',
        ]

        if self.grn_type == "SR":
            misc_list.extend(['nodocument_salesreturn', 'auto_credit_note'])

        self.misc_dict = get_multiple_misc_values(misc_list, self.warehouse.id)

        extra_batch_attributes = get_user_attributes(self.warehouse, 'extra_batch_attributes')
        self.extra_batch_attributes = list(extra_batch_attributes.values_list('attribute_name', flat=True))

        #Fetching Next Lane to Move the Stock
        self.staging_lanes_mapping = INBOUND_STAGING_LANES_MAPPING
        self.next_lane = ''
        if self.misc_dict.get('inbound_staging_lanes'):
            self.next_lane = self.misc_dict.get('inbound_staging_lanes', '').split(',')[0]

    def get_request_header_details(self, request_details):
        self.request_header_details = {
            "headers": {
                "Warehouse": request_details.get('request_headers', {}).get("Warehouse"),
                "Authorization": request_details.get('request_headers', {}).get('Authorization', ''),
            },
            "request_meta": request_details.get('request_meta', {}),
            "request_scheme": request_details.get('request_scheme', ''),
            "transaction_type": self.grn_type
        }
                
    def get_grn_prefix(self) -> tuple:
        """
        Get the receipt type and GRN prefix value based on the GRN type.

        Returns:
            tuple: A tuple containing the receipt type and GRN prefix value.
        """
        if self.grn_type == 'PO':
            receipt_type = 'Self Receipt'
            grn_prefix_val = 'grn_prefix'
            grn_default_prefix = 'GRN'
        elif self.grn_type == 'ST':
            receipt_type = 'Stock Transfer'
            grn_prefix_val = 'st_grn_prefix'
            grn_default_prefix = 'STGRN'
        elif self.grn_type == 'SR':
            receipt_type = 'Sale Return'
            grn_prefix_val = 'sales_return_grn'
            grn_default_prefix = 'SRGRN'

        return receipt_type, grn_prefix_val, grn_default_prefix
    
    def get_po_type_and_dept_code(self) -> tuple:
        """
            Retrieves the purchase order type and department code based on the given PO number.
        """
        po_obj_first, po_type, dept_code = '', '', ''
        filter_dict = {'open_po__sku__user':self.warehouse.id}
        if not self.po_number:
            filter_dict['open_po__po_name'] = self.po_reference
        else:
            filter_dict['po_number'] = self.po_number
        if self.grn_type != 'SR':
            po_obj_first = PurchaseOrder.objects.filter(**filter_dict)[0]
            po_type = po_obj_first.po_type
            self.po_number = po_obj_first.po_number
        else:
            po_type = self.grn_extra_dict.get('return_type') or 'sales_return_putaway'

        return po_type, dept_code
    
    def generate_grn_number(self, grn_number: str, sku_code: str, dept_code: str) -> tuple:
        """
            Generates a GRN number based on Prefix and Incremental Number.
            Returns the receipt type, receipt number, and GRN number.
        """

        #Fetching GRN Prefix to Generate GRN Number
        receipt_type, grn_prefix_val, grn_default_prefix = self.get_grn_prefix()
        #Generating GRN Number
        if grn_number:
            summary = SellerPOSummary.objects.filter(
                purchase_order__open_po__sku__user = self.warehouse.id,
                purchase_order__po_number=self.po_number).order_by('-id')
            if summary.exists():
                receipt_number = int(summary[0].receipt_number) + 1
        else:
            receipt_number, grn_prefix, grn_number, check_grn_prefix, inc_status = \
                get_user_prefix_incremental(
                    self.warehouse, grn_prefix_val, sku_code, 
                    dept_code=dept_code, create_default=grn_default_prefix)
        
        return receipt_type, receipt_number, grn_number
        
    def frame_import_po_data(self, import_data: dict, common_json_data: dict) -> tuple:
        """
            Frames the import PO data and updates the JSON data to save in SPS.
            Returns the BOE GRN data and batch JSON to save in Batch Detail.
        """

        boe_grn_data = {
            'boe_value': import_data.get('boe_value', 0),
            'remittance_adv_no': import_data.get('remittance_adv_no', ''),
            'remittance_adv_date': import_data.get('remittance_adv_date', ''),
            'exchange_rate_date': import_data.get('exchange_rate_date', ''),
            'boe_grn_total': import_data.get('boe_grn_total', 0),
            'exchange_rate': import_data.get('exchange_rate', 1)
            }
        common_json_data.update(boe_grn_data)
        
        batch_json = {
            'boe_number': import_data.get('boe_number',''),
            'boe_date': import_data.get('boe_date', ''),
            'bond_number': import_data.get('bond_number',''),
            'bond_date': import_data.get('bond_number','')
            }
        return boe_grn_data, batch_json

    def frame_serial_dict_for_creation(self, sku_code: str) -> dict:
        """
        Framing a dictionary with the given SKU code and serial number prefix for creation.

        Returns:
            dict: The dictionary containing the framed serial details.
        """
        serial_dict = {
            'reference_number': self.grn_number,
            'reference_type': 'pending_putaway',
            'sku_code': sku_code,
            'sku_details': [],
            'serial_num_prefix': self.serial_num_prefix
        }
        return serial_dict

    def create_or_update_carton(self):
        """
            Creates or updates the carton based on the carton number.
        """
        carton_number = self.each_grn.get("carton_number")
        self.json_data.update({"carton_number": self.each_grn["carton_number"]})
        carton_objs = AuthorizedBins.objects.filter(user=self.warehouse, bin_number=carton_number)
        if carton_objs.exists():
            self.carton = carton_objs[0]
        else:
            self.carton = AuthorizedBins.objects.create(
                user=self.warehouse, bin_number=carton_number,
                create_user=self.request_username)
    
    def create_po_records_for_free_sku(self):
        """
            Creates PO records for free SKUs.
            Returns the created PO object.
        """

        open_po_dict = self.each_grn['open_po_dict']
        open_po_dict['json_data'].update({"created_by": self.request_username})
        open_po_obj = OpenPO.objects.create(**open_po_dict)
        po_obj = PurchaseOrder.objects.create(
            order_id=self.each_grn.get('order_id', ''),
            open_po_id=open_po_obj.id,
            received_quantity=self.each_grn.get("quantity", 0),
            status='free_sku',
            prefix=self.each_grn.get('prefix', ''),
            po_type= self.po_type,
            po_number=self.each_grn.get('po_number', '')
            )
        return po_obj
    
    def create_batch_detail_record(self, batch_dict, sku_attributes):
        """
            Creates or updates a batch detail record based on the provided parameters.
            Returns the batch object.
        """
        batch_dict["receipt_number"] = self.receipt_number
        if self.batch_json:
            batch_json_data = {**self.batch_json}
            if batch_dict.get("json_data", {}): 
                batch_dict["json_data"].update(batch_json_data)
            else:
                batch_dict["json_data"] = batch_json_data

        #To Send SKU Attributes in Inventory Callback, Saving in Batch Detail
        if sku_attributes:
            if batch_dict.get("json_data", {}):
                batch_dict["json_data"].update({'sku_attributes':sku_attributes})
            else:
                batch_dict["json_data"]={'sku_attributes':sku_attributes}

        #Batch Detail Create or Update
        batch_update = False
        if self.each_grn.get("asn_id"):
            batch_update = True
        error_list, batch_obj = get_or_create_batch(
                self.warehouse, batch_dict, update=batch_update, extra_attributes=self.extra_batch_attributes
                )
        if error_list:
            raise ValueError(error_list)
        
        return batch_obj

    def create_sps_record(self):
        """
            Creates a SellerPOSummary record for the GRN.
        """
        seller_po_summary = SellerPOSummary.objects.create(
            sku_id = self.sku_id,
            receipt_number = self.receipt_number,
            quantity = self.grn_quantity,
            putaway_quantity = self.grn_quantity,
            accepted_quantity = self.accepted_quantity,
            damaged_quantity = self.rejected_quantity,
            invoice_quantity= self.each_grn.get("invoice_quantity", 0),
            line_invoice_quantity = self.each_grn.get("line_invoice_quantity", 0),
            purchase_order_id = self.purchase_order_id,

            sales_return_id = self.each_grn.get('sale_return_id', None),
            sales_return_batch_id = self.each_grn.get('batch_return_id', None),
            credit_note_number = self.credit_note_number,
    
            grn_type = self.grn_type,
            grn_number= self.grn_number,
            grn_reference = self.grn_reference,
            batch_detail= self.batch_obj,
            carton = self.carton,
            price= self.each_grn.get("price", 0),
            invoice_value= self.each_grn.get("invoice_value", 0), 
            invoice_date= self.each_grn.get("invoice_date", None),
            invoice_number= self.each_grn.get("invoice_number", None),
            cess_tax= self.each_grn.get("cess_tax", 0),
            apmc_tax= self.each_grn.get("apmc_tax", 0),
            remarks = self.each_grn.get("rejected_reason", ""),
            json_data = self.json_data,
            
            discount_percent= self.each_grn.get("discount_percent", 0),
            challan_number= self.each_grn.get("challan_number", None),
            challan_date= self.each_grn.get("challan_date", None),
            order_status_flag= self.each_grn.get("order_status_flag", None),
            round_off_total= self.each_grn.get("round_off_total", 0),
            overall_discount= self.each_grn.get("overall_discount", 0),
            credit_status= self.each_grn.get("credit_status", 0),
            user = self.warehouse,
            status = 3 if self.hold_grn else 0,
            account_id = self.warehouse.userprofile.id
            )
        
        if self.each_grn.get("asn_id"):
            self.update_asn_object(self.grn_quantity + self.returned_quantity)
            seller_po_summary.asn_id= self.each_grn["asn_id"]
        seller_po_summary.save()

        self.sps = seller_po_summary
        self.sps_id = seller_po_summary.id
    
    def update_asn_object(self, asn_grn_quantity=0, received_free_short_qty=0):
        '''
            Updating ASN Quantity and Status Based on GRN Quantity
        '''
        asn_obj = ASNSummary.objects.get(id=self.each_grn.get("asn_id"))
        asn_quantity = asn_obj.quantity
        remining_quantity = asn_quantity - asn_grn_quantity
        if remining_quantity<= 0 and asn_obj.status in [1, 2, 0, 5]:
            asn_obj.quantity= 0
            asn_obj.status= 0
        else:
            asn_obj.quantity = remining_quantity
            asn_obj.status = 2
        if self.each_grn.get('received_free_quantity', 0) and asn_obj.json_data:
            if asn_obj.json_data.get('received_free_quantity'):
                asn_obj.json_data['received_free_quantity'] = asn_obj.json_data['received_free_quantity'] + self.each_grn.get('received_free_quantity', 0) or received_free_short_qty
            else:
                asn_obj.json_data['received_free_quantity'] = self.each_grn.get('received_free_quantity', 0) or received_free_short_qty
        else:
            asn_obj.json_data.update({'received_free_quantity': self.each_grn.get('received_free_quantity', 0) or received_free_short_qty})
        if self.misc_dict.get('allow_asn_creation_without_batch', '') == 'true':
            asn_obj.batch_detail_id = self.batch_obj.id if self.batch_obj else None
        asn_obj.save()

    def reduce_quantity_in_po_obj(self, reduce_grn_quantity, received_free_short_qty):
        """
            Reduces the quantity and update the Status in the PO object based on the GRN Quantity.
        """
        if not self.each_grn.get('free_sku'):
            self.po_obj.status = 'grn-generated'
        self.po_obj.received_quantity = self.po_obj.received_quantity + reduce_grn_quantity - self.each_grn.get('received_free_quantity', 0)
        self.aggregated_sku_count[self.grn_key] -= reduce_grn_quantity
        self.po_obj.received_free_quantity = self.po_obj.received_free_quantity + self.each_grn.get('received_free_quantity', 0) + received_free_short_qty
        self.po_obj.save()

    def reduce_asn_quantity_in_po_obj(self, asn_quantity, reduce_grn_quantity, received_free_short_qty):
        '''
            Reduces the ASN quantity in the PO object based on the GRN Quantity.
        '''
        temp_asn_quantity = asn_quantity
        asn_quantity = asn_quantity - (reduce_grn_quantity - self.each_grn.get('received_free_quantity', 0)) #(self.grn_quantity+self.returned_quantity)
        if asn_quantity<0:
            self.each_grn['received_free_quantity'] = reduce_grn_quantity - temp_asn_quantity
            asn_quantity = 0
        self.po_obj.saved_quantity = asn_quantity
        self.po_obj.saved_free_quantity = self.po_obj.saved_free_quantity - self.each_grn.get('received_free_quantity', 0) - received_free_short_qty
        self.po_obj.save()

    def reduce_quantity_in_sr_obj(self, reduce_grn_quantity):
        '''
            Updating the Sales Return Quantity in the SalesReturnBatch object based on the GRN Quantity.
        '''
        self.po_obj.quantity = self.po_obj.quantity - reduce_grn_quantity
        self.po_obj.save()
    
    def get_putaway_check_flag(self):
        sku_type =  self.each_grn.get('sku_type','')
        putaway_check = (sku_type != 'Expense' or
            (sku_type == 'Expense' and self.misc_dict.get('expense_item_putaway') == 'true'))
        return putaway_check

    def get_putzone(self):
        '''
            Fetching the Putaway Zone for the Putaway Suggestions
        '''
        put_zone = ''
        put_zone_id = self.each_grn['put_zone_id']
        if put_zone_id:
            put_zone= ZoneMaster.objects.get(id=put_zone_id)
            put_zone= put_zone.zone
        return put_zone     

    def create_quality_check_records(self):
        '''
            Create quality check records for the GRN.
            Planning to Remove QC Records, By Adding Accepted and Rejected Quantities in SPS
        '''
        qc_put_zone = 'QC_ZONE'
        location = LocationMaster.objects.filter(
            zone__user=self.warehouse.id, zone__zone = qc_put_zone).order_by('fill_sequence')
        if location:
            location_id = location[0].id
        else:
            locations = create_default_zones(self.warehouse, 'QC_ZONE', 'QC1', 99999)
            location_id = locations[0].id
        
        po_loc = POLocation.objects.create(
            purchase_order_id = self.purchase_order_id,
            location_id = location_id,
            sku_id = self.each_grn["sku_id"],
            quantity = self.grn_quantity,
            seller_po_summary_id = self.sps_id,
            original_quantity = self.grn_quantity,
            status = 2,
            putaway_type = self.putaway_type,
            carton = self.carton,
            receipt_number = self.receipt_number,
            account_id = self.warehouse.userprofile.id,
            reference_number = self.grn_number
            )
        
        QualityCheck.objects.create(
            purchase_order_id = self.purchase_order_id,
            accepted_quantity = self.accepted_quantity,
            rejected_quantity = self.rejected_quantity,
            po_location_id = po_loc.id,
            carton = self.carton,
            putaway_quantity = self.grn_quantity,
            status = 'qc_cleared',
            account_id = self.warehouse.userprofile.id)
    
    def po_double_grn_quantity_check(self, po_number):
        '''
            Cross Checking PO Receivable Quantity Before Doing GRN
        '''
        batch_number = self.each_grn.get('batch_detail',{}).get('batch_no')
        mrp = self.each_grn.get('batch_detail',{}).get('mrp', 0)
        batch_reference = self.each_grn.get('batch_detail',{}).get('batch_reference', '')
        vendor_batch = self.each_grn.get('batch_detail', {}).get('vendor_batch_no', '')
        rejected_reason = self.each_grn.get('rejected_reason', '')
        self.grn_key = po_number, self.sku_code, self.unique_line_id, batch_number, self.each_grn['price'], mrp, batch_reference, vendor_batch, rejected_reason, self.each_grn.get('asn_id')
        aggregated_count = self.aggregated_sku_count.get(self.grn_key, 0)
        check_rec_quantity = truncate_float(self.each_grn['po_order_quantity'] - self.po_obj.received_quantity, self.each_grn['decimal_limit'])
        error_message = ''
        if not self.each_grn.get('free_sku'):
            if self.each_grn.get('asn_id'):
                if aggregated_count > check_rec_quantity+ self.each_grn.get('saved_quantity', 0) + self.each_grn.get('saved_free_quantity', 0) + self.each_grn.get('extend_qty', 0):
                    error_message = GRN_ERROR_MESSAGE[101]

            elif aggregated_count > check_rec_quantity + self.each_grn.get('extend_qty', 0) and not self.each_grn.get('free_sku'):
                error_message = GRN_ERROR_MESSAGE[101]
        return error_message
    
    def sr_double_grn_quantity_check(self):
        '''
            Cross Checking SR Receivable Quantity Before Doing GRN
        '''
        error_message = ''
        if self.grn_quantity > self.po_obj.quantity:
            error_message = GRN_ERROR_MESSAGE[101]
        return error_message
        
    def frame_qc_data(self, po_json_data):
        '''
        Framing Data for Creating Quality Check Records
        '''
        next_stage_lane = 'qc'
        qc_zone = 'QUALITY_CHECK'
        location = LocationMaster.objects.filter(
            zone__user=self.warehouse.id, zone__zone=qc_zone).order_by('fill_sequence')
        if location:
            qc_location_id = location[0].id
        else:
            locations = create_default_zones(self.warehouse, 'QUALITY_CHECK', 'QUALITY_CHECK', 99999)
            qc_location_id = locations[0].id

        if self.lpns:
            for lpn_data in self.lpns:
                packed_quantity = lpn_data.get("packed_quantity", 0)
                po_json_data_lpn = dict(po_json_data)
                po_json_data_lpn.update({'lpn_number': lpn_data.get("lpn_number")})
                self.create_and_append_qc_dict(packed_quantity, po_json_data_lpn, qc_location_id)
        else:
            self.create_and_append_qc_dict(self.grn_quantity, po_json_data, qc_location_id)

        return next_stage_lane

    def create_and_append_qc_dict(self, packed_qty, po_json, qc_location_id):
        qc_dict = {
            'custom_reference': self.sku_id,
            'warehouse': self.warehouse.id,
            'transaction_type': self.grn_extra_dict.get('qc_transaction_type'),
            'reference_number': self.grn_number,
            'transaction_id': self.sps_id,
            'location_id': qc_location_id,
            'batch_id': self.batch_obj.id if self.batch_obj else 0,
            'total_quantity': packed_qty,
            'quantity': packed_qty,
            'account_id': self.warehouse.userprofile.id,
            'qc_category': 'Full',
            'json_data': {
                'receipt_type': self.receipt_type,
                **po_json,
            }
        }
        if self.misc_dict.get('enable_inbound_staging_lanes') == 'true':
            qc_dict['quantity'] = 0
        if self.sku_combo_dict.get(self.sku_code):
            extra_params = {
                **self.grn_extra_dict,
                'qc_dict': qc_dict,
                'receipt_number': self.receipt_number,
                'transaction_type': 'qc',
                'sps_id': self.sps_id,
            }
            child_qc_data, staging_data = ChildSKUBatchSet().creation(
                self.sku_code, self.batch_combo_dict, packed_qty, self.warehouse, extra_params)
            self.qc_data.extend(child_qc_data)
        else:
            self.qc_data.append(qc_dict)

    
    def create_putaway_records(self, put_zone, batch_dict, cartons_location_mapping_dict, po_json_data, packed_quantity=0, lpn_number=None, replenish_data=None):
        sku_master = SKUMaster.objects.get(user=self.warehouse.id, sku_code=self.sku_code)
        accepted_quantity = self.accepted_quantity
        if packed_quantity:
            accepted_quantity = packed_quantity
        po_location_dict = {
            "sku_id": self.each_grn["sku_id"],
            "sku_uom" : self.each_grn["sku_uom"],
            "sku_code": self.each_grn["sku_code"],
            "sku_category": self.each_grn.get("sku_category", ''),
            "sku_mrp": self.each_grn.get("sku_mrp", 0),
            "sku_buy_price": self.each_grn.get("sku_buy_price", 0),
            "purchase_order_id": self.purchase_order_id,
            "seller_summary_id": self.sps_id,
            "grn_number": self.grn_number,
            "grn_quantity": accepted_quantity,
            "receipt_type" : self.receipt_type,
            "receipt_number": self.receipt_number,
            "batch_obj": self.batch_obj,
            "next_location" : self.stage_loc_id,
            "location_id": cartons_location_mapping_dict.get(self.each_grn.get("carton_number"), None),
            "transact_type": "po_loc",
            "next_lane" : 'pnd', 
            "company_id" : self.each_grn['company_id'],
            "decimal_limit" : self.each_grn['decimal_limit'],
            "supplier_obj": self.grn_extra_dict.get('supplier_obj'),
            "uom_qty": self.uom_qty,
            "json_data": po_json_data,
            "carton": self.carton,
            "putaway_type": self.po_type,
            "reference_number": self.grn_number,
            "lpn_number" : lpn_number,
            "replenish_data" : replenish_data,
        }
        if self.sku_combo_dict.get(self.sku_code):
            extra_params = {
                **self.grn_extra_dict,
                'po_location_dict' : po_location_dict,
                'receipt_number' : self.receipt_number,
                'cartons_location_mapping_dict' : cartons_location_mapping_dict,
                'extra_data' : self.extra_dat,
                'transaction_type' : 'accepted',
                'misc_permission_dict' : self.misc_dict,
            }
            child_qc_data, staging_data, = ChildSKUBatchSet().creation(
                    self.sku_code, self.batch_combo_dict, accepted_quantity, self.warehouse, extra_params)
        else:
            if self.uom_qty > 1 and po_json_data:
                po_json_data.update({'sku_landed_cost': po_json_data.get('sku_landed_cost',1)/self.uom_qty})

            status, staging_data = save_suggested_po_location(
                put_zone, po_location_dict, batch_dict, self.warehouse, cartons_location_mapping_dict,
                sku_master, self.extra_dat, self.misc_dict)
        staging_data = [dict(item, **{'remarks': self.each_grn.get("remarks", "")}) for item in staging_data]
        self.nonqc_staging_list.extend(staging_data)
            
    def frame_dock_staging_lane_dict(self, next_stage_lane):
        staging_lane_dict = {
            "grn_number": self.grn_number,
            "quantity": self.grn_quantity,
            "sku_id": self.each_grn["sku_id"],
            "location_id" : self.stage_loc_id,
            "carton_id" : self.carton.id,
            "receipt_number": self.sps_id,
            "receipt_type": self.staging_lanes_mapping.get('dock', {}).get('name', ''),
            "remarks": self.each_grn.get("remarks", "")
        }
        if self.batch_obj:
            staging_lane_dict['batch_detail_id'] = self.batch_obj.id
        if next_stage_lane == "qc":
            self.qc_staging_list.append(staging_lane_dict)
    
    def frame_import_grn_json_data(self):
        self.json_data.update({
            'custom_duty_charges': self.each_grn.get('custom_duty_charges', 0),
            'additional_charges': self.each_grn.get('additional_charges', 0),
            'boe_total': self.each_grn.get('boe_total', 0),
            'gst_amt': self.each_grn.get('tax_percent', 0),
            'cess_amt': self.each_grn.get('cess_tax', 0)
            })
        self.each_grn['tax_percent'] = 0
        self.each_grn['cess_tax'] = 0
        #FIX THIS
        # batch_dict['tax_percent'], batch_dict['cess_percent'] = 0, 0
    
    def handle_short_skus(self):
        sku_short_key = (self.sku_code, self.unique_line_id)
        if self.misc_dict.get('blind_grn', '')== 'true' and self.grn_type =='PO':
            sku_short_key = (self.sku_code, self.unique_line_id, self.each_grn.get('asn_id'))
        elif self.each_grn.get('asn_id'):
            sku_short_key = self.each_grn['asn_id']
        sku_short_qty = round(self.sku_shortage_dict.get(sku_short_key, 0), self.each_grn['decimal_limit'])
        batch_id = self.batch_obj.id if self.batch_obj else None
        batch_detail_id = self.each_grn.get('asn_batch_id') if self.each_grn.get('asn_batch_id') else batch_id
        received_free_short_qty = 0
        if self.each_grn.get('asn_id'):
            inv_qty = self.each_grn.get("invoice_quantity", 0) or self.each_grn.get("line_invoice_quantity", 0)
            if sku_short_qty <= inv_qty:
                received_short_qty = sku_short_qty
                received_free_short_qty = 0
            else:
                received_short_qty = inv_qty
                received_free_short_qty = sku_short_qty - inv_qty
        else:
            received_short_qty = sku_short_qty
        
        if self.uom_qty:
            sku_short_qty = round(sku_short_qty * self.uom_qty, self.each_grn['decimal_limit'])
        if sku_short_qty >0:
            self.json_data.update({
                'remarks':'shortage',
                "request_username" : self.request_username,
                'Shortage' :sku_short_qty
            })
            seller_po_data = {
                'sku_id' : self.sku_id,
                'quantity': sku_short_qty,
                'grn_number': self.grn_number,
                "grn_reference": self.grn_reference,
                'receipt_number': self.receipt_number,
                'purchase_order_id': self.purchase_order_id,
                'invoice_quantity': self.each_grn.get("invoice_quantity", 0),
                'line_invoice_quantity': self.each_grn.get("line_invoice_quantity", 0),
                'invoice_number': self.each_grn['invoice_number'],
                'putaway_quantity': sku_short_qty,
                'remarks': 'Shortage',
                'batch_detail_id': batch_detail_id,
                'credit_status': self.each_grn.get("credit_status", 0),
                'invoice_date' : self.each_grn.get('invoice_date'), 'status': 0,
                'grn_type': self.grn_type,
                'asn_id': self.each_grn.get("asn_id"),
                'json_data' : self.json_data,
                'sales_return_id' : self.each_grn.get('sale_return_id', None),
                'sales_return_batch_id' : self.each_grn.get('batch_return_id', None),
                'user': self.warehouse,
            }
            seller_po_obj = SellerPOSummary.objects.create(
                **seller_po_data, account_id = self.warehouse.userprofile.id,)
            seller_summary_id = seller_po_obj.id
            #Reducing in PO Obj
            self.reduce_quantities(received_short_qty, received_free_short_qty)
            if self.each_grn.get("asn_id"):
                #Reducing in ASN Obj
                self.update_asn_object(received_short_qty, received_free_short_qty)

            self.sku_list.append((self.sku_code, self.unique_line_id, self.each_grn.get('asn_id')))
            short_putaway_mapping = self.grn_extra_dict.get('short_putaway_mapping', {})
            location_id = None
            if short_putaway_mapping:
                if short_putaway_mapping.get('fixed_bin_mapping', []):
                    location_id = short_putaway_mapping['fixed_bin_mapping']
                elif short_putaway_mapping.get('dynamic_bin_mapping', []):
                    location_id = short_putaway_mapping['dynamic_bin_mapping']
            if not location_id:
                put_zone = "SHORT_ZONE"
                locations = LocationMaster.objects.filter(
                    zone__user=self.warehouse.id, status=1, zone__zone=put_zone).order_by('fill_sequence')
                if not locations.exists():
                    locations = create_default_zones(self.warehouse, 'SHORT_ZONE', 'SHORT1', 99999, segregation='non_sellable')
                location_id = locations[0].id

            location_data = {
                'location_id': location_id, "status" : 0,
                "receipt_number": self.receipt_number,
                "json_data": {'remarks' : 'shortage'},
                "purchase_order_id": self.purchase_order_id,
                "seller_po_summary_id": seller_summary_id,
                "quantity" :0,
                "original_quantity":sku_short_qty,
                "sku_id": self.sku_id,
                "account_id" : self.warehouse.userprofile.id,
                "putaway_type": self.putaway_type,
                "reference_number": self.grn_number
                }

            stock_detail_dict = {
                'grn_number' : self.grn_number,
                'receipt_date' :  datetime.datetime.now(),
                'location_id' : location_id,
                'status' : 1,
                'receipt_type' : self.receipt_type,
                'supplier' : self.grn_extra_dict.get('supplier_obj'),
                'account_id' : self.warehouse.userprofile.id
            }
            if self.sku_combo_dict.get(self.sku_code):
                extra_params = {
                    **self.grn_extra_dict,
                    'po_location_dict' : location_data,
                    'receipt_number' : self.receipt_number,
                    'transaction_type' : 'short',
                    'stock_detail_dict' : stock_detail_dict,
                }
                #CHECK COMBO SHORT
                child_qc_data, staging_data = \
                    ChildSKUBatchSet().creation(self.sku_code, self.batch_combo_dict,
                                                                sku_short_qty, self.warehouse, extra_params)
            else:
                po_loc = POLocation.objects.create(**location_data)
                stock_detail_dict.update({
                    'receipt_number' : po_loc.id, 
                    'original_quantity' : sku_short_qty,
                    'quantity' : sku_short_qty,
                    'sku_id' : self.sku_id, 
                    'batch_detail_id' : batch_detail_id,
                })
                stock = StockDetail.objects.create(**stock_detail_dict)
                self.prepare_serial_item_list(sps=seller_po_obj, stock=stock, transact_type='short')
        else:
            self.sku_list.append((self.sku_code, self.unique_line_id))

    def handle_wrong_skus(self, po_json_data):
        put_zone = "WRONG_ZONE"
        locations = LocationMaster.objects.filter(
            zone__user=self.warehouse.id, status=1, zone__zone=put_zone).order_by('fill_sequence')
        if not locations.exists():
            locations = create_default_zones(
                self.warehouse, 'WRONG_ZONE', 'WRONG1', 99999, segregation='non_sellable')
        rejected_qty = self.rejected_quantity * self.uom_qty
        for loc in locations:
            location_data = {
                'purchase_order_id': self.purchase_order_id,
                'location_id': loc.id,
                'status' : 1,
                'sku_id' : self.sku_id,
                "receipt_number": self.receipt_number,
                "json_data": po_json_data,
                "seller_po_summary_id": self.sps_id,
                'quantity': rejected_qty,
                'original_quantity' : rejected_qty,
                "account_id" : self.warehouse.userprofile.id,
                "putaway_type": self.putaway_type,
                "reference_number": self.grn_number
                }
            POLocation.objects.create(**location_data)
            break
    
    def update_rejected_uploaded_files(self, random_gen_number):
        master_filters = {
            'master_id' : random_gen_number, 'user' : self.warehouse.id,
            'master_type' : "grn_rejected_files"
        }
        master_docs_obj = MasterDocs.objects.filter(**master_filters)
        if master_docs_obj.exists():
            for master_doc_obj in master_docs_obj:
                master_doc_obj.master_id = self.sps_id
                master_doc_obj.master_type = "grn_rejected_docs"
            MasterDocs.objects.bulk_update_with_rounding(master_docs_obj, ['master_id', 'master_type'])

    def handle_damaged_skus(self, batch_dict, po_json_data, next_stage_lane, packed_quantity=0):
        rejected_quantity = self.rejected_quantity
        if packed_quantity:
            rejected_quantity = packed_quantity
        put_zone = "DAMAGED_ZONE"
        rej_reason_putaway_mapping = self.grn_extra_dict.get('rej_reason_putaway_mapping', {})
        rej_putaway_mapping = rej_reason_putaway_mapping.get(self.each_grn.get('rejected_reason', {}))
        location_id = None
        if rej_putaway_mapping:
            if rej_putaway_mapping.get('fixed_bin_mapping', []):
                location_id = rej_putaway_mapping['fixed_bin_mapping'][0]
            elif rej_putaway_mapping.get('dynamic_bin_mapping', []):
                location_id = rej_putaway_mapping['dynamic_bin_mapping'][0]
        if not location_id:
            locations = LocationMaster.objects.filter(
                zone__user=self.warehouse.id, status=1, zone__zone=put_zone).order_by('fill_sequence')
            if not locations.exists():
                locations = create_default_zones(self.warehouse, 'DAMAGED_ZONE', 'D1', 99999, segregation='non_sellable')
            location_id = locations[0].id
        rejected_qty = rejected_quantity * self.uom_qty
        #Frame PO Location creation dict
        location_data= {
            'purchase_order_id': self.purchase_order_id,
            'location_id': location_id, 'status' : 1,
            "receipt_number": self.receipt_number,
            "json_data": po_json_data,
            "sku_id" : self.sku_id, "seller_po_summary_id": self.sps_id,
            "carton_id": self.carton.id if self.carton else None,
            'quantity' : rejected_qty,
            'original_quantity' : rejected_qty,
            'account_id' : self.warehouse.userprofile.id,
            'putaway_type': self.putaway_type,
            'reference_number': self.grn_number
            }
        if self.sku_combo_dict.get(self.sku_code):
            extra_params = {
                **self.grn_extra_dict,
                'po_location_dict' : location_data,
                'receipt_number' : self.receipt_number,
                'transaction_type' : 'rejected',
            }
            _, _ = \
                ChildSKUBatchSet().creation(self.sku_code, self.batch_combo_dict,
                                        self.rejected_quantity, self.warehouse, extra_params)
        else:
            location_data['batch_detail'] = self.batch_obj
            po_loc= POLocation.objects.create(**location_data)
            new_batch_dict = copy.deepcopy(batch_dict)
            new_batch_dict["transact_id"] = po_loc.id
            new_batch_dict["transact_type"] = "po_loc"
            if "account_id" not in new_batch_dict:
                new_batch_dict["account_id"] = self.warehouse.userprofile.id

            #Update or Create BatchDetail
            self.batch_obj = None
            if self.each_grn.get('batch_based'):
                error_list, self.batch_obj = get_or_create_batch(
                    self.warehouse, new_batch_dict, extra_attributes=self.extra_batch_attributes)
            
                if error_list:
                    raise ValueError(error_list)

            if next_stage_lane == "non_qc":
                staging_dict = {
                    "grn_number": self.grn_number,
                    "quantity": self.rejected_quantity,
                    "sku_id": self.sku_id,
                    "batch_detail": self.batch_obj,
                    "receipt_number": po_loc.id,
                    "receipt_type": self.staging_lanes_mapping.get(self.next_lane, {}).get('name', ''),
                    "location_id": self.stage_loc_id,
                    "po_loc_id": po_loc.id,
                    "remarks": self.each_grn.get("remarks", "")
                }
                self.nonqc_staging_list.append(staging_dict)

    def delete_tempjson_data(self, model_name, po_number):
        saved_grn = TempJson.objects.filter(model_reference=po_number, model_name=model_name)
        if saved_grn.exists():
            saved_grn.delete()
    
    def quality_control_bulk_create(self, request_user):
        qc_request = HttpRequest()
        qc_request.user = request_user
        qc_request.warehouse = self.warehouse
        qc_request.POST = {'data': self.qc_data}
        quality_control = QualityControlSet()
        quality_control.request = qc_request
        quality_control.post()
    
    def moving_stock_to_staging_lane(self):
        # Creating StagingLocation and Stock Detail with the Framed Dicts
        staginglane = StagingLaneSet()
        if self.next_lane == 'dock':
            if self.qc_staging_list:
                staging_loc = {'segregation' : self.staging_lanes_mapping.get('qc', {}).get('name', '') }
                staginglane.post(self.warehouse.id, self.qc_staging_list, '', 'Receiving Staging', staging_loc)
            if self.nonqc_staging_list:
                staging_loc = {'segregation' : self.staging_lanes_mapping.get('pnd', {}).get('name', '')}
                staginglane.post(self.warehouse.id, self.nonqc_staging_list, self.stage_loc_id, 'Receiving Staging', staging_loc)
        elif self.next_lane =='pnd' and self.nonqc_staging_list:
            staging_loc = {'segregation' : self.staging_lanes_mapping.get('pnd', {}).get('name', '')}
            staginglane.post(self.warehouse.id, self.nonqc_staging_list, self.stage_loc_id, 'Putaway Staging', staging_loc)
    
    def update_wac_for_received_skus(self, sku_codes):
        action_dict = {'type' : 'grn', 'number' : self.grn_number}
    
    def master_doc_mapping(self, reference_number):
        master_filters = {
            'master_id' : reference_number, 'user' : self.warehouse.id,
            'master_type__in' : ['PO_TEMP', 'ASN_GRN_Mapping']
        }
        master_docs_obj = MasterDocs.objects.filter(**master_filters)
        if master_docs_obj.exists():
            for master_doc_obj in master_docs_obj:
                master_doc_obj.master_id = self.grn_number
                master_doc_obj.master_type = "GRN_PO_NUMBER"
            MasterDocs.objects.bulk_update_with_rounding(master_docs_obj, ['master_id', 'master_type'])
        
        #Check BOE Number
    
    def frame_discrepancy_data(self, iteration):
        discrepency_reason = self.each_grn.get("discrepency_reason", "")
        po_new_data = {}
        mfg_date, exp_date = '', ''
        if self.each_grn.get('batch_detail', {}).get("batch_no", ""):
            cond = (
                self.purchase_order_id, self.each_grn["sku_code"],'',
                self.each_grn.get("price", 0),
                self.each_grn.get("cgst_tax",0), self.each_grn.get('sgst_tax', 0),
                self.each_grn.get("igst_tax", 0), self.each_grn.get("utgst_tax",0),
                self.each_grn.get('sku_desc', ''), self.each_grn.get('cess_tax', 0), 0,
                self.each_grn.get('apmc_tax', 0), self.each_grn.get('batch_detail', {}).get("batch_no", ""),
                self.each_grn.get('mrp', 0), iteration,  mfg_date, exp_date
                )
        else:
            cond = (
                self.purchase_order_id, self.each_grn["sku_code"],'',
                self.each_grn.get("price", 0), self.each_grn.get("cgst_tax",0),
                self.each_grn.get('sgst_tax', 0), self.each_grn.get("igst_tax", 0),
                self.each_grn.get("utgst_tax",0), self.each_grn.get('sku_desc', ''),
                self.each_grn.get('cess_tax', 0),0, self.each_grn.get('apmc_tax', 0),
                self.each_grn.get('mrp', 0), iteration,  mfg_date, exp_date)
        po_new_data.setdefault(cond, {'discrepency_quantity': 0,'discrepency_reason': '', 'value': 0 })
        po_new_data[cond]['discrepency_quantity']+= self.returned_quantity
        po_new_data[cond]['discrepency_reason'] = discrepency_reason
        po_new_data[cond]['po_id'] = self.purchase_order_id
        po_new_data[cond]["value"] += self.grn_quantity
    
    def grn_discrepancy(self, po_reference):
        po_data, po_new_data, report_data_dict, data_dict = {}, {}, {}, []
        supplier_email, telephone, order_date = '', '', ''
        discrepency_rendered, data_dict_po = generate_discrepancy_data(
                        self.warehouse, po_new_data, print_des=False, **report_data_dict)
        if discrepency_rendered:
            send_email_discrepancy = self.misc_dict.get('grn_discrepancy' , 'false')
            if send_email_discrepancy == 'true':
                secondary_supplier_email = list(MasterEmailMapping.objects.filter(
                    master_id=data_dict[1][1], user=self.warehouse.id, master_type='supplier'
                    ).values_list('email_id', flat=True).distinct())
                
                supplier_email_id = []
                supplier_email_id.insert(0, supplier_email)
                supplier_email_id.extend(secondary_supplier_email)
                write_and_mail_pdf(
                    po_reference, discrepency_rendered, self.warehouse,
                    supplier_email_id, telephone, po_data, order_date, internal=True,
                    report_type="Discrepancy Note", data_dict_po=data_dict_po
                    )
    
    def generate_credit_note(self):
        '''
            Credit Note Generation
        '''
        credit_note_instance = CreditNoteGeneration(
            self.request_user, self.warehouse, credit_note_number=self.credit_note_number)
        credit_note_instance.credit_note_generation_process()

    def delete_grn_inspection_data(self):
        if self.asn_number and self.misc_dict.get('blind_grn', '') == 'true':
            asn_skus = list(ASNSummary.objects.filter(
                asn_user_id = self.warehouse.id, asn_number = self.asn_number, status__in=[1, 2]
            ).values_list('purchase_order__open_po__sku_id', flat=True))
            TempJson.objects.filter(
                warehouse=self.warehouse,
                model_name='grn_inspection',
                model_reference=self.asn_number
            ).exclude(model_id__in=asn_skus).delete()

    def grn_post_actions(self, sku_codes, request_details):
        try:
            #Deleting saved GRN Data
            self.delete_tempjson_data('PO', self.po_number)
            if self.credit_note_number:
                self.generate_credit_note()
            
            if self.grn_extra_dict.get('lpn_numbers', []):

                #To Change the ASN Transactional Packing Status "Open" to "Completed"
                update_packing_status_to_complete(
                    self.request_user, self.warehouse.id,
                    self.reference_number, "asn",
                    self.grn_extra_dict.get('lpn_numbers', []), self.request_header_details
                    )
                transact_type = 'grn_packing' if self.grn_type == 'PO' else 'sr_grn'
                update_packing_status_to_complete(
                    self.request_user, self.warehouse.id,
                    self.reference_number, transact_type,
                    self.grn_extra_dict.get('lpn_numbers', []), self.request_header_details
               )

            if (self.misc_dict.get('enable_inbound_staging_lanes') == 'true' and self.old_putaway_suggestions and not self.hold_grn and self.po_type.lower() != 'dropship') or (self.qc_check and self.misc_dict.get('enable_inbound_staging_lanes') == 'true'):

                create_grn_lpn_transactions(self.request_user.id,  self.warehouse.id, grn_number = self.grn_number, extra_params = self.request_header_details, grn_type=self.grn_type)

            #mapping master doc files to GRN number for PO - GRN
            self.master_doc_mapping(self.reference_number)

        except Exception as e:
            log.debug(traceback.format_exc())
            log.info(generate_log_message("GRNPostActionsFailure", warehouse_name = self.warehouse.username,
                        grn_number=self.grn_number, error=str(e)))

    def fetch_po_obj_details(self):
        if self.each_grn.get("open_po_dict", {}):
            #Creating PO objects for Free SKU
            po_obj = self.create_po_records_for_free_sku()
            purchase_order_id = self.po_obj.id
        elif self.grn_type != 'SR':
            purchase_order_id = self.each_grn.get("purchase_order_id", None)
            po_obj = PurchaseOrder.objects.select_for_update().get(id=purchase_order_id)
        else:
            po_obj = SalesReturnBatchLevel.objects.select_for_update().get(id=self.each_grn['batch_return_id'])
            self.sales_return_batch_ids.append(self.each_grn['batch_return_id'])
            purchase_order_id = None
        return po_obj, purchase_order_id
    
    def reduce_quantities(self, reduce_grn_quantity, received_free_short_qty):
        #Reducing Quantities
        if self.grn_type != 'SR':
            self.reduce_quantity_in_po_obj(reduce_grn_quantity, received_free_short_qty)
            asn_quantity =  float(self.po_obj.saved_quantity)
            if reduce_grn_quantity and self.each_grn.get("asn_id"):
                self.reduce_asn_quantity_in_po_obj(asn_quantity, reduce_grn_quantity, received_free_short_qty)
        else:
            self.reduce_quantity_in_sr_obj(reduce_grn_quantity)

    def fetch_quantities(self):
        #Calculating Quantities
        self.grn_quantity = self.each_grn.get("quantity", 0)
        self.accepted_quantity = self.each_grn.get("accepted_quantity", 0) 
        self.serial_numbers = self.each_grn.get("serial_numbers", '')
        self.accepted_serial_numbers = self.each_grn.get("accepted_serial_numbers", [])
        self.rejected_quantity = 0
        self.rejected_serial_numbers = []
        self.returned_quantity = self.each_grn.get("return_quantity", 0)  
        if not self.returned_quantity:                            
            self.rejected_quantity = self.each_grn.get("rejected_quantity", 0)
            self.rejected_serial_numbers = self.each_grn.get("rejected_serial_numbers", [])
        if not self.serial_numbers:
            self.serial_numbers = self.accepted_serial_numbers + self.rejected_serial_numbers
        
        self.putaway_quantity = self.accepted_quantity + self.rejected_quantity
        if self.misc_dict.get('return_po_qty')=="true":
            self.grn_quantity= self.grn_quantity - self.each_grn.get("return_quantity", 0)
    
    def grn_quantity_check(self, po_number):
        if  self.grn_type != 'SR':
            error_message = self.po_double_grn_quantity_check(po_number)
        else:
            error_message = self.sr_double_grn_quantity_check()
        return error_message
        
    def handle_damaged_wrong_and_short_skus(self, batch_dict, po_json_data, next_stage_lane):
        sku_short_key = (self.sku_code, self.unique_line_id)
        if self.misc_dict.get('blind_grn') == 'true' and self.grn_type =='PO':
            sku_short_key = (self.sku_code, self.unique_line_id, self.each_grn.get('asn_id'))
        elif self.each_grn.get('asn_id'):
            sku_short_key = self.each_grn['asn_id']
        if sku_short_key in self.sku_shortage_dict and sku_short_key not in self.sku_list:
            self.handle_short_skus()

        po_json_data.update({'quantity_type': 'rejected'})
        rejected_reason = self.each_grn.get('rejected_reason', '').lower()
        is_wrong_sku = rejected_reason == 'wrong_sku'
        random_gen_number = self.each_grn.get('json_data', {}).get('rejected_file_id')
        if random_gen_number:
            self.update_rejected_uploaded_files(random_gen_number)
        if (self.old_putaway_suggestions and not self.hold_grn and self.rejected_quantity) or self.po_type.lower() == 'dropship':
            if is_wrong_sku:
                self.handle_wrong_skus(po_json_data)
            elif not self.qc_check and not self.lpns:
                self.handle_damaged_skus(batch_dict, po_json_data, next_stage_lane)

    def update_credit_note_number_in_salereturn(self):
        '''
            CreditNoteNumber Updation in SalesReturnModels
        '''
        if self.grn_type == "SR" and self.credit_note_number and self.sales_return_batch_ids:
                SalesReturnLineLevel.objects.filter(
                        salesreturnbatchlevel__in=self.sales_return_batch_ids
                    ).update(credit_note_number=self.credit_note_number)
        
    def putaway_for_accepted_quantity(self, po_json_data, batch_dict, cartons_location_mapping_dict):
        #Putaway_data Saving
        put_zone = self.get_putzone()
        replenish_data = self.each_grn.get('replenish_data', [])
        next_stage_lane = 'non_qc'
        sku_type = self.each_grn.get('sku_type','')
        po_json_data.update({'quantity_type': 'accepted'})
        if (sku_type != 'Expense' or (sku_type == 'Expense' and self.misc_dict.get('expense_item_putaway') == 'true')):                                
            if self.lpns:
                for lpn_data in self.lpns:
                    lpn_number = lpn_data.get("lpn_number")
                    po_json_data.update({'lpn_number': lpn_number})
                    packed_quantity = lpn_data.get("packed_quantity")
                    replenished = lpn_data.get("replenished", True)
                    replen_data = replenish_data if replenished else []
                    if lpn_data.get("quantity_type", '') == 'accepted_quantity':
                        po_json_data.update({'quantity_type': 'accepted'})
                        self.create_putaway_records(put_zone, batch_dict, cartons_location_mapping_dict, po_json_data, packed_quantity, lpn_number, replenish_data= replen_data)
                    else:
                        po_json_data.update({'quantity_type': 'rejected'})
                        self.handle_damaged_skus(batch_dict, po_json_data, next_stage_lane, packed_quantity)
            else:
                self.create_putaway_records(put_zone, batch_dict, cartons_location_mapping_dict, po_json_data)
        return next_stage_lane
    
    def generating_inc_numbers(self, data, first_sku_code, dept_code):
        asn_number = data.get('asn_number','')
        grn_number = data.get('grn_number')
        grn_reference = data.get('grn_reference')
        
        reference_type = self.grn_extra_dict.get("reference_type", '')

        #Generating GRN Number
        self.receipt_type, self.receipt_number, self.grn_number = \
            self.generate_grn_number(grn_number, first_sku_code, dept_code)
        
        self.grn_reference = grn_reference or self.grn_number

        self.reference_number = self.po_number
        if self.asn_number:
            self.reference_number = self.asn_number

        self.credit_note_number = ''
        if self.grn_type == 'SR':
            self.reference_number = self.return_id
            #Checking CreditNote Configuration for Salesreturn GRN
            no_doc_credit_note = (reference_type == 'no_document' and self.misc_dict.get('nodocument_salesreturn'))
            create_credit_note = self.misc_dict.get('auto_credit_note') and (reference_type != 'no_document' or no_doc_credit_note)
            if create_credit_note:
                _, _, self.credit_note_number, \
                    _, _ = get_user_prefix_incremental(self.warehouse, 'credit_note_prefix', "", create_default="CR")
    
    def frame_json_data(self, common_json_data):
        #json_data -> GRN JSON Data
        self.json_data = common_json_data
        req_json_data = self.each_grn.get('json_data', {})
        self.json_data.update(req_json_data)
        lpn_number, lpn_data, lpn_number_list = '', [], []
        if self.each_grn.get('zone_restriction'):
            self.json_data["zone_restriction"] = self.each_grn['zone_restriction']

        self.json_data.update({
            "grn_remarks" : self.each_grn.get("po_remarks", ""),
            "sku_remarks" : self.each_grn.get("remarks", ""),
            "grn_invoice_qty" : self.each_grn.get("po_invoice_quantity", 0),
            "created_by" : self.request_username,
            "to_location" : self.grn_extra_dict.get("to_location"),
            "to_location_id" : self.grn_extra_dict.get("to_location_id"),
            'pcf' : self.each_grn.get("pcf", 1),
            })

        short_quantity = self.sku_shortage_dict.get((self.sku_code, self.unique_line_id))
        if self.misc_dict.get('blind_grn', '') == 'true':
            short_quantity = self.sku_shortage_dict.get((self.sku_code, self.unique_line_id, self.each_grn.get('asn_id')))
        elif self.each_grn.get('asn_id'):
            short_quantity = self.sku_shortage_dict.get(self.each_grn['asn_id'])

        if short_quantity:
            self.json_data.update({
                "Shortage": short_quantity
                })
        if 'lpn_number' in self.json_data:
            lpn_number = self.json_data.get('lpn_number')
        
        if 'lpns' in self.json_data:
            lpn_data = self.json_data.get('lpns')
        
        self.json_data.update(self.extra_json_data)

        if lpn_number and lpn_number not in lpn_number_list:
            lpn_number_list.append(lpn_number)

        for data in lpn_data:
            if data.get('lpn_number') not in lpn_number_list:
                lpn_number_list.append(data.get('lpn_number'))

        self.json_data.update({
            'lpn_number_list': lpn_number_list
            })
            
    def calculate_sku_landed_cost(self):
        if self.each_grn.get("additional cost", 0) or self.each_grn.get("discount", 0):
            total_grn_amount = self.grn_extra_dict.get('total_grn_amount', 1)
            additional_amount_with_discount = self.extra_json_data.get('additional_cost', 0) - self.extra_json_data.get('discount', 0)
            additional_cost = (self.grn_quantity * self.each_grn.get("price", 0)) / total_grn_amount * additional_amount_with_discount
            each_sku_additional_cost = additional_cost / self.grn_quantity
            landed_cost = self.each_grn.get("price", 0) + each_sku_additional_cost
            self.extra_json_data.update({'sku_additional_cost' : each_sku_additional_cost, 'sku_landed_cost' : landed_cost})
        else:
            landed_cost = self.each_grn.get('price', 0)
            self.extra_json_data = {'sku_landed_cost' : landed_cost}
    def get_strategy_mapping_data(self):
        sku_strategy_mapping = self.grn_extra_dict.get('sku_strategy_mapping', {})
        category_strategy_mapping = self.grn_extra_dict.get('category_strategy_mapping', {})
        putaway_strategies = sku_strategy_mapping.get(self.sku_code, set()) | category_strategy_mapping.get(self.each_grn.get('sku_category', ''), set())
        self.extra_dat['putaway_strategies'] = putaway_strategies
        self.extra_dat['mapped_locations'] = self.grn_extra_dict.get('mapped_locations',{}).get(self.sku_code,[])
        mapped_zones = self.grn_extra_dict.get('mapped_zones', {})
        self.extra_dat['mapped_zones'] = mapped_zones.get(self.sku_code, []) or mapped_zones.get(self.each_grn.get('sku_category', ''), [])
        self.extra_dat['location_mapping_capacity'] = self.grn_extra_dict.get('location_mapping_capacity', {})
    
    def prepare_serial_item_list(self, sps=None, stock=None, transact_type='accepted'):
        lpns = self.lpns
        transact_id = self.each_grn.get('asn_id') or self.each_grn.get('batch_return_id')
        if transact_type == 'accepted' and lpns:
            for lpn_data in lpns:
                transact_type_ = 'accepted' if lpn_data.get('quantity_type', '') == 'accepted_quantity' else 'rejected'
                serial_numbers = lpn_data.get('serial_numbers', [])
                lpn_number = lpn_data.get('lpn_number')
                if serial_numbers:
                    self.sn_item_list.append({
                        'transact_id': self.sps_id,
                        'transact_type': transact_type_,
                        'lpn_number': lpn_number,
                        'serial_numbers': serial_numbers,
                        'sku_code': self.each_grn['sku_code'],
                        'sku_id': self.sps.sku.id,
                        'batch_number': self.sps.batch_detail.batch_no if self.sps.batch_detail else '',
                        'batch_detail_id': self.sps.batch_detail.id if self.sps.batch_detail else None,
                        'status': 1,
                        'serial_status': 2,
                    })
        elif transact_type == 'short':
            short_serial_numbers = self.short_serial_dict.get(transact_id)
            self.sn_item_list.append({
                'transact_id': sps.id,
                'transact_type': 'short',
                'serial_numbers': short_serial_numbers,
                'sku_code': self.each_grn['sku_code'],
                'sku_id': sps.sku.id,
                'batch_number': sps.batch_detail.batch_no if sps.batch_detail else '',
                'batch_detail_id': sps.batch_detail.id if sps.batch_detail else None,
                'location': stock.location.location,
                'zone': stock.location.zone.zone,
                'location_id': stock.location.id,
                'stock_id': stock.id,
                'status': 0,
                'serial_status': 1,
            })
        else:
            transact_dict = {'accepted': self.accepted_serial_numbers, 'rejected': self.rejected_serial_numbers}
            for transact_type, serial_numbers in transact_dict.items():
                if serial_numbers:
                    self.sn_item_list.append({
                        'transact_id': self.sps_id,
                        'transact_type': transact_type,
                        'serial_numbers': serial_numbers,
                        'sku_code': self.each_grn['sku_code'],
                        'sku_id': self.sps.sku.id,
                        'batch_number': self.sps.batch_detail.batch_no if self.sps.batch_detail else '',
                        'batch_detail_id': self.sps.batch_detail.id if self.sps.batch_detail else None,
                        'status': 1,
                        'serial_status': 2,
                    })
        if transact_type != 'short' and transact_id:
            self.previous_snt_list.append({
                'transact_id': transact_id,
                'transact_type': 'accepted',
                'serial_numbers': self.existing_serials_dict.get(transact_id, []),
                'sku_code': self.each_grn['sku_code'],
                'sku_id': self.sps.sku.id,
                'batch_number': self.sps.batch_detail.batch_no if self.sps.batch_detail else '',
                'batch_detail_id': self.sps.batch_detail.id if self.sps.batch_detail else None,
                'status': 0
            })

    def create_serial_number_mapping(self):
        self.sn_transact_dict = {}
        if self.previous_snt_list:
            self.sn_transact_dict = {
                'reference_number': self.each_grn.get('asn_number') or self.return_id,
                'items': self.previous_snt_list
            }
            self.sn_transact_dict['reference_type'] = 'sales_return' if self.grn_type == 'SR' else 'asn'

            SerialNumberTransactionMixin(None, self.warehouse, self.sn_transact_dict).create_update_sn_transaction()
        if self.sn_item_list:
            self.sn_transact_dict = {
                'reference_number': self.grn_number,
                'reference_type': 'grn',
                'items': self.sn_item_list
            }
            if not self.existing_serials_dict:
                self.sn_transact_dict.update({'extra_params': {'serial_creation': True}})

            SerialNumberTransactionMixin(None, self.warehouse, self.sn_transact_dict).create_update_sn_transaction()    

    def process_each_grn_record(self, common_json_data, cartons_location_mapping_dict):
        self.carton, self.sku_id = None, self.each_grn.get('sku_id')
        self.unique_line_id = self.each_grn.get('unique_line_id')
        self.lpns = self.each_grn.get('json_data',{}).get('lpns', [])
        self.po_obj, self.purchase_order_id = self.fetch_po_obj_details()
        next_stage_lane = ''
        hold_grn_po_types = self.misc_dict.get('hold_grn_po_types', 'false').split(',')
        self.hold_grn = True if self.po_type in hold_grn_po_types else False
        self.old_putaway_suggestions = True if self.misc_dict.get('old_putaway_suggestions') == 'true' else False
        
        self.sku_code = self.each_grn['sku_code']
        self.uom_qty = 1
        if self.each_grn.get("pcf", 0) > 1:
            self.uom_qty = self.each_grn.get("pcf")

        if self.each_grn.get('asn_id'):
            self.asn_ids.append(self.each_grn.get('asn_id'))
        self.fetch_quantities()

        po_number = self.each_grn.get('po_number') if self.each_grn.get('po_number') else self.po_number
        #Checking Double GRN Quantity
        error_message = self.grn_quantity_check(po_number)
        if error_message:
            return False

        #po_json_data -> Putaway JSON Data
        po_json_data = {
            "source_warehouse_user": self.each_grn.get('warehouse_user',None),
            "user_id": self.each_grn.get('user',None),
            "grn_type": self.grn_type,
            "po_type": self.po_type,
            "request_user": self.request_username,
            "to_location" : self.grn_extra_dict.get("to_location"),
            "to_location_id" : self.grn_extra_dict.get("to_location_id")
            }
        self.calculate_sku_landed_cost()
        po_json_data.update(self.extra_json_data)
        #Creating or Updating Carton
        if self.each_grn.get("carton_number"):
            self.create_or_update_carton()
            po_json_data.update({
                "carton_number" : self.each_grn.get("carton_number")
                })
        
        self.frame_json_data(common_json_data)            
        if self.boe_grn_data:
            self.frame_import_grn_json_data()
            
        #Creating Batch Details
        batch_dict = self.each_grn.get("batch_detail", {})
        self.batch_obj= None
        if self.each_grn.get("batch_based"):
            #Adding SKU Attributes Data in Batch Detail Json Data, to Access Accross the Models
            self.batch_obj = self.create_batch_detail_record(
                batch_dict, self.each_grn.get("sku_attributes",{}))
            
        #Creating SellerPOSummary Record
        if self.grn_quantity:
            self.create_sps_record()

            #Reducing Qty in PO, ASN, SR Objects
            self.reduce_quantities(self.grn_quantity, received_free_short_qty=0)

            self.prepare_serial_item_list()

            if self.po_type.lower() != 'dropship' and self.each_grn.get('send_for_qc'):
                self.qc_check = True
                self.frame_qc_data(po_json_data)

            #Not Calling Putaway Suggestions in case of GRN Hold or To use New Putaway Suggestions
            if (self.old_putaway_suggestions and not self.hold_grn and not self.qc_check) or self.po_type.lower() == 'dropship':

                self.get_strategy_mapping_data()

                next_stage_lane = self.putaway_for_accepted_quantity(
                    po_json_data, batch_dict, cartons_location_mapping_dict)

        self.handle_damaged_wrong_and_short_skus(batch_dict, po_json_data, next_stage_lane)

        return True

    def get_putaway_type(self):
        """ Fetch Putaway type based on GRN type"""
        if self.grn_type == 'SR':
            self.putaway_type = 'sales_return'
        else:
            self.putaway_type = 'po_grn'

    def delete_temp_batch_details(self):
        if self.asn_ids and self.misc_dict.get('allow_asn_creation_without_batch', '') == 'true':
            TempJson.objects.filter(
                model_id__in=self.asn_ids, model_name='DraftASN', warehouse=self.warehouse, model_reference=self.asn_number
            ).delete()


    def create_grn(self, warehouse, data, grn_data_list, grn_extra_dict, request_details):
        status, error_message = True, ''
        self.qc_staging_list, self.nonqc_staging_list = [], []

        self.qc_data = []
        self.sku_list = []
        self.sales_return_batch_ids = []
        self.sn_item_list = []
        self.previous_snt_list = []
        
        self.warehouse = warehouse
        self.request_user = request_details.get('user')
        self.request_username = request_details.get('user').username
        
        self.grn_extra_dict = grn_extra_dict
        self.sku_shortage_dict = self.grn_extra_dict.get('sku_shortage_dict', {})
        self.sku_combo_dict = self.grn_extra_dict.get('sku_combo_dict', {})
        self.batch_combo_dict = self.grn_extra_dict.get('batch_combo_dict', {})
        self.child_sku_objs_dict = self.grn_extra_dict.get('child_sku_objs_dict', {})
        self.short_serial_dict = self.grn_extra_dict.get('short_serials_dict', {})
        self.existing_serials_dict = self.grn_extra_dict.get('existing_serials_dict', {})

        #PO Number is Not Mandatory in Payload, Can be Done against PO Reference
        self.po_number = self.grn_extra_dict.get("po_number")
        self.grn_type = self.grn_extra_dict.get("grn_type", "")
        self.po_reference = self.grn_extra_dict.get("po_reference", '')
        self.asn_number = data.get('asn_number', '')
        self.return_id = data.get("return_id", '')
        self.hold_grn = False
        self.qc_check = False

        self.po_type, dept_code = self.get_po_type_and_dept_code()
        first_sku_code= grn_data_list[0].get("sku_code", "")
        
        rendered, discrepency_rendered= "", ""        
        common_json_data = {
            "source": self.grn_extra_dict.get('source'),
            "request_username": self.request_username,
            "request_user_id": request_details.get('user').id 
            }
                
        self.extra_json_data = {
            "discount": float(data.get('discount_amount',0)),
            "additional_cost": float(data.get('additional_cost',0))
            }
        common_json_data.update(self.extra_json_data)
        
        self.aggregated_sku_count = grn_extra_dict.get('aggregated_sku_count', {})
        self.stage_loc_id = grn_extra_dict.get('to_location_id')
            
        self.get_configurations()

        #Get Request Header Details
        self.get_request_header_details(request_details)

        #Generating GRN Number, Credit Note Number, Serial Prefixes
        self.generating_inc_numbers(data, first_sku_code, dept_code)
                
        #Framing Import PO Data
        self.boe_grn_data, self.batch_json = {}, {}
        if self.po_type.lower() == 'import':
            self.boe_grn_data, self.batch_json = self.frame_import_po_data(grn_extra_dict.get('import_data'), common_json_data)
        #Fetching Putaway type
        self.get_putaway_type()

        sku_codes= []
        self.asn_ids = []
        try:
            self.extra_dat = prepare_grn_ext_data(self.warehouse)

            with transaction.atomic('default'):
                cartons_location_mapping_dict= {}
                iteration = 1
                for self.each_grn in grn_data_list:
                    with transaction.atomic('default'):
                        created = self.process_each_grn_record(common_json_data, cartons_location_mapping_dict)
                        if not created:
                            raise ValueError(GRN_ERROR_MESSAGE[101])
                        if self.misc_dict.get('return_po_qty')=="true":
                            self.frame_discrepancy_data(iteration)
                        
                        sku_codes.append(self.each_grn.get("sku_code", ""))
                        iteration +=1

                if self.sn_item_list:
                    self.create_serial_number_mapping()
                                   
                if self.qc_data:
                    self.quality_control_bulk_create(self.request_user)
     
                if self.grn_type == 'SR':
                    self.update_credit_note_number_in_salereturn() 

                save_grn_extra_fields(self.warehouse, self.grn_number, data.get('extra_fields', {}))

                log.info(generate_log_message("GRNSuccess", warehouse_name = self.warehouse.username,
                    grn_number = self.grn_number, grn_data_list=grn_data_list))

                #Calling New Putaway Suggestions based on Config
                if not self.old_putaway_suggestions and not self.hold_grn and not self.qc_check and self.po_type.lower() != 'dropship':
                    putaway_suggestions_for_grn(
                        self.warehouse, self.request_user, self.grn_type, self.po_type, self.grn_number,
                        grn_ids=[], extra_params=self.request_header_details
                    )

        except Exception as e:
            log.debug(traceback.format_exc())
            log.info(generate_log_message("GRNFailure", warehouse_name = self.warehouse.username,
                        grn_number= self.grn_number, grn_data_list=grn_data_list, error=str(e)))
            status= False

        self.grn_post_actions(sku_codes, request_details)
        if not self.hold_grn:
            integration_trigger = 'sales_return_grn' if self.grn_type == 'SR' else 'grn_creation'
            filters = {'sku_codes': sku_codes}
            webhook_integration_3p(self.warehouse.id, integration_trigger, filters)

        if self.po_type.lower() == 'dropship':
            try:
                drop_ship_auto_confirm({}, self.warehouse, self.po_number, self.po_reference)
            except Exception as e:
                log.debug(traceback.format_exc())
                log.info(generate_log_message("DropShipAutoConfirmFailure", warehouse_name = self.warehouse.username,
                        grn_number= self.grn_number, grn_data_list=grn_data_list, error=str(e)))

        # Delete tempjson data for approval GRN
        self.delete_grn_inspection_data()
        #Delete Temp Batch Details
        self.delete_temp_batch_details()
        return self.grn_number, status, error_message, rendered, discrepency_rendered


def drop_ship_auto_confirm(request, warehouse, po_number, po_reference):
    po_json_data = OpenPO.objects.filter(po_name=po_reference,sku__user=warehouse.id).values('json_data')
    if po_json_data :
        json_data = po_json_data[0].get('json_data','')
        if json_data and json_data.get('auto_inv'):
            purchase_obj = PurchaseOrder.objects.filter(
                open_po__sku__user = warehouse.id,po_number=po_number,open_po__po_name=po_reference)
            if purchase_obj.exists():
                purchase_obj = purchase_obj.aggregate(
                    ord_qty=Sum('open_po__order_quantity'),rec_qty=Sum('received_quantity'))
                if purchase_obj.get('ord_qty',0) - purchase_obj.get('rec_qty',0) == 0:
                    seller_po_sum_objs = SellerPOSummary.objects.filter(
                        purchase_order__po_number = po_number,purchase_order__open_po__sku__user = warehouse.id).distinct()
                    grn_numbers = list(seller_po_sum_objs.values_list('grn_number',flat=True))
                    order_detail_objs = OrderDetail.objects.filter(user=warehouse.id,order_reference=po_reference)
                    if grn_numbers and order_detail_objs.exists():
                        error_skus, picklist_number = drop_ship_generate_picklist(request, order_detail_objs,{},warehouse,grn_numbers=grn_numbers,auto_flag=True)
    return True
