''' Pending Putaway for all Putaway Types'''
import json
import pytz
import datetime
import traceback
import copy
import pandas as pd

from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Q, Count, F
from django.http import JsonResponse, HttpRequest
from django.test.client import RequestFactory


#Model Imports
from wms_base.models import User
from core.models import TempJson
from inbound.models import POLocation
from inventory.models import StockDetail
from lms.models import PutawayTask, EmployeeMaster
from wms.celery import app as celery_app


#Method Imports
from core_operations.views.common.main import (
    WMSListView, get_warehouse, get_user_ip,
    get_multiple_misc_values, get_user_time_zone,
    get_local_date_known_timezone, get_sku_pack_repr,
    get_pagination_info, get_paging_data, generate_log_message,
    get_misc_value
    )
from core_operations.serializers.main import get_serializer_error_message
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

from inbound.views.common.common import (
    get_serial_numbers_with_reference_number,
    get_sku_pack_data_with_sku_ids,
    get_sku_attributes_with_sku_ids, get_serial_numbers,
    frame_filters_with_request_dict
    )
from inbound.views.common.fetch_query import get_data_from_custom_params
from inventory.views.masters.sku_pack_master import get_sku_pack_uom_for_transactions
from .common import (
    fetch_putaway_stock_from_staging_lane
    )
from inbound.views.purchase_order.common import get_asn_grn_custom_attributes_value

from inbound.views.common.constants import (
    DEFAULT_DATETIME_FORMAT, INBOUND_STAGING_LANES_MAPPING, INVALID_PAYLOAD_CONST
)
from inbound.serializers.putaway import PutawaySerializer

from outbound.views.picklist.helpers import generate_picklist_with_stock
from .validation import validate_putaway, get_existing_serials, fetch_serial_numbers_for_sps_id
from .confirmation import ConfirmPutaway


from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/pending_putaway' + today + '.log')
log_err = init_logger('logs/pending_putaway.log')

from .constants import (
    PUTAWAY_API_KEYS, PUTAWAY_API_KEY_MAPPING, PUTAWAY_TYPES,
    PUTAWAY_FILTER_MAPPING, PUTAWAY_TYPES_REV_MAPPING,
    PUTAWAY_STATUS, PUTAWAY_STATUS_REV_MAPPING
    )
NON_SERIAL_STOCK = "Non Serial Stock"

class PutawaySet(WMSListView):
    '''
    Returns Pending Putaway Data for all Putaway Types.
    Using For SKU-Level Excel Download, Datatable Pop-UP and Print Putaway.
    '''
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None

    def fetch_configurations(self, warehouse: User):
        '''Fetching Configurations'''

        self.configurations = {
            'pack_repr' : False,
            'is_serial_based' : False,
        }
        misc_types = ['inbound_packing', 'inbound_staging_lanes', 'pallet_switch']
        #Pallet Switch Use Case
        misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
        self.configurations.update(misc_dict)

    def frame_get_values_list(self, values_list: list):    
        '''Framing Values List to Fetch from the Query'''
        get_values_list = values_list.copy()

        #These Values Handled from Framed Dicts, So Removing to Fetch from POLocation
        self.remove_values = ['serial_number', 'pack_repr', 'from_location', 'pending_stock_id']
        self.process_values = ['putaway_status', 'sku_weight', 'po_type', 'batch_based']
        self.date_keys = ['receipt_date', 'manufactured_date', 'expiry_date']
        self.datetime_keys = ['creation_date', 'updation_date']
        for each_key in self.remove_values:
            if each_key in values_list:
                get_values_list.remove(each_key)
        return get_values_list

    def fetch_pending_stock_for_putaway(self, filter_key: str, user_id: int, configurations: dict):
        '''Returns Staging Filter and Pending Stock Dict for StockDetails in Staging Lane '''

        #Fetching Configurations
        inbound_staging_lanes = configurations.get('inbound_staging_lanes', '')

        pnd_staging = INBOUND_STAGING_LANES_MAPPING.get('pnd', {}).get('name', '')
        has_pending_lane = bool('pnd' in inbound_staging_lanes.split(','))
        staging_filter, pending_stock_dict =  {}, {}
        if has_pending_lane:
            stock_filter = {'receipt_type':pnd_staging, 'quantity__gt':0, 'sku__user': user_id}
            stock_data = list(StockDetail.objects.filter(**stock_filter)\
                .values('id', 'receipt_number', 'quantity', 'location__location'))
            for each_row in stock_data:
                pending_stock_dict[each_row['receipt_number']] = each_row
            staging_filter[filter_key] = list(pending_stock_dict.keys())
        return has_pending_lane, staging_filter, pending_stock_dict

    def fetch_po_location_data(self, putaway_type: str, extra_params: dict):
        ''' Returns POLocation Data with the given Parameters'''
        reference_numbers, sku_ids, sku_codes, sps_ids, pol_ids = [], [], [], [], []
        po_location_data = \
            get_data_from_custom_params(POLocation, putaway_type, extra_params)

        if po_location_data:
            for each_row in po_location_data:
                reference_numbers.append(each_row.get('reference_number'))
                sku_ids.append(each_row.get('sku_id'))
                sku_codes.append(each_row.get('sku_code'))
                if each_row.get('serial_based'):
                    sps_ids.append(each_row.get('seller_po_summary_id'))
                    pol_ids.append(each_row.get('id'))

        return po_location_data, reference_numbers, sku_ids, sku_codes, sps_ids, pol_ids

    def get_existing_serials(self, warehouse, reference_type, sps_ids):
        values_list = ['sku_code', 'location', 'batch_number', 'lpn_number', 'serial_number', 'transact_id', 'transact_type']
        if not sps_ids:
            return pd.DataFrame(columns=values_list)
        serial_filter = {'filters':{'reference_type': reference_type, 'transact_id__in': sps_ids, 'status': 1}}
        serial_mixin = SerialNumberTransactionMixin(warehouse, warehouse, serial_filter)
        existing_serials = serial_mixin.get_sntd_details()
        serial_df = pd.DataFrame(existing_serials.get('data', []))
        if serial_df.empty:
            serial_df = pd.DataFrame(columns=values_list)
        return serial_df

    def fetch_serial_numbers_for_sps_id(self, sps_id, serial_df, lpn_number, transact_type):
        if not sps_id:
            return []
        sps_ids = str(sps_id).split(',')
        serial_numbers = []
        for sps_id in sps_ids:
            if sps_id.isdigit():
                filtered_serials = serial_df[(serial_df['transact_id'] == int(sps_id)) & (serial_df['lpn_number'] == lpn_number)]
                if transact_type:
                    filtered_serials = filtered_serials[filtered_serials['transact_type'] == transact_type]
                if filtered_serials.empty:
                    continue
                serial_numbers.extend(filtered_serials['serial_numbers'].tolist()[0])
        return serial_numbers
    
    def get_processed_value(self, get_key, value, line_wise_dict, sku_id, extra_data):
        #Fetching Receipt Types
        receipt_type_dicts = {'StockTransfer' : 'StockTransfer'}
 
        #Fetching Pack Repr from Framed Dict
        if get_key == 'pack_repr':
            pack_list = []
            sku_packs = self.sku_pack_dict.get(sku_id, {})
            if sku_packs:
                pack_list = [sku_packs]
                self.configurations['pack_repr'] = True
            pack_quantity = line_wise_dict.get('quantity', 0)
            value = get_sku_pack_repr(pack_list, pack_quantity)
        
        #Fetching PO Type
        elif get_key == 'po_type':
            value = receipt_type_dicts.get(value, 'po_receipt')
        
        #Fetching From Location From Pending Stock Dict
        elif get_key == 'from_location':
            po_loc_id = line_wise_dict['id']
            value = self.pending_stock_dict.get(po_loc_id,{}).get('location__location', '')
        
        #Fetching Pending Stock ID From Pending Stock Dict
        elif get_key == 'pending_stock_id':
            po_loc_id = line_wise_dict['id']
            value = self.pending_stock_dict.get(po_loc_id,{}).get('id', '')
        
        #Fetching PO Type
        elif get_key == 'putaway_status':
            value = PUTAWAY_STATUS.get(extra_data['putaway_status'])
        
        elif get_key == 'batch_based':
            value = True if value else False
                
        #Fetching SKU Weight
        elif get_key == 'sku_weight' and not value:
            value = self.sku_attr_dict.get(sku_id,{}).get('weight', '')
        
        return value
    
    def get_formatted_dates(self, get_key, value, timezone):
        #Date Conversions to Local Time Zone
        if value and get_key in self.date_keys:
            date_obj = get_local_date_known_timezone(timezone, value, send_date=True)
            if not isinstance(date_obj, str):
                value = date_obj.strftime('%m/%d/%Y')
        
        #Date Conversions to Local Time Zone
        if value and get_key in self.datetime_keys:
            date_obj = get_local_date_known_timezone(timezone, value, send_date=True)
            value = date_obj.strftime(DEFAULT_DATETIME_FORMAT)
        
        return value

    def fetch_pack_uom_for_each_sku_putaway(self, sku_code, line_wise_dict):
        if sku_code in self.pack_uom_data:
            line_wise_dict.update(**self.pack_uom_data[sku_code])
            if line_wise_dict.get('pack_uom_qty'):
                line_wise_dict['transacted_pending_qty'] = round(line_wise_dict.get('pending_quantity', 1)/line_wise_dict['pack_uom_qty'], 2)

    def frame_pending_quantity_from_staging_stock(self, line_wise_dict, putaway_type):
        '''Framing Pending Quantity from Staging Stock'''
        if self.inbound_staging and putaway_type in ['po_putaway', 'po_grn']:
            po_loc_id = line_wise_dict['id']
            quantity = self.stock_dict.get(po_loc_id, 0)
            line_wise_dict['pending_quantity'] = quantity

    def frame_line_level_putaway_data(self, po_loc_reults, values_list, pending_stock_dict, warehouse: User, putaway_type:str):
        ''' Framing Line Level Pending Putaway Data '''

        po_location_data, ref_numbers, sku_ids, sku_codes, sps_ids, pol_ids = po_loc_reults

        timezone = get_user_time_zone(warehouse)

        self.pending_stock_dict = pending_stock_dict

        #Fetching Serial Numbers for sps_ids
        if putaway_type == 'cp_putaway':
            serial_df = self.get_existing_serials(warehouse, 'cancel_picklist', pol_ids)
        else:
            serial_df = self.get_existing_serials(warehouse, 'grn', sps_ids)  

        #Fetching SKU Pack Data with SKU IDs
        self.sku_pack_dict = {}
        if 'pack_repr' in values_list:
            self.sku_pack_dict = get_sku_pack_data_with_sku_ids(sku_ids, warehouse)

        #Fetching SKU Attribute Data with SKU IDs
        self.sku_attr_dict = {}
        if 'sku_weight' in values_list:
            self.sku_attr_dict = get_sku_attributes_with_sku_ids(sku_ids, warehouse)
        
        #Fetching SKU UOM Data with SKU Codes

        self.pack_uom_data, _ = get_sku_pack_uom_for_transactions(warehouse.id, sku_codes, 'Putaway')

        integer_fields = [
            'buy_price', 'unit_price', 'mrp',
            'quantity', 'original_quantity', 'pending_quantity', 'putaway_quantity'
            ]
        pending_putaway_list = []
        for data in po_location_data:
            pol_id = data.get('id')
            sku_id = data.get('sku_id')
            sku_code = data.get('sku_code')
            json_data = data.get('json_data', {}) or {}
            line_wise_dict = {}
            
            extra_data = {
                'putaway_status' : data.get('putaway_status', '')
                }
            for get_key in values_list:
                value = data.get(get_key) or ''
                
                if get_key in integer_fields:
                    value = data.get(get_key) or 0
                
                elif get_key in self.remove_values + self.process_values:
                    value = self.get_processed_value(get_key, value, line_wise_dict, sku_id, extra_data)
                
                elif get_key in self.date_keys + self.datetime_keys:
                    value = self.get_formatted_dates(get_key, value, timezone)

                elif get_key == "is_barcode_required":    
                    value = self.format_data_for_barcoding(get_key, data)
                                
                #Framing Line Wise Dict
                line_wise_dict[get_key] = value
                line_wise_dict['lpn_number'] = json_data.get('lpn_number', '')
            #Frame pending quantity form staging stock
            self.frame_pending_quantity_from_staging_stock(line_wise_dict, putaway_type)

            serial_key = pol_id if putaway_type == 'cp_putaway' else data.get('seller_po_summary_id', '')
            line_wise_dict['serial_numbers'] = self.fetch_serial_numbers_for_sps_id(serial_key, serial_df, json_data.get('lpn_number', ''), json_data.get('quantity_type', ''))
            line_wise_dict['check_digit'] = data.get('location__check_digit')
            line_wise_dict['putaway_type'] = PUTAWAY_TYPES.get(line_wise_dict.get('putaway_type'), line_wise_dict.get('putaway_type'))
            if 'batch_reference' in values_list:
                line_wise_dict['batch_display_key'] = line_wise_dict['batch_reference'] or line_wise_dict.get('batch_no', '')
            line_wise_dict_cp_putaway = line_wise_dict.get('order_json', {})
            line_wise_dict_sr_putaway = line_wise_dict.get('sales_return_sku_json', {})
            line_wise_dict['uom'] = ''
            if putaway_type == "cp_putaway" and line_wise_dict_cp_putaway:
                line_wise_dict['uom'] = line_wise_dict_cp_putaway.get('pack_id','')
            elif putaway_type == "sr_putaway" and line_wise_dict_sr_putaway:
                line_wise_dict['uom'] = line_wise_dict_sr_putaway.get('pack_uom_id','')

            #fetch pack uom data for each sku
            self.fetch_pack_uom_for_each_sku_putaway(sku_code, line_wise_dict)
            pending_putaway_list.append(line_wise_dict)
        return pending_putaway_list
    
    def format_data_for_barcoding(self, get_key, data):
        value = "No"
        if data.get('is_barcode_required'):
            value = "Yes"

        return value

    def fetch_pending_putaway_data(self, warehouse: User, putaway_type, pending_extra_params, bypass_staging=False):
        '''Fetching Pending Putaway Data from POLocation with Custom Params'''

        putaway_status = pending_extra_params.get('status')
        values_list = pending_extra_params.get('values_list', [])
        order_by = pending_extra_params.get('order_by', [])
        temp_ids = pending_extra_params.get('temp_ids', [])
        aggregateds = pending_extra_params.get('aggregateds', {})

        user_id = warehouse.id        
        excludes = {}

        #Fetching Configurations to Fetch Data
        self.fetch_configurations(warehouse)

        #Fetching Values List to Fetch Data
        get_values_list = self.frame_get_values_list(values_list)

        #Default Filters for Pending Putaway
        filters = {
            'quantity__gt' : 0,
            'status__in' : [0,1], 'sku__user' : user_id
            }
        if putaway_status == 'pending':
            filters['status__in'] = [1]
        elif putaway_status == 'completed':
            filters['status__in'] = [0]

        extra_filters = pending_extra_params.get('filters', {})
        if extra_filters:
            filters.update(extra_filters)
        
        #Updating Q Filters with Params
        q_filters = pending_extra_params.get('qfilters', []) or []
        
        excludes, distinct_values = {}, []
        if not order_by:
            order_by =  ['purchase_order_id', 'seller_po_summary_id']
        
        #Staging Lanes Pending Stock Filters
        key = 'id__in'
        has_pending_lane, staging_filter, pending_stock_dict = \
            self.fetch_pending_stock_for_putaway(key, user_id, self.configurations)
        if has_pending_lane:
            if key in filters:
                filters[key] = set(filters[key]).intersection(set(staging_filter[key]))
            else:
                filters.update(staging_filter)
        self.inbound_staging = False
        self.stock_dict = {}
        if putaway_type in ('po_putaway', 'sr_putaway') and not bypass_staging:
            grn_number = pending_extra_params.get('grn_number')
            self.inbound_staging, self.stock_dict, _ = fetch_putaway_stock_from_staging_lane(warehouse.id, grn_number)
            if self.inbound_staging:
                filters['id__in'] = self.stock_dict.keys()
        #Excluding POLocIds which are Fetched from TempJson
        if temp_ids:
            if key in filters:
                filters[key] = list(set(filters[key])- set(temp_ids))
            else:
                excludes[key] = temp_ids
        get_values_list.append('location__check_digit')
        #Framming Filters to get Pending Stock
        extra_params = {
            'filters' : filters, 'q_filters' : q_filters,'distincts' : distinct_values,
            'aggregateds' : aggregateds, 'order_by' : order_by, 'value_keys' : get_values_list,
            'return_type' : 'values_list', 'excludes' : excludes
        }
        po_loc_reults = self.fetch_po_location_data(putaway_type, extra_params)

        #Framing Line Wise for the result Data
        pending_putaway_list = \
            self.frame_line_level_putaway_data(po_loc_reults, values_list,
                        pending_stock_dict, warehouse, putaway_type)
        return pending_putaway_list, self.configurations
        
    def frame_date_filters(self, request_data):
        date_filters = {}
        error_message = ''
        timezone = get_user_time_zone(self.warehouse)

        try:
            filter_date = {}
            filter_date = {}
            date_keys = ['from_date', 'to_date', 'updated_at_gte', 'updated_at_lte']
            for key in date_keys:
                if request_data.get(key):
                    parsed_date = parser.parse(request_data[key])
                    localized_date = pytz.timezone(timezone).localize(parsed_date)
                    filter_date[key] = localized_date.astimezone(pytz.UTC)

        except Exception as e:
            error_message = "Invalid Date Filter Format"
            log.debug(traceback.format_exc())
            log.info(generate_log_message("PendingPutawayDateFiltersFailure",
                    warehouse_name = self.warehouse, request_data=request_data, error=str(e)))

        if 'from_date' in filter_date:
            date_filters['creation_date__gt'] = filter_date['from_date']
            del request_data['from_date']

        if 'to_date' in filter_date:
            date_filters['creation_date__lt'] = filter_date['to_date']
            del request_data['to_date']
        
        if filter_date.get('updated_at_gte'):
            date_filters['updation_date__gte'] = filter_date['updated_at_gte']
            del request_data['updated_at_gte']

        if filter_date.get('updated_at_lte'):
            date_filters['updation_date__lte'] = filter_date['updated_at_lte']
            del request_data['updated_at_lte']

        return date_filters, error_message

    def frame_filters(self):
        date_filter_keys = ['from_date', 'to_date', 'updated_at_gte', 'updated_at_lte']
        filters, error_messages = {}, []
        request_data = self.request.GET.copy()

        #Fetching Values List to Fetch Data
        get_values_list = self.frame_get_values_list(PUTAWAY_API_KEYS)
        
        #Updating Filters Based on Putaway Status
        putaway_status = request_data.get('putaway_status', 'Completed')
        if putaway_status in PUTAWAY_STATUS_REV_MAPPING:
            if 'putaway_status' in request_data:
                del request_data['putaway_status']
            filters['status__in'] = [PUTAWAY_STATUS_REV_MAPPING.get(putaway_status)]
        else:
            error_messages.append("Invalid Putaway Status")
        
        batch_based = request_data.get('batch_based')
        if batch_based and batch_based not in [0, 1, '0', '1']:
            error_messages.append("Invalid Batch Filter")
    
        putaway_type = 'putaway'
        putaway_type_value = request_data.get('putaway_type', '')
        if putaway_type_value:
            del request_data['putaway_type']
            putaway_type = PUTAWAY_FILTER_MAPPING.get(putaway_type_value)
            if putaway_type and putaway_type in PUTAWAY_API_KEY_MAPPING:
                filters['putaway_type'] = PUTAWAY_TYPES_REV_MAPPING[putaway_type_value]
                get_values_list = PUTAWAY_API_KEY_MAPPING[putaway_type] + get_values_list
            else:
                error_messages.append("Invalid Putaway Type")
                
        message, framed_filters, [] = frame_filters_with_request_dict(request_data, get_values_list+date_filter_keys)
        if message:
            error_messages.append("Invalid Filters")
        date_filters, error_message = self.frame_date_filters(request_data)
        if error_message:
            error_messages.append(error_message)
        elif date_filters:
            filters.update(date_filters)
        if framed_filters:
            filters.update(framed_filters)

        return putaway_type, filters, get_values_list, error_messages

    def get(self, *args, **kwargs):
        self.set_user_credientials()

        limit = int(self.request.GET.get('limit', 10))
        offset = int(self.request.GET.get('offset', 0))

        
        #Fetching Configurations to Fetch Data
        self.fetch_configurations(self.warehouse)

        #Default Filters for Putaway
        filters = {
            'original_quantity__gt' : 0,
            'status__in' : [0,1], 'sku__user' : self.warehouse.id
            }
        
        putaway_type, framed_filters, get_values_list, error_messages = self.frame_filters()
        if error_messages:
            return JsonResponse({
                    'data' : [],
                    'paging' : {},
                    'errors' : error_messages
                }, status=400)

        if framed_filters:
            filters.update(framed_filters)
        
        #Staging Lanes Pending Stock Filters
        key = 'id__in'
        has_pending_lane, staging_filter, pending_stock_dict = \
            self.fetch_pending_stock_for_putaway(key, self.warehouse.id, self.configurations)
        if has_pending_lane:
            if key in filters:
                filters[key] = set(filters[key]).intersection(set(staging_filter[key]))
            else:
                filters.update(staging_filter)

        self.inbound_staging, self.stock_dict, _ = fetch_putaway_stock_from_staging_lane(self.warehouse.id)

        #Framming Filters to get Pending Stock
        extra_params = {
            'filters' : filters, 'order_by' : ['-creation_date'],
            'value_keys' : get_values_list,
            'return_type' : 'values_list',
            'slicing_dict' : {'start_index' : offset, 'stop_index' : offset + limit}
        }
        po_loc_reults = self.fetch_po_location_data(putaway_type, extra_params)
        
        #Framing Line Wise for the result Data
        putaway_list = \
            self.frame_line_level_putaway_data(po_loc_reults, get_values_list,
                        pending_stock_dict, self.warehouse, putaway_type)

        page_info = get_pagination_info({'limit' : limit, 'offset' : offset})
        paging_details = get_paging_data(self.request, page_info, len(putaway_list))

        return JsonResponse({
            'data' : putaway_list,
            'paging' : paging_details,
            'errors' : []
        })
    
    def unmerge_batch_consolidated_data(self, request_data):
        """
        Unmerges the batch consolidated data by splitting each item into multiple items based on the 'ids_dict' values.

        Args:
            request_data (dict): The request data containing the batch consolidated items.

        Returns:
            dict: The updated request data with the unmerged items.

        """
        unmapped_items = []
        for item in request_data['items']:
            total_putaway_quantity = item['putaway_quantity']
            total_serial_numbers = item.get('serial_numbers', [])
            if not item.get('ids_dict'):
                return request_data
            for key, value in item['ids_dict'].items():
                new_item = copy.deepcopy(item)
                del new_item['ids_dict']
                new_item.update(value)
                new_item['id'] = key
                serial_numbers = value.get('serial_numbers', [])
                if serial_numbers:
                    #Fetch Common serial Numbers for the Given Quantity and Serial Numbers
                    new_item['serial_numbers'] = list(set(total_serial_numbers) & set(serial_numbers))
                    new_item['putaway_quantity'] = min(total_putaway_quantity, len(new_item['serial_numbers']))
                else:
                    new_item['serial_numbers'] = []
                    new_item['putaway_quantity'] = min(total_putaway_quantity, value['quantity'])
                
                new_item['original_quantity'] = value['quantity']
                total_putaway_quantity -= new_item['putaway_quantity']
                unmapped_items.append(new_item)
                if not total_putaway_quantity:
                    break

        request_data['items'] = unmapped_items
        return request_data

    def post(self, *args, **kwargs):
        self.set_user_credientials()
        try:
            request = self.request
            request_data = json.loads(request.body)

        except Exception:
            try:
                request_data = json.loads(request.POST.get("data"))
            except Exception:
                request_data = ""
            if not request_data:
                return JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)
            
        log.info('Request params for Putaway Confirmation ' + request.user.username + ' is ' + str(request_data) + 'ip_address:' + str(get_user_ip(request)))

        if not request_data.get("items"):
            return JsonResponse({'error': [{'message': 'Items are mandatory'}]}, status=400)
        
        request_data = self.unmerge_batch_consolidated_data(request_data)

        valid_ser = PutawaySerializer(data=request_data)
        putaway_data_list = []
        if valid_ser.is_valid():
            data = valid_ser.validated_data
            validated_error_dict, po_loc_dict_data, putaway_data_list = validate_putaway(data, warehouse=self.warehouse)
            if validated_error_dict:
                status= False
                error_message_dict = validated_error_dict
            elif po_loc_dict_data:
                status = True
                putaway_extra_dict = {
                    'request_user' : request.user.username,
                     "headers": {
                        "Warehouse": self.request.headers.get("Warehouse"),
                        "Authorization": self.request.headers.get('Authorization', '')
                        },
                    "request_meta": self.request.META,
                    "request_scheme": self.request.scheme,
                }
                putaway_status = ConfirmPutaway().confirm(putaway_data_list, self.warehouse, putaway_extra_dict)



        else:
            error_dict = valid_ser.errors
            error_message_dict = get_serializer_error_message(request_data, error_dict)
            status= False
        if status:
            return JsonResponse({'message' : putaway_status }, status=200)
        else:
            return JsonResponse({'error': [{'message': error_message_dict }]}, status=400)

def get_excluded_serial_numbers(temp_json_data, location, model_id):
    exclude_serial_numbers = []
    check_serials = []
    location_serials = []
    for each_model_id, model_data in temp_json_data.items():
        temp_data = json.loads(model_data)

        #Excluding Other Model ID Saved Serial Numbers
        if str(each_model_id) != str(model_id):
            for location_key in temp_data:
                exclude_serial_numbers.extend([serial_number.get('serial') for serial_number in temp_data[location_key]])
        else:
            #Fetching Saved Serial Numbers for the Given Location and Model ID
            location_serials = temp_data.get(location)
            if location_serials:
                check_serials = [serial_number.get('serial') for serial_number in location_serials]
            for data in temp_data:
                #Excluding Other Location Saved Serial Numbers for the Model ID
                if location != data:
                    exclude_serial_numbers.extend([serial_number.get('serial') for serial_number in temp_data[data]])
    return location_serials, check_serials, exclude_serial_numbers

@get_warehouse
def get_putaway_sku_serial_numbers(request, warehouse: User):
    ''' Get Serial Numbers with the Requested Filters on View Click of Putaway PopUP'''

    reference_number = request.GET.get('reference_number', '')
    reference_type = request.GET.get('reference_type', '')
    model_id = request.GET.get('model_id')
    location = request.GET.get('location')
    sku_code = request.GET.get('sku_code')
    batch_number = request.GET.get('batch_number', '')
    model_name_dict = {
        'picklist' : 'cancelled_location',
        'pending_putaway': 'po_location',
        'jo_pending_putaway': 'po_location',
    }        
    sn_request = RequestFactory
    sn_request.user = request.user
    sn_request.warehouse = warehouse
    sn_request.method= 'GET'
    sn_request.GET = {
        'reference_number': reference_number,
        'sku_code' : sku_code,
        'reference_type' : reference_type
        }
    filters = {}
    if location and reference_type == 'picklist':
        sn_request.GET.update({'reference_type' : 'cancelled_location'})
        filters.update({'transaction_id': model_id})

    if batch_number:
        filters.update({'serial__batch_detail__batch_no': batch_number})
    serial_num_dict = get_serial_numbers(sn_request, filters)
    all_serial_numbers = {}
    for sku_data in serial_num_dict['items']:
        serial_num_list = []
        for serial_num in sku_data.get('serial_numbers'):
            serial_dict = {"serial" :serial_num['suggested'] , "check": False}
            all_serial_numbers[serial_num['suggested']] = serial_dict
            serial_num_list.append(serial_dict)

    model_name = model_name_dict.get(reference_type)
    
    #excluding serial numbers from other locations
    poloc_filters = {}
    if reference_type == "pending_putaway":
        poloc_filters = {
            'location__zone__user': warehouse.id, 'seller_po_summary__grn_number': reference_number,
            'purchase_order__open_po__sku__sku_code': sku_code,
            'seller_po_summary__batch_detail__batch_no': batch_number,
            }
    elif reference_type == "jo_pending_putaway":
        poloc_filters = {
            'location__zone__user': warehouse.id, 'jo_grn__grn_number': reference_number,
            'jo_grn__job_order__product_code__sku_code': sku_code,
            'jo_grn__batch_detail__batch_no': batch_number}
    elif reference_type == "sales_return":
        poloc_filters = {
            'location__zone__user': warehouse.id,
            'seller_po_summary__sales_return__return_id': reference_number,
            'seller_po_summary__sales_return_batch__sales_return_sku__sku__sku_code': sku_code,
            'seller_po_summary__sales_return_batch__batch_detail__batch_no': batch_number,
            }
        
    poloc_ids_dict = dict(POLocation.objects.filter(**poloc_filters, status=1).values_list('id', 'json_data'))
    if poloc_ids_dict:
        model_ids = list(poloc_ids_dict.keys())
    else:
        model_ids = [model_id]
    #Fetching temp_json objects for all poloc ids

    #Fetching temp_json objects for all Model IDs
    #This Block Executes After saving the SerialNumbers in TempJson
    temp_json_data = dict(TempJson.objects.filter(
        model_id__in=model_ids, model_name=model_name).values_list('model_id', 'model_json'))

    location_serials, check_serials, exclude_serial_numbers = get_excluded_serial_numbers(temp_json_data, location, model_id)

    for po_loc_id, json_data in poloc_ids_dict.items():
        if str(po_loc_id) != model_id and 'rejected_serial_numbers' in json_data:
            exclude_serial_numbers.extend(json_data['rejected_serial_numbers'])

    new_location_serials = []
    if location_serials:
        new_location_serials.extend(location_serials)
    
    rejected_serials = poloc_ids_dict.get(int(model_id),{}).get('rejected_serial_numbers', [])
    if rejected_serials:
        for each_serial in rejected_serials:
            if each_serial not in check_serials:
                new_location_serials.append(all_serial_numbers.get(each_serial))
    else:
        #Extra Serial Numbers which can be Selected to do Putaway(with check:False)
        for each_serial_number in all_serial_numbers:
            if each_serial_number not in exclude_serial_numbers and each_serial_number not in check_serials:
                new_location_serials.append(all_serial_numbers.get(each_serial_number))

    return JsonResponse({"serial_numbers" :new_location_serials})

def get_sku_pack_ref(sku_id, sku_pack_dict, configurations, line_wise_dict):
    sku_packs = sku_pack_dict.get(sku_id, {})
    if sku_packs:
        configurations['pack_repr'] = True
        value = get_sku_pack_repr([sku_packs], line_wise_dict.get('quantity', 0))
    else:
        value = ''
    return value

def get_po_pending_putaway_data(warehouse, search_filters, search_qfilters, values_list):
    '''Fetching Pending Putaway Data from POLocation with Custom Params'''
    configurations = {
        'pack_repr' : False,
        'is_serial_based' : False,
    }
    #Fetching Configurations
    misc_types = ['inbound_packing']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    configurations['inbound_packing'] = misc_dict.get('inbound_packing')

    #Updating Filters with Params
    filters = {'status' : 1}
    if search_filters:
        filters.update(search_filters)
    
    #Updating Q Filters with Params
    q_filters = [{
        'purchase_order__open_po__sku__user' : warehouse.id,
        }]
    if search_qfilters:
        q_filters.extend(search_qfilters)

    distinct_values, aggregateds = [], {}
    order_by =  ['purchase_order_id', 'seller_po_summary_id']
    
    #Serial Number handled from Another Dict
    get_values_list = values_list.copy()
    if 'serial_number' in values_list:
        get_values_list.remove('serial_number')

    if 'pack_repr' in values_list:
        get_values_list.remove('pack_repr')

    extra_params = {
            'filters' : filters, 'q_filters' : q_filters,'distincts' : distinct_values,
            'aggregateds' : aggregateds, 'order_by' : order_by, 'value_keys' : get_values_list,
            'return_type' : 'values_list'
        }
    po_location_data = \
        get_data_from_custom_params(POLocation,'po_putaway', extra_params)

    #Fetching GRN Numbers from Filtered Data
    grn_numbers = []
    sku_ids = []
    for each_row in po_location_data:
        grn_numbers.append(each_row.get('grn_number'))
        sku_ids.append(each_row.get('sku_id'))
    
    #Fetching SerialNumbers on GRN Number, SKU Based 
    sn_filters = {
        'reference_type': 'pending_putaway',
        'reference_number__in': set(grn_numbers),
        'status' : 0,
        }
    sn_return_type = 'value_dict'
    value_keys = ['reference_number', 'serial_number', 'sku_code']
    serial_number_dict = get_serial_numbers_with_reference_number(sn_filters, warehouse.id, sn_return_type, value_keys)
    sku_pack_dict = get_sku_pack_data_with_sku_ids(sku_ids, warehouse)
    sku_attr_dict = get_sku_attributes_with_sku_ids(sku_ids, warehouse)
    
    receipt_type_dicts = {'StockTransfer' : 'StockTransfer'}
    
    timezone = get_user_time_zone(warehouse)    
    pending_putaway_list = []
    for data in po_location_data:
        serial_number_get_key = (data.get('grn_number'), data.get('sku_code'))
        serial_numbers = serial_number_dict.get(serial_number_get_key, [NON_SERIAL_STOCK])
        sku_id = data.get('sku_id')
        for serial_number in serial_numbers:
            line_wise_dict = {}
            if serial_number == NON_SERIAL_STOCK:
                serial_number = ''
            else:
                configurations['is_serial_based'] = True
                data['quantity'] = 1
            for get_key in values_list:
                value = data.get(get_key) or ''
                if value and get_key in ['receipt_date', 'manufactured_date', 'expiry_date']:
                    value= get_local_date_known_timezone(timezone, value, send_date=True).strftime('%m/%d/%Y')
                elif get_key == 'po_type':
                    value = receipt_type_dicts.get(value, 'po_receipt')
                elif get_key == 'serial_number':
                    value = serial_number
                elif get_key == 'pack_repr':
                    value = get_sku_pack_ref(sku_id, sku_pack_dict, configurations, line_wise_dict)
                elif get_key == 'sku_weight' and not value:
                    value = sku_attr_dict.get(sku_id,{}).get('weight', '')
                #Framing Line Wise Dict
                line_wise_dict[get_key] = value
            pending_putaway_list.append(line_wise_dict)
    return pending_putaway_list, configurations


@get_warehouse
def aggregated_putaway_count(request, warehouse: User):
    """
    Aggregates and returns the count of various putaway tasks for a given warehouse.

    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse user object.

    Returns:
        JsonResponse: A JSON response containing the aggregated putaway counts.
    """

    putaway_data = {}
    enable_inbound_staging_lanes = get_misc_value('enable_inbound_staging_lanes', warehouse.id)

    filters = {
        'warehouse_id': warehouse.id,
        'status' : False,
        'employee': None,
    }
    if enable_inbound_staging_lanes == 'true':
        stock_data = list(StockDetail.objects
            .filter(receipt_type='grn_packing', quantity__gt=0, location__zone__storage_type='PUT',
                    sku__user=warehouse.id)
            .values_list('receipt_number', flat=True)
        )
        task_ref_ids = stock_data
        cp_po_loc_ids = list(PutawayTask.objects.filter(**filters, task_ref_type='CancelledPutaway').values_list('task_ref_id', flat=True))
        task_ref_ids.extend(cp_po_loc_ids)
        filters['task_ref_id__in'] = task_ref_ids
    # Fetching pending POLocation IDs
    po_loc_ids = PutawayTask.objects.filter(**filters).exclude(task_ref_type__in=['BATOSAPutaway', 'NTEPutaway']).aggregate(ids=ArrayAgg('task_ref_id'))['ids'] or []
    if po_loc_ids:
        # Fetching Putaway aggregated data
        putaway_data = dict(POLocation.objects
            .filter(id__in=po_loc_ids, status=1)
            .aggregate(
                po_grn_items=Count('purchase_order__open_po__sku'),
                po_grns=Count('seller_po_summary__grn_number', filter=Q(purchase_order__isnull=False), distinct=True),
                sr_items=Count('seller_po_summary__sales_return_batch__sales_return_sku__sku'),
                sr_grns=Count('seller_po_summary__grn_number', filter=Q(seller_po_summary__sales_return__isnull=False), distinct=True),
                jo_items=Count('job_order__product_code'),
                jo_grns=Count('seller_po_summary__grn_number', filter=Q(job_order__isnull=False), distinct=True),
                cp_items=Count('picklist__sku'),
                cp_numbers=Count('picklist__picklist_number', distinct=True),
            )
        )

    putaway_dict = {
        'open': {
            'GRN': putaway_data.get('po_grns', 0),
            'Sales Return': putaway_data.get('sr_grns', 0),
            'Job Order': putaway_data.get('jo_grns', 0),
            'Cancelled': putaway_data.get('cp_numbers', 0),
        },
        'grn': {
            'open': {
                'items': putaway_data.get('po_grn_items', 0),
                'orders': putaway_data.get('po_grns', 0),
            }
        },
        'sales_return': {
            'open': {
                'items': putaway_data.get('sr_items', 0),
                'orders': putaway_data.get('sr_grns', 0),
            }
        },
        'jo': {
            'open': {
                'items': putaway_data.get('jo_items', 0),
                'orders': putaway_data.get('jo_grns', 0),
            }
        },
        'cancelled_picklist': {
            'open': {
                'items': putaway_data.get('cp_items', 0),
                'orders': putaway_data.get('cp_numbers', 0),
            }
        },
    }

    return JsonResponse({'putaway': putaway_dict})

def validate_auto_putaway_data(request_data):
    """
    Validates the request data for the auto putaway API.

    Args:
        request_data (dict): The request data to be validated.

    Returns:
        dict: The error messages, if any.
    """
    errors = []

    if not request_data.get('grn_number'):
        errors.append({'message': 'GRN number is required.'})
    if not request_data.get('putaway_type'):
        errors.append({'message': 'Putaway type is required.'})

    return errors

def prepare_putaway_data(request, warehouse, grn_number, putaway_type):
    employee_id = None
    employee_obj = EmployeeMaster.objects.filter(user_id=request.user.id).first()
    if employee_obj:
        employee_id = employee_obj.id
    putaway_data = {
        'warehouse': warehouse.username,
        'source': 'auto_putaway',
        'putaway_type': putaway_type,
        'employee_id': employee_id,
        'items': []
    }
    values_list = ['id', 'seller_po_summary_id', 'original_quantity']
    values_dict = {
        "sku_code": F("sku__sku_code"),
        "sku_desc": F("sku__sku_desc"),
        "zone": F("location__zone__zone"),
        "location_": F("location__location"),
        "putaway_quantity": F("quantity"),
        "lpn_number": F("json_data__lpn_number"),
        "enable_serial_based": F("sku__enable_serial_based"),
        "quantity_type": F("json_data__quantity_type")
    }
    pol_data = list(POLocation.objects.filter(seller_po_summary__grn_number=grn_number, status=1).values(*values_list, **values_dict))
    if not pol_data:
        return putaway_data, [{'message': 'No data found for the given reference number.'}]
    sps_ids = []
    for data in pol_data:
        if data.get('enable_serial_based'):
            sps_ids.append(data['seller_po_summary_id'])
    
    serial_df = get_existing_serials(warehouse, 'grn', sps_ids)

    items_data = []
    for data in pol_data:
        item_data = {
            'sku_code': data['sku_code'],
            'sku_desc': data['sku_desc'],
            'zone': data['zone'],
            'location': data['location_'],
            'original_quantity': data['original_quantity'],
            'putaway_quantity': data['putaway_quantity'],
            'id': data['id'],
            'lpn_number': data['lpn_number'],
            'serial_numbers': fetch_serial_numbers_for_sps_id(data['seller_po_summary_id'], serial_df, data['lpn_number'], data['quantity_type'])
        }
        items_data.append(item_data)
    
    putaway_data['items'] = items_data

    return putaway_data, []

@celery_app.task
def async_auto_putaway_for_grn(grn_number, putaway_type, request_META, request_user_id, warehouse_id, validation_type, grn_type, asn_number):
    new_request = HttpRequest()
    request_user = User.objects.get(id=request_user_id)
    warehouse = User.objects.get(id=warehouse_id)
    new_request.warehouse = warehouse
    new_request.user = request_user
    new_request.META = request_META
    new_request.method = 'POST'
    new_request._body = json.dumps({'grn_number': grn_number, 'putaway_type': putaway_type}).encode('utf-8')
    auto_putaway_for_grn(new_request)
    if validation_type == 'asn_grn_creation' and grn_type == 'PO':
        async_order_fulfil.apply_async(args=[request_user_id, warehouse_id, grn_number, asn_number])

@get_warehouse
def auto_putaway_for_grn(request, warehouse:User):
    from .auto_putaway import confirm_putaway
    request_data = request.body

    if request_data:
        request_data = json.loads(request_data)
    else:
        return JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)
    
    errors = validate_auto_putaway_data(request_data)
    if errors:
        return JsonResponse({'error': errors}, status=400)
    
    grn_number = request_data.get('grn_number')
    putaway_type = request_data.get('putaway_type')

    putaway_data, errors = prepare_putaway_data(request, warehouse, grn_number, putaway_type)

    if errors:
        return JsonResponse({'error': errors}, status=400)
    
    response = confirm_putaway(request, warehouse, putaway_data)

    return response

@celery_app.task
def async_order_fulfil(request_user_id: int, warehouse_id: int, grn_number: str, asn_number: str) -> None:
    """Asynchronously process order fulfillment based on ASN and GRN details."""
    try:
        # Get order reference from ASN
        custom_attributes = get_asn_grn_custom_attributes_value({'user': warehouse_id, 'receipt_no': asn_number})
        if not (order_reference := custom_attributes.get(asn_number, {}).get('order_reference')):
            log.info(f"No order details found for warehouse {warehouse_id} and ASN {asn_number}")
            return

        # Get Putaway details from POLocation
        stock_details = list(POLocation.objects.filter(
            sku__user=warehouse_id, quantity=0,
            seller_po_summary__grn_number=grn_number, status=0
        ).values(
            'sku__sku_code', 'seller_po_summary__purchase_order__line_reference',
            'batch_detail__batch_no', 'location__location',
            'original_quantity', 'json_data__lpn_number'
        ))
        if not stock_details:
            log.info(f"No stock details found for warehouse {warehouse_id} and GRN {grn_number}")
            return

        # Build SKU-line reference mapping
        sku_line_ref_map = {}
        for stock in stock_details:
            if not (line_ref := stock.get('seller_po_summary__purchase_order__line_reference')):
                continue
            key = (stock.get('sku__sku_code'), line_ref)
            batch_detail = {
                'batch_number': stock.get('batch_detail__batch_no'),
                'location': stock.get('location__location'),
                'quantity': stock.get('original_quantity'),
                'lpn_number': stock.get('json_data__lpn_number', '')
            }
            sku_line_ref_map.setdefault(key, []).append(batch_detail)

        if sku_line_ref_map:
            log.info(f"Processing order warehouse {warehouse_id} and order reference {order_reference} with {len(stock_details)} stock items")
            generate_picklist_with_stock(request_user_id, warehouse_id, order_reference, sku_line_ref_map)
        else:
            log.info(f"No valid SKU-line reference mapping found for warehouse {warehouse_id} and GRN {grn_number}")
    except Exception as e:
        log.error(f"Error in async_order_fulfil for warehouse {warehouse_id} and GRN {grn_number}: {str(e)}", exc_info=True)