from datetime import datetime
from django.db.models import Q
from functools import reduce
from itertools import chain
import pandas as pd

from wms_base.models import User
from inventory.models import LocationMaster, BatchDetail, StockDetail

from wms_base.wms_utils import MILKBASKET_BULK_ZONE
from core_operations.views.common.main import (
    get_multiple_misc_values, get_all_zones,
    get_company_id, get_decimal_value, get_uom_decimals,
    get_user_time_zone, get_local_date_known_timezone
    )
from inbound.views.common.fetch_query import get_data_from_custom_params
from inbound.views.purchase_order.constants import PO_ORDER_TYPES
from inbound.views.common.constants import DEFAULT_DATE_FORMAT
from .constants import PUTAWAY_TYPE_INFO
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
from inbound.views.common.common import (
    sku_batch_mixing_validation, preload_stock_details_for_locations
)


def validate_mrp_weight(data_dict, warehouse):
    collect_dict_form = {}
    status = ''
    collect_all_sellable_location = list(LocationMaster.objects.filter(
        zone__segregation='sellable',  zone__user=warehouse.id, status=1).values_list('location', flat=True))
    bulk_zones= get_all_zones(warehouse ,zones=[MILKBASKET_BULK_ZONE])
    bulk_locations=list(LocationMaster.objects.filter(
        zone__zone__in=bulk_zones, zone__user=warehouse.id, status=1).values_list('location', flat=True))
    sellable_bulk_locations=list(chain(collect_all_sellable_location ,bulk_locations))
    if data_dict['location'] in sellable_bulk_locations:
        sku_mrp_weight_map = StockDetail.objects.filter(
            sku__user=warehouse.id, quantity__gt=0, sku__wms_code=data_dict['sku_code'],
            location__location__in=sellable_bulk_locations
                ).exclude(batch_detail__mrp=data_dict.get('mrp',0), batch_detail__weight=data_dict.get('weight','')
                ).exclude(batch_detail__mrp=None, batch_detail__weight=None
                ).values_list('sku__wms_code', 'batch_detail__mrp', 'batch_detail__weight').distinct()
        
        if sku_mrp_weight_map:
            for sku_code, mrp, weight_dict in sku_mrp_weight_map:
                mrp_weight_dict = {'mrp':[str(mrp)], 'weight':[weight_dict]}
                if sku_code in collect_dict_form.keys():
                    collect_dict_form[sku_code]['mrp'].append(mrp_weight_dict['mrp'][0])
                    collect_dict_form[sku_code]['weight'].append(mrp_weight_dict['weight'][0])
                else:
                    collect_dict_form[sku_code] = mrp_weight_dict
            
            if data_dict['sku_code'] in collect_dict_form.keys():
                stock_mrp_list =  collect_dict_form[data_dict['sku_code']]["mrp"]
                stock_weight_list =  collect_dict_form[data_dict['sku_code']]["weight"]
                mrp_list, weight_list = [], []
                for index in range(len(stock_mrp_list)):
                    if data_dict['mrp'] != stock_mrp_list[index] or str(data_dict['weight']) != stock_weight_list[index]:
                        mrp_list.append(stock_mrp_list[index])
                        weight_list.append(stock_weight_list[index])
                if mrp_list or weight_list:
                    status = 'For SKU "'+str(data_dict['sku_code'])+'", MRP "'+ str(",".join(mrp_list)) \
                            +'" and WEIGHT "'+str(",".join(weight_list))+'" are only accepted.'
    return status

def get_extra_data_to_validate_putaway(putaway_type, sku_codes):
    batosa_putaway = {
        'update_filter_dict': {'sku__sku_code__in': sku_codes},
        'extend_value_list': [
            'uom', 'serial_based', 'json_data_dict','sku_id',
            'receipt_number', 'batch_detail_id', 'original_quantity'
        ],
        'stock_status_config' : 'after_putaway'
        }
    po_putaway = {
        'sku_id_field_name': 'sku_id',
        'update_filter_dict' : {'sku__sku_code__in' :sku_codes},
        'extend_value_list' : [
            'original_quantity', 'purchase_order_id', 'supplier',
            'po_received_quantity', 'order_id', 'sku_type','po_sku', 'serial_based', 'grn_price',
            'po_order_quantity', 'po_sku_id', 'po_number',
            'sps_json_data', 'seller_po_summary_id',
            'batch_detail_id', 'inspection_lot_number',
            'batch_number', 'batch_reference',
            'manufactured_date', 'expiry_date', 'retest_date',
            'grn_number', 'uom_qty', 'po_cancelled_quantity',
            ],
        'stock_status_config' : 'after_putaway'
            }
    cp_putaway = {
        'sku_id_field_name': 'sku_id',
        'update_filter_dict' : {'sku__sku_code__in' : sku_codes},
        'extend_value_list' : [
            'original_quantity',
            'buy_price', 'grn_price', 'serial_based', 'json_data',
            'grn_number','po_order_quantity', 'sku_id',
            'picklist_id', 'picklist_json',
            'batch_detail_id', 'inspection_lot_number', 'order_reference',
            'batch_number', 'batch_reference',
            'manufactured_date', 'expiry_date', 'retest_date'
            ],
        'stock_status_config' : 'after_putaway'
        }

    jo_putaway = {
        'sku_id_field_name': 'sku_id',
        'update_filter_dict' : {'sku__sku_code__in' : sku_codes},
        'extend_value_list' : [
            'po_received_quantity','order_id','original_quantity','job_order_id',
            'po_received_quantity', 'order_id', 'serial_based',
            'sku_type','jo_sku','po_order_quantity', 'jo_sku_id',
            'sps_json_data', 'grn_number',
            'batch_detail_id', 'inspection_lot_number',
            'batch_number', 'batch_reference',
            'manufactured_date', 'expiry_date', 'retest_date',
            'grn_price',
            ],
        'stock_status_config' : 'after_jo_putaway'
        }
    sr_putaway = {
            'sku_id_field_name': 'sku_id',
            'update_filter_dict' : {'sku__sku_code__in' : sku_codes},
            'extend_value_list' : [
                'original_quantity', 'seller_po_summary_id',
                'serial_based', 'batch_no', 'line_level_id', 'return_id', 'po_received_quantity',
                'po_order_quantity', 'sr_sku_id', 'sps_json_data',
                'grn_number',
                'batch_detail_id', 'inspection_lot_number',
                'batch_number', 'batch_reference',
                'manufactured_date', 'expiry_date', 'retest_date',
                'grn_price',
                ],
            'stock_status_config' : 'after_putaway'
            }
    
    putaway_type_info = {
        'batosa_putaway': batosa_putaway,
        'nte_putaway': batosa_putaway,
        'po_putaway' : po_putaway,
        'cp_putaway' : cp_putaway,
        'jo_putaway' : jo_putaway,
        'sr_putaway' : sr_putaway,
    }
    putaway_info = putaway_type_info[putaway_type]
    putaway_info.update(PUTAWAY_TYPE_INFO[putaway_type])
    return putaway_info


def putaway_location_validations(location_codes, warehouse, zone_restrictions):
    key_wise_error_dict = {}
    # q_list = map(lambda n: Q(location__iexact=n), location_codes)
    # q_list = reduce(lambda a, b: a | b, q_list)
    locations = LocationMaster.objects.filter(location__in=location_codes, zone__user=warehouse.id)
    locations_lower =[str(loc) for loc in location_codes]
    location_query_lower = [str(loc) for loc in locations.values_list('location', flat=True)]
    #Location Validation
    if locations:
        for each_row in locations:
            if each_row.zone and each_row.zone.restrict_one_location_to_one_sku:
                zone_restrictions['restrict_one_location_to_one_sku'].add(each_row.location)

            if each_row.zone and each_row.zone.unique_batch:
                zone_restrictions['unique_batch'].add(each_row.location)

    location_check = set(locations_lower) - set(location_query_lower)
    if location_check:
        tail_message = ' is invalid location: ' if len(location_check)==1 else ' are invalid locations: .'
        key_wise_error_dict['header'] = ['Locations: '+', '.join(location_check) + tail_message]
    
    #Locked Location Validation
    locked_status = ['Inbound', 'Inbound and Outbound']
    locked_locations = list(locations.filter(lock_status__in=locked_status).values_list('location', flat=True))
    if locked_locations:
        tail_message = ' is locked for Inbound operations.' if len(locked_locations)==1 else ' are locked for Inbound operations.'
        key_wise_error_dict['header'] = ['Locations: '+', '.join(locked_locations) + tail_message]

    #Staging Location Validation
    staging_segregation = ['inbound_staging', 'outbound_staging']
    staging_locations = list(locations.filter(zone__segregation__in=staging_segregation).values_list('location', flat=True))
    if staging_locations:
        key_wise_error_dict['header'] = ['Staging Locations are not allowed to do Putaway.']
    
    #inactive Location Validation
    inactive_locations = list(locations.filter(status=0).values_list('location', flat=True))
    if inactive_locations:
        tail_message = ' is inactive and not allowed to do putaway.' if len(inactive_locations)==1 else ' are inactive and not allowed to do putaway.'
        key_wise_error_dict['header'] = ['Locations: '+', '.join(inactive_locations) + tail_message]
    
    carton_managed_dict, loc_zone_mapping = {}, {}
    if not key_wise_error_dict:
        #Locations dict of Carton managed zone type
        carton_managed_dict = dict(locations.values_list('location', 'zone__carton_managed'))
        #Location Zone ID Mapping Dict
        location_zone_mapping = list(locations.values('id', 'location', 'zone_id', 'zone__zone'))
        loc_zone_mapping = {each_row['location'] : each_row for each_row in location_zone_mapping}

    return key_wise_error_dict, carton_managed_dict, loc_zone_mapping

def extra_validate_commented_code():
    #1.UniqueBatchSKULocation Check
    #PicklistLocation Check
    #back_order = get_misc_value('back_order', user.id)
    #if back_order:
    #    pick_res_quantity = PicklistLocation.objects.filter(picklist__order__sku__sku_code=sku_code,
    #            stock__location__zone__zone="BAY_AREA", status=1, picklist__status__icontains='open', 
    #            picklist__order__user=user.id).aggregate(Sum('reserved'))['reserved__sum'] or 0
    #    po_loc_quantity = POLocation.objects.filter(purchase_order__open_po__sku__sku_code=sku_code, status=1,
    #            purchase_order__open_po__sku__user=user.id).aggregate(Sum('quantity'))['quantity__sum'] or 0
    #    if pick_res_quantity - po_loc_quantity:
    #        key_wise_error_dict['header'] = 'Bay Area Stock %s is reserved for %s in Picklist. You cannot putaway this stock.' % (pick_res_quantity, sku_code)

    #3.CycleCountLock Check
    pass


def putaway_sku_validation(putaway_type, po_number, sku_codes, po_loc_skus):
    key_wise_error_dict = {}
    #SKU Validation
    sku_code_check = set(sku_codes) - set(po_loc_skus)
    if sku_code_check:
        tail_message = ' is Not in PO: ' if len(sku_code_check)==1 else ' are Not in PO: .'
        if putaway_type == 'jo_putaway':
             tail_message = ' is Not in JO: ' if len(sku_code_check)==1 else ' are Not in JO: .'
        key_wise_error_dict['header'] = ['SKUCodes: '+', '.join(sku_code_check) + tail_message +  po_number] #change appr msg
        # return key_wise_error_dict, po_loc_dict_data, putaway_data_list
    return key_wise_error_dict

def validate_zone_override(zone, suggested_zone, key_wise_error_dict, index_sku_key, misc_permission_dict):
    """
    Validates if zone override is allowed and if the suggested zone matches the existing zone.
    """
    if misc_permission_dict.get('restrict_putaway_override', '') == 'restrict_zone_override':
        if zone != suggested_zone:
            key_wise_error_dict[index_sku_key] = ["Zone Override is not allowed"]

def validate_location_override(location, suggested_location, key_wise_error_dict, index_sku_key, misc_permission_dict):
    """
    Validates if location override is allowed and if the suggested location matches the existing location.
    """
    if misc_permission_dict.get('restrict_putaway_override') == 'restrict_location_override':
        if location != suggested_location:
            key_wise_error_dict[index_sku_key] = ["Location Override is not allowed"]

def get_existing_serials(warehouse, reference_type, sps_ids):
    values_list = ['sku_code', 'location', 'lpn_number', 'batch_number', 'serial_number', 'transact_id', 'transact_type']
    serial_filter = {'filters':{'reference_type': reference_type, 'transact_id__in': sps_ids, 'status': 1}}
    serial_mixin = SerialNumberTransactionMixin(warehouse, warehouse, serial_filter)
    existing_serials = serial_mixin.get_sntd_details()
    serial_df = pd.DataFrame(existing_serials.get('data', []))
    if serial_df.empty:
        serial_df = pd.DataFrame(columns=values_list)
    return serial_df

def fetch_serial_numbers_for_sps_id(sps_id, serial_df, lpn_number, transact_type):
    if not sps_id:
        return []
    if not lpn_number:
        lpn_number = ''
    sps_ids = str(sps_id).split(',')
    serial_numbers = []
    for sps_id in sps_ids:
        if sps_id.isdigit():
            filtered_serials = serial_df[(serial_df['transact_id'] == int(sps_id)) & (serial_df['lpn_number'] == lpn_number)]
            if transact_type:
                filtered_serials = filtered_serials[filtered_serials['transact_type'] == transact_type]
            if filtered_serials.empty:
                continue
            serial_numbers.extend(filtered_serials['serial_numbers'].tolist()[0])
    return serial_numbers

def validate_serial_numbers(each_row, sps_id, serial_df, key_wise_error_dict, index_sku_key, transact_type):
    if not each_row.get('serial_numbers'):
        key_wise_error_dict[index_sku_key] = ["Serial Numbers are required for Serial Based SKU"]
    serial_numbers = fetch_serial_numbers_for_sps_id(sps_id, serial_df, each_row.get('lpn_number'), transact_type)
    if set(each_row.get('serial_numbers', [])) - set(serial_numbers):
        key_wise_error_dict[index_sku_key] = ["Serial Numbers are invalid"]

def validate_putaway(data, warehouse: User):
    key_wise_error_dict = {}
    po_loc_dict_data = {}
    putaway_data_list = []
    if not data.get("items", {}):
        key_wise_error_dict['header'] = ["Enter the putaway quantity"]
        return key_wise_error_dict, {}, {}
    sku_codes, location_codes, po_loc_ids = [], [], []
    #Framing Data to Query from Requested Data
    new_data_items = []
    for row in data['items']:
        if row["putaway_quantity"]:
            new_data_items.append(row)
            sku_codes.append(row['sku_code'])
            location_codes.append(row['location'])
            po_loc_ids.append(row["id"])
    
    data["items"]= new_data_items

    #Fetching Header Level Data from Requested Data
    po_number = data.get('po_number', '')
    grn_number = data.get('grn_number', '')
    job_code = data.get('job_code','')
    putaway_type = data.get('putaway_type', 'po_putaway')
    
    #Fetching Configurations
    misc_types = ['unique_mrp_putaway', 'express_putaway', 'sku_serialisation', 'restrict_putaway_override', 'restrict_sku_batch_mixing']
    misc_permission_dict = get_multiple_misc_values(misc_types, warehouse.id)
    unique_mrp = misc_permission_dict.get('unique_mrp_putaway', '')
    express_putaway_type = misc_permission_dict.get('express_putaway', '')
    sku_serialisation = misc_permission_dict.get('sku_serialisation', '')
    restrict_sku_batch_mixing = misc_permission_dict.get('restrict_sku_batch_mixing', '')
    timezone = get_user_time_zone(warehouse)

    company_id = get_company_id(warehouse)
    decimal_limit = get_decimal_value(warehouse.id)
    stock_map = {}

    if restrict_sku_batch_mixing == 'true':
        stock_map = preload_stock_details_for_locations(warehouse.id, set(location_codes))
    putaway_location_sku_batch_map = {}
    #Default Filters to get Pending Putaway
    filter_dict = {
        'id__in' : po_loc_ids,
        'status' : 1,
        'location__zone__user' : warehouse.id
    }

    # SKU id feild name for different Putaways
    sku_id_field_name = 'sku_id'
    
    #Default Values to get Pending Putaway
    values_list = [
        'id', 'quantity', 'location_id', 'sku_code', 'sku_desc', 'user',
        'putaway_zone', 'json_data', 'uom', 'putaway_location', 'seller_po_summary_id',
        ]
    
    #Fetching Required Data to Validate Putaway from POLocation, CancelledLocation, ReturnsLocation
    if putaway_type == "returns_putaway" and data.get('grn_type') == "SR":
        putaway_type = 'sr_putaway'
    
    #Fetching Putaway Type Filters and Extra Values to Validate Putaway
    putaway_info = get_extra_data_to_validate_putaway(putaway_type, sku_codes)
    model_name = putaway_info['model_name']
    usage = putaway_info['usage']
    transact_type = putaway_info['transact_type']
    values_list.extend(putaway_info.get('extend_value_list'))
    sku_id_field_name = putaway_info.get('sku_id_field_name', sku_id_field_name)
    values_list.append(sku_id_field_name)

    order_type = putaway_info['order_type']
    filter_dict.update(putaway_info.get('update_filter_dict'))

    stock_status_config = putaway_info['stock_status_config']
    
    
    #Fetching Pending Putaway Query Data
    extra_params = {
            'filters' : filter_dict, 'value_keys' : values_list, 
            'return_type' : 'values_list',
        }
                
    po_loc_values = \
                get_data_from_custom_params(model_name, usage, extra_params)

    po_loc_skus, sps_ids = [], []
    zone_restrictions = {
        'restrict_one_location_to_one_sku': set(),
        'unique_batch': set()
    }
    for each_row in po_loc_values:
        po_loc_dict_data[each_row['id']] = each_row
        po_loc_skus.append(each_row["sku_code"])
        sps_ids.append(each_row['seller_po_summary_id'])
    
    po_loc_ids = po_loc_dict_data.keys()
    serial_df = None
    #Fetching Serial Numbers for SPS ID
    if sku_serialisation == 'true':
        if putaway_type == 'cp_putaway':
            serial_df = get_existing_serials(warehouse, 'cancel_picklist', po_loc_ids)
        else:
            serial_df = get_existing_serials(warehouse, 'grn', sps_ids)

    batch_detail = list(BatchDetail.objects.filter(transact_id__in = po_loc_ids, transact_type=transact_type).values())
    batch_dict = {}
    for each_row in batch_detail:
        batch_dict[each_row['transact_id']] = each_row
    
    #SKU and Location Validation
    sku_error_dict = putaway_sku_validation(putaway_type, po_number, sku_codes, po_loc_skus)
    if sku_error_dict:
        return sku_error_dict, po_loc_dict_data, putaway_data_list

    
    #Location Validations
    location_error_dict, carton_managed_dict, loc_zone_mapping = putaway_location_validations(location_codes, warehouse, zone_restrictions)
    if location_error_dict:
        return location_error_dict, po_loc_dict_data, putaway_data_list

    status_dict= PO_ORDER_TYPES
    counter = 0
    if po_loc_values:
        aggregated_sku_count = {}
        for each_row in data['items']:
            sku_code = each_row['sku_code']
            po_loc_id = each_row['id']
            sps_id = po_loc_dict_data[po_loc_id]['seller_po_summary_id']
            serial_based = po_loc_dict_data[po_loc_id].get('serial_based', 0)
            pol_json_data = po_loc_dict_data[po_loc_id].get('json_data', {})
            error_message = []
            index_sku_key = str(each_row.get('index_key', counter)) +'_'+ sku_code
            key = po_loc_id
            if unique_mrp == 'true' and warehouse.userprofile.industry_type == 'FMCG':
                data_dict = {"sku_code": str(sku_code), "mrp": batch_dict.get(po_loc_id,{}).get("mrp", 0), "weight": batch_dict.get(po_loc_id,{}).get("weight", ""), "location": each_row['location']}
                validation_status = validate_mrp_weight(data_dict, warehouse)
                if validation_status:
                    key_wise_error_dict[index_sku_key] = [validation_status]

            value = each_row['putaway_quantity']
            if key in aggregated_sku_count:
                 aggregated_sku_count[key] += value
            else:
                aggregated_sku_count[key] = value

            if error_message:
                key_wise_error_dict[index_sku_key] = [error_message]

            if aggregated_sku_count[key] > po_loc_dict_data[po_loc_id]['quantity']:
                key_wise_error_dict[index_sku_key] =['Putaway quantity cannot exceed pending Putaway quantity']

            # Carton managed zone location validations
            location = each_row.get('location', '')
            if location and restrict_sku_batch_mixing == 'true':
                if location not in putaway_location_sku_batch_map:
                    putaway_location_sku_batch_map[location] = {
                        'skus': set(),
                        'batches': set()
                    }
                is_location_compatible, error_message = sku_batch_mixing_validation(
                    stock_map,
                    location,
                    each_row['sku_code'],
                    each_row.get('batch_number', ''),
                    putaway_location_sku_batch_map,
                    zone_restrictions
                    )
                if not is_location_compatible:
                    key_wise_error_dict[index_sku_key] = [error_message]
                else:
                    putaway_location_sku_batch_map[location]['skus'].add(sku_code)
                    if each_row.get('batch_number', ''):
                        putaway_location_sku_batch_map[location]['batches'].add(each_row.get('batch_number', ''))


            if carton_managed_dict.get(location, False):
                if not each_row.get('lpn_number'):
                    # Not Packed SKU not allowing to do putaway in Carton Zone
                    key_wise_error_dict[index_sku_key] = ["SKU Level putaway cannot be done in Carton Zone"]
                # SKU level Carton Putaway validations
                elif each_row.get('lpn_number') and express_putaway_type == 'sku_level_putaway':
                    key_wise_error_dict[index_sku_key] = ["SKU Level putaway cannot be done in Carton Zone"]

            zone_restriction = po_loc_dict_data[po_loc_id].get('sps_json_data', {}).get('zone_restriction')
            if zone_restriction:
                zone = loc_zone_mapping[each_row['location']]['zone__zone']
                if zone_restriction != zone:
                    key_wise_error_dict[index_sku_key] = ["Putaway Location is not in Suggested Zone"]

            putaway_zone = loc_zone_mapping[each_row['location']]['zone__zone']
            suggested_zone = po_loc_dict_data[po_loc_id].get('putaway_zone', '')
            #Validate zone override
            validate_zone_override(putaway_zone, suggested_zone, key_wise_error_dict, index_sku_key, misc_permission_dict)
            suggested_location = po_loc_dict_data[po_loc_id].get('putaway_location', '')
            #Validate location override
            validate_location_override(each_row['location'], suggested_location, key_wise_error_dict, index_sku_key, misc_permission_dict)
            #validate serial numbers
            if serial_based:
                if sku_serialisation  == 'true':
                    serial_key = po_loc_id if putaway_type == 'cp_putaway' else sps_id
                    validate_serial_numbers(each_row, serial_key, serial_df, key_wise_error_dict, index_sku_key, pol_json_data.get('quantity_type', ''))
                else:
                    key_wise_error_dict[index_sku_key] = ["Invertory Serialization is not enabled"]
            #Overriding OrderType for PO and STPO Putaway
            if putaway_type == 'po_putaway':
                transact_type = 'PO'
                if po_loc_dict_data[po_loc_id].get('purchase_order__open_po__order_type', ""):
                    order_type = status_dict[po_loc_dict_data[po_loc_id].get('purchase_order__open_po__order_type', "")]
            
            #Fetching GRN / Picklist Number to Save in StockDetail
            ref_grn_number = po_loc_dict_data[po_loc_id].get('grn_number','') #fetched from DB
            if each_row.get("grn_number",''):
                ref_grn_number = each_row.get("grn_number",'')
            if not ref_grn_number:
                ref_grn_number = grn_number
            
            uom_decimal_limit = ''
            if po_loc_dict_data[po_loc_id].get('sku_uom',''):
                sku_uom = po_loc_dict_data[po_loc_id]['sku_uom']
                uom_decimals = get_uom_decimals(company_id, uom_codes = [sku_uom])
                uom_decimal_limit = uom_decimals.get(sku_uom)
            
            round_off = uom_decimal_limit or decimal_limit
            if round_off and isinstance(round_off, str):
                round_off = int(round_off)
            
            date_fields = ['manufactured_date', 'expiry_date', 'retest_date']
            for date_key in date_fields:
                date_value = po_loc_dict_data[po_loc_id].get(date_key)
                if date_value and isinstance(date_value, datetime):
                    date_obj = get_local_date_known_timezone(timezone, date_value, send_date=True)
                    po_loc_dict_data[po_loc_id][date_key] = date_obj.strftime(DEFAULT_DATE_FORMAT) 

            counter+=1
            putaway_data_dict = {
                    "user" : warehouse.id,
                    "warehouse_user": warehouse.id,
                    'source': data.get("source", ""),
                    "location_entry": each_row.get("location_entry", ""),
                    "sku_entry": each_row.get("sku_entry", ""),
                    "po_number": po_number  if po_number else po_loc_dict_data[po_loc_id].get('po_number',0),
                    "job_code": job_code if job_code else each_row.get("job_code"),
                    "grn_number" : grn_number,
                    "ref_grn_number" : ref_grn_number,
                    "grn_type" : data.get('grn_type', ''),
                    "stock_status_config" : stock_status_config,
                    "order_type": order_type,
                    "transact_type": transact_type,
                    "grn_price" : po_loc_dict_data[po_loc_id].get('grn_price',0),
                    "grn_reference" : po_loc_dict_data[po_loc_id].get('grn_reference'),
                    "po_loc_id" : po_loc_id,
                    "original_poloc_id": po_loc_id,
                    "uom_qty": po_loc_dict_data[po_loc_id].get("uom_qty"),
                    "po_id" : po_loc_dict_data[po_loc_id].get('purchase_order_id', ''),
                    "return_id" :po_loc_dict_data[po_loc_id].get('return_id', ''),
                    "return_sku_id" : po_loc_dict_data[po_loc_id].get('line_level_id', ''),
                    "po_order_id" : po_loc_dict_data[po_loc_id].get('order_id',''),
                    "seller_po_summary_id" : po_loc_dict_data[po_loc_id].get('seller_po_summary_id'),
                    "original_quantity": po_loc_dict_data[po_loc_id].get('original_quantity',0),
                    "po_order_quantity" : po_loc_dict_data[po_loc_id].get('po_order_quantity',0),
                    "po_received_quantity" : po_loc_dict_data[po_loc_id].get('po_received_quantity'),
                    "po_cancelled_quantity" : po_loc_dict_data[po_loc_id].get('po_cancelled_quantity',0),
                    "purchase_order_id" : po_loc_dict_data[po_loc_id].get('purchase_order_id',''),
                    "job_order_id": po_loc_dict_data[po_loc_id].get('job_order_id',''),
                    "picklist_id": po_loc_dict_data[po_loc_id].get('picklist_id',''),
                    "picklist_json": po_loc_dict_data[po_loc_id].get('picklist_json', {}),
                    "employee_id": data.get('employee_id'),
                    "sku_id" : po_loc_dict_data[po_loc_id][sku_id_field_name],
                    "sku_code" : po_loc_dict_data[po_loc_id]['sku_code'],
                    "sku_desc" : po_loc_dict_data[po_loc_id]['sku_desc'],
                    "sku_type" : po_loc_dict_data[po_loc_id].get('sku_type', ''),
                    "sugg_zone" : po_loc_dict_data[po_loc_id]['putaway_zone'],
                    "sugg_location" : po_loc_dict_data[po_loc_id]['location_id'],
                    "zone_id": loc_zone_mapping[each_row['location']]['zone_id'],
                    "location_id" : loc_zone_mapping[each_row['location']]['id'],
                    "location" : location,
                    "zone": each_row.get('zone'),
                    "putaway_quantity" : value,
                    "quantity" : value,
                    "pcf_value" : 1,
                    "batch_no": po_loc_dict_data[po_loc_id].get('batch_no') if po_loc_dict_data[po_loc_id].get('batch_no') else batch_dict.get(po_loc_id,{}).get('batch_no', ''),
                    "batch_detail_id": po_loc_dict_data[po_loc_id].get('batch_detail_id') if po_loc_dict_data[po_loc_id].get('batch_detail_id') else batch_dict.get(po_loc_id,{}).get("id", None),
                    "batch_detail_buy_price": po_loc_dict_data[po_loc_id].get('buy_price') if po_loc_dict_data[po_loc_id].get('buy_price') else batch_dict.get(po_loc_id,{}).get("buy_price", 0),
                    "inspection_lot_number" : po_loc_dict_data[po_loc_id].get('inspection_lot_number'),
                    "batch_details" : {
                        "batch_number" : po_loc_dict_data[po_loc_id].get('batch_number'),
                        "batch_reference" : po_loc_dict_data[po_loc_id].get('batch_reference'),
                        "manufactured_date" : po_loc_dict_data[po_loc_id].get('manufactured_date') if po_loc_dict_data[po_loc_id].get('manufactured_date') else None,
                        "expiry_date" : po_loc_dict_data[po_loc_id].get('expiry_date'),
                        "retest_date" : po_loc_dict_data[po_loc_id].get('retest_date'),
                        "inspection_lot_number" : po_loc_dict_data[po_loc_id].get('inspection_lot_number'),
                    },
                    "lpn_number": each_row.get("lpn_number", ""),
                    "supplier_id": po_loc_dict_data[po_loc_id].get('supplier', ''),
                    "serial_based": po_loc_dict_data[po_loc_id].get('serial_based', 0),
                    "serial_numbers": each_row.get("serial_numbers", []),
                    "pending_stock_id" : each_row.get("pending_stock_id"),
                    "receipt_number":po_loc_dict_data[po_loc_id].get('receipt_number'),
                    "picked_location": po_loc_dict_data[po_loc_id].get('json_data_dict', {}).get('picked_location',''),
                    "picked_carton": po_loc_dict_data[po_loc_id].get('json_data_dict', {}).get('carton',''),
                    "po_loc_json": po_loc_dict_data[po_loc_id].get('json_data'),
                    "round_off" : round_off,
                    "putaway_type" : putaway_type,
                    "order_reference" : po_loc_dict_data[po_loc_id].get('order_reference'),
                    "uom" : po_loc_dict_data[po_loc_id].get('uom'),
                    }
            if each_row.get("pending_stock_id"):
                putaway_data_dict["pending_stock_id"] = int(each_row.get("pending_stock_id"))
            putaway_data_list.append(putaway_data_dict)
    else:
        key_wise_error_dict['header'] = ["No Matched records found"]
    return key_wise_error_dict, po_loc_dict_data, putaway_data_list
