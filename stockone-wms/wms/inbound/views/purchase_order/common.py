import datetime
from datetime import timed<PERSON>ta
import math
import json
import copy
from collections import OrderedDict
import pandas as pd

from django.db.models import F, Q, DateField, Sum, Value, Case, When, CharField
from django.http import JsonResponse, HttpResponse
from django.db.models.functions import Cast, Concat

from wms.settings.base import reports_database

#Model Imports
from wms_base.models import User, UserProfile, UserAddresses
from core.models import (
    SKUMaster, SKUAttributes, EANNumbers, 
    PurchaseApprovalConfig, MasterDocs
)
from inbound.models import SupplierMaster, SKUSupplier, PurchaseOrder, Pofields, POHeader

#Method Imports
from wms_base.wms_utils import (
    SKU_NAME_FIELDS_MAPPING, SUMMARY_INTER_STATE_STATUS, init_logger,
    PO_ORDER_TYPES, PO_RECEIPT_TYPES,
    )
from core_operations.views.common.main import (
    get_warehouse,
    check_and_update_incremetal_type_val,
    get_permission_based_sub_users_emails, get_all_warehouses,
    get_multiple_misc_values, get_misc_value, build_search_term_query,
    get_company_id,
    get_user_attributes, get_sku_master, get_uploaded_files_data
    )
from inbound.views.supplier.main import get_supplier_sku_price_values
from core_operations.views.common.user_attributes import get_wms_field_values_dict
from inbound.views.common.common import check_margin_percentage, frame_filters_with_request_dict
from inbound.views.common.fetch_query import get_data_from_custom_params

from inbound.views.grn.constants import DEFAULT_FILE_UPLOAD_NAMES

log = init_logger('logs/purchase_order.log')

def get_po_types(warehouse):
    result_data = {}
    misc_types = ['po_types']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    po_types = misc_dict.get('po_types', 'false')
    if po_types == 'false' :
        result_data['po_types'] = []
    else:
        result_data['po_types'] = po_types.split(',')
    # Subuser Emails with PO Approval permission
    result_data['po_approval_permissive_emails'] =  \
        get_permission_based_sub_users_emails(warehouse, 'change_pendingpo')
    return result_data

@get_warehouse
def get_po_types_list(request, warehouse):
    result_data = get_po_types(warehouse)
    return JsonResponse({'data': result_data})

def get_po_numbers_list(filters, warehouse):
    filter_dict = {'open_po__sku__user__in' :  [warehouse.id]}
    filter_keys = [
        'id', 'supplier_id', 'po_number', 'sku_code', 'warehouse',
        'po_reference', 'po_type', 'creation_date__gt', 'creation_date__lt',
        'updation_date__gte', 'updation_date__lte',
    ]

    message, framed_filters, [] = frame_filters_with_request_dict(filters, filter_keys)
    if message:
        return message, framed_filters, []
    if framed_filters:
        filter_dict.update(framed_filters)
    
    #Fetch Supplier Master Data
    extra_params = {
        'filters' : filter_dict, 'order_by' : ['po_number'],
        'distincts' : ['po_number'],
        'value_keys' : ['po_number'], 'return_type' : 'values_list',
        'flat' : True
        }

    po_numbers = \
        get_data_from_custom_params(PurchaseOrder, 'purchase_order', extra_params)
        
    return '', filter_dict, po_numbers

def get_po_header_status_from_po_data(warehouse_ids, po_numbers=[], filter_status=[], order_by=[]):
    ''' Returns PO Header status from PO line level data '''

    po_data_dict = {}
    values_dict = {
        'po_qty': F('open_po__order_quantity'),
        'user_id': F('open_po__sku__user'),
    }
    values_list = ['po_number', 'status', 'received_quantity', 'cancelled_quantity']
    if filter_status == ['Open', 'Partially Received'] and not po_numbers:
        po_numbers = list(PurchaseOrder.objects.using(reports_database).filter(
            open_po__sku__user=warehouse_ids[0], status__in=['', 'grn-generated']).values_list('po_number', flat=True))
    elif filter_status == ['Cancelled'] and not po_numbers:
        po_numbers = list(PurchaseOrder.objects.using(reports_database).filter(
            open_po__sku__user=warehouse_ids[0], status__in=['location-assigned']).values_list('po_number', flat=True))

    po_objs = PurchaseOrder.objects.using(reports_database).filter(
        open_po__sku__user=warehouse_ids[0]).order_by(*order_by)
    if po_numbers:
        po_objs = po_objs.filter(po_number__in=po_numbers)
    
    po_data = po_objs.values(*values_list, **values_dict)

    # storing PO line level status
    for po_line_data in po_data:
        uniq_key = (po_line_data['po_number'], po_line_data['user_id'])
        po_line_status = 'Open'
        if po_line_data['status'] == 'confirmed-putaway':
            po_line_status = 'Closed'
        elif po_line_data['status'] == 'grn-generated':
            if po_line_data['cancelled_quantity']:
                po_line_status = 'Partially Received - Closed'
            elif po_line_data['received_quantity'] >= po_line_data['po_qty']:
                po_line_status = 'Received'
            else:
                po_line_status = 'Partially Received'
        elif po_line_data['status'] == 'location-assigned':
            po_line_status = 'Cancelled'

        if uniq_key not in po_data_dict:
            data_dict = {
                'po_qty': po_line_data['po_qty'],
                'received_qty': po_line_data['received_quantity'],
                'po_status_list': [po_line_status],
            }
            po_data_dict[uniq_key] = data_dict
        else:
            po_data_dict[uniq_key]['po_qty'] += po_line_data['po_qty']
            po_data_dict[uniq_key]['received_qty'] += po_line_data['received_quantity']
            if po_line_status not in po_data_dict[uniq_key]['po_status_list']:
                po_data_dict[uniq_key]['po_status_list'].append(po_line_status)

    # PO header status is based on PO line status
    for uniq_key in list(po_data_dict.keys()):
        po_status_list = po_data_dict[uniq_key]['po_status_list']
        if po_status_list in [['Open'], ['Cancelled'], ['Closed']]:
            po_status = po_status_list[0]
        elif 'Partially Received' in po_status_list:
            po_status = 'Partially Received'
        elif set(po_status_list).issubset(['Received']):
            #Handle The Other COmbinations later(CHECK_LATER)
            po_status = 'Received'
        elif set(po_status_list).issubset(['Received','Partially Received - Closed', 'Cancelled', 'Closed']):
            po_status = 'Partially Received - Closed'
        else:
            po_status = 'Partially Received'
        po_data_dict[uniq_key]['po_status'] = po_status

        # If any status is filtered, then removing other status po data
        if filter_status and po_status not in filter_status  :
            del po_data_dict[uniq_key]
    return po_data_dict

def get_mapping_values_po(wms_code = '',supplier_id ='', warehouse =''):

    """
    Get the mapping values for a purchase order.

    Args:
        wms_code (str): The WMS code of the SKU.
        supplier_id (str): The ID of the supplier.
        warehouse (str): The ID of the warehouse.

    Returns:
        dict: A dictionary containing the mapping values for the purchase order.
    """
   
    
    data = {}
    margin_check = get_misc_value('enable_margin_price_check', warehouse.id, number=False, boolean=True)
    try:
        sku_master = SKUMaster.objects.get(wms_code=wms_code, user=warehouse.id)
        if wms_code.isdigit():
            ean_number = wms_code
            sku_supplier = SKUSupplier.objects.filter(
                Q(sku__ean_number=wms_code) | Q(sku__wms_code=wms_code),
                supplier_id=supplier_id, sku__user=warehouse.id)
        else:
            ean_number = ''
            sku_supplier = SKUSupplier.objects.filter(
                sku__wms_code=wms_code, supplier__supplier_id=supplier_id, sku__user=warehouse.id)
        if not sku_supplier:
            attr_mapping = copy.deepcopy(SKU_NAME_FIELDS_MAPPING)
            for attr_key, attr_val in attr_mapping.items():
                supplier_sku = SKUSupplier.objects.filter(
                    user=warehouse.id,supplier__supplier_id=supplier_id,
                    attribute_type=attr_key,attribute_value=getattr(sku_master, attr_val))
                if supplier_sku.exists():
                    sku_supplier = supplier_sku
        
        sup_markdown = SupplierMaster.objects.get(supplier_id=supplier_id, user=warehouse.id)
        data = {
            'supplier_code': '',
            'price': sku_master.cost_price,
            'sku': sku_master.sku_code,
            'sku_size': sku_master.sku_size,
            'weight':'',
            'ean_number': 0,
            'measurement_unit': sku_master.measurement_type
            }
        if sku_master.block_options:
            data['sku_block'] = sku_master.block_options
        else:
            data['sku_block'] = ''
        skuattributes = SKUAttributes.objects.filter(sku_id=sku_master.id, attribute_name = 'weight' )
        weight = ''
        if skuattributes.exists():
            weight = skuattributes[0].attribute_value
        data['weight'] = weight
        if sku_supplier:
            mrp_value = sku_master.mrp
            if sku_supplier[0].costing_type == 'Margin Based':
                margin_percentage = sku_supplier[0].margin_percentage
                prefill_unit_price = mrp_value - ((mrp_value * margin_percentage) / 100)
                tax=0
                tax_list=get_supplier_sku_price_values(supplier_id, wms_code, warehouse)
                if len(tax_list):
                    tax_list=tax_list[0].get('taxes',[])
                    if len(tax_list):
                        tax_list = tax_list[0]
                        if tax_list.get('inter_state'):
                            tax=tax_list.get('igst_tax',0)+tax_list.get('apmc_tax',0)+tax_list.get('cess_tax',0)
                        else:
                            tax=tax_list.get('cgst_tax',0)+tax_list.get('sgst_tax',0)+tax_list.get('apmc_tax',0)+tax_list.get('cess_tax',0)

                prefill_unit_price = (prefill_unit_price * 100) / (100 + tax)
                data['price'] = float("%.2f" % prefill_unit_price)
            elif sku_supplier[0].costing_type == 'Markup Based':
                 markup_percentage = sku_supplier[0].markup_percentage
                 prefill_unit_price = mrp_value / (1+(markup_percentage/100))
                 data['price'] = float("%.2f" % prefill_unit_price)
            else:
                data['price'] = sku_supplier[0].price
            data['supplier_code'] = sku_supplier[0].supplier_code
            data['ean_number'] = ean_number
            if sku_supplier[0].sku is not None:
                data['sku'] = sku_supplier[0].sku.sku_code
                data['measurement_unit'] = sku_supplier[0].sku.measurement_type
        else:
            # if int(sup_markdown.ep_supplier):
            #     data['price'] = 0
            mandate_supplier = get_misc_value('mandate_sku_supplier', warehouse.id)
            # if mandate_supplier == 'true' and not int(sup_markdown.ep_supplier):
            if mandate_supplier == 'true':
                data['supplier_mapping'] = True
        # if sku_master.block_options == "PO":
            # if not int(sup_markdown.ep_supplier):
            #     data = {'error_msg':'This SKU is Blocked for PO'}
        if margin_check:
            status = check_margin_percentage(sku_master.id, sup_markdown.id, warehouse)
            if status:
                data = {'error_msg': status}

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Getting po Values failed for %s and params are %s and error statement is %s' % (
        str(warehouse.username), str(wms_code), str(e)))
    return data

def tax_calculation_master(data, warehouse, profile, taxes):
    total, amount, company_address = 0, 0, ''
    unit_price = data.get('price', 0)
    quantity = data.get('quantity', 0)
    tax = sum(taxes.values())
    amount = unit_price * quantity
    total += amount + ((amount / 100) * float(tax))
    if warehouse.userprofile.wh_address:
        company_address = warehouse.userprofile.wh_address
        if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
            if warehouse.userprofile.user.email:
                company_address = ("%s, Email:%s") % (company_address, warehouse.userprofile.user.email)
            if warehouse.userprofile.phone_number:
                company_address = ("%s, Phone:%s") % (company_address, warehouse.userprofile.phone_number)
            if warehouse.userprofile.gst_number:
                company_address = ("%s, GSTINo:%s") % (company_address, warehouse.userprofile.gst_number)
    else:
        company_address = warehouse.userprofile.address
    if profile.wh_address:
        address = profile.wh_address
    else:
        address = profile.address
    if not (address and company_address):
        company_address, address = '', ''
    if profile.user.email:
        address = ("%s, Email:%s") % (address, profile.user.email)
    return total, amount, company_address, address

def format_printing_data(datum, purchase_order, wms_code, supplier_code, measurement_unit, table_headers, display_remarks, show_cess_tax, show_apmc_tax):
    amount = 0
    delivery_date = ''
    amount = float(datum.quantity) * float(purchase_order.price)
    amount = float("%.2f" % amount)
    total_tax_amt = (purchase_order.utgst_tax + purchase_order.sgst_tax + purchase_order.cgst_tax + purchase_order.igst_tax + purchase_order.cess_tax + purchase_order.apmc_tax + purchase_order.utgst_tax) * (amount/100)
    total_sgst = purchase_order.sgst_tax * (amount/100)
    total_cgst = purchase_order.cgst_tax * (amount/100)
    total_igst = purchase_order.igst_tax * (amount/100)
    total_tax_amt = float("%.2f" % total_tax_amt)
    total_sku_amt = total_tax_amt + amount
    try:
        delivery_date = datetime.datetime.strftime(datum.delivery_date, '%d-%m-%Y')
    except Exception as e:
        pass
    po_temp_data = [wms_code, purchase_order.sku.hsn_code, supplier_code, purchase_order.sku.sku_desc, delivery_date, float(datum.quantity),
                measurement_unit,
                purchase_order.price, purchase_order.mrp, amount, purchase_order.sgst_tax, total_sgst, purchase_order.cgst_tax, total_cgst,
                purchase_order.igst_tax, total_igst,
                total_sku_amt
                ]
    if show_cess_tax:
        po_temp_data.insert(table_headers.index('CESS (%)'), purchase_order.cess_tax)
    if show_apmc_tax:
        po_temp_data.insert(table_headers.index('APMC (%)'), purchase_order.apmc_tax)
    if display_remarks == 'true':
        po_temp_data.append(purchase_order.remarks)
    return po_temp_data

def get_purchase_order_data(order):
    order_data = {}
    status_dict = PO_ORDER_TYPES
    # rw_purchase = RWPurchase.objects.filter(purchase_order_id=order.id)
    # st_order = STPurchaseOrder.objects.filter(po_id=order.id)
    temp_wms = ''
    owner_email = '',
    unit = ""
    gstin_number = ''
    order_type = 'purchase order'
    supplier_id = ''
    intransit_quantity = 0
    if 'job_code' in dir(order):
        order_data = {'wms_code': order.product_code.wms_code, 'sku_group': order.product_code.sku_group,
                      'sku': order.product_code,
                      'supplier_code': '', 'load_unit_handle': order.product_code.load_unit_handle,
                      'sku_desc': order.product_code.sku_desc,'sku_brand':order.product_code.sku_brand,
                      'sku_reference': order.product_code.sku_reference,
                      'cgst_tax': 0, 'sgst_tax': 0, 'igst_tax': 0, 'utgst_tax': 0, 'apmc_tax': 0, 'tin_number': '',
                      'intransit_quantity': intransit_quantity, 'shelf_life': order.product_code.shelf_life,
                      'show_imei': order.product_code.enable_serial_based,
                      'minimum_shelf_life' : order.product_code.minimum_shelf_life.total_seconds() / timedelta(days=1).total_seconds()}
        return order_data
    # elif rw_purchase and not order.open_po:
    #     rw_purchase = rw_purchase[0]
    #     open_data = rw_purchase.rwo
    #     user_data = UserProfile.objects.get(user_id=open_data.vendor.user)
    #     supplier_id = user_data.user.id
    #     address = open_data.vendor.address
    #     email_id = open_data.vendor.email_id
    #     username = open_data.vendor.name
    #     order_quantity = open_data.job_order.product_quantity
    #     sku = open_data.job_order.product_code
    #     order_type = ''
    #     price = 0
    #     mrp = 0
    #     supplier_code = ''
    #     cgst_tax = 0
    #     sgst_tax = 0
    #     igst_tax = 0
    #     utgst_tax = 0
    #     apmc_tax = 0
    #     cess_tax = 0
    #     tin_number = ''
    elif order.open_po:
        open_data = order.open_po
        remarks = order.open_po.remarks
        user_data = order.open_po.supplier
        supplier_id = order.open_po.supplier.supplier_id
        address = user_data.address
        email_id = user_data.email_id
        # owner_email = user_data.owner_email_id
        username = user_data.name
        order_quantity = open_data.order_quantity
        intransit_quantity = order.intransit_quantity
        sku = open_data.sku
        user = sku.user
        price = open_data.price
        mrp = open_data.mrp
        user_profile = UserProfile.objects.get(user_id=sku.user)
        #if user_profile.user_type == 'warehouse_user':
        #    mrp = sku.mrp
        unit = open_data.measurement_unit
        order_type = status_dict[order.open_po.order_type]
        supplier_code = open_data.supplier_code
        gstin_number = order.open_po.supplier.tin_number
        po_ref_num = order.open_po.po_name
        cgst_tax = open_data.cgst_tax
        sgst_tax = open_data.sgst_tax
        igst_tax = open_data.igst_tax
        utgst_tax = open_data.utgst_tax
        cess_tax = open_data.cess_tax
        apmc_tax = open_data.apmc_tax
        tin_number = open_data.supplier.tin_number
        po_type = 'Purchase Order'
        if sku.wms_code == 'TEMP':
            temp_wms = open_data.wms_code
    # elif st_order and not order.open_po:
    #     st_picklist = STOrder.objects.filter(stock_transfer__st_po_id=st_order[0].id)
    #     open_data = st_order[0].open_st
    #     user_data = UserProfile.objects.get(user_id=st_order[0].open_st.warehouse_id)
    #     supplier_id = st_order[0].open_st.warehouse_id
    #     address = user_data.location
    #     email_id = user_data.user.email
    #     username = user_data.user.username
    #     order_quantity = open_data.order_quantity
    #     sku = open_data.sku
    #     user = sku.user
    #     remarks = ''
    #     price = open_data.price
    #     mrp = open_data.mrp
    #     po_ref_num = ''
    #     order_type = ''
    #     supplier_code = ''
    #     cgst_tax = open_data.cgst_tax
    #     sgst_tax = open_data.sgst_tax
    #     igst_tax = open_data.igst_tax
    #     utgst_tax = 0
    #     cess_tax = open_data.cess_tax
    #     apmc_tax = 0
    #     tin_number = ''
    #     order_type = 'stock transfer'
    #     po_type = 'Stock Transfer'
    ean_number = ""
    ean_numbers = []
    if sku:
        ean_number_objs = EANNumbers.objects.filter(sku__sku_code=sku.sku_code, sku__user=sku.user)
        if ean_number_objs:
            ean_number = ean_number_objs[0].ean_number
            ean_numbers = list(ean_number_objs.values_list('ean_number', flat=True))
    order_data = {'order_quantity': order_quantity, 'price': price, 'mrp': mrp,'wms_code': sku.wms_code, 'ean_number':ean_number,
            'sku_code': sku.sku_code, 'sku_brand':sku.sku_brand, 'sku_reference': sku.sku_reference,
                  'supplier_id': supplier_id, 'zone': sku.zone,
                  'qc_check': sku.qc_check, 'supplier_name': username, 'gstin_number': gstin_number,
                  'sku_desc': sku.sku_desc, 'address': address, 'unit': unit, 'load_unit_handle': sku.load_unit_handle,
                  'phone_number': user_data.phone_number, 'email_id': email_id, 'size': sku.sku_size,
                  'sku_group': sku.sku_group, 'sku_id': sku.id, 'sku': sku, 'temp_wms': temp_wms,
                  'order_type': order_type,'owner_email':owner_email, 'po_reference' : po_ref_num,
                  'supplier_code': supplier_code, 'cgst_tax': cgst_tax, 'sgst_tax': sgst_tax, 'igst_tax': igst_tax,
                  'utgst_tax': utgst_tax, 'cess_tax':cess_tax, 'apmc_tax': apmc_tax,
                  'intransit_quantity': intransit_quantity, 'order_type': order_type,
                  'image_url': str(sku.image_url) if sku.image_url  else "images/wms/dflt.jpg",
                  'tin_number': tin_number, 'shelf_life': sku.shelf_life-1 if sku.shelf_life else 0, 'show_imei': sku.enable_serial_based, 'po_type': po_type,'remarks' : remarks,
                  'minimum_shelf_life' : sku.minimum_shelf_life.total_seconds() / timedelta(days=1).total_seconds(),
                  'ean_numbers': ean_numbers}
    return order_data

def get_receive_po_datatable_filters(warehouse, filters, request):
    ''' Returns search parameters for filtering Purchase Orders in the Purchase Order datatable '''
    search_params, search_params1, search_params2 = {}, {}, {}
    supplier_search = ''

    # Prepare search filters for each search index
    if filters.get('search_0'):
        col_val = filters['search_0']
        po_ids = PurchaseOrder.objects.filter(
            po_number__icontains=col_val,
            open_po__sku__user__in=warehouse,
            received_quantity__lt=F('open_po__order_quantity')
        ).exclude(
            status__in=['location-assigned', 'confirmed-putaway']
        ).values_list('id', flat=True)
        search_params['id__in'] = po_ids
        search_params1['po_id__in'] = search_params['id__in']
    if filters.get('search_1'):
        search_params['open_po__po_name__icontains'] = filters['search_1']
        search_params1['po__open_po__po_name__icontains'] = filters['search_1']
        search_params2['purchase_order__open_po__po_name__icontains'] = filters['search_1']
    if filters.get('search_3'):
        search_params['creation_date__regex'] = filters['search_3']
    if filters.get('search_10'):
        supplier_search = 'search_10'
    if filters.get('search_9'):
        plant_filter_ids = list(
            User.objects.filter(first_name__icontains=filters['search_9']).values_list('id', flat=True)
        )
        if plant_filter_ids:
            search_params['open_po__sku__user__in'] = plant_filter_ids
    if filters.get(supplier_search):
        search_params['open_po__supplier__supplier_id__icontains'] = filters[supplier_search]
        search_params1['open_st__warehouse__id__icontains'] = filters[supplier_search]
        search_params2['rwo__vendor__id__icontains'] = filters[supplier_search]
    if filters.get('search_3'):
        search_params['open_po__supplier__name__icontains'] = filters['search_3']
        search_params1['open_st__warehouse__username__icontains'] = filters['search_3']
        search_params2['rwo__vendor__name__icontains'] = filters['search_3']

    # Return the three dictionaries of search parameters
    return search_params, search_params1, search_params2

def get_filtered_purchase_order_ids(request, user, search_term, filters, col_num, order_term, start_index, stop_index):
    all_prod_catgs = True
    sort_col = 'po__creation_date'
    if order_term == 'desc':
        sort_col = '-po__creation_date'
    purchase_order_list = ['order_id', 'po_number', 'open_po__po_name', 'open_po__supplier__name', 'order_id', 'order_id', 'order_id',
                           'order_id', 'order_id', 'order_id', 'order_id', 'open_po__supplier__name', 'order_id',
                           'order_id','order_id', 'po_number', 'po_type']

    sort_col = purchase_order_list[col_num]
    if order_term == 'desc':
        sort_col = '-%s' % (sort_col)
    st_purchase_list_sort = []
    date_filter_params, delivery_date_filter_params = {}, {}
    if 'date_filter' in filters:
        date_filter_params = filters.get('date_filter')
    if 'delivery_date_filter' in filters:
        delivery_date_filter_params = filters.get('delivery_date_filter')
    order_qtys_dict, receive_qtys_dict, st_order_qtys_dict, st_receive_qtys_dict = {}, {}, {}, {}

    purchase_order_query = build_search_term_query(purchase_order_list, search_term)
    search_params, search_params1, search_params2 = get_receive_po_datatable_filters(user, filters, request)
    results_objs = PurchaseOrder.objects.filter(open_po__sku__user=user[0], \
                            **search_params, **date_filter_params, **delivery_date_filter_params) \
                            .exclude(status__in=['location-assigned', 'confirmed-putaway', 'free_sku']) \
                            .filter(purchase_order_query, received_quantity__lt=F('open_po__order_quantity'))
    '''po_result_order_ids = PurchaseOrder.objects.filter(po_number__in=results_objs.values_list('po_number', flat=True)).order_by(tmp_sort_col, '-order_id')
    po_ord_qty = po_result_order_ids.values_list('order_id', 'prefix', 'po_number').distinct().annotate(total_order_qty=Sum('open_po__order_quantity'))
    po_recv_qty = po_result_order_ids.values_list('order_id', 'prefix', 'po_number').distinct().annotate(total_received_qty=Sum('received_quantity'))
    if po_ord_qty.exists():
        order_qtys_dict.update(generate_po_qty_dict(po_ord_qty, start_index, stop_index))
    if po_recv_qty.exists():
        receive_qtys_dict.update(generate_po_qty_dict(po_recv_qty, start_index, stop_index))'''
    po_order_ids_list = results_objs.values_list('id',flat=True)
    results1 = po_order_ids_list
    results = PurchaseOrder.objects.filter(id__in=results1).exclude(status__in=['deleted', 'free_sku']).\
                annotate(po__creation_date=Cast('creation_date', DateField())).\
                order_by(sort_col, '-order_id').\
                values('po_number').distinct()
    return results

def newFetchConfigNameRangesMap(warehouse, po_type, purchase_type='PR', approval_type='PO'):
    # confMap = OrderedDict()
    confMap = []
    error_status = ''
    approval_config_data = PurchaseApprovalConfig.objects.filter(
        user=warehouse, purchase_type=purchase_type, approval_type=approval_type, po_type=po_type, status=1)
    if not approval_config_data.exists():
        approval_config_data = PurchaseApprovalConfig.objects.filter(
            user=warehouse, purchase_type=purchase_type, approval_type=approval_type, po_type='all', status=1)
    approval_config_data = approval_config_data.distinct().values_list('name', 'min_Amt', 'max_Amt').order_by('min_Amt')
    for rec in approval_config_data:
        name, min_Amt, max_Amt = rec
        # confMap[name] = (min_Amt, max_Amt)
        confMap.append({name: (min_Amt, max_Amt)})
    if not approval_config_data:
        approval_config_data = PurchaseApprovalConfig.objects.filter(
            user=warehouse, purchase_type=purchase_type, approval_type=approval_type, status=1).exclude(po_type='')
        if not approval_config_data.exists():
            # Checking if Approval matrix is defined for atleast one po type
            error_status = 'No Approvers defined'
    return confMap, error_status

def newFindReqConfigName(warehouse, totalAmt, po_type='', purchase_type='PR'):
    reqConfigName = ''
    configNameRangesMap, error_status = \
            newFetchConfigNameRangesMap(warehouse, po_type, purchase_type=purchase_type)
    if configNameRangesMap:
        for conf_data in configNameRangesMap:
            confName, priceRanges = list(conf_data.items())[0]
            min_Amt, max_Amt = priceRanges
            # if totalAmt <= min_Amt:
            #     reqConfigName = confName
            #     break
            if min_Amt <= totalAmt <= max_Amt:
                reqConfigName = confName
                break
        if not reqConfigName and max_Amt < totalAmt:
            error_status = 'PO value is higher than defined ranges'
    else:
        reqConfigName = ''
    return reqConfigName, error_status

def findLastLevelToApprove(warehouse, pr_number, totalAmt, purchase_type='PR', product_category='', approval_type='PO',
                           sku_category='', po_type=''):
    if not product_category:
        product_category = 'Kits&Consumables'
    final_level = 'level1'
    company_id = get_company_id(warehouse)
    totalAmt = math.ceil(totalAmt)
    #reqConfigName = findReqConfigName(user, totalAmt, purchase_type=purchase_type, product_category=product_category,
    #                                  approval_type=approval_type, sku_category=sku_category)
    req_config_name, error_status = \
        newFindReqConfigName(warehouse, totalAmt, purchase_type=purchase_type, po_type=po_type)
    configQs = list(PurchaseApprovalConfig.objects.filter(
            name=req_config_name, user_id=warehouse.id, approval_type=approval_type,
            purchase_type=purchase_type, min_Amt__lte=totalAmt, max_Amt__gte=totalAmt
        ).values_list('level', flat=True).order_by('-level'))
    
    if configQs:
        final_level = configQs[0]
    return req_config_name, final_level

def get_total_amount_from_skus(warehouse_id, sku_dict, supplier_id):
    # Returns total amount of all SKU's in PO

    sku_codes = [key[1] for key in sku_dict.keys()]
    sku_prices = dict(SKUMaster.objects.filter(user=warehouse_id, sku_code__in=sku_codes).values_list('sku_code', 'price'))
    supplier_sku_prices = dict(SKUSupplier.objects.filter(
            user=warehouse_id, supplier__supplier_id=supplier_id, sku__sku_code__in=sku_codes
        ).values_list('sku__sku_code', 'price'))

    total_amt = 0
    for key, data in sku_dict.items():
        sku_code = key[1]
        
        price = data['price']
        if price in ['', None]:
            if supplier_sku_prices.get(sku_code, ''):
                price = supplier_sku_prices[sku_code]
            else:
                price = sku_prices[sku_code]        
        total_amt += float(data['quantity']) * float(price)
    return total_amt

def check_purchase_order_created(warehouse, po_id, po_prefix):
    po_data = PurchaseOrder.objects.filter(open_po__sku__user=warehouse.id,
                                           order_id=po_id, prefix=po_prefix)
    if not po_data.exists():
        check_and_update_incremetal_type_val(po_id, warehouse, 'po')

@get_warehouse
def get_sellers_list(request, warehouse):
    misc_types = ['raisepo_terms_conditions', 'po_types', 'inco_terms']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    get_ship_add_users = [warehouse]
    ship_address_details = []
    ship_address_names = []
    user_ship_address = UserAddresses.objects.filter(user_id__in=get_ship_add_users)
    if user_ship_address:
        for data in user_ship_address:
            ship_address_names.append(str(data.shipping_address_id) + ' - ' + data.address_name)
            ship_address_details.append({
                'title':str(data.shipping_address_id) + ' - ' + data.address_name,
                'addr_name':data.user_name,
                'mobile_number':data.mobile_number,
                'pincode':data.pincode,
                'address':data.address
                })
    seller_list = []
    seller_supplier = {}
    user_list = get_all_warehouses(warehouse)
    sku_master, sku_master_ids = get_sku_master(warehouse)
    kc_catgs = list(sku_master.exclude(sku_category='').values_list('sku_category', flat=True).distinct())
    prod_catg_map = OrderedDict((
                ('Kits&Consumables', kc_catgs),
            ))
    po_types_str = misc_dict.get('po_types', '')
    if po_types_str and po_types_str not in ['', 'false', False, None, 'null']:
        po_types_list = po_types_str.split(',')
    else:
        po_types_list = []
    inco_terms_str = misc_dict.get('inco_terms', '')
    inco_terms = []
    if inco_terms_str and inco_terms_str not in ['', 'false', False, None, 'null']:
        inco_terms = inco_terms_str.split(',')
    base_currency = warehouse.userprofile.base_currency
    sales_return_attributes = get_user_attributes(warehouse,'sales_return')

    manifest_attributes = get_user_attributes(warehouse,'manifest')

    # PO Extra Fields
    po_attributes = get_user_attributes(warehouse, 'purchase_order')
    wms_fields = list(po_attributes.filter(attribute_type='WMS Field').values_list('attribute_values', flat=True))
    wms_fields_dict = get_wms_field_values_dict(wms_fields, warehouse.id)
    #(CHECK_NOW)
    # return_types = get_return_types(user)
    return_types = []

    return HttpResponse(json.dumps({
        'sellers': seller_list, 'tax': 5.5, 'receipt_types': PO_RECEIPT_TYPES,
        'shipment_add_names':ship_address_names,
        'seller_supplier_map': seller_supplier, 'warehouse' : user_list,
        'raise_po_terms_conditions' : '', 'po_types': po_types_list,
        'shipment_addresses' : ship_address_details, 'prodcatg_map': prod_catg_map, 'inco_terms': inco_terms,
        'warehouse_currency': base_currency, 'po_attributes': list(po_attributes),
        'wms_fields_dict': wms_fields_dict, 'return_types': return_types,
        'manifest_attributes' : list(manifest_attributes),
        'sales_return_attributes' : list(sales_return_attributes)
        }))

@get_warehouse
def last_transaction_details(request, warehouse):
    get_supplier_each_seller_list = []
    check_supplier = ''
    seller_id = 0
    wms_code_list = request.POST.getlist('wms_code', [])
    supplier_name_list = request.POST.getlist('supplier_id', [])
    sku_wise_obj = PurchaseOrder.objects.filter(
        open_po__sku__wms_code__in=wms_code_list, open_po__sku__user=warehouse.id).order_by('-creation_date')
    if check_supplier:
        supplier_wise_obj = sku_wise_obj.filter(
            open_po__supplier__name__in = get_supplier_each_seller_list).order_by('-creation_date')
    else:
        supplier_wise_obj = sku_wise_obj.filter(
            open_po__supplier__name__in = supplier_name_list).order_by('-creation_date')
    sku_wise_list = []
    supplier_wise_list = []
    for data in sku_wise_obj[0:3]:
        data_dict = {}
        data_dict['total_tax'] = data.open_po.sgst_tax + data.open_po.igst_tax + data.open_po.cess_tax + data.open_po.cgst_tax + data.open_po.utgst_tax
        data_dict['po_date'] = str(data.po_date)
        data_dict['supplier_name'] = data.open_po.supplier.name
        data_dict['received_quantity'] = data.received_quantity
        data_dict['price'] = data.open_po.price
        data_dict['po_number'] = data.po_number #get_po_reference(data)
        sku_wise_list.append(data_dict)
    
    for data in supplier_wise_obj[0:3]:
        supplier_data_dict = {}
        supplier_data_dict['total_tax'] = data.open_po.sgst_tax + data.open_po.igst_tax + data.open_po.cess_tax + data.open_po.cgst_tax + data.open_po.utgst_tax
        supplier_data_dict['po_date'] = str(data.po_date)
        supplier_data_dict['supplier_name'] = data.open_po.supplier.name
        supplier_data_dict['received_quantity'] = data.received_quantity
        supplier_data_dict['price'] = data.open_po.price
        supplier_data_dict['po_number'] = data.po_number #get_po_reference(data)
        supplier_wise_list.append(supplier_data_dict)
    data_resp = {}
    data_resp['sku_wise_table_data'] = sku_wise_list
    data_resp['wms_code_list'] = wms_code_list
    data_resp['supplier_wise_table_data'] = supplier_wise_list
    return HttpResponse(json.dumps(data_resp))

@get_warehouse
def get_mapping_values(request, warehouse):
    wms_code = request.GET['wms_code']
    supplier_id = request.GET['supplier_id']
    data = get_mapping_values_po(wms_code ,supplier_id, warehouse)
    return HttpResponse(json.dumps(data), content_type='application/json')

@get_warehouse
def check_generated_label(request, warehouse):
    status = {}
    order_id = request.GET.get('order_id', '')
    label = request.GET.get('label', '')
    log.info('Request params for Check Labels for ' + warehouse.username + ' is ' + str(request.GET.dict()))
    try:
        if order_id and label:
            po_labels = POLabels.objects.filter(sku__user=warehouse.id, label=label)
            if not po_labels:
                status = {'message': 'Invalid Serial Number', 'data': {}}
            elif po_labels[0].purchase_order and not int(po_labels[0].purchase_order.order_id) == int(order_id):
                status = {'message': 'Serial Number is mapped with PO Number ' + get_po_reference(po_labels[0].purchase_order), 'data': {}}
            elif po_labels[0].job_order and not int(po_labels[0].job_order.job_code) == int(order_id):
                status = {'message': 'Serial Number is mapped with JO Number ' + \
                                     str(po_labels[0].job_order.job_code), 'data': {}}
            elif int(po_labels[0].status) == 0:
                if po_labels[0].purchase_order:
                    status = {
                        'message': 'Serial Number already mapped with ' + get_po_reference(po_labels[0].purchase_order),
                        'data': {}}
                elif po_labels[0].job_order:
                    status = {
                        'message': 'Serial Number already mapped with ' + str(po_labels[0].job_order.job_code),
                        'data': {}}
            else:
                po_label = po_labels[0]
                data = {'sku_code': po_label.sku.sku_code, 'label': po_label.label}
                status = {'message': 'Success', 'data': data}
        else:
            status = {'message': 'Missing required parameters', 'data': {}}
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info("Check Labels failed for params " + str(request.GET.dict()) + " and error statement is " + str(e))
        status = {'message': 'Check Labels Failed', 'data': {}}

    return HttpResponse(json.dumps(status))

def get_sku_wise_intransit_quantity_df(sku_ids):
    ''' Returns SKU ID wise intransit quantity '''
    po_intransit_data = pd.DataFrame(
        PurchaseOrder.objects.exclude(status__in=['location-assigned', 'confirmed-putaway']
            ).filter(open_po__sku__id__in=sku_ids
            ).values('open_po__sku__id', 'open_po__delivery_date'
            ).annotate(total_ordered=Sum('open_po__order_quantity'), total_received=Sum('received_quantity')
            ).annotate(intransit_qty=F('total_ordered') - F('total_received')
            ).values('open_po__sku__id', 'open_po__delivery_date', 'intransit_qty'))

    if po_intransit_data.empty:
        po_intransit_data = pd.DataFrame(columns=['open_po__sku__id', 'open_po__delivery_date', 'intransit_qty'])
    
    po_intransit_data = po_intransit_data.rename(columns={
        'open_po__sku__id': 'sku_id', 
        'open_po__delivery_date': 'delivery_date'
    })
    return po_intransit_data

def save_or_update_po_extra_fields(warehouse_id, extra_fields, po_number='', receipt_no='', field_type='po_field'):
    ''' Save PO/GRN extra fields '''
    warehouse = User.objects.get(id=warehouse_id)
    insertion_list, updation_list = [], []
    for field, value in extra_fields.items():
        if value not in ['', None]:
            fields_data = Pofields.objects.filter(
                user=warehouse_id, name=field, po_number=po_number,
                receipt_no=receipt_no, field_type=field_type
                )
            if fields_data.exists() and fields_data[0].value != value:
                fields_data = fields_data[0]
                fields_data.value = value
                updation_list.append(fields_data)
            else:
                #saving grn_number in receipt_no incase of grn extra fields
                data_dict = {
                    'user': warehouse_id,
                    'po_number': po_number,
                    'receipt_no': receipt_no,
                    'name': field,
                    'value': value,
                    'field_type': field_type,
                    'account_id' : warehouse.userprofile.id,
                }
                insertion_list.append(Pofields(**data_dict))
    if insertion_list:
        Pofields.objects.bulk_create_with_rounding(insertion_list)
    if updation_list:
        Pofields.objects.bulk_update_with_rounding(updation_list, ['value', 'updation_date'])

def get_asn_grn_custom_attributes_value(filter_dict):
    custom_attribute_data = Pofields.objects.filter(**filter_dict).values('receipt_no', 'name', 'value')
    custom_attribute_dict = {}
    for data in custom_attribute_data:
        custom_attribute_dict.setdefault(data.get('receipt_no', ''), {})[data.get('name', '')] = data.get('value', '')
    return custom_attribute_dict
    

def get_master_doc_details(request, warehouses, reference_numbers, reference_type):
    master_docs_filter = {
        'master_id__in': reference_numbers,
        'master_type': reference_type,
        'user_id__in': warehouses,
    }
    master_docs = get_uploaded_files_data(request, master_docs_filter, return_type='list')
    uploaded_files = {}
    for each_file in master_docs:
        each_file['file_type']  = DEFAULT_FILE_UPLOAD_NAMES.get(each_file['extra_flag'], '')
        master_id = each_file.get('master_id', '')
        if not uploaded_files.get(master_id):
            uploaded_files[master_id] = []

        uploaded_files[master_id].append(each_file)
    return uploaded_files

def frame_po_header_data(po_header_dict, po_obj, warehouse):
    po_number = po_obj.po_number
    if po_number in po_header_dict:
        return po_header_dict
    po_header_dict[po_number] = POHeader(**{
        'account_id': warehouse.userprofile.id,
        'warehouse_id': warehouse.id,
        'po_number': po_number,
        'po_reference': po_obj.open_po.po_name,
        'supplier_id': po_obj.open_po.supplier_id,
        'po_type': po_obj.po_type,
        'status': 'Open',
        'expected_date': po_obj.expected_date
    })
    return po_header_dict

def resolve_final_status(statuses):
    """Resolve the final status based on the given logic."""
    if statuses == {"Open"} or statuses == {"Cancelled"} or statuses == {"Closed"}:
        return list(statuses)[0]
    elif statuses.issubset({"Open", "Cancelled"}) and "Open" in statuses:
        return "Open"
    elif "Partially Received" in statuses:
        return "Partially Received"
    elif statuses.issubset({"Received"}):
        return "Received"
    elif statuses.issubset({"Received", "Partially Received - Closed", "Cancelled", "Closed"}):
        return "Partially Received - Closed"
    return "Partially Received"

def update_po_header(po_numbers, warehouse_id):
    """
    Update or create POHeader statuses for multiple order references.
    If created=True, all records are created with status='Open'.
    If created=False, existing records are updated with bulk_update.
    """
    initial_data = (
        PurchaseOrder.objects.select_related('open_po').filter(
            po_number__in=po_numbers, open_po__sku__user=warehouse_id
        )
        .annotate(
            total_order_quantity=Sum('open_po__order_quantity'),
            total_received_quantity=Sum('received_quantity'),
            total_cancelled_quantity=Sum('cancelled_quantity'),
            total_processed_quantity=F('total_cancelled_quantity') + F('total_received_quantity'),
            derived_status=Case(
                When(status='confirmed-putaway', then=Value('Closed')),
                When(
                    status='grn-generated',
                    then=Case(
                        When(total_received_quantity__gte=F('total_order_quantity'), then=Value('Received')),
                        When(total_processed_quantity__gte=F('total_order_quantity'), then=Value('Partially Received - Closed')),
                        default=Value('Partially Received'),
                    ),
                ),
                When(status='location-assigned', then=Value('Cancelled')),
                default=Value('Open'),
                output_field=CharField(),
            ),
        )
        .values('po_number', 'open_po__po_name', 'open_po__supplier_id', 'derived_status')
    )

    # Group data and resolve statuses
    po_data_dict = {}
    for row in initial_data:
        po_number = row['po_number']
        if po_number not in po_data_dict:
            po_data_dict[po_number] = {
                'po_reference': row['open_po__po_name'],
                'supplier_id': row['open_po__supplier_id'],
                'po_status_list': set(),  # Use a set for unique statuses
            }
        po_data_dict[po_number]['po_status_list'].add(row['derived_status'])
    
    create_or_update_po_headers(po_data_dict, warehouse_id)

def create_or_update_po_headers(po_data_dict, warehouse_id):
    # Retrieve existing records
    existing_headers = POHeader.objects.filter(
        po_number__in=po_data_dict.keys(),
        warehouse_id=warehouse_id
    )
    account_id = None
    existing_headers_map = {header.po_number: header for header in existing_headers}
    # Prepare updates and creations
    po_headers_to_update, po_headers_to_create = [], []
    for po_number, data in po_data_dict.items():
        resolved_status = resolve_final_status(data['po_status_list'])
        if po_number in existing_headers_map:
            header = existing_headers_map[po_number]
            header.status = resolved_status
            po_headers_to_update.append(header)
        else:
            if not account_id:
                account_id = UserProfile.objects.get(user_id=warehouse_id).id
            # Handle new records (unlikely in update flow)
            po_headers_to_create.append(POHeader(
                account_id=account_id,
                po_number=po_number,
                po_reference=data['po_reference'],
                supplier_id=data['supplier_id'],
                warehouse_id=warehouse_id,
                status=resolved_status,
            ))

    chunk_size = 100
    for i in range(0, len(po_headers_to_update), chunk_size):
        chunk_po_updates = po_headers_to_update[i:i + chunk_size]
        POHeader.objects.bulk_update_with_rounding(chunk_po_updates, ['status'])
    for i in range(0, len(po_headers_to_create), chunk_size):
        chunk_po_creates = po_headers_to_create[i:i + chunk_size]
        POHeader.objects.bulk_create_with_rounding(chunk_po_creates)

def get_po_header_status_from_po_header_table(warehouse_id, po_numbers=[], filters=[], order_by=[]):
    """
    Fetch PO header status from POHeader table for a list of PO numbers.

    Args:
        po_numbers (list): List of PO numbers to fetch status for
        warehouse_id (int): Warehouse ID

    Returns:
        dict: Dictionary with PO number as key and status as value
    """

    if isinstance(po_numbers, str):
        po_numbers = [po_numbers]

    if isinstance(warehouse_id, list):
        warehouse_id = warehouse_id[0]

    filter_dict = {}
    if filters:
        filter_dict = {'status__in': filters}

    if po_numbers:
        filter_dict['po_number__in'] = po_numbers

    # Query POHeader table for the given PO numbers and warehouse
    po_headers = POHeader.objects.filter(
        warehouse_id=warehouse_id, **filter_dict
    ).order_by(*order_by).values('po_number', 'status')

    # Create a dictionary with PO number as key and status as value
    po_status_dict = {}
    for header in po_headers:
        po_number = header['po_number']
        status = header['status']
        po_status_dict[po_number] = status

    # For any PO numbers not found in the table, set status as 'Open'
    for po_number in po_numbers:
        if po_number not in po_status_dict:
            po_status_dict[po_number] = 'Open'
    
    return po_status_dict
