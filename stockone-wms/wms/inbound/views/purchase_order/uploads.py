import json
import datetime
import copy
import itertools
import math
import pytz
from dateutil import parser
from collections import OrderedDict
from xlrd import xldate_as_tuple

from django.http import HttpResponse
from django.template import loader
from django.db.models import Q
from django.test.client import RequestFactory

#Model Imports
from wms_base.models import User, UserProfile, UserAddresses
from core.models import (
    SKUMaster, TaxMaster, CurrencyExchangeMaster, MasterEmailMapping,
    UserAttributes
)
from inbound.models import (
    PurchaseOrder, OpenPO, SupplierMaster, PaymentTerms,
    SKUSupplier, PendingPO, POHeader
    )
from outbound.models import CustomerMaster

#Method Imports
from wms_base.wms_utils import (
    get_permission, SUMMARY_INTER_STATE_STATUS, PO_DATA,
    ISO_COMPANY_LOGO_PATHS, COMPANY_LOGO_PATHS, LEFT_SIDE_COMPNAY_LOGO
    )
from core_operations.views.common.main import (
    get_warehouse,
    get_multiple_misc_values, get_user_attributes, 
    get_local_date_known_timezone, get_local_date,
    get_misc_value, get_sku_weight, number_in_words,
    get_user_prefix_incremental, get_sku_ean_list,
    get_purchase_company_address, get_po_company_logo,
    generate_log_message, get_user_time_zone
    )

from core_operations.views.common.user_attributes import validate_attributes
from inbound.views.common.mail_pdf import write_and_mail_pdf
from inbound.views.purchase_order.common import get_total_amount_from_skus, frame_po_header_data
from inbound.views.purchase_order.create_po import get_filtered_sku_pack_data
from inbound.views.common.constants import DEFAULT_DATETIME_FORMAT
from inbound.views.common.common import get_max_pack_uom_quantity

from .pending_po import send_purchase_order_for_approval, save_or_update_po_extra_fields
from .common import (
    check_margin_percentage, newFindReqConfigName, get_mapping_values_po,
    tax_calculation_master, check_purchase_order_created, get_total_amount_from_skus
    )
from .constants import PO_SUGGESTIONS_DATA, PURCHASE_ORDER_UPLOAD_MAPPING
from .integrations import po_call_back_3p_integration


from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/po_upload' + today + '.log')
log_err = init_logger('logs/po_upload.log')

#define PO limit
po_limit = 500


def purchase_order_form(warehouse, extra_params={}):
    excel_headers = copy.deepcopy(PURCHASE_ORDER_UPLOAD_MAPPING)
    po_extra_fields = dict(UserAttributes.objects.filter(
        user=warehouse.id, attribute_model='purchase_order', status=1).values_list('attribute_name', 'is_mandatory'))
    if po_extra_fields:
        extra_headers = OrderedDict()
        for field, is_mandatory in po_extra_fields.items():
            key = field
            value = 'attr - ' + key
            if is_mandatory:
                value += '*'
            extra_headers[key] = value
        excel_headers.update(extra_headers)
    userprofile = warehouse.userprofile
    if not userprofile.user_type == 'marketplace_user' and "Seller ID" in excel_headers:
        del excel_headers["Seller ID"]
    return excel_headers

def purchase_approval_excel_upload(request, user, data_list):
    """upload purchase order for approval from excel"""
    failed_status_dict = {}
    for data_dict in data_list:
        status, po_number, rendered = \
            send_purchase_order_for_approval(request, user, data_dict, source='UPLOAD')
        if status:
            failed_status_dict[po_number] = status

def purchase_order_upload(request_user, warehouse, data_to_integrate, extra_params={}):
    """upload purchase order from excel to db"""
    misc_types = ["purchase_order_preview", "enable_pending_approval_pos"]
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    purchase_order_view = misc_dict.get('purchase_order_preview', 'false')
    send_for_approval = False
    if misc_dict.get('enable_pending_approval_pos', 'false') == 'true':
        send_for_approval = get_permission(request_user, 'add_pendingpo')
        if not send_for_approval:
            return HttpResponse('No permission to send for approval')

    status, data_list, pending_po_data_list = validate_purchase_order(warehouse, data_to_integrate, send_for_approval)
    if status != 'Success':
        return data_list

    base_url = extra_params['base_url']
    request = RequestFactory
    request.user= request_user
    request.warehouse = warehouse
    request.method= 'GET'
    request.body = json.dumps({'data':data_list})
    # request.META['HTTP_ORIGIN'] = base_url
    request.GET = {'base_url' : base_url}
    if purchase_order_view == 'true':
        content = purchase_order_preview_generation(request, warehouse, data_list, pending_po_data_list)
        return HttpResponse(json.dumps(content))
    if send_for_approval:
        purchase_approval_excel_upload(request, warehouse, pending_po_data_list)
    else:
        purchase_order_excel_upload(request, warehouse, data_list)
    return "Success"


def fetch_supplier_and_sku_codes_from_data(data_to_integrate):
    """fetch supplier and sku codes from data"""
    sku_codes = []
    all_suppliers = []
    po_types = []
    for data_row in data_to_integrate:
        for key, value in data_row.items():
            if key == 'Supplier ID*':
                all_suppliers.append(value)
            elif key == 'SKU Code*':
                sku_codes.append(value)
            elif key == 'PO Type':
                po_types.append(value.lower())

    return sku_codes, all_suppliers, po_types

def find_exceeded_po_limits(data_to_integrate):
    """find exceeded po limits from data"""
    excel_data = []
    for data_row in data_to_integrate:
        row_data = []
        for key, value in data_row.items():
            row_data.append(value)
        excel_data.append(row_data)
    key_func = lambda x: (x[0],x[1],x[2],x[4])

    limit_exceeded_list = []
    for key, group in itertools.groupby(excel_data, key_func):
        if len(list(group)) > po_limit:
            limit_exceeded_list.append(key)

    return limit_exceeded_list

def get_validate_limit_exceeding_check(cell_data, data_dict, error_list, limit_exceeded_list):
    """validate limit exceeding check"""
    exceed_check = False
    for each_po_type, po_name, po_date, supplier_id in limit_exceeded_list:
        if isinstance(supplier_id, (int, float)):
            supplier_id = str(int(supplier_id))
        if supplier_id == cell_data:
            if isinstance(po_name, (int, float)):
                po_name = str(int(cell_data))
            if isinstance(po_date, float):
                year, month, day, hour, minute, second = xldate_as_tuple(po_date, 0)
                strp_po_date = str(datetime.datetime(year, month, day, hour, minute, second))
            else:
                strp_po_date = str(datetime.datetime.strptime(po_date, "%m-%d-%Y"))
            # Exceed Error Check
            if data_dict['po_name'] == po_name and data_dict['po_date'] == strp_po_date and data_dict.get('po_type', '') == each_po_type:
                exceed_check = True  # Exceeded limit found
    if exceed_check:
        error_list.append("Cannot Raise More than %s SKUs to Supplier in a PurchaseOrder." % po_limit)

    return error_list

def validate_supplier_field(warehouse, cell_data, demo_data, user_profile, error_list,data_dict, limit_exceeded_list,timezone, base_currency):
    """validate supplier field"""
    supplier = None
    if isinstance(cell_data, (int, float)):
        cell_data = str(int(cell_data))
    if demo_data:
        cell_data = user_profile.prefix + '_' + cell_data
    if not cell_data:
        error_list.append('Supplier ID is Mandatory')
        return error_list, data_dict, supplier

    supplier = SupplierMaster.objects.filter(user=warehouse.id, supplier_id=cell_data, status=1)
    if not supplier:
        error_list.append("Supplier ID doesn't exist")
        return error_list, data_dict, supplier

    #PO Reference Exists Check
    po_reference = data_dict.get('po_name','')
    if po_reference:
        po_exists_filter = {
            'open_po__po_name' : po_reference,
            'open_po__sku__user' : warehouse.id
            }
        pending_po_exists_filter = {
            'po_name' : po_reference,
            'wh_user_id' : warehouse.id,
        }
        po_name_exists_check = PurchaseOrder.objects.filter(**po_exists_filter)
        po_name_exists_check_pending_po = PendingPO.objects.filter(**pending_po_exists_filter).exclude(final_status__in=['saved', 'reverted'])
        open_po_exists_check = OpenPO.objects.filter(po_name=po_reference, sku__user=warehouse.id, purchaseorder__isnull=True)
        if po_name_exists_check.exists() or po_name_exists_check_pending_po.exists() or open_po_exists_check.exists():
            error_list.append('%s PO Reference already exists' %(po_reference))
    po_type = data_dict.get('po_type', '')
    if supplier[0].supplier_type == 'import' and po_type.lower() != 'import':
        error_list.append("PO type Doesn\'t match with Supplier Type Import")
    elif supplier[0].supplier_type != 'import' and po_type.lower() == 'import':
        error_list.append("Supplier Doesn\'t match with PO Type Import")
    elif supplier[0].supplier_type == 'import' and po_type.lower() == 'import':
        po_date = ''
        if data_dict.get('po_date', ''):
            po_date = datetime.datetime.strptime(data_dict.get('po_date', ''), DEFAULT_DATETIME_FORMAT).date()
        if not po_date:
            po_date = datetime.datetime.now().date()
        supplier_currency = supplier[0].currency_code if supplier[0].currency_code else base_currency
        currency_exchange = CurrencyExchangeMaster.objects.filter(warehouse_id=warehouse.id, from_currency=supplier_currency, to_currency=base_currency, start_date__lte=po_date)
        if currency_exchange.exists():
            if currency_exchange.last().end_date:
                end_date = get_local_date_known_timezone(timezone, currency_exchange.last().end_date, send_date=True).date()
                if not po_date <= end_date:
                    error_list.append("Currency Exchange is not defined for this supplier on this date")
        else:
            error_list.append("Currency Exchange is not defined for this supplier on this date")

    # PO Type, PO Reference, Supplier and PO Date limit Exceeding check
    error_list = get_validate_limit_exceeding_check(cell_data, data_dict, error_list, limit_exceeded_list)

    data_dict['supplier_id'] = supplier[0].id
    # ep_supplier = int(supplier[0].ep_supplier)
    data_dict['supplier_tax_type'] = supplier[0].tax_type
    data_dict['supplier'] = supplier[0].supplier_id

    return error_list, data_dict, supplier

def validate_date_fields(cell_data, data_dict, key, error_list, mapping_fields, timezone):
    """
    Validate and process date fields.
    """
    if not cell_data:
        error_list.append(f'{mapping_fields[key]} is Mandatory')
        return error_list, data_dict

    parsed_date = ''
    try:
        if isinstance(cell_data, float):
            date_tuple = xldate_as_tuple(cell_data, 0)
            parsed_date = datetime.datetime(*date_tuple)
        elif '-' in cell_data:
            parsed_date = parser.parse(cell_data)
            if parsed_date.time() == datetime.time(0, 0):
                if key == "po_delivery_date":
                    parsed_date = parsed_date.replace(hour=23, minute=59, second=59)
                else:
                    parsed_date = parsed_date.replace(hour=0, minute=0, second=0)
            data_dict[key] = str(parsed_date)
            if key == 'po_delivery_date':
                if data_dict.get('po_date') and parsed_date < datetime.datetime.strptime(data_dict['po_date'], '%Y-%m-%d %H:%M:%S'):
                    error_list.append('Delivery Date should be greater than PO Date')

        else:
            raise ValueError("Invalid date format")

    except (ValueError, TypeError):
        error_list.append(f'Invalid Date Format for {mapping_fields[key]}')
    data_dict[key] = str(parsed_date)
    return error_list, data_dict

def fetch_sku_code_from_supplier_sku_code(warehouse, cell_data, error_list, supplier, supplier_code):
    """Ferch sku code from supplier sku code"""
    if not cell_data and not supplier_code:
        error_list.append("Either the SKU code or the supplier's SKU code is required.")
    elif supplier and supplier_code:
        sup_sku = SKUSupplier.objects.filter(
                supplier_id=supplier[0].id, supplier_code=supplier_code, sku__user=warehouse.id
                ).values('sku__sku_code')
        if not sup_sku:
            error_list.append("Supplier SKU Code Doesn't exist")
        elif cell_data and sup_sku[0].get('sku__sku_code') != cell_data:
            error_list.append("Supplier SKU Code doesn't match with SKU Code")
        else:
            cell_data = sup_sku[0].get('sku__sku_code')

    return error_list, cell_data

def validate_wms_fields(warehouse, cell_data, error_list, data_dict, extra_params):
    """validate wms fields"""
    margin_check = extra_params.get('margin_check', False)
    supplier = extra_params.get('supplier', None)
    data_row = extra_params.get('data_row', {})
    misc_dict = extra_params.get('misc_dict', {})
    sku_pack_mappping = extra_params.get('sku_pack_mappping', {})
    sku_expense_check = extra_params.get('sku_expense_check', False)
    sku_non_expense_check = extra_params.get('sku_non_expense_check', False)
    po_types = extra_params.get('po_types', [])
    sales_uom_dict = extra_params.get('sales_uom_dict', {})
    measurement_units = extra_params.get('measurement_units', {})
    dest_user = extra_params.get('dest_user', None)
    dest_sku_data = extra_params.get('dest_sku_data', {})
    pack_id = cell_data
    mrp = 0
    ep_supplier = 0
    supplier_code = data_row.get('Supplier SKU Code', '')
    error_list, cell_data = fetch_sku_code_from_supplier_sku_code(warehouse, cell_data, error_list, supplier, supplier_code)
    if cell_data:
        if isinstance(cell_data, (int, float)):
            cell_data = str(int(cell_data))
        sku_master = SKUMaster.objects.filter(wms_code=cell_data, user=warehouse.id)
        if not sku_master:
            error_list.append("WMS Code doesn't exist")
        else:
            if not ep_supplier:
                if sku_master[0].block_options == 'PO':
                    error_list.append("WMS Code is blocked for PO")
            if  margin_check and sku_master and supplier :
                status = check_margin_percentage(sku_master[0].id, supplier[0].id, warehouse)
                if status:
                    error_list.append(status)
            if sku_master[0].sku_type == 'Expense':
                sku_expense_check = True
            else:
                sku_non_expense_check = True
            if sku_expense_check and sku_non_expense_check:
                error_list.append("Cannot Raise Both Expense and Non Expense SKUs in PO")

            #validating if pack id is available in destination warehouse for stocktransfer orders
            sku_code = sku_master[0].sku_code
            pack_data = sku_pack_mappping.get(sku_code, {})
            purchase_uom = misc_dict.get('enable_purchase_uom','false')

            if pack_data:
                if pack_id in pack_data:
                    pack_id = pack_id
                else:
                    max_value = max(pack_data.values())
                    pack_id = [k for k, v in pack_data.items() if v == max_value][0]
                pack_quantity = pack_data.get(pack_id,1)
            else:
                pack_id = ''
                pack_quantity = 1
            
            if purchase_uom == 'true' and measurement_units and 'stocktransfer' in po_types and not sales_uom_dict.get(sku_code, {}).get(pack_id,''):
                error_list.append(f"Mentioned pack id for {sku_code} is not available in destination warehouse")
            if 'stocktransfer' in po_types:
                if not dest_sku_data.get(sku_code,{}):
                    error_list.append(f"SKU {sku_code} is not available in destination warehouse")
                else:
                    if dest_sku_data[sku_code].get('batch_based',0) != sku_master[0].batch_based:
                        error_list.append(f"Batch based attribute mismatch for {sku_code} with destination warehouse")
                    if dest_sku_data[sku_code].get('enable_serial_based',0) != sku_master[0].enable_serial_based:
                        error_list.append(f"Serial based attribute mismatch for {sku_code} with destination warehouse")

            data_dict['sku_id'] = sku_master[0].id
            data_dict['wms_code'] = sku_code
            data_dict['sku_product_type'] = sku_master[0].product_type
            data_dict['measurement_unit'] = pack_id or sku_master[0].measurement_type
            data_dict['base_uom'] = sku_master[0].measurement_type
            data_dict['pcf'] = pack_quantity
            data_dict['batch_based'] = sku_master[0].batch_based
            data_dict['enable_serial_based'] = sku_master[0].enable_serial_based
            data_dict['pack_id'] = pack_id
            mrp = sku_master[0].mrp

    return error_list, data_dict, mrp

def validate_quantity_field(cell_data, data_dict, error_list, key):
    """Validate quantity field."""
    if cell_data:
        try:
            quantity = float(cell_data)
            if quantity < 0:
                error_list.append('Negative quantity is not allowed')
            else:
                data_dict[key] = quantity
        except ValueError:
            error_list.append('Quantity should be a number')
    else:
        error_list.append('Missing Quantity')
    
    return error_list, data_dict

def validate_free_quantity_field(cell_data, data_dict, error_list, key):
    """validate free quantity field"""
    if cell_data:
        try:
            quantity = float(cell_data)
            if quantity < 0:
                error_list.append('Negative Free quantity is not allowed')
            else:
                data_dict[key] = quantity
        except ValueError:
            error_list.append('Free Quantity should be a number')
    else:
        data_dict[key] = 0

    return error_list, data_dict

def validate_price_field(warehouse, cell_data, data_dict, error_list, key, misc_dict):
    """validate price field"""
    if cell_data != '':
        if not isinstance(cell_data, (int, float)):
            try:
                if float(cell_data):
                    data_dict[key] = float(cell_data)
            except:
                error_list.append('Unit Price should be a number')
        else:
            data_dict[key] = float(cell_data)
    else:
        data_dict[key] = ''
        if data_dict.get('sku_id', '') and data_dict.get('supplier_id', ''):
            sku_supplier = SKUSupplier.objects.filter(sku_id=data_dict['sku_id'], supplier_id=data_dict['supplier_id'], sku__user=warehouse.id)
            if not sku_supplier.exists() :
                mandate_supplier = misc_dict.get('mandate_sku_supplier', 'false')
                if mandate_supplier == 'true':
                    error_list.append('Please Create Sku Supplier Mapping')

    return error_list, data_dict

def validate_char_field(cell_data, data_dict, key):
    """validate po name field"""
    if isinstance(cell_data, (int, float)):
        cell_data = str(int(cell_data))
    data_dict[key] = cell_data
    
    return data_dict

def validate_address_field(warehouse, cell_data, data_dict, error_list):
    """validate address field"""
    # If Address Title is passed, getting Address of corresponding ID
    if cell_data:
        user_address = UserAddresses.objects.filter(address_name=cell_data.lower(), user=warehouse.id)
        if user_address.exists():
            user_address = user_address[0]
            data_dict['ship_to'] = '%s %s, %s' % (user_address.address_name, user_address.address, user_address.pincode)
        else:
            error_list.append('Invalid Address Title')
    return error_list, data_dict

def validate_ship_to_field(warehouse, cell_data, data_dict, error_list, key, final_data_list):
    """validate ship to field"""
    if cell_data:
        data_dict[key] = cell_data
    else:
        if final_data_list and final_data_list[-1].get('ship_to'):
            data_dict[key] = final_data_list[-1].get('ship_to', '')
        else:
            user_address = UserAddresses.objects.filter(user=warehouse.id)
            if user_address.exists():
                user_address = user_address[0]
                data_dict[key] = '%s %s, %s' % (user_address.address_name, user_address.address, user_address.pincode)
            else:
                error_list.append('No Address found, please add Address in Profile')
    return error_list, data_dict

def validate_mrp_field(cell_data, data_dict, error_list, key, mrp, mrp_check, allow_unitprice_gt_mrp):
    """validate mrp field"""
    if cell_data == '':
        cell_data = mrp
    if not isinstance(cell_data, (int, float)):
        try:
            if float(cell_data):
                cell_data = float(cell_data)
                if allow_unitprice_gt_mrp != 'true' and mrp_check == 'true' and data_dict.get('price') and data_dict.get('price') > cell_data:
                    error_list.append('Unit Price cannot be greater than MRP')
                else:
                    data_dict[key] = cell_data
        except Exception:
            error_list.append('MRP should be a number')
        

    return error_list, data_dict

def validate_payment_terms_field(cell_data, data_dict, error_list, supplier_payment_terms_dict):
    """validate payment terms field"""
    if cell_data and cell_data not in supplier_payment_terms_dict.get(data_dict.get('supplier'), []):
        error_list.append('Payment Terms not mapped to %s Supplier' % (data_dict['supplier']))
    else:
        data_dict['supplier_payment_terms'] = cell_data

    return error_list, data_dict

def validate_numeric_field(cell_data, data_dict, error_list, key, number_fields, mapping_fields):
    """validate numeric field"""
    if key in number_fields:
        try:
            cell_data = float(cell_data)
            data_dict[key] = cell_data
        except Exception:
            error_list.append('%s is Number Field' % mapping_fields[key])
    else:
        data_dict[key] = cell_data

    return error_list, data_dict

def validate_non_tax_fields(cell_data, data_dict, key, number_fields):
    """validate non tax fields"""
    if key in number_fields and key not in ['cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax']:
        data_dict[key] = 0
    else:
        data_dict[key] = cell_data

    return data_dict

def validate_tax_fields(cgst_tax, sgst_tax, igst_tax, sku_code, error_list, sum_of_taxes):
    """validate tax fields"""
    if cgst_tax == '':
        cgst_tax = 0
    if sgst_tax == '':
        sgst_tax = 0
    if igst_tax == '':
        igst_tax = 0
    if (cgst_tax ==0 and sgst_tax ==0 and igst_tax ==0) or (cgst_tax >0 and sgst_tax>0 and cgst_tax == sgst_tax and igst_tax ==0 and cgst_tax + igst_tax + sgst_tax in sum_of_taxes) \
                        or (igst_tax>0 and cgst_tax ==0 and sgst_tax ==0 and cgst_tax + igst_tax + sgst_tax in sum_of_taxes) :
        pass

    else:
        error_list.append('Multiple taxes have mentioned for sku %s' % (sku_code))

    return error_list, cgst_tax, sgst_tax, igst_tax

def validate_import_po_fields(data_dict, error_list, unique_po_dict, supplier, po_type, po_group_key, misc_dict, cgst_tax, sgst_tax, inco_terms):
    """validate import po fields"""
    if supplier and supplier[0].supplier_type == 'import' and po_type.lower() == 'import':
        if cgst_tax or sgst_tax:
            error_list.append("For import PO, only IGST tax applicable")
        if inco_terms:
            inco_terms = inco_terms.split(',')
            user_inco_terms = misc_dict.get('inco_terms', '').split(',')
            if not set(inco_terms).issubset(user_inco_terms):
                error_list.append("Inco Terms are not mapped to Warehouse - %s" %(','.join([inco for inco in inco_terms if inco not in user_inco_terms])))
            elif po_group_key in unique_po_dict and inco_terms != unique_po_dict[po_group_key]:
                error_list.append("Multiple Inco Terms have mentioned for PO Refernce %s " %(data_dict.get('po_name', '')))
        else:
            error_list.append("Inco Terms are mandatory for import type PO")
        if not error_list:
            data_dict['cgst_tax'] = 0
            data_dict['sgst_tax'] = 0
            data_dict['price'] = ''
            data_dict['inco_terms'] = inco_terms
            unique_po_dict[po_group_key] = inco_terms
    else:
        data_dict['inco_terms'] = []
    
    return error_list, data_dict

def check_for_sku_muliple_rows(data_dict, error_list, unique_row_dict):
    """check for sku multiple rows"""
    unique_row_key = (data_dict.get('supplier_id',''), data_dict.get('po_name', ''), data_dict.get('sku_id',''))
    for data in unique_row_dict.get(unique_row_key, []):
        if data.get('sku_id','')== data_dict.get('sku_id','') and data.get('supplier_id','') == data_dict.get('supplier_id','') and \
            data.get('po_name', '') == data_dict.get('po_name', ''):
            if not data_dict.get('line_reference', ''):
                error_list.append("Line Reference is required for the duplicate row ")
                break
            elif data_dict.get('line_reference', '') == data.get('line_reference', ''):
                error_list.append("Duplicate line reference found for this SKU")
                break
    row_dict = {'sku_id': data_dict.get('sku_id',''), 'supplier_id': data_dict.get('supplier_id',''), 'po_name': data_dict.get('po_name', ''), 'line_reference': data_dict.get('line_reference', '')}
    if unique_row_key in unique_row_dict:
        unique_row_dict[unique_row_key].append(row_dict)
    else:
        unique_row_dict[unique_row_key] = [row_dict]
    return error_list

def validate_extra_fields(warehouse, data_dict, error_list):
    """validate extra fields"""
    if data_dict.get('extra_fields', {}):
        error_messages, data_dict['extra_fields'] = \
            validate_attributes(warehouse, data_dict['extra_fields'], attribute_model='purchase_order')
        for error_message in error_messages:
            error_list.append(error_message)
    return error_list

def prepare_po_approval_data(data_dict, send_for_approval, number_fields, po_approval_data_dict, error_list, po_group_key, po_row_ids_dict, row_index):
    """prepare po approval data"""
    if send_for_approval:
        po_header_keys = ['po_delivery_date', 'ship_to', 'po_name', 'supplier_id', 'inco_terms', 'supplier_payment_terms', 'po_type', 'po_date']
        po_sku_keys = ['wms_code'] + number_fields
        approve_po_header_dict = {po_key:data_dict.get(po_key, '') for po_key in po_header_keys}
        approve_po_header_dict['supplier_id'] = data_dict.get('supplier')
        approve_po_header_dict['po_reference'] = data_dict.get('po_name')
        approve_po_line_dict = {po_key:data_dict.get(po_key, '') for po_key in po_sku_keys}
        approve_po_line_dict['order_quantity'] = data_dict.get('quantity',0)
        approve_po_line_dict['measurement_unit'] = data_dict.get('measurement_unit', data_dict.get('base_uom', ''))
        approve_po_line_dict['base_uom'] = data_dict.get('base_uom', '')
        approve_po_line_dict['pcf'] = data_dict.get('pcf', 1)
        approve_po_line_dict['price'] = data_dict.get('price', 0)
        approve_po_header_dict['items'] = [approve_po_line_dict]
        approve_po_header_dict['extra_fields'] = data_dict.get('extra_fields', {})

        if po_group_key not in po_approval_data_dict:
            po_approval_data_dict[po_group_key] = approve_po_header_dict

        else:
            po_approval_data_dict[po_group_key]['items'].append(approve_po_line_dict)

        # Storing row_id of a PO, to return error status
        if po_group_key not in po_row_ids_dict:
            po_row_ids_dict[po_group_key] = row_index
    return error_list

def prepare_supplier_payment_terms_dict(warehouse, all_suppliers):
    """prepare supplier payment terms dict"""
    supplier_payment_terms_dict = {}
    payment_objs = PaymentTerms.objects.filter(
        supplier__supplier_id__in=all_suppliers, supplier__user=warehouse.id).values('payment_code', 'supplier__supplier_id')
    for each_payment_obj in payment_objs:
        if each_payment_obj['supplier__supplier_id'] not in supplier_payment_terms_dict:
            supplier_payment_terms_dict[each_payment_obj['supplier__supplier_id']] = [each_payment_obj['payment_code']]
        else:
            supplier_payment_terms_dict[each_payment_obj['supplier__supplier_id']].append(each_payment_obj['payment_code'])

    return supplier_payment_terms_dict

def validate_approval_data(warehouse,po_approval_data_dict,error_list, error_data_list, po_row_ids_dict):
    """validate approval data"""
    if po_approval_data_dict and not error_list:
        pending_po_data_list = list(po_approval_data_dict.values())
        for pending_po_data in pending_po_data_list:
            # getting total amt to get approver
            sku_dict = {}
            po_type = pending_po_data.get('po_type', '')
            for ind, sku in enumerate(pending_po_data['items']):
                key = (ind, sku['wms_code'])
                sku_dict[key] = {
                    'quantity': sku['order_quantity'],
                    'price': sku['price'],
                }
            total_amt = get_total_amount_from_skus(warehouse.id, sku_dict, pending_po_data.get('supplier_id'))
            total_amt = math.ceil(total_amt)
            req_config_name, error_status = newFindReqConfigName(warehouse, total_amt, po_type=po_type, purchase_type='PO')
            if error_status:
                po_group_key = (
                    pending_po_data.get('supplier_id', ''), pending_po_data.get('po_type', ''),
                    pending_po_data.get('po_reference', ''), pending_po_data.get('po_date', ''),
                    pending_po_data.get('seller_id', '')
                )
                if po_group_key in po_row_ids_dict:
                    if error_data_list[0].get('Status'):
                        error_data_list[0]['Status'].append(error_status)
                    else:
                        error_data_list[0]['Status'] = [error_status]
    return error_data_list

def get_dest_user_data(dest_user, sku_codes):
    """get dest user data"""
    dest_sku_data = {}
    if dest_user:
        dest_sku_data = SKUMaster.objects.filter(sku_code__in=sku_codes, user=dest_user.id).values('sku_code', 'batch_based', 'enable_serial_based')
        dest_sku_data = {item['sku_code']: item for item in dest_sku_data}
    return dest_sku_data

def validate_purchase_order(warehouse, data_to_integrate, send_for_approval=False, demo_data=False):
    """validate purchase order"""
    error_status, error_data_list = False, []
    pending_po_data_list = []
    purchase_mapping = purchase_order_form(warehouse)
    mrp = 0
    po_approval_data_dict, po_row_ids_dict = {}, {}
    unique_po_dict = {}
    misc_types = ['po_fields', 'enable_margin_price_check', 'mandate_sku_supplier', 'allow_unitprice_gt_mrp',
                  'inco_terms', 'supplier_payment_terms', 'enable_purchase_uom', 'show_mrp_raise_po', 'po_types']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    margin_check = True if misc_dict.get('enable_margin_price_check', False) == 'true' else False
    po_attributes = get_user_attributes(warehouse, 'purchase_order')
    po_fields = dict(po_attributes.values_list('attribute_name', 'is_mandatory'))
    purchase_mapping_dict = dict(zip(purchase_mapping.values(), purchase_mapping.keys()))
    po_types_str = misc_dict.get('po_types', '')
    misc_po_types = []
    if po_types_str:
        misc_po_types = [po_type.strip().lower() for po_type in po_types_str.split(',')]
    if not set(['po_name', 'po_date', 'po_delivery_date', 'supplier_id', 'wms_code', 'quantity', 'price', 'mrp',
                'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'apmc_tax', 'ship_to']).issubset(purchase_mapping_dict.keys()):
        return 'Invalid File', [], []
    mapping_fields = {'po_date': 'PO Date', 'po_delivery_date': 'PO Delivery Date', 'mrp': 'MRP',
                      'cgst_tax': 'CGST Tax', 'sgst_tax': 'SGST Tax', 'igst_tax': 'IGST Tax', 'utgst_tax': 'UTGST Tax',
                      'cess_tax': 'CESS Tax', 'apmc_tax': 'APMC Tax'}
    number_fields = ['mrp', 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'cess_tax', 'apmc_tax']
    user_profile = warehouse.userprofile
    base_currency = user_profile.base_currency
    timezone = timezone = get_user_time_zone(warehouse)

    # PO Type, PO Reference, PO Date and Supplier group by check for PO Limit (500) Entries
    limit_exceeded_list  = find_exceeded_po_limits(data_to_integrate)

    sku_expense_check = False
    sku_non_expense_check = False
    #fetch all supplier and sku codes from data
    sku_codes, all_suppliers, po_types = fetch_supplier_and_sku_codes_from_data(data_to_integrate)
    
    supplier_payment_terms_dict = prepare_supplier_payment_terms_dict(warehouse, all_suppliers)
    supplier = ''
    final_data_list = []
    sales_uom_dict = {}

    #Fetching sku uom details
    mrp_check = misc_dict.get('show_mrp_raise_po', 'false')
    allow_unitprice_gt_mrp = misc_dict.get('allow_unitprice_gt_mrp', 'false')
    if 'stocktransfer' in po_types and all_suppliers:
        dest_user_obj = User.objects.filter(username=all_suppliers[0])
        if dest_user_obj.exists():
            dest_user = dest_user_obj[0]
        else:
            dest_user = None
    else:
        dest_user = None
    sku_pack_mappping, measurement_units = get_max_pack_uom_quantity(warehouse, sku_codes, misc_dict)
    dest_sku_data = {}
    if dest_user:
        sales_uom_dict = get_filtered_sku_pack_data(dest_user, sku_codes, measurement_units, 'sales')
        dest_sku_data = get_dest_user_data(dest_user, sku_codes)
    unique_row_dict = {}
    for row_index, data_row in enumerate(data_to_integrate):
        data_dict, error_list = {}, []
        for map_key, value in data_row.items():
            cell_data = value
            key = purchase_mapping[map_key]
            
            if key == 'supplier_id':
                error_list, data_dict, supplier = validate_supplier_field(
                    warehouse, cell_data, demo_data, user_profile, error_list,
                    data_dict, limit_exceeded_list,timezone, base_currency
                )
            elif key in ['po_date', 'po_delivery_date']:
                error_list, data_dict = validate_date_fields(
                    cell_data, data_dict, key, error_list, mapping_fields, timezone
                )
            elif key == 'wms_code':
                sku_code = cell_data
            
            elif key == 'pack_id':
                extra_params = {
                    'dest_user': dest_user,
                    'dest_sku_data': dest_sku_data,
                    'po_types': po_types,
                    'sales_uom_dict': sales_uom_dict,
                    'measurement_units': measurement_units,
                    'sku_pack_mappping': sku_pack_mappping,
                    'sku_expense_check': sku_expense_check,
                    'sku_non_expense_check': sku_non_expense_check,
                    'data_row': data_row,
                    'misc_dict': misc_dict,
                    'cell_data': cell_data,
                    'margin_check': margin_check,
                    'supplier': supplier
                }
                error_list, data_dict, mrp = validate_wms_fields(
                    warehouse, sku_code, error_list, data_dict, extra_params
                )

            elif key == 'quantity':
                error_list, data_dict = validate_quantity_field(
                    cell_data, data_dict, error_list, key
                )
            elif key == 'free_quantity':
                error_list, data_dict = validate_free_quantity_field(
                    cell_data, data_dict, error_list, key
                )
            elif key == 'price':
                error_list, data_dict = validate_price_field(
                    warehouse, cell_data, data_dict, error_list, key, misc_dict
                )

            elif key in ['po_name']:
                data_dict = validate_char_field(cell_data, data_dict,key)

            elif key == 'address_title':
                error_list, data_dict = validate_address_field(
                    warehouse, cell_data, data_dict, error_list
                )

            elif key == 'ship_to':
                if data_dict.get('ship_to', ''):
                    # If shipping address ID is passed
                    continue
                error_list, data_dict = validate_ship_to_field(
                    warehouse, cell_data, data_dict, error_list, key, final_data_list
                )
            elif key == 'mrp':
                error_list, data_dict = validate_mrp_field(
                    cell_data, data_dict, error_list, key, mrp, mrp_check, allow_unitprice_gt_mrp
                )
            elif key == 'supplier_payment_terms':
                error_list, data_dict = validate_payment_terms_field(
                   cell_data,data_dict, error_list, supplier_payment_terms_dict
                )
            elif key =='line_reference':
                data_dict = validate_char_field(cell_data, data_dict,key)
                error_list = check_for_sku_muliple_rows(data_dict, error_list, unique_row_dict)

            elif purchase_mapping_dict[key] in po_fields.keys():
                data_dict.setdefault('extra_fields', {})
                data_dict['extra_fields'].update({purchase_mapping_dict[key]: cell_data})
            elif key == 'po_type':
                if misc_po_types and cell_data.lower() not in misc_po_types:
                    error_list.append('Invalid PO Type')
            elif cell_data:
                error_list, data_dict = validate_numeric_field(
                    cell_data, data_dict, error_list, key, number_fields, mapping_fields
                )
            elif not cell_data:
                data_dict = validate_non_tax_fields(cell_data, data_dict, key, number_fields)

        cgst_tax = data_dict.get('cgst_tax',0)
        sgst_tax = data_dict.get('sgst_tax',0)
        igst_tax = data_dict.get('igst_tax',0)
        sku_code = data_dict.get('wms_code')
        sum_of_taxes = [5,18,12,28,0]
        error_list, cgst_tax, sgst_tax, igst_tax = validate_tax_fields(
            cgst_tax, sgst_tax, igst_tax, sku_code, error_list, sum_of_taxes
        )
        po_group_key = (data_dict.get('supplier', ''), data_dict.get('po_type', ''), data_dict.get('po_name', ''), data_dict.get('po_date', ''), data_dict.get('seller_id', ''))
        inco_terms = data_dict.get('inco_terms', '')
        error_list, data_dict = validate_import_po_fields(
            data_dict, error_list, unique_po_dict, supplier, data_dict.get('po_type', ''),
            po_group_key, misc_dict, cgst_tax, sgst_tax, inco_terms
        )
        #Check for multiple rows of same sku and supplier
        mrp_check = misc_dict.get('show_mrp_raise_po', 'false')

        #Validate PO Extra Fields
        error_list = validate_extra_fields(warehouse, data_dict, error_list)

        error_list = prepare_po_approval_data(
            data_dict, send_for_approval, number_fields, po_approval_data_dict,
            error_list, po_group_key, po_row_ids_dict, row_index
        )
        if error_list:
            data_row['Status'] = error_list
            error_status = True
        error_data_list.append(data_row)
        final_data_list.append(data_dict)

    error_data_list = validate_approval_data(
        warehouse,po_approval_data_dict,error_list,
        error_data_list, po_row_ids_dict
    )

    if not error_status:
        return 'Success', final_data_list, pending_po_data_list
    return '', error_data_list, pending_po_data_list

def purchase_order_preview_generation(request, warehouse, data_list, pending_po_data_list=[]):
    profile = UserProfile.objects.get(user_id=warehouse.id)
    if profile.industry_type == 'FMCG':
        table_headers = ['WMS Code', 'Supplier Code', 'Desc', 'Qty', 'Unit Price', 'MRP', 'Amt',
                                                  'SGST (%)', 'CGST (%)', 'IGST (%)', 'UTGST (%)', 'CESS (%)', 'APMC (%)', 'Total']
    else:
        table_headers = ['WMS Code', 'Supplier Code', 'Desc', 'Qty', 'Unit Price', 'MRP', 'Amt',
                             'SGST (%)', 'CGST (%)', 'IGST (%)', 'UTGST (%)', 'Total']
    if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
        table_headers.insert(4, 'Weight')
    data_preview, templete_data = {}, {}
    supplier_list = [data['supplier_id'] for data in data_list]
    unique_sippliers = list(set(supplier_list))
    supplier_currency_dict = dict(SupplierMaster.objects.filter(id__in=unique_sippliers).values_list('id', 'currency_code'))
    data_dict = []
    base_currency = profile.base_currency
    for sup_id in unique_sippliers:
        po_data, total_amt, total_qty =[], 0, 0
        supplier_currency = supplier_currency_dict.get(sup_id, '')
        if not supplier_currency:
            supplier_currency = base_currency
        each_supplier_table_headers = copy.deepcopy(table_headers)
        each_supplier_table_headers[each_supplier_table_headers.index('Unit Price')] = 'Unit Price ({})'.format(supplier_currency)
        for data in data_list:
            if sup_id == data['supplier_id']:
                sku = SKUMaster.objects.get(id=data['sku_id'], user=warehouse.id)
                supplier = SupplierMaster.objects.get(id=data['supplier_id'], user=warehouse.id)
                if data['price'] == '':
                    # mapping_data = get_mapping_values_po(data['wms_code'], data['supplier_id'] ,user)
                    mapping_data = get_mapping_values_po(sku.wms_code, supplier.supplier_id ,warehouse)
                    data['price'] = mapping_data.get('price',0)
                unit_price = data['price']
                taxes = {'cgst_tax': 0, 'sgst_tax': 0, 'igst_tax': 0, 'utgst_tax': 0 ,'cess_tax':0,'apmc_tax':0}
                if data.get('cgst_tax', 0) == data.get('sgst_tax', 0) == data.get('igst_tax', 0) == data.get('utgst_tax', 0) =='':
                    inter_state_dict = dict(zip(SUMMARY_INTER_STATE_STATUS.values(), SUMMARY_INTER_STATE_STATUS.keys()))
                    inter_state = inter_state_dict.get(data['supplier_tax_type'], 2)
                    tax_master = TaxMaster.objects.filter(
                            user_id=warehouse, inter_state=inter_state,product_type=data['sku_product_type'],
                            min_amt__lte=unit_price, max_amt__gte=unit_price
                        ).values('cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax','cess_tax','apmc_tax')
                    if tax_master.exists():
                        taxes = copy.deepcopy(tax_master[0])
                else:
                    for tax_name in taxes.keys():
                        try:
                            taxes[tax_name] = float(data.get(tax_name, 0))
                        except:
                            taxes[tax_name] = 0
                if data.get('cess_tax', 0) != '':
                    taxes['cess_tax'] = data.get('cess_tax', 0)
                if data.get('apmc_tax', 0) != '':
                    taxes['apmc_tax'] = data.get('apmc_tax', 0)
                total, amount, company_address, ship_to_address = tax_calculation_master(data, warehouse, profile, taxes)
                total_amt += total
                total_qty += data['quantity']
                if data['mrp'] == '':
                    data['mrp'] = sku.mrp
                if profile.industry_type == 'FMCG':
                    po_temp_data = [sku.sku_code, data['supplier_id'], sku.sku_desc, data['quantity'], data['price'],
                                data['mrp'], amount, taxes['sgst_tax'], taxes['cgst_tax'], taxes['igst_tax'],
                                taxes['utgst_tax'], taxes['cess_tax'], taxes['apmc_tax'], total
                               ]
                else:
                    po_temp_data = [sku.sku_code, data['supplier_id'], sku.sku_desc, data['quantity'], data['price'],
                                 data['mrp'], amount, taxes['sgst_tax'], taxes['cgst_tax'], taxes['igst_tax'],
                                 taxes['utgst_tax'], total
                                ]
                if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
                    weight = get_sku_weight(sku)
                    po_temp_data.insert(4, weight)
                po_data.append(po_temp_data)
        data_dict.append({'table_headers': each_supplier_table_headers,
                        'data':po_data,
                        'address': supplier.address,
                        'order_id': '',
                        'telephone': supplier.phone_number,
                        'ship_to_address': ship_to_address,
                        'name': supplier.name,
                        'order_date': get_local_date(request.user, datetime.datetime.now()),
                        'total': round(total_amt),
                        'po_reference': '',
                        'user_name': '',
                        'total_amt_in_words': number_in_words(round(total_amt)) + ' ONLY',
                        'total_qty': total_qty,
                        'location': '',
                        'w_address': ship_to_address,
                        'vendor_name': '',
                        'vendor_address': '',
                        'vendor_telephone': '',
                        'receipt_type': '',
                        'title': '',
                        'gstin_no': supplier.tin_number,
                        'industry_type': '',
                        'expiry_date': '',
                        'wh_telephone': warehouse.userprofile.wh_phone_number,
                        'wh_gstin': profile.gst_number,
                        'wh_pan': profile.pan_number,
                        'terms_condition': '',
                        'supplier_pan':supplier.pan_number,
                        'company_name': profile.company.company_name,
                        'company_address': company_address,
                        'supplier_currency': supplier_currency
                    })
    templete_data['data'] = data_dict
    t = loader.get_template('templates/toggle/upload_po_preview.html')
    data = t.render(templete_data)
    data_preview['data_preview'] = data
    data_preview['data_list'] = data_list
    if pending_po_data_list:
        data_preview['pending_po_data_list'] = pending_po_data_list
    return data_preview

def get_po_name(order_data, po_number):
    if not order_data.get('po_name'):
        return po_number
    return order_data.get('po_name')

def purchase_order_excel_upload(request, warehouse, data_list, demo_data=False):
    order_ids = {}
    user_profile = warehouse.userprofile
    timezone = get_user_time_zone(warehouse)
    creation_date = datetime.datetime.now()
    show_cess_tax = False
    show_apmc_tax = False
    ean_flag = False
    wms_codes_list = list(set(map(lambda d: d['wms_code'], data_list)))
    ean_data = SKUMaster.objects.filter(Q(ean_number__gt=0) | Q(eannumbers__ean_number__gt=0),
                                        wms_code__in=wms_codes_list, user=warehouse.id)
    if ean_data.exists():
        ean_flag = True
    for final_dict1 in data_list:
        if final_dict1.get('cess_tax', 0):
            show_cess_tax = True
        if final_dict1.get('apmc_tax', 0):
            show_apmc_tax = True
        if show_cess_tax and show_apmc_tax and ean_flag:
            break
    if user_profile.industry_type == 'FMCG':
        table_headers = ['WMS Code', 'Supplier Code', 'Desc', 'Qty', 'UOM', 'Unit Price', 'MRP', 'Amt',
                         'SGST (%)', 'CGST (%)', 'IGST (%)', 'UTGST (%)', 'Total(with tax)']
        if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
            table_headers.insert(4, 'Weight')
    else:
        table_headers = ['WMS Code', 'Supplier Code', 'Desc', 'Qty', 'UOM', 'Unit Price', 'Amt',
                         'SGST (%)', 'CGST (%)', 'IGST (%)', 'UTGST (%)', 'Total']

    if ean_flag:
        table_headers.insert(1, 'EAN')
    if show_cess_tax:
        table_headers.insert(table_headers.index('UTGST (%)'), 'CESS (%)')
    if show_apmc_tax:
        table_headers.insert(table_headers.index('UTGST (%)'), 'APMC (%)')
    po_data = []
    ids_dict = {}
    po_header_dict = {}
    send_mail_data = OrderedDict()
    check_prefix = ''
    create_order_dict = {} 
    userwise_st_customer = {}
    supplier_to_customer_mapping_dict= {}
    seller_master_id= None
    dest_user =""
    today_date= datetime.datetime.now()
    create_so=True
    for final_dict in data_list:
        final_dict['sku'] = SKUMaster.objects.get(id=final_dict['sku_id'], user=warehouse.id)
        final_dict['supplier'] = SupplierMaster.objects.get(id=final_dict['supplier_id'], user=warehouse.id)
        # if final_dict.get('seller_id', ''):
        #     final_dict['seller'] = SellerMaster.objects.get(id=final_dict['seller_id'], user=user.id)
        po_type= final_dict.get("po_type", "").lower()
        if po_type=="stocktransfer" and final_dict['supplier_id'] not in supplier_to_customer_mapping_dict:
            dest_user= User.objects.filter(username=final_dict['supplier'].supplier_id)
            if dest_user.exists():
                supplier_to_customer_mapping_dict[final_dict['supplier_id']] = { "dest_user": dest_user[0], "customer_data": {}}
                customer_objs = CustomerMaster.objects.filter(user=dest_user[0].id, customer_type="Stock Transfer", customer_code__in = [warehouse.username])
                if customer_objs.exists():
                    each_customer = customer_objs[0]
                    # seller_master_objs = SellerMaster.objects.filter(seller_id=2, user=dest_user[0].id)
                    # if seller_master_objs.exists():
                    #     seller_master_id= int(seller_master_objs[0].id)
                    supplier_to_customer_mapping_dict[final_dict['supplier_id']]["customer_data"] = { 
                                 "customer_id": each_customer.customer_id,
                                 "name": each_customer.name,
                                 "email_id": each_customer.email_id,
                                 "phone_number": each_customer.phone_number,
                                 "customer_code": each_customer.customer_code,
                                 "id": each_customer.id
                                }


        final_dict['po_date'] = datetime.datetime.strptime(final_dict.get('po_date', ''), DEFAULT_DATETIME_FORMAT)
        if final_dict.get('po_delivery_date', '') != '':
            final_dict['po_delivery_date'] = datetime.datetime.strptime(final_dict.get('po_delivery_date', ''), DEFAULT_DATETIME_FORMAT)
            ist_timezone = pytz.timezone(timezone)
            final_dict['po_delivery_date'] = ist_timezone.localize(final_dict['po_delivery_date'])
        total_qty = 0
        total = 0
        order_data = copy.deepcopy(PO_SUGGESTIONS_DATA)
        data = copy.deepcopy(PO_DATA)
        order_data['supplier_id'] = final_dict['supplier'].id
        order_data['sku_id'] = final_dict['sku'].id
        order_data['order_quantity'] = final_dict['quantity']
        order_data['free_quantity'] = final_dict.get('free_quantity', 0)
        order_data['price'] = final_dict.get('price', 0)
        mapping_data = get_mapping_values_po(final_dict['sku'].wms_code ,final_dict['supplier'].supplier_id ,warehouse)
        if order_data['price'] == '':
            order_data['price'] = mapping_data.get('price',0)
        order_data['supplier_code'] = mapping_data.get('supplier_code','')
        order_data['po_name'] = final_dict['po_name']
        order_data['mrp'] = final_dict.get('mrp', 0)
        order_data['cgst_tax'] = final_dict.get('cgst_tax', 0)
        order_data['sgst_tax'] = final_dict.get('sgst_tax', 0)
        order_data['igst_tax'] = final_dict.get('igst_tax', 0)
        order_data['utgst_tax'] = final_dict.get('utgst_tax', 0)
        order_data['cess_tax'] = final_dict.get('cess_tax', 0)
        order_data['apmc_tax'] = final_dict.get('apmc_tax', 0)
        if order_data['mrp'] == '':
            order_data['mrp'] = final_dict['sku'].mrp
        taxes = {'cgst_tax': 0, 'sgst_tax': 0, 'igst_tax': 0, 'utgst_tax': 0 ,'cess_tax':0,'apmc_tax':0}
        supplier_tax_type = final_dict['supplier'].tax_type
        if order_data['cgst_tax'] == '' and order_data['sgst_tax'] == '' and order_data['igst_tax'] == '' :
            price = order_data['price']
            inter_state_dict = dict(zip(SUMMARY_INTER_STATE_STATUS.values(), SUMMARY_INTER_STATE_STATUS.keys()))
            inter_state = inter_state_dict.get(supplier_tax_type, 2)
            tax_master = TaxMaster.objects.filter(
                    user_id=warehouse, inter_state=inter_state,
                    product_type=final_dict['sku'].product_type,
                    min_amt__lte=price, max_amt__gte=price
                    ).values('cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax','cess_tax','apmc_tax')
            
            if tax_master.exists():
                taxes = copy.deepcopy(tax_master[0])
            order_data['cgst_tax'] = taxes.get('cgst_tax',0)
            order_data['sgst_tax'] = taxes.get('sgst_tax' ,0)
            order_data['igst_tax'] = taxes.get('igst_tax',0)
            if not order_data['utgst_tax'] :
                order_data['utgst_tax'] = taxes.get('utgst_tax',0)
            if not order_data['cess_tax'] :
                order_data['cess_tax'] = taxes.get('cess_tax',0)
            if not order_data['apmc_tax'] :
                order_data['apmc_tax'] = taxes.get('apmc_tax',0)
        else:
            for key,value in taxes.items():
                if not order_data[key] :
                    order_data[key] = value
        order_data['measurement_unit'] = final_dict['measurement_unit']
        order_data['creation_date'] = creation_date
        if final_dict.get('po_delivery_date', ''):
            order_data['delivery_date'] = final_dict['po_delivery_date']
        data['po_date'] = final_dict['po_date']
        data['ship_to'] = final_dict['ship_to']
        order_data['ship_to'] = final_dict['ship_to']
        data['creation_date'] = creation_date
        seller_id = ''
        excel_seller_id = ''
        if final_dict.get('seller', ''):
            seller_id = final_dict['seller'].id
            excel_seller_id = final_dict['seller'].seller_id
        customer_data= supplier_to_customer_mapping_dict.get(final_dict['supplier_id'], {})
        group_key = (order_data['po_name'], order_data['supplier_id'], data['po_date'], seller_id, po_type)
        if group_key not in order_ids.keys():
            sku_code = final_dict['sku'].sku_code
            po_id, prefix, po_number, check_prefix, inc_status = get_user_prefix_incremental(warehouse, 'po_prefix', sku_code)
            if inc_status:
                return HttpResponse("Prefix not defined")
            order_ids[group_key] = {'po_id': po_id, 'prefix': prefix, 'po_number': po_number}
        else:
            po_id = order_ids[group_key]['po_id']
            prefix = order_ids[group_key]['prefix']
            po_number = order_ids[group_key]['po_number']
        order_data['json_data'] = {
            "created_by": request.user.username,
            "created_from": "UPLOAD",
        }

        po_name = get_po_name(order_data, po_number)
        order_data['po_name'] = po_name

        if final_dict.get('inco_terms', []):
            order_data['json_data'].update({'inco_terms': final_dict['inco_terms']})
        if final_dict.get('supplier_payment_terms', ''):
            order_data['json_data'].update({'supplier_payment_terms': final_dict['supplier_payment_terms']})
        order_data['status'] = 0
        order_data['account_id'] = warehouse.userprofile.id
        order_data['created_by'] = request.user
        data1 = OpenPO(**order_data)
        data1.save()

        if final_dict.get('extra_fields', {}):
            save_or_update_po_extra_fields(warehouse.id, final_dict['extra_fields'], po_number)

        purchase_order = OpenPO.objects.get(id=data1.id, sku__user=warehouse.id)
        sup_id = purchase_order.id
        supplier = purchase_order.supplier_id
        if supplier not in ids_dict:
            ids_dict[supplier] = po_id
        data['open_po_id'] = sup_id
        data['order_id'] = po_id
        data['prefix'] = prefix
        data['po_number'] = po_number
        data['pcf'] = final_dict.get('pcf', 1)
        data['po_type'] = final_dict.get('po_type', 'Normal') or 'Normal'
        data['account_id'] = warehouse.userprofile.id
        data['created_by'] = request.user
        data['line_reference'] = final_dict.get('line_reference', '')
        order = PurchaseOrder(**data)
        order.save()
        order.po_date = data['po_date']
        order.save()
        po_header_dict = frame_po_header_data(po_header_dict, order, warehouse)
        weight = ''
        if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
            weight_obj = data1.sku.skuattributes_set.filter(attribute_name='weight'). \
                    only('attribute_value')
            if weight_obj.exists():
                weight = weight_obj[0].attribute_value

        if po_type=="stocktransfer" and customer_data:
            if supplier_tax_type == 'intra_state':
                cgst_tax = sgst_tax = igst_tax = utgst_tax = cess_tax = 0
            else:
                cgst_tax, sgst_tax, igst_tax, utgst_tax, cess_tax = (
                    order_data['cgst_tax'],
                    order_data['sgst_tax'],
                    order_data['igst_tax'],
                    order_data['utgst_tax'],
                    order_data['cess_tax']
                    )
            dest_user = customer_data.get("dest_user")
            destnation_sku_obj = SKUMaster.objects.filter(wms_code=final_dict['sku'].sku_code, user=dest_user.id)
            if destnation_sku_obj.exists():
                destnation_sku_obj = destnation_sku_obj[0]
                customerdata= customer_data.get("customer_data", {})
                order_dict_payload={
                        "order_detail": {
                            'user': dest_user.id,
                            'customer_identifier_id': customerdata.get("id"),
                            'status': '1',
                            'customer_id': customerdata.get("customer_id", ""),
                            'customer_name': customerdata.get("name", ""),
                            'email_id': customerdata.get("email_id", ""),
                            'telephone': customerdata.get("phone_number", ""),
                            'sku_id': destnation_sku_obj.id,
                            'title': destnation_sku_obj.sku_desc,
                            'quantity': final_dict['quantity'],
                            'original_quantity': final_dict['quantity'],
                            'marketplace': '',
                            'unit_price': order_data['price'],
                            'order_reference': po_number,
                            'slot_from': today_date,
                            'slot_to': today_date,
                            'shipment_date': today_date,
                            'order_type': 'StockTransfer',
                            'json_data': {
                                 "source": 'UPLOAD', 
                                  'weight': weight,
                                  "request_user": request.user.username,
                                  'pack_uom_quantity': final_dict.get('pcf', 1),
                                  'pack_id': final_dict.get('pack_id', '')
                            },
                        },
                        'customer_order_summary': {
                             "order_id": None,
                             'cgst_tax': cgst_tax,
                             'sgst_tax': sgst_tax,
                             'igst_tax': igst_tax,
                             'utgst_tax': utgst_tax,
                             'cess_tax': cess_tax,
                             'mrp': order_data['mrp'],
                        },
                        'seller_order': {
                             "seller_id": seller_master_id,
                             "order_id":  None,
                             "quantity": order_data['order_quantity'],
                             "creation_date": today_date,
                             'order_status': 'PENDING',
                        },
                        'order_header': {
                            'warehouse_id': dest_user.id,
                            'order_reference': po_number,
                            'order_type': 'StockTransfer',
                            'status': '1',
                            'customer_id': customerdata.get("id"),
                        },
                    }
                if (dest_user.id, po_number, "") in create_order_dict:
                    create_order_dict[(dest_user.id, po_number, "")].append(order_dict_payload)
                else:
                    create_order_dict[(dest_user.id, po_number, "")]= [order_dict_payload]
            else:
                create_so = False
        amount = data1.order_quantity * data1.price
        total_qty += data1.order_quantity
        total_tax_amt = (data1.utgst_tax + data1.sgst_tax + data1.cgst_tax + data1.igst_tax + data1.cess_tax + data1.apmc_tax + data1.utgst_tax) * (
                                    amount / 100)
        total_sku_amt = total_tax_amt + amount
        total += total_sku_amt
        if user_profile.industry_type == 'FMCG':
            po_temp_data = [data1.sku.wms_code, data1.supplier_code, data1.sku.sku_desc,
                            data1.order_quantity,
                            data1.measurement_unit,
                            data1.price, data1.mrp, amount, data1.sgst_tax,
                            data1.cgst_tax,
                            data1.igst_tax,
                            data1.utgst_tax,
                            total_sku_amt
                            ]
            if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
                po_temp_data.insert(4, weight)
        else:
            po_temp_data = [data1.sku.wms_code, data1.supplier_code, data1.sku.sku_desc,
                            data1.order_quantity,
                            data1.measurement_unit,
                            data1.price, amount, data1.sgst_tax, data1.cgst_tax,
                            data1.igst_tax,
                            data1.utgst_tax,
                            total_sku_amt
                            ]
        if ean_flag:
            ean_number = ''
            eans = get_sku_ean_list(data1.sku, order_by_val='desc')
            if eans:
                ean_number = eans[0]
            po_temp_data.insert(1, ean_number)
        if show_cess_tax:
            po_temp_data.insert(table_headers.index('CESS (%)'), data1.cess_tax)
        if show_apmc_tax:
            po_temp_data.insert(table_headers.index('APMC (%)'), data1.apmc_tax)
        po_data.append(po_temp_data)
        send_mail_data.setdefault(str(order.order_id), {'purchase_order': order, 'po_data': [],
                                  'data1': data1, 'total_qty': 0, 'total': 0,'seller_id':excel_seller_id})
        send_mail_data[str(order.order_id)]['po_data'].append(po_temp_data)
        send_mail_data[str(order.order_id)]['total_qty'] += total_qty
        send_mail_data[str(order.order_id)]['total'] += total

        #mail_result_data = purchase_order_dict(data1, data_req, purchase_order, user, order)
    if create_so and create_order_dict:
        try:
            from outbound.views.orders.stock_transfer import create_order_new
            create_order_new(None, create_order_dict, po_creation=False)
        except Exception as e:
            pass
    if po_header_dict:
        POHeader.objects.bulk_create_with_rounding(list(po_header_dict.values()))
    try:
        po_call_back_3p_integration(warehouse, po_number)
    except Exception as e:
        log.info(generate_log_message("Exception Raised on PO CallBack",
            warehouse_name = warehouse.username, po_number = po_number, error=str(e)))


    excel_headers = ['Seller ID' , 'PO Reference' , 'PO Date', 'Supplier ID']+table_headers
    for key, send_mail_dat in send_mail_data.items():
        try:
            purchase_order = send_mail_dat['data1']
            #(CHECK_LATER)
            # po_3p_integration(request, user, send_mail_dat['purchase_order'].po_number)
            po_data = send_mail_dat['po_data']
            total_qty = send_mail_dat['total_qty']
            total = send_mail_dat['total']
            address = purchase_order.supplier.address
            address = '\n'.join(address.split(','))
            if purchase_order.ship_to:
                ship_to_address = purchase_order.ship_to
                if warehouse.userprofile.wh_address:
                    company_address = warehouse.userprofile.wh_address
                    if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
                        if warehouse.userprofile.user.email:
                            company_address = "%s, Email:%s" % (company_address, warehouse.userprofile.user.email)
                        if warehouse.userprofile.phone_number:
                            company_address = "%s, Phone:%s" % (company_address, warehouse.userprofile.phone_number)
                        if warehouse.userprofile.gst_number:
                            company_address = "%s, GSTINo:%s" % (company_address, warehouse.userprofile.gst_number)
                else:
                    company_address = warehouse.userprofile.address
            else:
                ship_to_address, company_address = get_purchase_company_address(warehouse.userprofile)
            wh_telephone = warehouse.userprofile.wh_phone_number
            ship_to_address = '\n'.join(ship_to_address.split(','))
            vendor_name = ''
            vendor_address = ''
            vendor_telephone = ''
            if purchase_order.order_type == 'VR':
                vendor_address = purchase_order.vendor.address
                vendor_address = '\n'.join(vendor_address.split(','))
                vendor_name = purchase_order.vendor.name
                vendor_telephone = purchase_order.vendor.phone_number
            telephone = purchase_order.supplier.phone_number
            name = purchase_order.supplier.name
            supplier = purchase_order.supplier_id
            order_id = po_id
            supplier_email = purchase_order.supplier.email_id
            secondary_supplier_email = list(MasterEmailMapping.objects.filter(
                master_id=supplier, user=warehouse.id, master_type='supplier').values_list('email_id',flat=True).distinct())

            supplier_email_id =[]
            supplier_email_id.insert(0,supplier_email)
            supplier_email_id.extend(secondary_supplier_email)
            phone_no = purchase_order.supplier.phone_number
            gstin_no = purchase_order.supplier.tin_number
            po_exp_duration = purchase_order.supplier.po_exp_duration
            order_date = get_local_date(request.user, order.creation_date)
            if po_exp_duration:
                expiry_date = order.creation_date + datetime.timedelta(days=po_exp_duration)
            else:
                expiry_date = ''
            po_reference = order.po_number
            report_file_names = []
            if str(warehouse.userprofile.sap_code).lower()== "milkbasket":
                wb, ws, path, file_name = get_excel_variables(po_reference+' '+'Purchase Order Form', 'purchase_order_sheet', excel_headers)

                excel_common_list = [send_mail_dat['seller_id'], purchase_order.po_name,
                                     purchase_order.creation_date.strftime("%d-%m-%Y"), purchase_order.supplier_id]
                for i in range(len(po_data)):
                    row_count=i+1
                    column_count=0
                    excel_data = excel_common_list+po_data[i]
                    for value in excel_data:
                        ws, column_count = write_excel_col(ws, row_count, column_count, value, bold=False)
                wb.save(path)
                report_file_names.append({'name': file_name, 'path': path})
            profile = UserProfile.objects.get(user=warehouse.id)
            company_name = profile.company.company_name
            title = 'Purchase Order'
            receipt_type = request.GET.get('receipt_type', '')
            total_amt_in_words = number_in_words(round(total)) + ' ONLY'
            round_value = float(round(total) - float(total))
            company_logo = get_po_company_logo(warehouse, COMPANY_LOGO_PATHS, request)
            iso_company_logo = get_po_company_logo(warehouse, ISO_COMPANY_LOGO_PATHS, request)
            left_side_logo = get_po_company_logo(warehouse, LEFT_SIDE_COMPNAY_LOGO, request)
            data_dict = {'table_headers': table_headers, 'data': po_data, 'address': address.encode('ascii', 'ignore'),
                         'order_id': order_id,
                         'telephone': str(telephone), 'ship_to_address': ship_to_address.encode('ascii', 'ignore'),
                         'name': name, 'order_date': order_date, 'total': round(total), 'po_reference': po_reference,
                         'user_name': request.user.username, 'total_amt_in_words': total_amt_in_words,
                         'total_qty': total_qty, 'company_name': company_name, 'location': profile.location,
                         'w_address': ship_to_address.encode('ascii', 'ignore'),
                         'vendor_name': vendor_name, 'vendor_address': vendor_address.encode('ascii', 'ignore'),
                         'vendor_telephone': vendor_telephone, 'receipt_type': receipt_type, 'title': title,
                         'gstin_no': gstin_no, 'industry_type': user_profile.industry_type, 'expiry_date': expiry_date,
                         'wh_telephone': wh_telephone, 'wh_gstin': profile.gst_number, 'wh_pan': profile.pan_number,
                         'terms_condition': '',
                         'company_address': company_address.encode('ascii', 'ignore'),
                         'company_logo': company_logo, 'iso_company_logo': iso_company_logo,
                         'left_side_logo': left_side_logo}
            if round_value:
                data_dict['round_total'] = "%.2f" % round_value
            t = loader.get_template('templates/toggle/po_download.html')
            rendered = t.render(data_dict)
            if get_misc_value('raise_po', warehouse.id) == 'true':
                data_dict_po = {'contact_no': profile.wh_phone_number, 'contact_email': warehouse.email,
                                'gst_no': profile.gst_number, 'supplier_name':purchase_order.supplier.name,
                                'billing_address': profile.address, 'shipping_address': ship_to_address,
                                'table_headers': table_headers}
                if get_misc_value('allow_secondary_emails', warehouse.id) == 'true':
                    write_and_mail_pdf(
                        po_reference, rendered, warehouse, supplier_email_id,
                        phone_no, po_data, str(order_date).split(' ')[0],
                        ean_flag=ean_flag, data_dict_po=data_dict_po,
                        mail_attachments=report_file_names
                        )
                elif get_misc_value('raise_po', warehouse.id) == 'true':
                    write_and_mail_pdf(
                        po_reference, rendered, warehouse, supplier_email,
                        phone_no, po_data, str(order_date).split(' ')[0], ean_flag=ean_flag,
                        data_dict_po=data_dict_po, mail_attachments=report_file_names
                        )
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Purchase Order send mail failed for %s and params are %s and error statement is %s' % (
            str(warehouse.username), str(request.POST.dict()), str(e)))
    for key, value in order_ids.items():
        if value.get('po_id'):
            check_purchase_order_created(warehouse, value['po_id'], check_prefix)
    return 'success'

@get_warehouse
def purchase_order_upload_preview(request, warehouse):
    data_list = json.loads(request.POST.get('data_list', ''))
    send_for_approval = get_misc_value('enable_pending_approval_pos', warehouse.id)
    if send_for_approval == 'true':
        purchase_approval_excel_upload(request, warehouse, data_list)
    else:
        purchase_order_excel_upload(request, warehouse, data_list)
    return HttpResponse('Success')
