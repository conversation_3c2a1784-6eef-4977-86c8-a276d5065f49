import datetime
import json
import pytz
import traceback
from dateutil import parser
from itertools import chain

from collections import OrderedDict
from django.http import JsonResponse

from django.db.models import (
    F, Q, Max, Value, TextField, Case, When, CharField
)
from django.contrib.postgres.aggregates import StringAgg
from django.db.models.functions import Cast

from django.db import transaction

from wms.celery import app as celery_app
from core_operations.views.common.main import WMSListView
from core_operations.views.common.main import (
    get_search_params, get_sku_ean_numbers, get_decimal_value,
    get_user_time_zone, get_local_date_known_timezone, get_misc_value, 
    get_multiple_misc_values, scroll_data, get_sku_pack_repr,
    get_pagination_info, get_paging_data, generate_log_message,
    get_or_none
    )
from django.test.client import RequestFactory

from inbound.models import PurchaseOrder, ASNSummary, OpenPO
from inventory.models import LocationMaster, SKUPackMaster
from outbound.models import SalesReturnBatchLevel
from wms_base.models import User
from core.models.common import AsyncAPIExecution

from .validation import validate_purchase_orders, validate_po_updation
from .pending_po import send_purchase_order_for_approval
from .create_po import confirm_add_po
from .update_po import update_po, update_stock_transfer_order
from .common import (
    get_master_doc_details,
    get_po_numbers_list, get_po_header_status_from_po_header_table
    )

from inbound.views.grn.pending_grn import SalesReturnPendingGRN
from .po_data import SKUWisePO

from inbound.views.common.common import get_po_data_with_po_number
from inbound.views.common.constants import DEFAULT_DATETIME_FORMAT, INVALID_PAYLOAD_CONST
from inbound.views.purchase_order.integrations import po_call_back_3p_integration

from wms_base.wms_utils import get_permission, init_logger

#request user storaging middleware
from wms_base.middleware.current_user import CurrentUserMiddleware

today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/purchase_order' + today + '.log')
log_err = init_logger('logs/purchase_order.log')

class PurchaseOrderSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None
    
    def frame_date_filters(self, request_data):         
        date_filters = {}
        error_message = ''
        try:
            date_keys_mapping = {
                'from_date': 'creation_date__gt',
                'to_date': 'creation_date__lt',
                'updated_at_gte': 'updation_date__gte',
                'updated_at_lte': 'updation_date__lte'
            }
            for req_key, filter_key in date_keys_mapping.items():
                if request_data.get(req_key):
                    parsed_date = parser.parse(request_data[req_key])
                    localized_date = pytz.timezone(self.timezone).localize(parsed_date)
                    date_filters[filter_key] = localized_date.astimezone(pytz.UTC)
                    del request_data[req_key]
        except Exception as e:
            error_message = "Invalid Date Filter Format"
            log.debug(traceback.format_exc())
            log.info(generate_log_message("GetPODateFiltersFailure",
                warehouse_name=self.warehouse, request_data=request_data, error=str(e)))

        return date_filters, error_message

    def get(self, *args, **kwargs):

        self.set_user_credientials()
        self.timezone = get_user_time_zone(self.warehouse)
        request_data = self.request.GET.copy()

        limit = int(self.request.GET.get('limit', 10))
        offset = int(self.request.GET.get('offset', 0))
        
        #Framing Date Filters
        date_filters, error_message = self.frame_date_filters(request_data)
        if error_message:
            return JsonResponse({
                'data' : [],
                'paging' : {},
                'errors' : [error_message]
                }, status=400)
        
        if date_filters:
            request_data.update(date_filters)

        #Framing Status Filters
        status_search = []
        if 'status' in request_data:
            status_search.append(request_data.get('status', ''))
            del request_data['status']

        if 'warehouse' in request_data:
            request_data.pop('warehouse')

        #Fetch PO Numbers with the Requested Filters
        message, filter_dict, po_numbers  = get_po_numbers_list(request_data, self.warehouse)
        if message:
            return JsonResponse({
                'data' : [],
                'paging' : {},
                'errors' : ['Invalid Filters']
                }, status=400)
                
        #Getting PO header status
        po_status_dict = {}
        if po_numbers:
            po_status_dict = get_po_header_status_from_po_header_table([self.warehouse.id], po_numbers, status_search, order_by=['-creation_date'])

        limit = limit if limit else len(po_numbers)
        po_numbers = list(po_status_dict.keys())[offset:limit+offset]
        
        #Fetching Uploaded Documents
        extra_dict = {}
        if request_data.get('po_number'):
            extra_dict['po_grn_doc_details'] = get_master_doc_details(
                self.request, [self.warehouse.id], po_numbers, 'PO_TEMP')
            extra_dict['po_doc_details'] = get_master_doc_details(
                self.request, [self.warehouse.id], po_numbers, 'PO')
        data_list = []
        if po_numbers:
            data_list = SKUWisePO().get_data(po_numbers, po_status_dict, self.warehouse, extra_dict)

        page_info = get_pagination_info({'limit' : limit, 'offset' : offset})
        paging_details = get_paging_data(self.request, page_info, len(data_list))

        return JsonResponse({
            'data' : data_list,
            'paging' : paging_details,
            'errors' : []
        })

    def post(self, *args, **kwargs):
        try:
            request = self.request
            purchase_orders = json.loads(request.body)
        except:
            return {'error': [{'message': INVALID_PAYLOAD_CONST}], 'status': 400}
        log.info('Request params for ' + request.user.username + ' is ' + str(purchase_orders))

        self.set_user_credientials()

        error_data, po_data = validate_po_payload(self.warehouse, purchase_orders)
        if error_data:
            return {'error': list(error_data['errors']), 'status': 400}

        result = create_po(request, purchase_orders, po_data, request.warehouse)
        result_data = {}
        if result.get('status') == 200:
            result_data['data'] = result.get('data')
            if result.get('po_number'):
                result_data['po_number'] = result.get('po_number')
                result_data['po_reference'] = result.get('po_reference', '')
            return JsonResponse( result_data, status=200)
        else:
            return JsonResponse({'error': result.get('error')}, status=result.get('status'))

    def put(self, *args, **kwargs):
        """Update the PO"""
        self.set_user_credientials()
        try:
            request = self.request
            request_data = json.loads(self.request.body)
        except:
            return JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST}], 'status': 400})
        log.info('Request params for Update PO for' + request.user.username + ' is ' + str(request_data))
        try:
            failed_status, po_entries = validate_po_updation(request_data, self.warehouse)
            if failed_status:
                return {'error': failed_status, 'status': 400}
            if not isinstance(request_data, list):
                request_data = [request_data]
            for data in request_data:
                if data.get('update_order', True) and data.get('po_type', '').lower() == 'stocktransfer':
                    update_stock_transfer_order(request, data)
            status = update_po(request_data, po_entries, self.warehouse, request.user.username)
            if status != 'Success':
                return JsonResponse({'error': [{'message': status}], 'status': 400})
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('Update PO API failed for %s and params are %s and error statement is %s' % (
            str(request.user.username), str(request.body), str(e)))
            return JsonResponse({'error': [{'message': 'Update PO failed'}]}, status=400)

        return JsonResponse({'data': [{'message':'Success'}]}, status=200)

    def delete(self, *args, **kwargs):
        """
        Delete the PO based on the PO Number or PO Reference
        """
        self.set_user_credientials()
        request = self.request
        self.request_data = request.GET
        try:
            request_data = []
            filter_dict = {}
            reason = self.request_data.get('reason','')
            po_numbers = self.request_data.get('po_number','')
            po_references = self.request_data.get('po_reference','')
            if po_numbers:
                filter_dict['po_number__in'] = po_numbers.split(',')
                for each_po_number in po_numbers.split(','):
                    request_data.append({'po_number': each_po_number, 'status':'cancel', 'reason': reason})
            elif po_references:
                filter_dict['open_po__po_name__in'] = po_references.split(',')
                for each_po_reference in po_references.split(','):
                    request_data.append({'po_reference': each_po_reference, 'status':'cancel', 'reason': reason})
            failed_status, _ = validate_po_updation(request_data, self.warehouse, delete_po=True)
            if failed_status:
                return {'error': failed_status, 'status': 400}

            po_type_dict = dict(PurchaseOrder.objects.filter(open_po__sku__user=self.warehouse.id, **filter_dict).values_list('po_number', 'po_type'))
            for data in request_data:
                if data.get('update_order', True) and po_type_dict.get(data.get('po_number', ''), '').lower() == 'stocktransfer':
                    update_stock_transfer_order(request, data, update_type='cancel')
            status = update_po(request_data, [], self.warehouse, request.user.username)
            if status != 'Success':
                return JsonResponse({'error': [{'message': status}], 'status': 400})
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('Delete PO API failed for %s and params are %s and error statement is %s' % (
            str(request.user.username), str(request.GET), str(e)))
            return JsonResponse({'error': [{'message': 'Delete PO failed'}], 'status': 400})
        return JsonResponse({'data': [{'message':'Success'}]}, status=200)

class GetPurchaseOrderSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None
    
    def frame_date_filters(self, request_data):
        """
        Frame date filter dict
        """
        date_filters = {}
        try:
            filter_date = {}
            if request_data.get('from_date'):
                filter_date['from_date'] = parser.parse(request_data['from_date'])
            if request_data.get('to_date'):
                filter_date['to_date'] = parser.parse(request_data['to_date'])
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('GET PO API Date Filters Failed for %s and params are %s and error statement is %s' % (
                str(self.request.user.username), str(request_data), str(e)))

        if 'from_date' in request_data:
            filter_date['from_date'] = datetime.datetime.combine(
                filter_date['from_date'], datetime.time())
            date_filters['creation_date__gt'] = filter_date['from_date']
            del request_data['from_date']

        if 'to_date' in request_data:
            filter_date['to_date'] = datetime.datetime.combine(
                filter_date['to_date'] + datetime.timedelta(1), datetime.time())
            date_filters['creation_date__lt'] = filter_date['to_date']
            del request_data['to_date']
        
        return date_filters

    def get_pending_sr_data(self, return_id=''):
        """Get the pending Sales Return Data based on the Return ID"""
        search_po_list = []
        #Get the Pending Sales Return Data
        return_objects = SalesReturnBatchLevel.objects.filter(
            quantity__gt=0,
            sales_return_sku__sales_return__warehouse=self.warehouse.id
        ).order_by("-creation_date")
        if return_objects.exists():
            search_po_list = list(return_objects.filter(
                sales_return_sku__sales_return__return_id__icontains=return_id
            ).values(
                value=F('sales_return_sku__sales_return__return_id'),
                return_id=F('sales_return_sku__sales_return__return_id'),
                return_reference=F('sales_return_sku__sales_return__return_reference'),
                warehouse_id=F('sales_return_sku__sales_return__warehouse'),
                return_type = F('sales_return_sku__sales_return__return_type')
            ).annotate(
                return_date=Max('sales_return_sku__sales_return__creation_date'),
                return_display_key=  Case(When(
                    sales_return_sku__sales_return__return_reference__isnull=False,
                    sales_return_sku__sales_return__return_reference__gt=Value(''),
                    then=F("sales_return_sku__sales_return__return_reference")
                ),
                default=F("sales_return_sku__sales_return__return_id"), output_field=CharField())
            ).order_by('-value').distinct())
            #Converting the Date to Local Timezone
            for row in search_po_list:
                    row['return_date'] = get_local_date_known_timezone(self.timezone, row['return_date'])
        return search_po_list

    def get_supplier_search_results(self, search_po_list, search_value):
        """
        Fetch purchase orders based on the supplier id and supplier name
        """
        asn_objs= ASNSummary.objects.filter(asn_user__in=[self.warehouse.id], status__in=[1,2]).order_by("-creation_date")
        if asn_objs.exists():
            search_po_list = list(asn_objs.filter(
                Q(purchase_order__open_po__supplier__supplier_id__icontains=search_value) | Q(purchase_order__open_po__supplier__name__icontains=search_value)
                ).values(
                    'asn_number', 'asn_type',
                    value=F('invoice_number'), po_number=F('purchase_order__po_number'),
                    warehouse_id=F('asn_user_id'), supplier_id=F('purchase_order__open_po__supplier__supplier_id'),
                    supplier_name=F('purchase_order__open_po__supplier__name')
                ).annotate(
                    asn_date=Max('creation_date'),
                    po_display_key=Case(When(purchase_order__open_po__po_name__isnull=False, then=F("purchase_order__open_po__po_name")),
                    default=F("po_number"),output_field=CharField())
                ).order_by('-value').distinct())

        all_pos = PurchaseOrder.objects.filter(
                open_po__sku__user__in=[self.warehouse.id], received_quantity__lt=F('open_po__order_quantity')
            ).exclude(status__in=['location-assigned', 'confirmed-putaway'])

        if all_pos.exists():
            search_po_list1 = list(all_pos.filter(Q(open_po__supplier__supplier_id__icontains=search_value) | Q(open_po__supplier__name__icontains=search_value)
                ).values(
                    'po_number','po_type',
                    value= F('po_number'), supplier_id=F('open_po__supplier__supplier_id'),
                    supplier_name=F('open_po__supplier__name')
                ).annotate(
                    po_date=Max('creation_date'),
                    po_display_key=Case(When(open_po__po_name__isnull=False, then=F("open_po__po_name")),
                    default=F("po_number"),output_field=CharField())
                ).order_by("-po_number").distinct())
            search_po_list.extend(search_po_list1)
        return search_po_list

    def get_po_search_results(self, search_po_list, search_type, search_value, source_from):
        """
        Fetch purchase orders based on the po number and po reference
        """
        open_pos = []
        filter_dict = {'status__in':['', 'grn-generated']}
        filter_status = ['Open', 'Partially Received']
        if source_from.lower() == 'web':
            open_pos = list(OpenPO.objects.filter(Q(po_name__icontains=search_value)| Q(wms_code__icontains=search_value), purchaseorder__isnull=True, sku__user=self.warehouse.id).values(
                po_display_key= F('po_name'), supplier_name=F('supplier__name'), open_po_number=F('wms_code'),
            ).annotate(po_date=Max('creation_date')).order_by('wms_code'))
            filter_dict = {}
            filter_status = []
        all_pos = list(PurchaseOrder.objects.filter(
                **filter_dict,
                open_po__sku__user__in = [self.warehouse.id]
            ).values_list('id', flat=True).order_by("-creation_date"))
        if search_type:
            search_po_list = list(PurchaseOrder.objects.filter(open_po__po_name__icontains=search_value, id__in=all_pos
            ).select_related(
                'open_po', 'open_po__supplier'
            ).values(
                'po_number','po_type',value=F('po_number'),
                supplier_id=F('open_po__supplier__supplier_id'),
                supplier_name=F('open_po__supplier__name')
            ).annotate(
                po_date=Max('creation_date'),
                po_display_key=Case(When(open_po__po_name__isnull=False, then=F("open_po__po_name")),
                    default=F("po_number"),output_field=CharField())).order_by("-po_number").distinct())

        #Fetch PO header status from po numbers
        po_numbers = [each_row['po_number'] for each_row in search_po_list]
        po_status_dict = get_po_header_status_from_po_header_table([self.warehouse.id], po_numbers, filters=filter_status)
        search_po_list = list(chain(open_pos, search_po_list))
        for row in search_po_list:
            row['po_date'] = get_local_date_known_timezone(self.timezone, row['po_date'])
            if 'po_number' not in row:
                row['po_status'] = 'Draft'
            else:
                row['po_status'] = po_status_dict.get(row['po_number'], '')

        return search_po_list

    def get_asn_search_results(self, search_po_list, search_value, source_from):
        """
        Fetch purchase orders based on the provided asn number
        """
        #ASN status mapping dict
        asn_status_map = {
        '1': 'In Transit',
        '2': 'Partially Received',
        '0': 'Received',
        '5': 'Draft',
        '3': 'Cancelled',
        '7': 'Reverted',
        '6': 'Approval Pending'
        }
        filter_dict = {'status__in': [1,2]}
        if source_from.lower() == 'web':
            filter_dict = {}
        asn_objs= ASNSummary.objects.filter(**filter_dict, asn_user__in=[self.warehouse.id]).order_by("-creation_date")
        json_details = asn_objs.filter(asn_number__icontains=search_value).values('json_data__lpns_list', 'asn_number')
        asn_lpn_check = []
        for each_data in json_details:
            if each_data['json_data__lpns_list'] and each_data['asn_number'] not in asn_lpn_check:
                asn_lpn_check.append(each_data['asn_number'])

        if asn_objs.exists():
            search_po_list= list(asn_objs.filter(asn_number__icontains=search_value
                ).values(
                    "invoice_number", 'asn_type',
                    value=F('asn_number'), warehouse_id=F('asn_user_id'),
                    po_number=F('purchase_order__po_number'),
                    supplier_id=F('purchase_order__open_po__supplier__supplier_id'),
                    supplier_name=F('purchase_order__open_po__supplier__name')
                ).annotate(
                    asn_status=StringAgg(Cast('status', TextField()),delimiter=',',default=Value('')),
                    asn_date=Max('creation_date'),
                    po_display_key=Case(When(purchase_order__open_po__po_name__isnull=False, then=F("purchase_order__open_po__po_name")),
                    default=F("po_number"),output_field=CharField())
                ).order_by('-value').distinct())
            for row in search_po_list:
                asn_status = set(row['asn_status'].split(",")).pop()
                row['lpn_check'] = True if row['value'] in asn_lpn_check else False
                if len(asn_status) == 1:
                    row['asn_status'] = asn_status_map.get(asn_status, '')
                else:
                    row['asn_status'] = 'Partially Received'
                row['asn_date'] = get_local_date_known_timezone(self.timezone, row['asn_date'])

        return search_po_list

    def get_invoice_number_search_results(self, search_po_list, search_value):
        """
        Fetch purchase orders based on asn number
        """
        asn_objs= ASNSummary.objects.filter(asn_user__in=[self.warehouse.id], status__in=[1,2]).order_by("-creation_date")
        json_details = asn_objs.filter(invoice_number__icontains=search_value).values('json_data__lpns_list', 'asn_number')

        asn_lpn_check = []
        for each_data in json_details:
            if each_data['json_data__lpns_list'] and each_data['asn_number'] not in asn_lpn_check:
                asn_lpn_check.append(each_data['asn_number'])
        if asn_objs.exists():
            search_po_list= list(asn_objs.filter(invoice_number__icontains=search_value
                ).values(
                    'asn_number', 'asn_type', value=F('invoice_number'),
                    po_number=F('purchase_order__po_number'),
                    warehouse_id=F('asn_user_id'),
                    supplier_id=F('purchase_order__open_po__supplier__supplier_id'),
                    supplier_name=F('purchase_order__open_po__supplier__name')
                ).annotate(
                    asn_date=Max('creation_date'),
                    po_display_key=Case(When(purchase_order__open_po__po_name__isnull=False, then=F("purchase_order__open_po__po_name")),
                    default=F("po_number"),output_field=CharField())
                    ).order_by('-value').distinct())
            for row in search_po_list:
                row['lpn_check'] = True if row['asn_number'] in asn_lpn_check else False
                row['asn_date'] = get_local_date_known_timezone(self.timezone, row['asn_date'])
        return search_po_list

    def get_search_results(self, search_type, search_value, source_from):
        """
        Search Filters for Purchase Order GET API
        """
        results1 = []
        search_po_list = []
        if search_type=="asn_number":
           #Fetch PO data with asn number
           search_po_list = self.get_asn_search_results(search_po_list, search_value, source_from)
        elif search_type=="invoice_number":
            #Fetch PO data with invoice number
            search_po_list = self.get_invoice_number_search_results(search_po_list, search_value)
        elif search_type == 'supplier':
            #Fetch PO data with supplier id or supplier Number
            search_po_list = self.get_supplier_search_results(search_po_list, search_value)
        elif search_type == 'return_id':
            #Fetch Sales Return data with return id
            search_po_list = self.get_pending_sr_data(search_value)
        else:
            #Fetch PO data with po number or po reference
            search_po_list = self.get_po_search_results(search_po_list, search_type, search_value, source_from)
        return results1, search_po_list
    
    def get_po_data(self, all_pos, po_numbers):
        return all_pos.filter(po_number__in = po_numbers).values('saved_quantity', po_id=F('id'),
                    supplier=F('open_po__supplier_id__user'), supplier_id=F('open_po__supplier__supplier_id'),
                    purchaseorder_number=F('po_number'), purchaseorder_date=F('po_date'),
                    po_creation_date=F('creation_date'),order_quantity=F('open_po__order_quantity'),
                    sku_id=F('open_po__sku_id'), sku_code=F('open_po__sku_id__sku_code'),
                    sku_desc=F('open_po__sku_id__sku_desc'),supplier_name=F('open_po__supplier_id__name'),
                    image_url=F('open_po__sku_id__image_url'), sku_size=F('open_po__sku_id__sku_size'), 
                    mrp=F('open_po__mrp'),po_received_quantity=F('received_quantity'),
                    price =F('open_po__price'), cgst_tax = F('open_po__cgst_tax'), sgst_tax = F('open_po__sgst_tax'),
                    igst_tax = F('open_po__igst_tax'),utgst_tax = F('open_po__utgst_tax'),
                    cess_tax = F('open_po__cess_tax'),pmc_tax = F('open_po__apmc_tax'),
                    shelf_life=F('open_po__sku__shelf_life'), customer_shelf_life=F('open_po__sku__customer_shelf_life'),
                    minimum_shelf_life=F('open_po__sku__minimum_shelf_life'), sku_reference=F("open_po__sku__sku_reference"),
                    batch_based=F('open_po__sku__batch_based'),
                    enable_serial_based=F("open_po__sku__enable_serial_based")
                    )

    def linewise_keys(self, po_data):
        keys_list = [
            ('id', 'po_id'),('sku_code','sku_code'),('sku_desc','sku_desc'),('ean_numbers', 'ean_numbers'),\
            ('price', 'price'), ('cgst_tax', 'cgst_tax'), ('sgst_tax', 'sgst_tax'), ('igst_tax', 'igst_tax'),\
            ('cess_tax', 'cess_tax'), ('saved_quantity', 'saved_quantity'),('sku_size','sku_size'),\
            ('image_url','image_url'), ('po_quantity','order_quantity'), ('mrp','mrp'),\
            ('received_quantity','po_received_quantity'), ('shelf_life','shelf_life'),\
            ('customer_shelf_life','customer_shelf_life'), ('minimum_shelf_life', 'minimum_shelf_life'),
            ('sku_reference', 'sku_reference'), ('batch_based', 'batch_based'), ('serial_based', 'enable_serial_based')
            ]

        line_list = []
        for row in po_data:
            sku_pack_qtys = SKUPackMaster.objects.filter(sku_id=row['sku_id'], status=1).order_by(
                '-pack_quantity'). \
                values('pack_quantity', 'pack_id')
            if sku_pack_qtys:
                self.display_pack = True
            line_dict = {}
            for keys in keys_list:
                if keys[0] == 'ean_numbers':
                    line_dict[keys[0]] = self.ean_numbers_dict.get(line_dict.get('sku_code'),[])
                else:
                    line_dict[keys[0]] = row[keys[1]]
            line_dict['pack_repr'] =  get_sku_pack_repr(sku_pack_qtys, line_dict.get('po_quantity',0))
            total_tax = line_dict.get('cgst_tax', 0) + line_dict.get('sgst_tax', 0) + line_dict.get('igst_tax', 0) + line_dict.get('cess_tax', 0)
            receivable_quantity = line_dict.get("po_quantity", 0)  - line_dict.get("received_quantity", 0) - line_dict.get("saved_quantity", 0)
            asn_quantity = line_dict.get("saved_quantity", 0)
            line_dict.update({
                    "receivable_quantity": receivable_quantity,
                    "image_url": line_dict.get("image_url", ""),
                    'manufacture_date' : '', 'expiry_date' : '', 
                    'accepted_quantity' : '', 'batch_number' : '', 'rejected_quantity' : '',
                    'invoice_number' : '', 'total_tax': total_tax, 'asn_quantity': asn_quantity
                    })
            line_dict['customer_shelf_life'] = int(str(line_dict['customer_shelf_life'])[0])
            line_dict['minimum_shelf_life'] = int(str(line_dict['minimum_shelf_life'])[0])
            line_dict['shelf_life'] = line_dict['shelf_life']-1 if line_dict['shelf_life'] else 0
            line_list.append(line_dict)
        return line_list
    
    def get_lane_details(self):
        inbound_staging_lanes = self.misc_dict.get('inbound_staging_lanes')
        next_lane, lane_locations = '', []
        if inbound_staging_lanes:
            next_lane = inbound_staging_lanes.split(',')[0]
            lane_locations = list(LocationMaster.objects.filter(zone__storage_type=next_lane, status=1, zone__user=self.warehouse.id).values_list('location', flat=True))
        return next_lane, lane_locations

    
    def get_rejected_reasons(self, line_wise_dict):
        grn_rejection_reasons, discrepancy_reasons = [], []
        if line_wise_dict:
            grn_reasons = self.misc_dict.get('grn_rejection_reasons', 'false')
            discrepancy_reasons = self.misc_dict.get('discrepancy_reasons', 'false')
            if grn_reasons != 'false':
                grn_rejection_reasons = grn_reasons.split(',')
            if discrepancy_reasons != 'false':
                discrepancy_reasons = discrepancy_reasons.split(',')
        return grn_rejection_reasons, discrepancy_reasons

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        search_params = self.request.GET
        search_parameters = {}
        
        self.frame_date_filters(search_params)

        if 'po_number' in search_params:
            search_parameters['po_number__in'] = search_params['po_number'].split(',')

        search_type, search_value = "", ""
        if 'search' in search_params:
            search_type = search_params['search_type']
            search_value = search_params['search_value']

        source_from = search_params.get('source_from', '')
        limit = int(self.request.GET.get('limit', 10))
        offset = int(self.request.GET.get('offset', 0))
        error_status = []
        search_po_list = []
       
        self.timezone = get_user_time_zone(self.warehouse)
        self.misc_dict = get_multiple_misc_values([
            'grn_rejection_reasons', 'discrepancy_reasons', 'inbound_staging_lanes'], self.warehouse.id)

        results1, search_po_list = self.get_search_results(search_type, search_value, source_from)
        if search_po_list or (search_type and search_value) :
            search_po_list = search_po_list[offset:offset + limit]
            page_info = scroll_data(self.request, search_po_list, limit=limit, request_type='GET')
            page_info['data'] = search_po_list
            page_info['message'] = "Success"
            page_info['status'] = 200
            page_info['page_info']['total_count'] = len(search_po_list)
            return page_info
        
        self.display_pack = False

        next_lane, lane_locations = self.get_lane_details()
        all_pos = PurchaseOrder.objects.filter(id__in = results1, **search_parameters).order_by("-po_number")
        limit = limit if limit else 10
        data_list = []
        if all_pos.exists():
            po_numbers = list(all_pos.values_list('po_number',flat=True).distinct()[offset:limit+offset])
            results = self.get_po_data(all_pos, po_numbers)

            line_wise_dict = {}
            for res_row in results:
                if res_row['purchaseorder_number'] in line_wise_dict:
                    line_wise_dict[res_row['purchaseorder_number']].append(res_row)
                else:
                    line_wise_dict[res_row['purchaseorder_number']] = [res_row]
            
            sku_codes = list(map(lambda data: data['sku_code'], results))
            
            self.ean_numbers_dict = get_sku_ean_numbers(sku_codes, self.warehouse)
            
            grn_rejection_reasons, discrepancy_reasons = self.get_rejected_reasons(line_wise_dict)
            
            for key, data in line_wise_dict.items():
                data_dict = OrderedDict((
                    ('po_number', key),
                    ('supplier_id', data[0]['supplier_id']),
                    ('supplier_name', data[0]['supplier_name']),
                    ('po_date',  get_local_date_known_timezone(self.timezone, data[0]['purchaseorder_date'], send_date=True).strftime(DEFAULT_DATETIME_FORMAT) if data[0]['purchaseorder_date'] else ''),
                    ('order_type', 'PO'),
                    ('has_staging_lanes', True if next_lane else False),
                    ('next_lane', next_lane),
                    ('lane_locations', lane_locations),
                    ('items', self.linewise_keys(data)),
                    ('grn_rejection_reasons', grn_rejection_reasons),
                    ('discrepency_reason', discrepancy_reasons)
                    ))
                data_list.append(data_dict)
        
        page_info = scroll_data(self.request, data_list, limit=limit, request_type='GET')
        page_info['data'] = data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = len(data_list)
        page_info['error'] = [{'message': error_status}]
        return page_info
    

def validate_po_payload(warehouse, po_data):
    ''' Validating Purchase Order Payload '''
    po_order_data, error_data = [], []
    if not isinstance(po_data, list):
        failed_status, final_data_dict, _ = validate_purchase_orders(po_data, warehouse=warehouse)
        return failed_status, final_data_dict
    else:
        for data in po_data:
            failed_status, final_data_dict, _ = validate_purchase_orders(data, warehouse=warehouse)
            if failed_status:
                error_data.extend(failed_status['errors'])
            else:
                po_order_data.append(final_data_dict)
        return error_data, po_order_data
    
def create_po(request, po_data, validated_data, warehouse, callback=True):
    try:
        pending_po = get_misc_value('enable_pending_approval_pos', warehouse.id)
        if pending_po == 'true':
            raise_pending_po_permission = get_permission(request.user, 'add_pendingpo')
            if not raise_pending_po_permission:
                return {'error': [{'message': 'No access to raise pending PO'}], 'status': 400}
        else:
            permission = get_permission(request.user, 'add_purchaseorder')
            if not permission:
                return {'error': [{'message':'No access'}], 'status': 400}
        if pending_po == 'true':
            if validated_data.get('ship_to', []):
                po_data['ship_to'] = validated_data['ship_to']
                po_data['supplier_id'] = validated_data['supplier_id']
            po_number = ''
            status, po_number, _ = send_purchase_order_for_approval(request, warehouse, po_data, api_call=True)
            if status:
                failed_status = [{'order_id': '', 'message': status}]
                return {'error': failed_status, 'status': 400}

            return {'data': {'message': 'Success', 'po_number': po_number}, 'status': 200}
        else:
            po_request = RequestFactory
            po_request.user = request.user
            po_request.warehouse = warehouse
            po_request.method = 'POST'
            po_request.GET = {}
            po_request.META = {'csrf_token': 'NOTPROVIDED'}
            po_request.body = json.dumps(validated_data)
            status = confirm_add_po(po_request, api_call=True, callback=callback)
        log.info(status)
        if status['message'] == 'Success':
            return {'data': 'Success', 'status': 200, "po_number": status['po_number'], 'po_reference': status.get('po_reference', '')}
        else:
            return {'error': status['message'], 'status': 400}
    
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info(f'create orders data failed for {request.user.username} and params are {request.body} and error statement is {e}')
        return {'error': [{'message':'Internal Server Error'}], 'status': 500}


@celery_app.task
def async_purchase_order_creation(user_id, warehouse_id, data):
    ''' Purchase Order Creation through async api. '''
    user = get_or_none(User, {'id': user_id})
    warehouse = get_or_none(User, {'id': warehouse_id})
    request = RequestFactory
    request.user = user
    CurrentUserMiddleware.set_current_user(request.user)
    error_data, po_data = validate_po_payload(warehouse, data)
    if error_data:
        return {'po_numbers': [], 'error_data': error_data}

    po_numbers = []
    for i,po in enumerate(po_data):
        result = create_po(request, data[i], po, warehouse, False)   
        if result.get('status') == 200 and result.get('po_number'):
            po_numbers.append({'po_number': result.get('po_number'), 'po_reference': result.get('po_reference')})
        else:
            error = result.get('error', '')
            if isinstance(error, list):
                error_data.extend(error)
            else:
                error_data.extend([error])
    
    result_data = {
        'po_numbers': po_numbers,
        'error_data': error_data
    }
    CurrentUserMiddleware.clear_current_user() 
    return result_data


@celery_app.task
def async_po_post_creation(results, warehouse_id, async_id):
    ''' Async post process for Purchase Order Creation '''
    warehouse = get_or_none(User, {'id': warehouse_id})
    async_api_record = get_or_none(AsyncAPIExecution, {'id': async_id})
    po_numbers, error_data = [], []
    for data in results:
        if data.get('po_numbers'):
            po_numbers.extend(data.get('po_numbers'))
        if data.get('error_data'):
            error_data.extend(data.get('error_data'))
    
    result_data = {}
    if not po_numbers:
        async_api_record.status = 3
        async_api_record.result = error_data
        async_api_record.save()
    else:
        result_data['po_numbers'] = po_numbers
        if error_data:
            async_api_record.status = 4
            result_data['error'] = error_data
        else:
            async_api_record.status = 2
        async_api_record.result = result_data
        async_api_record.save()
        # trigger Purchase Order callback
        try:
            po_call_back_3p_integration(warehouse, po_numbers, async_id)
        except Exception as e:
            log.info(generate_log_message("Exception Raised on PO CallBack",
                    warehouse_name = warehouse.username, po_number = po_numbers, error=str(e)))
    
    
    