import json
from collections import OrderedDict

from django.http import HttpRequest
from django.db.models import Q, Max

#Model Imports
from inbound.models import SellerPOSummary

#Method Imports
from core_operations.views.common.main import get_filtered_params
from quality_control.views import QualityControlSet
from .common import fetch_pending_stock_for_quality_control
from core_operations.views.common.main import (
    get_user_time_zone, get_local_date_known_timezone
)

def get_po_grn_qc_data(start_index, stop_index, temp_data, search_term, order_term, col_num, request, user, filters):
    """Returns PO GRN's QC Data Table data """

    lis = ['creation_date_only', 'grn_number', 'purchase_order__po_number', 'purchase_order__open_po__po_name',
    'purchase_order__open_po__supplier__supplier_id', 'purchase_order__open_po__supplier__name',
    'purchase_order__open_po__order_type']
    
    result_values = ['grn_number', 'purchase_order__po_number', 'purchase_order__open_po__po_name',
    'purchase_order__open_po__supplier__supplier_id', 'purchase_order__open_po__supplier__name',
    'purchase_order__open_po__order_type', 'grn_reference']

    count = True if request.GET.get('count', '') == 'true' else False
    order_data = lis[col_num]
    qc_order_by = 'reference_number'
    if order_term == 'desc':
        order_data = '-%s' % order_data
        qc_order_by = '-%s' % qc_order_by
    search_params = get_filtered_params(filters, lis)
    user_timezone = get_user_time_zone(user)
    _, sps_filter, staging_filter, _, _ = fetch_pending_stock_for_quality_control('', 'po_grn',user.id)
    sps_filter, column_filter, or_filter =  apply_column_filter(sps_filter, request)

    quality_control = QualityControlSet()
    qc_request = HttpRequest()
    qc_request.user = request.user
    qc_request.warehouse = user
    qc_request.GET = {'status': 0, 'transaction_type': 'after_grn', 'quantity__gt':0, **staging_filter}
    #Datatable Global Search or Line Level Search
    if search_term:
        qc_request.GET['transaction_id'] = list(SellerPOSummary.objects\
                    .filter(Q(grn_number__icontains=search_term) |
                        Q(purchase_order__open_po__supplier__name__icontains=search_term) |
                        Q(purchase_order__open_po__supplier__supplier_id__icontains=search_term) |
                        Q(purchase_order__po_number__icontains=search_term) |
                        Q(purchase_order__open_po__order_type__icontains=search_term) |
                        Q(purchase_order__open_po__po_name__icontains=search_term), **search_params, **sps_filter)\
                    .exclude(remarks='Shortage')\
                    .annotate(creation_date_only=Max('creation_date')).order_by(order_data).values_list('id', flat=True))
    elif search_params or column_filter or or_filter:
        qc_request.GET['transaction_id'] = list(SellerPOSummary.objects.filter(**search_params, **sps_filter).filter(or_filter).exclude(remarks='Shortage')\
            .annotate(creation_date_only=Max('creation_date')).order_by(order_data).values_list('id', flat=True))
    
    if stop_index:
        limit = stop_index - start_index
        offset = start_index/limit
        qc_request.GET['limit'] = limit
        qc_request.GET['offset'] = offset

    quality_control.request = qc_request
    distinct_values = ['reference_number']
    qc_data = quality_control.get(distinct_values, qc_order_by)
    qc_data = json.loads(qc_data.content)
    search_parameters = {
        'purchase_order__open_po__sku__user' : user.id,
        'grn_number__in' : [dat.get('reference_number', '') for dat in qc_data.get('data',{}).get('data', {})]
        }

    master_data = SellerPOSummary.objects.filter(**search_parameters)\
                                        .exclude(remarks='Shortage')\
                                        .values(*result_values).distinct()\
                                        .annotate(creation_date_only=Max('creation_date'))\
                                        .order_by(order_data)
    if count:
        temp_data['count'] = qc_data.get('data', {}).get('page_info',{}).get('count', 10)
        return
    
    temp_data['recordsTotal'] = qc_data.get('data', {}).get('page_info',{}).get('count', 10)
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    for data in master_data:
        po_reference, po_number, grn_number, grn_reference = (
            data['purchase_order__open_po__po_name'], data['purchase_order__po_number'],
            data['grn_number'], data['grn_reference']
        )
        create_date_value = ''
        if data.get('creation_date_only',''):
            create_date_value = get_local_date_known_timezone(user_timezone, data.get('creation_date_only',''),True)
            create_date_value = create_date_value.strftime('%d-%m-%Y %H:%M')
        temp_data['aaData'].append(
            OrderedDict((
                ('Warehouse Id', user.id),
                ('creation_date', create_date_value),
                ('grn_number', grn_number),
                ('grn_reference', grn_reference),
                ('purchase_order__po_number',  po_number),
                ('purchase_order__open_po__po_name', po_reference),
                ('purchase_order__open_po__supplier__supplier_id', data['purchase_order__open_po__supplier__supplier_id']),
                ('purchase_order__open_po__supplier__name', data['purchase_order__open_po__supplier__name']),
                ('purchase_order__open_po__order_type',  data['purchase_order__open_po__order_type']),
                ('po_display_key', po_reference or po_number),
                ('grn_display_key', grn_reference or grn_number)
                )))
        
def apply_column_filter(sps_filter, request):
    column_filter = False
    or_filter = Q()
    column_filter_keys = json.loads(request.GET.get('columnFilter', {}))
    # Collect keys to remove after iteration
    keys_to_remove = []
    for key, value in column_filter_keys.items():
        if key == 'grn_display_key':
            or_filter |= Q(grn_reference = value) | Q(grn_number = value)
            keys_to_remove.append(key)
        elif key == 'po_display_key':
            or_filter |= Q(purchase_order__open_po__po_name = value) | Q(purchase_order__po_number = value)
            keys_to_remove.append(key)
        elif key == 'creation_date':
            sps_filter[key+"__date"] = value
            keys_to_remove.append(key)
        else:
            sps_filter[key+"__icontains"] = value
        column_filter = True
        
    # Remove the keys after the iteration
    for key in keys_to_remove:
        column_filter_keys.pop(key)

    return sps_filter, column_filter, or_filter

def get_sr_grn_qc_data(start_index, stop_index, temp_data, search_term, order_term, col_num, request, user, filters):
    """Returns SalesReturnGRN's QC Data Table data """
    lis = ['creation_date_only', 'grn_number', 'purchase_order__po_number', 'purchase_order__open_po__po_name',
    'purchase_order__open_po__supplier__supplier_id', 'purchase_order__open_po__supplier__name',
    'purchase_order__open_po__order_type']
    
    result_values = ['grn_number', 'sales_return__return_id', 'sales_return__return_reference',
    'sales_return__customer__name', 'sales_return__customer__customer_id', 'sales_return__return_type', 
    'grn_reference']
    
    count = True if request.GET.get('count', '') == 'true' else False
    order_data = lis[col_num]
    qc_order_by = 'reference_number'
    if order_term == 'desc':
        order_data = '-%s' % order_data
        qc_order_by = '-%s' % qc_order_by
    search_params = get_filtered_params(filters, lis)
    user_timezone = get_user_time_zone(user)
    _, sps_filter, staging_filter, _, _ = fetch_pending_stock_for_quality_control('','sr_grn',user.id)
    sps_filter, column_filter, or_filter =  apply_column_filter(sps_filter, request)

    quality_control = QualityControlSet()
    qc_request = HttpRequest()
    qc_request.user = request.user
    qc_request.warehouse = user
    qc_request.GET = {'status': 0, 'transaction_type': 'after_sr_grn', **staging_filter}
    
    #Datatable Global Search or Line Level Search
    if search_term:
        qc_request.GET['transaction_id'] = list(SellerPOSummary.objects\
                    .filter(Q(grn_number__icontains=search_term) |
                        Q(sales_return__return_id__icontains=search_term) |
                        Q(sales_return__return_reference__icontains=search_term) |
                        Q(sales_return__customer__name__icontains=search_term) |
                        Q(sales_return__return_type__icontains=search_term), **search_params, **sps_filter)\
                    .annotate(creation_date_only=Max('creation_date')).order_by(order_data).values_list('id', flat=True))
    elif search_params or column_filter or or_filter:
        qc_request.GET['transaction_id'] = list(SellerPOSummary.objects.filter(**search_params, **sps_filter).filter(or_filter)\
            .annotate(creation_date_only=Max('creation_date')).order_by(order_data).values_list('id', flat=True))
    
    if stop_index:
        limit = stop_index - start_index
        offset = start_index/limit
        qc_request.GET['limit'] = limit
        qc_request.GET['offset'] = offset
    
    #Fetching QC Data From QuanlityControl
    quality_control.request = qc_request
    distinct_values = ['reference_number']
    qc_data = quality_control.get(distinct_values, qc_order_by)
    qc_data = json.loads(qc_data.content)

    search_parameters = {
        'sales_return__warehouse' : user.id,
        'grn_number__in' : [dat['reference_number'] for dat in qc_data['data']['data']]
        }
    master_data = SellerPOSummary.objects.filter(**search_parameters)\
                                        .values(*result_values).distinct()\
                                        .annotate(creation_date_only=Max('creation_date'))\
                                        .order_by(order_data)
    if count:
        temp_data['count'] = qc_data['data'].get('page_info',{}).get('count', 10)
        return
    
    temp_data['recordsTotal'] = qc_data['data'].get('page_info',{}).get('count', 10)
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    for data in master_data:
        create_date_value = ''
        if data.get('creation_date_only',''):
            create_date_value = get_local_date_known_timezone(user_timezone, data.get('creation_date_only',''),True)
            create_date_value = create_date_value.strftime('%d-%m-%Y %H:%M')
        grn_number, grn_reference = data.get('grn_number'), data.get('grn_reference')
        temp_data['aaData'].append(
            OrderedDict((
                         ('Warehouse Id', user.id),
                         ('creation_date', create_date_value),
                         ('grn_number', grn_number),
                         ('grn_reference', grn_reference),
                         ('grn_display_key', grn_reference or grn_number),
                         ('sales_return__return_id',  data['sales_return__return_id']),
                         ('sales_return__return_reference', data['sales_return__return_reference']),
                         ('sales_return__customer__customer_id', data['sales_return__customer__customer_id']),
                         ('sales_return__customer__name', data['sales_return__customer__name']),
                         ('sales_return__return_type',  data['sales_return__return_type'])
                        )))