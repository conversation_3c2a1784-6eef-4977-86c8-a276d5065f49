import json
import copy
import datetime

from django.http import HttpRequest, HttpResponse, JsonResponse
from django.db.models import F, Max, Count
from django.db import transaction
from django.core.cache import cache


#Model Imports
from inbound.models import Seller<PERSON><PERSON><PERSON>mary, QualityCheck, POLocation
from inventory.models import (
    StockDetail, LocationMaster, BatchDetail, StagingLocation,
    )
from core.models import QCConfiguration, MasterDocs, SKUMaster
from quality_control.models import QualityControlSummary, QualityControl
from outbound.models import SellerOrderSummary
from quality_control.views import QualityControlSet


#Method Imports
from core_operations.views.common.main import (
    get_decimal_value, truncate_float,
    get_user_time_zone, get_local_date_known_timezone,
    get_uploaded_files_data, WMSListView, get_misc_value,
    get_multiple_misc_values, create_default_zones, prepare_grn_ext_data,
    scroll_data, get_user_ip, get_user_prefix_incremental
    )

from inbound.views.common.constants import INBOUND_STAGING_LANES_MAPPING, INVALID_PAYLOAD_CONST
from inbound.views.common.fetch_query import get_alias_names_for_model_fields
from inbound.views.common.common import get_sku_data_with_sku_ids
from inbound.views.putaway.suggestions import (
    remaining_location_carton_quantity, save_suggested_po_location
    )
from inbound.views.putaway.suggestions import putaway_suggestions_for_grn

from .common import (
    fetch_pending_stock_for_quality_control, fetch_pending_qc_quantity,
    get_sps_data_from_qc_trasaction_ids)
from .validation import validate_quality_check

from inventory.views.locator.stock_detail import reduce_stocks

from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/grn' + today + '.log')
log_err = init_logger('logs/grn.log')

def remining_carton_quantity(location_obj, warehouse):
    putaway_quantity = POLocation.objects.filter(
        location_id=location_obj.id, status=1, location__zone__user=warehouse.id).values("carton_id").distinct().count()
    filled_capacity = StockDetail.objects.filter(
        location_id=location_obj.id, quantity__gt=0, sku__user=warehouse.id).values("carton_id").distinct().count()
    max_capacity = location_obj.max_capacity
    filled_capacity += putaway_quantity
    remaining_capacity = max_capacity - filled_capacity
    return remaining_capacity

def create_qc_short_quantity_in_stock(warehouse, sps, sku_master, po_json_data, short_qty_details):
    """
    Create QC Short Quantity in Stock
    """
    warehouse_profile_id = warehouse.userprofile.id
    short_quantity = short_qty_details.get('short_quantity', 0)
    has_staging_lanes = short_qty_details.get('has_staging_lanes', False)
    receipt_type = short_qty_details.get('receipt_type', '')
    qc_staging = short_qty_details.get('qc_staging', '')
    stage_stock_id = short_qty_details.get('stage_stock_id', '')
    batch_obj = short_qty_details.get('batch_obj', None)
    putaway_type = short_qty_details.get('putaway_type', '')
    put_zone = "SHORT_ZONE"
    #check for short locations
    locations = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone=put_zone).order_by('fill_sequence')
    if not locations.exists():
        locations = create_default_zones(warehouse, 'SHORT_ZONE', 'SHORT1', 99999, segregation='non_sellable')

    #prepare polocation data for short qty
    po_json_data['remarks'] = 'shortage'
    for loc in locations:
        location_data = {
            'location_id': loc.id, "status" : 0,
            "sku_id": sku_master.id,
            "receipt_number":sps.receipt_number,
            "json_data": po_json_data,
            "purchase_order_id":sps.purchase_order_id,
            "seller_po_summary_id": sps.id,
            "quantity" :0,
            "batch_detail": batch_obj,
            "original_quantity" :short_quantity,
            "account_id" : warehouse_profile_id,
            "putaway_type": putaway_type,
            'reference_number': sps.grn_number
            }
        po_loc = POLocation.objects.create(**location_data)
        break

    if has_staging_lanes:
        if not stage_stock_id:
            stage_objs = StockDetail.objects.filter(receipt_type=qc_staging, receipt_number=sps.id)
            if stage_objs.exists():
                stage_stock_id = stage_objs[0].id
        StockDetail.objects.filter(id=stage_stock_id).update(quantity=F('quantity')-short_quantity)
    supplier_id = sps.purchase_order.open_po.supplier_id if sps.purchase_order else None
    StockDetail.objects.create(
                receipt_number = po_loc.id, grn_number = sps.grn_number,
                location_id = loc.id, receipt_date =  datetime.datetime.now(),
                original_quantity = short_quantity, supplier_id = supplier_id,
                quantity = short_quantity, sku_id = sku_master.id, status = 1,
                receipt_type = receipt_type, batch_detail=batch_obj,
                account_id=warehouse.userprofile.id
                )

def create_po_loc_for_qc_rejected_quantity(warehouse, sps, sku_master, reject_qty_details, staging_list):
    """
    Create po location objects fir QC rejected quantity 
    """
    rejected_quantity = reject_qty_details.get('rejected_quantity', 0)
    put_zone = "DAMAGED_ZONE"
    carton_id = reject_qty_details.get('carton_id')
    qc_staging = reject_qty_details.get('qc_staging', '')
    next_lane = reject_qty_details.get('next_lane', '')
    qc_remarks = reject_qty_details.get('qc_remarks', '')
    po_json_data  = reject_qty_details.get('po_json_data', '')
    stage_stock_id = reject_qty_details.get('stage_stock_id', '')
    batch_id = reject_qty_details.get('batch_id')
    putaway_type = reject_qty_details.get('putaway_type', '')
    #Check for damaged locations
    locations = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone=put_zone).order_by('fill_sequence')
    if not locations.exists():
        locations = create_default_zones(warehouse, 'DAMAGED_ZONE', 'D1', 99999, segregation='non_sellable')
    for loc in locations:
        location_data= {
            'purchase_order_id': sps.purchase_order_id, 'location_id': loc.id,
            'status' : 1, "receipt_number":sps.receipt_number,
            "json_data": po_json_data,
            "seller_po_summary_id": sps.id,
            "carton_id": carton_id,
            'quantity' : rejected_quantity,
            'original_quantity' : rejected_quantity,
            'sku_id': sku_master.id,
            'putaway_type': putaway_type,
            'batch_detail_id' : batch_id if batch_id else None,
            'account_id' : warehouse.userprofile.id,
            'reference_number': sps.grn_number
            }
        po_loc= POLocation.objects.create(**location_data)

        staging_location = StagingLocation.objects.filter(
            warehouse=warehouse, transaction_id=sps.id, segregation='QC Staging', status=1)
        if staging_location.exists():
            stage_loc = staging_location[0]
            stage_loc.quantity += rejected_quantity

        if not stage_stock_id:
            stage_objs = StockDetail.objects.filter(receipt_type=qc_staging, receipt_number=sps.id)
            if stage_objs.exists():
                stage_stock_id = stage_objs[0].id

        if next_lane == 'pnd':
            staging_list.append({
                "grn_number": sps.grn_number,
                "quantity":rejected_quantity,
                "sku_id": sku_master.id,
                "batch_detail": sps.batch_detail_id,
                "receipt_number": po_loc.id,
                "receipt_type": "Putaway Staging",
                "location_id": loc.id,
                "po_loc_id": po_loc.id,
                "stage_stock_id": stage_stock_id,
                "remarks" : qc_remarks,
            })
        break
    return staging_list


def find_carton_locations_for_putaway(warehouse, cartons_location_mapping_dict, carton_extra_dat):
    """
    Feteche carton locations for putaway
    and returns carton wise locations
    """
    extra_dat = carton_extra_dat['extra_dat']
    po_type = carton_extra_dat['po_type']
    express_putaway_type = carton_extra_dat['express_putaway_type']
    carton_number = carton_extra_dat['carton_number']
    inbound_packing = carton_extra_dat['inbound_packing']
    sku_master = carton_extra_dat['sku_master']
    #fetching cross dock locations for backorder
    if po_type.lower()=="backorder" and express_putaway_type == "full_carton_putaway":
        if carton_number not in cartons_location_mapping_dict:
            locations = LocationMaster.objects.filter(zone__segregation="cross_dock", zone__user=warehouse.id, status=1).order_by('fill_sequence')
            for location_rec_obj in locations:
                remaining_capacity = remining_carton_quantity(location_rec_obj, warehouse)
                if remaining_capacity <= 0:
                    continue
                cartons_location_mapping_dict[carton_number] = location_rec_obj.id
                break
    elif inbound_packing == 'true' and carton_number:
        if carton_number not in cartons_location_mapping_dict and not \
        (sku_master.sku_category.lower() in extra_dat['sku_category_list_pf'] or \
            sku_master.sku_category.lower() in extra_dat['sku_category_list_ba']):
            location_filters = {
                'zone__user' : warehouse.id,
                'status' : 1,
                'carton_capacity__gt' : 0
                }
            exclude_filters = {
                'zone__segregation__in' : ['outbound_staging', 'sorting']
                }
            locations = LocationMaster.objects.filter(**location_filters).exclude(**exclude_filters).order_by('fill_sequence')
            for location_rec_obj in locations:
                remaining_capacity = remaining_location_carton_quantity(location_rec_obj, warehouse)
                if remaining_capacity <= 0:
                    continue
                cartons_location_mapping_dict[carton_number] = location_rec_obj.id
                break

def prepare_po_loc_dict_data(warehouse, request_data, qc_dict):
    """Generate putaway suggestions after quality control."""
    staging_lanes_mapping = INBOUND_STAGING_LANES_MAPPING
    qc_staging = staging_lanes_mapping.get('qc', {}).get('name', '')
    staging_list = []
    po_uom_qty = 1
    #Retrieve configurations related to putaway suggestions
    misc_permission_dict = get_multiple_misc_values(['express_putaway', 'location_sku_mapping',
        'receive_po_mandatory_fields', 'return_po_qty', 'expense_item_putaway', 'inbound_packing',
        'inbound_staging_lanes',], warehouse.id)
    express_putaway_type = misc_permission_dict.get('express_putaway')
    inbound_packing = misc_permission_dict.get('inbound_packing')
    inbound_staging_lanes = misc_permission_dict.get('inbound_staging_lanes')
    next_lane, has_staging_lanes = '', False
    if inbound_staging_lanes:
        all_lanes = inbound_staging_lanes.split(',')
        if 'qc' in all_lanes and all_lanes[-1] != "qc":
            has_staging_lanes = True
            qc_index = all_lanes.index('qc')
            next_lane = all_lanes[qc_index+1]
    
    sku_ids, batch_ids = [], []
    for qc in qc_dict.values():
        sku_ids.append(qc['custom_reference'])
        batch_ids.append(qc['batch_id'])

    sku_objs_dict = SKUMaster.objects.in_bulk(sku_ids)
    batch_objs_dict = BatchDetail.objects.in_bulk(batch_ids)

    extra_dat = prepare_grn_ext_data(warehouse)
    for data in request_data:
        qc_data = qc_dict.get(data['id'], {})
        accepted_quantity = data['approved_quantity']
        rejected_quantity = data['rejected_quantity']
        short_quantity = data['short_quantity']

        sps = SellerPOSummary.objects.get(id=data['transaction_id'])
        stage_stock_id = data.get('stage_stock_id')
        qc_remarks = data.get('remarks')

        if sps.purchase_order:
            po_obj = sps.purchase_order
            sku_master = po_obj.open_po.sku
            po_type= str(po_obj.po_type)
            purchase_order_id = po_obj.id
            transact_type = 'po_loc'
            putaway_type = 'po_grn'
            supplier_id =  po_obj.open_po.supplier.id,
            po_uom_qty = po_obj.pcf or 1
        else:
            purchase_order_id = None
            sku_master = sps.sales_return_batch.sales_return_sku.sku
            po_type = ''
            sos_id = sps.sales_return_batch.sellerordersummary_id
            order_json = {}
            if sos_id:
                order_json = SellerOrderSummary.objects.filter(id=sos_id, order_status_flag__in=['customer_invoices', 'delivery_challans']).values('json_data')[0].get('json_data', {})
            if order_json:
                po_uom_qty = order_json.get('pack_uom_quantity', 1)
            else:
                po_uom_qty = 1
            putaway_type = 'sales_return'
            supplier_id = None
            transact_type = 'return_loc'

        carton = sps.carton
        carton_number = ''
        carton_id = None
        if carton:
            carton_number = carton.bin_number
            carton_id = carton.id
        
        if sku_objs_dict.get(qc_data.get('custom_reference')):
            sku_master = sku_objs_dict[qc_data['custom_reference']]

        batch_obj = None
        batch_dict = {}
        if batch_objs_dict.get(qc_data.get('batch_id')):
            batch_obj = batch_objs_dict[qc_data['batch_id']]

            batch_dict = copy.deepcopy(batch_obj.__dict__)

            del batch_dict['_state']
            del batch_dict['id']

            batch_dict['buy_price'] = batch_dict['buy_price'] / po_uom_qty
            if batch_dict['json_data'] and batch_dict['json_data'].get('sku_landed_cost'):
                batch_dict['json_data']['sku_landed_cost'] = batch_dict['json_data']['sku_landed_cost'] / po_uom_qty

        sku_master_dict = sku_master.__dict__
        cartons_location_mapping_dict = {}
        put_zone = sku_master.zone
        po_json_data = qc_data.get('json_data', {}).get('po_json_data', {})
        receipt_type = qc_data.get('json_data', {}).get('receipt_type', 'po_receipt')

        if accepted_quantity:
            carton_extra_dat = {
                'extra_dat' : extra_dat,
                'carton_number' : carton_number,
                'sku_master' : sku_master,
                'po_type' : po_type,
                'express_putaway_type' : express_putaway_type,
                'inbound_packing' : inbound_packing
            }
            #Fetch carton wise locations for carton putaway
            find_carton_locations_for_putaway(warehouse, cartons_location_mapping_dict, carton_extra_dat)

            po_location_dict = {
                "sku_id": sku_master_dict['id'],
                "sku_code": sku_master_dict['sku_code'],
                "purchase_order_id":purchase_order_id,
                "receipt_number": sps.receipt_number,
                "batch_obj": batch_obj,
                "batch_detail_id" : sps.batch_detail_id,
                "grn_quantity": accepted_quantity,
                "seller_summary_id": sps.id,
                "json_data": po_json_data,
                "carton": carton_id,
                "putaway_type": po_type,
                "sku_mrp": sku_master_dict['mrp'],
                "sku_buy_price": sku_master_dict['price'],
                "location_id": cartons_location_mapping_dict.get(carton_number, None),
                "grn_number": sps.grn_number,
                "receipt_type": receipt_type,
                "supplier_id": supplier_id,
                "transact_type": transact_type,
                "next_lane" : next_lane,
                "uom_qty": po_uom_qty,
                "stage_stock_id" : stage_stock_id,
                'reference_number': sps.grn_number
                }
            #generate putaway suggestions for accepted quantity
            status, staging_data = save_suggested_po_location(put_zone, po_location_dict, batch_dict, warehouse,
                                                                                cartons_location_mapping_dict, sku_master, extra_dat, misc_permission_dict)
            
            staging_data = [dict(item, **{'remarks': qc_remarks}) for item in staging_data]
            staging_list.extend(staging_data)

        #create short quantity in Stock
        if short_quantity:
            short_qty_details = {
                'has_staging_lanes': has_staging_lanes,
                'receipt_type': receipt_type,
                'short_quantity': short_quantity * po_uom_qty,
                'qc_staging': qc_staging,
                'stage_stock_id': stage_stock_id,
                'putaway_type': putaway_type,
                'batch_obj' : batch_obj
            }
            create_qc_short_quantity_in_stock(warehouse, sps, sku_master, po_json_data, short_qty_details)

        #generate putaway suggestions for rejected quantity
        if rejected_quantity:                
            reject_qty_details = {
                'qc_staging': qc_staging,
                'rejected_quantity': rejected_quantity * po_uom_qty,
                'qc_remarks': qc_remarks,
                'po_json_data': po_json_data,
                'next_lane': next_lane,
                'carton_id': carton_id,
                'batch_id' : qc_data.get('batch_id', 0),
                'putaway_type': putaway_type,
            }
            staging_list = create_po_loc_for_qc_rejected_quantity(warehouse, sps, sku_master, reject_qty_details, staging_list)
    return staging_list 



def prepare_jo_po_loc_dict_data(warehouse, request_data, qc_dict):
    extra_dat = prepare_grn_ext_data(warehouse)
    for data in request_data:
        qc_data = qc_dict.get(data['id'], {})
        accepted_quantity = data['approved_quantity']
        rejected_quantity = data['rejected_quantity']
        jo_grn = SellerPOSummary.objects.get(id=data['transaction_id'])
        jo_obj = jo_grn.job_order
        batch_obj = jo_grn.batch_detail
        sku_master = jo_obj.product_code
        sku_master_dict = sku_master.__dict__
        put_zone = sku_master.zone
        if put_zone:
            put_zone = put_zone.id
        if data.get('put_zone'):
            put_zone = data['put_zone']
        json_data = {}
        receipt_number = 1
        if qc_data.get('json_data', {}):
            json_data= qc_data.get('json_data', {}).get('po_json_data', {})
            receipt_number = qc_data.get('json_data', {}).get('receipt_number', 1)
        
        batch_dict = {}
        if batch_obj:
            batch_dict = copy.deepcopy(batch_obj.__dict__)
            del batch_dict['creation_date']
            del batch_dict['updation_date']
            del batch_dict['_state']
        
        if accepted_quantity:
            po_location_dict = {
                "sku_id": sku_master_dict['id'],
                "sku_code": sku_master_dict['sku_code'],
                "job_order_id": jo_obj.id,
                "grn_quantity": accepted_quantity,
                "putaway_type": "jo_putaway",
                "sku_mrp": sku_master_dict['mrp'],
                "sku_buy_price": sku_master_dict['price'],
                "receipt_type": "job_order",
                "json_data": json_data,
                "grn_number": jo_grn.grn_number,
                "receipt_number": receipt_number,
                "location_id": '',
                "jo_grn_id": jo_grn.id,
                'reference_number': jo_obj.job_code
            }
            if batch_obj:
                po_location_dict.update({
                    "batch_obj": batch_obj,
                    "batch_detail_id" : batch_obj.id,
                })
                del batch_dict['id']
            status, staging_data = save_suggested_po_location(put_zone, po_location_dict, batch_dict, warehouse,
                                                                    {}, sku_master, extra_dat, {}, job_order=True)
        if rejected_quantity:
            put_zone = "DAMAGED_ZONE"
            locations = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone=put_zone).order_by('fill_sequence')
            if not locations.exists():
                locations = create_default_zones(warehouse, 'DAMAGED_ZONE', 'D1', 99999, segregation='non_sellable')

            for loc in locations:
                location_data= {
                            'job_order_id': jo_obj.id,
                            'location_id': loc.id,
                            'status' : 1, 
                            "receipt_number":receipt_number,
                            "json_data": json_data,
                            "seller_po_summary_id": jo_grn.id,
                            'quantity' : rejected_quantity,
                            'original_quantity' : rejected_quantity,
                            'sku_id': sku_master.id,
                            'putaway_type': 'job_order',
                            'account_id' : warehouse.userprofile.id,
                            'batch_detail_id' : batch_obj.id if batch_obj else None,
                            'reference_number': jo_obj.job_code

                        }
                POLocation.objects.create(**location_data)
                break

class QCView(WMSListView):
    def get_qc_data(self, staging_filter):
        grn_number = self.request.GET['grn_number']

        quality_control = QualityControlSet()
    
        self.qc_type_dict = {
            'after_po_grn' : 'after_grn',
            'after_sr_grn' : 'after_sr_grn'
            }
        
        qc_request = HttpRequest()
        qc_request.warehouse = self.warehouse
        qc_request.user = self.request.user
        qc_request.GET = {
            'status': 0, 'reference_number' : grn_number, 'quantity__gt' : 0,
            'transaction_type': self.qc_type_dict.get(self.transact_type),
            **staging_filter
            }
        distinct_values = [
            'id','warehouse_id','transaction_type','reference_number', 'custom_reference',
            'batch_id', 'transaction_id','location_id','total_quantity','sampled_quantity', 
            'approved_quantity','rejected_quantity', 'short_quantity', 'json_data'
            ]

        quality_control.request = qc_request
        qc_data = quality_control.get(distinct_values)
        return json.loads(qc_data.content)['data']['data']

    def get_staging_stock(self, sps_dict):
        stock_dict = {}
        stock_filters = {
            'receipt_type' : self.qc_staging,
            'receipt_number__in' : list(sps_dict.keys()),
            'quantity__gt' : 0,
            'sku__user' : self.warehouse.id
            }
        stage_stock_data = list(StockDetail.objects.filter(**stock_filters).values('id', 'receipt_number', 'location__location'))
        for stock in stage_stock_data:
            if stock['receipt_number'] in stock_dict:
                if stock['location__location'] not in stock_dict[stock['receipt_number']]:
                    stock_dict[stock['receipt_number']].append(stock['location__location'])
            else:
                stock_dict[stock['receipt_number']] = [stock['location__location']]
        return stock_dict
    
    def get_grn_rejected_quantities(self, sps_dict):
        #To Pre-Populate Rejected Quantity in Pop-up
        qc_qty_dict= {}
        qc_quantities = list(QualityCheck.objects.filter(
            po_location__seller_po_summary__in=list(sps_dict.keys())
            ).values('po_location__seller_po_summary_id', 'accepted_quantity', 'rejected_quantity'))
        for each_row in qc_quantities:
            sps_id = each_row['po_location__seller_po_summary_id']
            qc_qty_dict[sps_id] = {'accepted_quantity' :each_row['accepted_quantity'], 'rejected_quantity' : each_row['rejected_quantity']}
        return qc_qty_dict
    
    def get_batch_details(self, batch_ids):
        batch_details = BatchDetail.objects.filter(
            id__in=batch_ids).values('id', 'batch_no', 'manufactured_date', 'expiry_date', 'mrp')
        batch_data_dict = {batch.pop('id'): batch for batch in batch_details}
        return batch_data_dict
    
    def get_uploaded_files(self, qc_ids):
        master_docs_filter = {
            'master_id__in': qc_ids,
            'master_type': 'QualityControl',
            'user_id': self.warehouse.id,
            'extra_flag': 'pending',
        }
        uploaded_file_dicts = get_uploaded_files_data(self.request, master_docs_filter)
        return uploaded_file_dicts
    
    def get_qc_reject_reasons(self):
        qc_config_filter = {
            'warehouse_id': self.warehouse.id,
            'transaction_type' : self.qc_type_dict.get(self.transact_type)
            }
        qc_reject_reasons = list(QCConfiguration.objects.filter(
            **qc_config_filter).values_list('reasons', flat=True))
        return qc_reject_reasons

    def get_rejected_quantity(self, data, qc_qty_dict, pending_quantity):
         #Rejected Quantity Calculation based on GRN Rejection
        rejected_quantity = 0
        grn_rejected_quantity = qc_qty_dict.get(data.get('transaction_id'),{}).get('rejected_quantity', 0)
        if data['json_data'].get('is_child_sku'):
            grn_rejected_quantity = data['json_data'].get('rejected_quantity')

        rem_rej_qty = truncate_float(grn_rejected_quantity - data['rejected_quantity'], self.decimal_limit)
        if rem_rej_qty > 0:
            rejected_quantity = rem_rej_qty
        if rejected_quantity > pending_quantity:
            rejected_quantity = pending_quantity
        
        return rejected_quantity
    
    def format_date_keys(self, date_keys, data_dict):
        for key in date_keys:
            if data_dict.get(key, ''):
                data_dict[key] = get_local_date_known_timezone(self.timezone, data_dict[key], send_date=True).strftime('%m/%d/%Y')
            else:
                data_dict[key] = ''
        return data_dict
        
    def get(self, *args, **kwargs):
        self.set_user_credientials()

        self.timezone = get_user_time_zone(self.warehouse)
        self.decimal_limit = get_decimal_value(self.warehouse.id)
        
        self.transact_type = self.request.GET['type']

        self.has_qc_staging_lane, sps_filter, staging_filter, pending_qc_dict, qc_stock_dict = \
                        fetch_pending_stock_for_quality_control('', 'po_grn', self.warehouse.id)

        self.qc_staging = 'QC Staging'
        qc_data = self.get_qc_data(staging_filter)
        qc_ids, sps_ids, sku_ids, batch_ids = [], [], [], []
        for qc in qc_data:
            qc_ids.append(qc['id'])
            sps_ids.append(qc['transaction_id'])
            sku_ids.append(qc['custom_reference'])
            batch_ids.append(qc['batch_id'])

        date_keys = ['manufactured_date', 'expiry_date', 'po_date', 'grn_date', 'return_date']

        #Fetching Seller PO Summary Data for Pending QC Transaction ids
        sps_dict = get_sps_data_from_qc_trasaction_ids(sps_ids, self.warehouse)
        
        #Get SKU Details 
        sku_data_dict = get_sku_data_with_sku_ids(sku_ids, self.warehouse)

        #Get Batch Details
        batch_data_dict = self.get_batch_details(batch_ids)
        
        #Get Staging Lane Stock
        stock_dict = self.get_staging_stock(sps_dict)
        
        #To Pre-Populate Rejected Quantity in Pop-up
        qc_qty_dict = self.get_grn_rejected_quantities(sps_dict)

        #Get Uploaded Documents
        uploaded_file_dicts = self.get_uploaded_files(qc_ids)

        #Fetching QC Rejected Reasons
        qc_reject_reasons = self.get_qc_reject_reasons()

        data_list = []
        for data in qc_data:
            stage_locations = stock_dict.get(data['transaction_id'], [''])
            for stage_loc in stage_locations:
                data_dict = copy.deepcopy(data)
                data_dict.update(sps_dict.get(data['transaction_id'], {}))  
                
                if sku_data_dict.get(data.get('custom_reference')):
                    sku_data_dict[data['custom_reference']].pop('id', '')
                    data_dict.update(sku_data_dict[data['custom_reference']])
                if batch_data_dict.get(data.get('batch_id')):
                    data_dict.update(batch_data_dict[data['batch_id']])
                
                data_dict = self.format_date_keys(date_keys, data_dict)                
                
                #Fetching Pending Quantity After Dropping into QC Staging
                if self.has_qc_staging_lane:
                    stage_stock_id = (data['transaction_id'], stage_loc)
                    data_dict.update({'stage_stock_id' : stage_stock_id})
                    pending_quantity = pending_qc_dict.get(stage_stock_id, 0)
                    data_dict['from_location'] = stage_loc
                else:
                    pending_quantity = truncate_float(data_dict['total_quantity'] - (
                        data_dict['approved_quantity'] + data_dict['rejected_quantity'] + data_dict['short_quantity']),
                            self.decimal_limit)

                
                rejected_quantity = self.get_rejected_quantity(data, qc_qty_dict, pending_quantity)
                if not pending_quantity:
                    continue

                data_dict['po_display_key'] = data_dict.get('po_reference', '') or data_dict.get('po_number', '')
                data_dict['grn_display_key'] = data_dict.get('grn_reference', '') or data_dict['grn_number']
                data_dict['batch_display_key'] = data_dict.get('batch_reference', '') or data_dict['batch_no']
                data_dict['pending_quantity'] = pending_quantity
                data_dict['approved_quantity'] = 0
                data_dict['rejected_quantity'] = rejected_quantity
                data_dict['short_quantity'] = 0
                data_dict['remarks'] = ''
                data_dict['qc_reject_reasons'] = qc_reject_reasons[0].split(',') if qc_reject_reasons else []
                data_dict['uploaded_file_dict'] = uploaded_file_dicts.get(str(data['id']), {})
                data_list.append(data_dict)
        
        return HttpResponse(json.dumps({'data':data_list}))


class WMSQualityControlSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None):
        return None
    

    def get_filters_for_qc_type(self, qc_type, line_values_list):
        if qc_type == 'po_grn':
            qc_transact_type = 'after_grn'
            user_filter = {'purchase_order__open_po__sku__user':  self.warehouse.id}
            map_values_list = ['po_number', 'po_reference', 'supplier_id','supplier_name', 'order_type']
            field_mapping, calc_formulae = get_alias_names_for_model_fields('grn')
            values_dict = {each_key : F(field_mapping[each_key]) for each_key in map_values_list}
            line_values_dict = {each_key : F(field_mapping[each_key]) for each_key in line_values_list}
            qc_config_filter = {'warehouse_id': self.warehouse.id, 'transaction_type' : 'after_grn'}
        elif qc_type == 'sr_grn':
            qc_transact_type = 'after_sr_grn'
            user_filter = {'sales_return__warehouse' :  self.warehouse.id}
            map_values_list = ['return_id', 'return_reference', 'customer_name','customer_id', 'return_type']
            field_mapping, calc_formulae = get_alias_names_for_model_fields('sr_grn')
            values_dict = {each_key : F(field_mapping[each_key]) for each_key in map_values_list}
            line_values_dict = {each_key : F(field_mapping[each_key]) for each_key in line_values_list}
            qc_config_filter = {'warehouse_id': self.warehouse.id, 'transaction_type' : 'after_sr_grn'}
        elif qc_type == 'jo_grn':
            qc_transact_type = 'after_jo_grn'
            user_filter = {'job_order__product_code__user' :  self.warehouse.id}
            map_values_list = ['jo_number', 'jo_reference']
            field_mapping, calc_formulae = get_alias_names_for_model_fields('jo_grn')
            values_dict = {each_key : F(field_mapping[each_key]) for each_key in map_values_list}
            line_values_list.remove('bin_number')
            line_values_dict = {each_key : F(field_mapping[each_key]) for each_key in line_values_list}
            qc_config_filter = {'warehouse_id': self.warehouse.id, 'transaction_type' : 'after_jo_grn'}
        
        return qc_transact_type, user_filter, values_dict, line_values_dict, qc_config_filter
    
    def get_staging_filter(self, qc_type):
        self.staging_filter, sps_filter, pending_qc_dict = {}, {}, {}
        inbound_staging_lanes = get_misc_value('inbound_staging_lanes', self.warehouse.id)
        self.qc_staging = self.staging_lanes_mapping.get('qc', {}).get('name', '')
        self.has_qc_staging_lane = False
        if qc_type == 'po_grn' and 'qc' in inbound_staging_lanes.split(','):
            self.has_qc_staging_lane = True
            pending_qc_dict = fetch_pending_qc_quantity(self.qc_staging, qc_type, self.warehouse.id)
            sps_filter['id__in'] = list(pending_qc_dict.keys())
            self.staging_filter['transaction_id'] = list(pending_qc_dict.keys())
        
        return sps_filter, pending_qc_dict
    
    def get_qc_data(self, reference_number, qc_transact_type, values_dict, line_values_dict):
        quality_control = QualityControlSet()
        qc_request = HttpRequest()
        qc_request.user = self.user
        qc_request.warehouse = self.warehouse
        qc_request.GET = {'status': 0, 'transaction_type': qc_transact_type, **self.staging_filter}
        if reference_number:
            qc_request.GET['reference_number'] = reference_number

        quality_control.request = qc_request
        distinct_values = ['reference_number']
        if self.line_items == 'true':
            values_dict.update(line_values_dict)
            distinct_values = ['id','warehouse_id',
                'transaction_type','reference_number','transaction_id', 'location_id',
                'total_quantity','sampled_quantity','approved_quantity','rejected_quantity',
                'short_quantity', 'custom_reference', 'batch_id', 'json_data'
                ]
        
        qc_data = quality_control.get(distinct_values, 'reference_number')
        qc_data = json.loads(qc_data.content)
        qc_list = qc_data['data']['data']
        return qc_list

    def get_grn_rejected_quantities(self, sps_dict):
        qc_qty_dict, sps_ids= {}, []
        qc_quantities = list(QualityCheck.objects.filter(
            po_location__seller_po_summary__in=list(sps_dict.keys())
            ).values('po_location__seller_po_summary_id', 'accepted_quantity', 'rejected_quantity'))
        
        for each_row in qc_quantities:
            sps_id = each_row['po_location__seller_po_summary_id']
            sps_ids.append(sps_id)
            qc_qty_dict[sps_id] = {
                'accepted_quantity' :each_row['accepted_quantity'],
                'rejected_quantity' : each_row['rejected_quantity']
                }
        return qc_qty_dict, sps_ids

    def get_batch_details(self, batch_ids):
        batch_details = BatchDetail.objects.filter(id__in=batch_ids).values('id', 'batch_no', 'manufactured_date','expiry_date', 'mrp')
        batch_data_dict = {batch.pop('id'): batch for batch in batch_details}
        return batch_data_dict
    
    def get_staging_stock(self, sps_ids):
        stock_detail = list(StockDetail.objects.filter(
                receipt_type=self.qc_staging, receipt_number__in=sps_ids).values('id','receipt_number', 'location__location'))
        qc_stock_dict = {each_row.get('receipt_number'): each_row for each_row in stock_detail}
        return qc_stock_dict

    def get_pending_quantity(self, data, data_dict, pending_qc_dict, qc_stock_dict):
         #Fetching Pending Quantity After Dropping into QC Staging
        if self.has_qc_staging_lane:
            pending_quantity = pending_qc_dict[data['transaction_id']]
            data_dict['from_location'] = qc_stock_dict.get(data['transaction_id'], {}).get('location__location', '')
        else:
            pending_quantity = truncate_float(data_dict['total_quantity'] - 
                (data_dict['approved_quantity'] + data_dict['rejected_quantity'] + data_dict['short_quantity']), self.decimal_limit)
        return pending_quantity
    
    def format_date_keys(self, data_dict, date_keys):
        for key in date_keys:
            if data_dict[key]:
                data_dict[key] = get_local_date_known_timezone(self.timezone, data_dict[key], send_date=True).strftime('%m/%d/%Y')
            else:
                data_dict[key] = ''
        return data_dict
    
    def get_rejected_quantity(self, data, qc_qty_dict, pending_quantity):
        #Rejected Quantity Calculation based on GRN Rejection
        grn_rejected_quantity = qc_qty_dict.get(data.get('transaction_id'),{}).get('rejected_quantity', 0)
        if data['json_data'].get('is_child_sku'):
            grn_rejected_quantity = data['json_data'].get('rejected_quantity')
        
        rejected_quantity = 0
        rem_rej_qty = truncate_float(grn_rejected_quantity - data['rejected_quantity'], self.decimal_limit)
        if rem_rej_qty > 0:
            rejected_quantity = rem_rej_qty
        if rejected_quantity > pending_quantity:
            rejected_quantity = pending_quantity
        
        return rejected_quantity

    def get(self,*args, **kwargs):
        self.staging_lanes_mapping = INBOUND_STAGING_LANES_MAPPING
        self.set_user_credientials()
        request = self.request
        
        self.source = request.GET.get('source')
        self.line_items = request.GET.get('line_items')
        limit = request.GET.get('limit', 10)
        
        self.decimal_limit = get_decimal_value(self.warehouse.id)
        self.timezone = get_user_time_zone(self.warehouse)

        #Incremental Searches
        inc_filter_mapping = {
            'grn_number' : 'grn_number__icontains',
            'po_number' : 'purchase_order__po_number__icontains',
            'supplier_name' : 'purchase_order__open_po__supplier__name__icontains',
            'jo_number' : 'job_order__job_code__icontains',
            'return_id' : 'sales_return__return_id__icontains',
            'customer_name' : 'sales_return__customer__name__icontains',
        }
        search_parameters = {}
        search_type = request.GET.get('search_type')
        search_value = request.GET.get('search_value')
        if search_type and search_value:
            search_parameters[inc_filter_mapping[search_type]] = search_value

                
        qc_type = request.GET.get('qc_type')
        reference_number = request.GET.get('reference_number')
        values_list = ['grn_number']
        line_values_list = ['sku_code', 'sku_desc', 'sku_size', 'sku_image',
            'sps_id', 'bin_number', 'mrp', 'batch_no', 'manufactured_date', 'expiry_date']
        date_keys =['manufactured_date', 'expiry_date']
        
        qc_transact_type, user_filter, values_dict, \
            line_values_dict, qc_config_filter = self.get_filters_for_qc_type(qc_type, line_values_list)
        
        sps_filter, pending_qc_dict = self.get_staging_filter(qc_type)
        
        #Fetching QC Rejected Reasons
        qc_reject_reasons = list(QCConfiguration.objects.filter(**qc_config_filter).values_list('reasons', flat=True))

        
        qc_list = self.get_qc_data(reference_number, qc_transact_type, values_dict, line_values_dict)

        if reference_number:
            search_parameters['grn_number'] = reference_number
        elif not search_parameters:
            search_parameters['grn_number__in'] = [dat['reference_number'] for dat in qc_list]

        final_data_list = []        
        
        sps_data = list(SellerPOSummary.objects.filter(
                **user_filter,**search_parameters, **sps_filter
            ).values(*values_list, **values_dict
            ).annotate(
                grn_creation_date=Max('creation_date'), sku_count=Count('id')
            ).distinct().order_by('grn_creation_date'))
        
        
        final_data_list = sps_data
        if self.line_items == 'true':
            sps_dict = {dat['sps_id']: dat for dat in sps_data}
    
            #To Pre-Populate Rejected Quantity in Pop-up
            qc_qty_dict, sps_ids = self.get_grn_rejected_quantities(sps_dict)
                    
            sku_ids, batch_ids = [], []
            for qc in qc_list:
                sku_ids.append(qc['custom_reference'])
                batch_ids.append(qc['batch_id'])

            sku_data_dict = get_sku_data_with_sku_ids(sku_ids, self.warehouse)
            
            batch_data_dict = self.get_batch_details(batch_ids)

            #To Show From Location in WEB (Inbound Staging Lanes)
            qc_stock_dict = self.get_staging_stock(sps_ids)

            
            data_list = []
            for data in qc_list:
                data_dict = data
                
                if sku_data_dict.get(data.get('custom_reference')):
                        sku_data_dict[data['custom_reference']].pop('id', '')
                        data_dict.update(sku_data_dict[data['custom_reference']])
                if batch_data_dict.get(data.get('batch_id')):
                        data_dict.update(batch_data_dict[data['batch_id']]) 
                data_dict.update(sps_dict.get(data['transaction_id'], {}))                
                
                self.format_date_keys(data_dict, date_keys)
                
                pending_quantity = self.get_pending_quantity(data, data_dict, pending_qc_dict, qc_stock_dict)
                rejected_quantity = self.get_rejected_quantity(data, qc_qty_dict, pending_quantity)
                               
                if not pending_quantity:
                    continue
                
                data_dict['pending_quantity'] = pending_quantity
                data_dict['approved_quantity'] = 0
                data_dict['short_quantity'] = 0
                data_dict['rejected_quantity'] = rejected_quantity
                data_dict['qc_reject_reasons'] = qc_reject_reasons[0].split(',') if qc_reject_reasons else []
                # data_dict['uploaded_file_dict'] = uploaded_file_dicts.get(str(data['id']), {})
                data_list.append(data_dict)
            final_data_list = data_list 

        page_info = scroll_data(request, final_data_list, limit=limit, request_type='GET')
        page_info['data'] = final_data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = len(final_data_list)
        page_info['error'] = [{'message': 'error_status'}]
        return page_info

    def prepare_lpn_wise_request_data(self, request_data):
        lpn_request_data = []
        for each_data in request_data:
            base_data = copy.deepcopy(each_data)
            if each_data.get('lpns'):
                base_data['rejected_quantity'] = 0
                for lpn in each_data['lpns']:
                    lpn_data = copy.deepcopy(base_data)
                    lpn_data['lpn_number'] = lpn.get('lpn_number', '')
                    if lpn.get('quantity_type', '') == 'rejected_quantity':
                        lpn_data['rejected_quantity'] = lpn.get('packed_quantity', 0)
                        lpn_data['approved_quantity'] = 0
                        lpn_data['short_quantity'] = 0
                    elif lpn.get('quantity_type', '') == 'accepted_quantity':
                        lpn_data['approved_quantity'] = lpn.get('packed_quantity', 0)
                        lpn_data['rejected_quantity'] = 0
                        lpn_data['short_quantity'] = 0
                    lpn_request_data.append(lpn_data)
            lpn_request_data.append(base_data)
        return lpn_request_data

    def create_qc_summary_records(self, request_data, qc_type, qc_file, stock_details):
        '''
        Generate QC Number, Create QualityControlSummary and Save QC files in Master Docs
        '''

        # Get QC prefix and increment QC number
        qc_prefix_val = 'qc'
        _, _, qc_number, _, _ = \
            get_user_prefix_incremental(self.user, qc_prefix_val, sku_code='', dept_code='', create_default="QC")

        # Define master type dictionary
        master_type_dict = {
            'after_grn': 'QualityControl',
            'after_sr_grn': 'QualityControl',
            'after_jo_grn': 'JOQualityControl'
        }

        qc_summary, qc_summary_ids = [], []

        # Prepare request data for LPN-wise processing
        request_data = self.prepare_lpn_wise_request_data(request_data)

        # Iterate over request data
        for data in request_data:
            # Skip if all quantities are zero
            if data['approved_quantity'] == 0 and data['short_quantity'] == 0 and data['rejected_quantity'] == 0:
                continue

            # Create QC summary dictionary
            to_location_id = stock_details.get(data['id'], '')
            qc_summary_dict = {
                'qc_number': qc_number,
                'quality_control_id': data['id'],
                'employee_id': self.request.user.id,
                'approved_quantity': data['approved_quantity'],
                'short_quantity': data['short_quantity'],
                'rejected_quantity': data['rejected_quantity'],
                'remarks': data['remarks'],
                'comments': data.get('comments', ''),
                'account_id' : self.warehouse.userprofile.id,
                'json_data': {'lpn_number': data.get('lpn_number', ''), 'to_location_id': to_location_id},
            }

            # Create QC summary and get its ID
            qc_summary = QualityControlSummary.objects.create(**qc_summary_dict)
            qc_summary_id = qc_summary.id
            qc_summary_ids.append(qc_summary_id)

            # Get master_doc id
            master_doc_id = data.get('uploaded_file_dict', {}).get('id')

            # Create master document if it doesn't exist
            master_dict = {
                'master_type': master_type_dict[qc_type],
                'user_id': self.user.id,
                'account_id' : self.user.userprofile.id
                }
            create_dict = {
                'master_id': str(qc_summary_id),
                'extra_flag': 'completed',
                'uploaded_file': qc_file
                }

            if master_doc_id:
                # Update existing master_doc
                master_filters = {'id': master_doc_id, 'extra_flag': 'pending', 'user_id': self.user.id}
                master_doc = MasterDocs.objects.filter(**master_filters)
                if master_doc.exists():
                    master_doc.update(master_id=qc_summary_id, extra_flag='completed')
            elif qc_file:
                # Create new master_doc
                MasterDocs.objects.create(**master_dict, **create_dict)

        return qc_summary_ids, qc_number

    def delete_cache_for_qc_ids(self, qc_ids):
        """
        Delete cache for the given quality check IDs.
        """
        for qc_id in qc_ids:
            # Generate cache key for each quality check ID
            cache_key = 'QC-' + str(qc_id)

            # Delete cache using the cache key
            cache.delete(cache_key)
    
    def frame_extra_params(self):
        '''
        Frame extra parameters for the request.
        This method prepares additional parameters to be included in the request.
        '''
        extra_params = {
            'headers': self.request.headers,
            'request_meta': self.request.META,
            'request_scheme': self.request.scheme
        }
        return extra_params
    
    def get_qc_staging_stock_details(self, qc_ids):
        '''
        Fetch stock details for quality control staging.
        This method retrieves stock details related to quality control staging
        based on the provided warehouse and quality control IDs.
        '''
        stock_details = dict(StockDetail.objects.filter(sku__user=self.warehouse.id, receipt_number__in=qc_ids, receipt_type = 'grn_packing', quantity__gt=0).values_list('receipt_number', 'location_id'))

        return stock_details

    def get_sku_id_dict(self, sps_ids):
        '''
        Fetch SKU ID dictionary for given Seller PO Summary IDs.
        This method retrieves a dictionary mapping Seller PO Summary IDs to their corresponding SKU IDs.
        '''
        sku_id_dict = dict(SellerPOSummary.objects.filter(id__in=sps_ids).values_list('id', 'sku_id'))

        return sku_id_dict

    def post(self, *args, **kwargs):
        '''
        QC Confirmation
        This method is used to confirm the quality check for inbound items.
        It receives a POST request with JSON data containing the quality check details.

        It validates the quality check data, creates quality control summary records,
        updates the status of quality control summary records, and triggers a callback.
        '''

        # Set user credentials
        self.set_user_credientials()
        response = {}
        request = self.request
        qc_file = ''

        try:
            request_data = json.loads(request.body)
        except:
            try:
                request_data = request.POST.get("data")
                qc_file = request.FILES.get('file')
                if request_data:
                    request_data = json.loads(request_data)
            except:
                response['message'] = INVALID_PAYLOAD_CONST
                return JsonResponse(response, status=400)

        extra_params = self.frame_extra_params()
        # Logging request params
        log.info(f"Request params for WMS QualityControl {request.user.username} is {request_data} ip_address: {get_user_ip(request)}")

        # Validate quality check data
        failed_status, failed_list, qc_data, qc_type, qc_dict, qc_ids, sps_ids = validate_quality_check(request_data, request.user)

        # Fetch qc staging stock details
        stock_details = self.get_qc_staging_stock_details(qc_ids)

        self.sku_id_dict = self.get_sku_id_dict(sps_ids)

        if failed_status:
            # Delete cache
            self.delete_cache_for_qc_ids(qc_ids)
            failed_status = failed_list
            return JsonResponse({'message': failed_status, 'status': 400}, status=400)

        else:
            # Creating QualityControlSummary Records
            qc_summary_ids, qc_number = self.create_qc_summary_records(request_data, qc_type, qc_file, stock_details)

            qc_request = HttpRequest()
            qc_request.user = self.user
            qc_request.warehouse = self.warehouse
            qc_request.POST = {'data': qc_data}
            quality_control = QualityControlSet()
            quality_control.request = qc_request

            staging_data = []
            for data in qc_data:
                staging_data.append({
                'sku_id': self.sku_id_dict.get(data['transaction_id'], ''),
                'receipt_numbers': [data['id']],
                'status': 0,
                'receipt_type': 'grn_packing',
                'batch_detail_id': data['batch_id'],
                'quantity': data['quantity'],
                'lpn_number': data.get('json_data', {}).get('lpn_number', ''),
            })
            with transaction.atomic('default'):
                qc_data = quality_control.put()
                qc_data = json.loads(qc_data.content)
                if qc_data['message'] != 'Success':
                    # Delete cache
                    self.delete_cache_for_qc_ids(qc_ids)
                    return JsonResponse({'message' : qc_data['error']}, status=400)

                # Updating Status When QualityControl has Updated
                QualityControlSummary.objects.filter(id__in=qc_summary_ids).update(status=1)

                # fetch po_type from qc data:
                po_type = ''
                qc_summary = QualityControl.objects.filter(id__in=qc_ids).first()
                if qc_summary:
                    qc_json_data = qc_summary.json_data
                    po_type = qc_json_data.get('po_type', '')

                # Putaway Suggestions for QC Confirmation
                if qc_type in ['after_grn', 'after_sr_grn']:
                    if qc_type == 'after_grn':
                        grn_type = 'PO'
                    else:
                        grn_type = 'SR'
                    extra_params['transaction_type'] = grn_type
                    putaway_suggestions_for_grn(self.warehouse, self.user, grn_type, po_type, '', '', extra_params=extra_params, qc_number=qc_number)
                    #prepare_po_loc_dict_data(self.warehouse, request_data, qc_dict)
                if qc_type == 'after_jo_grn':
                    prepare_jo_po_loc_dict_data(self.warehouse, request_data, qc_dict)

                # Delete cache
                self.delete_cache_for_qc_ids(qc_ids)
            if staging_data:
                reduce_stocks(self.warehouse, staging_data)

        return JsonResponse({'message': 'Success', 'qc_number' : qc_number}, status =200)
    

def update_inspection_quantity(warehouse, lpn_numbers, quantity_increment=False):
    queryset = QualityControl.objects.filter(
        warehouse=warehouse,
        json_data__lpn_number__in=lpn_numbers
    )

    if quantity_increment:
        # Update each object individually (bulk update with F expressions)
        queryset.update(quantity = F('total_quantity'))
    else:
        queryset.update(quantity=0)