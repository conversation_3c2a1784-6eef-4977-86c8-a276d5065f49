import datetime
from rest_framework import serializers
\
from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/putaway_serializer' + today + '.log')

class PutawaySKUSerializer(serializers.Serializer):
    sku_code = serializers.CharField(required=True)
    sku_desc = serializers.CharField(allow_blank=True, required=False)
    sku_size = serializers.CharField(allow_blank=True, required=False)
    id = serializers.IntegerField(min_value=0)
    original_quantity = serializers.FloatField(min_value=0, required = True)
    putaway_quantity = serializers.FloatField(min_value=0, required=False)
    po_price = serializers.FloatField(min_value=0, required=False)
    mrp = serializers.FloatField(min_value=0, required=False)
    batch_number = serializers.CharField(allow_blank=True, required=False)
    manufactured_date = serializers.DateField(required=False)
    expiry_date = serializers.DateField(required=False)
    remarks= serializers.CharField(allow_blank=True, required=False)
    reason = serializers.CharField(allow_blank=True, required=False)
    zone = serializers.CharField(allow_blank=True, required=False)
    location = serializers.CharField(allow_blank=False, required=True)
    measurement = serializers.CharField(allow_blank=True, required=False)
    lpn_number = serializers.CharField(required=False, allow_blank=True)
    serial_numbers = serializers.ListField(required=False, default=[])
    po_number = serializers.CharField(allow_blank=True, required=False)
    grn_number = serializers.CharField(allow_blank=True, required=False)
    pending_stock_id = serializers.CharField(allow_blank=True, required=False)
    location_entry = serializers.CharField(allow_blank=True, required=False)
    sku_entry = serializers.CharField(allow_blank=True, required=False)
    index_key = serializers.IntegerField(min_value=0, required=False)

class PutawaySerializer(serializers.Serializer):
    GRN_TYPE_CHOICES = (
        ('PO', 'PurchaseOrder'),
        ('ST', 'StockTransfer'),
        ('SR', 'SalesReturn'),
    )
    PUTAWAY_TYPE_CHOICES = (
            ('po_putaway', 'po_putaway'),
            ('cp_putaway', 'cp_putaway'),
            ('sr_putaway', 'sr_putaway'),
            ('jo_putaway', 'jo_putaway'),
            ('batosa_putaway', 'BA_TO_SA'),
            ('nte_putaway', 'NTE')
    )
    warehouse = serializers.CharField(required = False)
    employee_id = serializers.CharField(required = False)
    source = serializers.CharField(default="WEB")
    po_number = serializers.CharField(allow_blank=True, required=False)
    grn_number = serializers.CharField(allow_blank=True, required=False)
    grn_type = serializers.ChoiceField(choices=GRN_TYPE_CHOICES, required=False, allow_blank=True)
    putaway_type = serializers.ChoiceField(choices=PUTAWAY_TYPE_CHOICES, required=False, allow_blank=True)
    supplier_id = serializers.CharField(allow_blank=True, required=False)
    remarks = serializers.CharField(allow_blank=True, required=False)
    aux_data = serializers.JSONField(required=False)
    job_code = serializers.CharField(allow_blank=True, required=False)
    items = serializers.ListField(child=PutawaySKUSerializer())

class PutawayMappingSerializer(serializers.Serializer):
    id = serializers.IntegerField(min_value=0, required=False)
    sku_id = serializers.IntegerField(min_value=0, required=False)
    location_id = serializers.IntegerField(min_value=0, required=False)
    sku_code = serializers.CharField(allow_blank=True, required=False)
    sku_category = serializers.CharField(allow_blank=True, required=False)
    location = serializers.CharField(allow_blank=True, required=False)
    zone = serializers.CharField(allow_blank=True, required=False)
    strategy = serializers.CharField(required=True)
    status = serializers.ChoiceField(choices=["Active", "Inactive"], required=False)
    capacity = serializers.IntegerField(min_value=0, required=False)
    priority = serializers.IntegerField(min_value=0, required=False)
