#django imports
from django.db import models
from django.db.models import ForeignKey
from simple_history.models import HistoricalRecords


#wms base imports
from wms_base.models import User, TenantBaseModel

from .supplier import SupplierMaster, PaymentTerms

#history imports
from core_operations.history import CustomHistoricalRecords


class Pofields(TenantBaseModel):
    user = models.PositiveIntegerField()
    field_type = models.CharField(max_length=32, default='')
    po_number = models.CharField(max_length=128, default='')
    receipt_no = models.CharField(max_length = 34 ,default = 1)
    name = models.CharField(max_length=256, default='')
    value = models.CharField(max_length=256, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PO_FIELDS'

class OpenPO(TenantBaseModel):
    
    history = CustomHistoricalRecords()
    history_disable = True
    supplier = ForeignKey(SupplierMaster,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE, db_index=True)
    order_quantity = models.FloatField(default=0, db_index=True) 
    price = models.FloatField(default=0)
    wms_code = models.CharField(max_length=32, default='')  #Saved PO incremental number
    po_name = models.CharField(max_length=128, default='')
    supplier_code = models.CharField(max_length=32, default='')
    order_type = models.CharField(max_length=32, default='SR')
    remarks = models.CharField(max_length=256, default='')
    tax_type = models.CharField(max_length=32, default='')
    sgst_tax = models.FloatField(default=0)
    cgst_tax = models.FloatField(default=0)
    igst_tax = models.FloatField(default=0)
    cess_tax = models.FloatField(default=0)
    utgst_tax = models.FloatField(default=0)
    apmc_tax = models.FloatField(default=0)
    mrp = models.FloatField(default=0)
    delivery_date = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=32)
    measurement_unit = models.CharField(max_length=32, default='')
    ship_to = models.CharField(max_length=512, default='')
    json_data = models.JSONField(null=True, blank=True, default=dict)
    creation_date = models.DateTimeField(auto_now_add=True, db_index=True)
    updation_date = models.DateTimeField(auto_now=True)
    terms = models.TextField(default='', max_length=256)
    free_quantity = models.FloatField(default=0)

    class Meta:
        db_table = 'OPEN_PO'
        index_together = (('sku', 'supplier'), ('sku', ),  ('sku', 'order_quantity', 'price'), ('creation_date', ), ('supplier', ))

    round_fields = ['order_quantity']

    def __str__(self):
        return str(str(self.sku) + " : " + str(self.supplier))

class PendingPO(TenantBaseModel):
    
    history = HistoricalRecords()
    supplier = ForeignKey(SupplierMaster,on_delete=models.CASCADE, blank=True, null=True, db_index=True, related_name='pendingpos')
    supplier_payment = ForeignKey(PaymentTerms,on_delete=models.CASCADE, blank=True, null=True, db_index=True, related_name='pendingpos')
    open_po = ForeignKey(OpenPO,on_delete=models.CASCADE, blank=True, null=True, related_name='pendingpos')
    requested_user = ForeignKey(User,on_delete=models.CASCADE, related_name='pendingPO_RequestedUser')
    wh_user = ForeignKey(User,on_delete=models.CASCADE, related_name='pendingPOs')
    product_category = models.CharField(max_length=64, default='')
    sku_category = models.CharField(max_length=128, default='')
    po_number = models.PositiveIntegerField(blank=True, null=True) # Similar to PurchaseOrder->order_id field
    po_name = models.CharField(max_length=32, default='')
    po_type = models.CharField(max_length=32, default='')
    prefix = models.CharField(max_length=32, default='')
    full_po_number = models.CharField(max_length=32, default='', db_index=True)
    delivery_date = models.DateField(blank=True, null=True)
    ship_to = models.CharField(max_length=256, default='')
    pending_level = models.CharField(max_length=64, default='')
    final_status = models.CharField(max_length=32, default='')
    remarks = models.TextField(default='')
    terms = models.TextField(default='',max_length=256)
    json_data = models.JSONField(null=True, blank=True, default=dict)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PENDING_PO'

class PendingLineItems(TenantBaseModel):
    
    history = HistoricalRecords()
    pending_po = ForeignKey(PendingPO,on_delete=models.CASCADE, related_name='pending_polineItems', blank=True, null=True)
    purchase_type = models.CharField(max_length=32, default='')
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE, related_name='pendingLineItems', db_index=True)
    quantity = models.FloatField(default=0, db_index=True)
    price = models.FloatField(default=0)
    mrp = models.FloatField(default=0)
    discount_percent = models.FloatField(default=0)
    measurement_unit = models.CharField(max_length=32, default='')
    sgst_tax = models.FloatField(default=0)
    cgst_tax = models.FloatField(default=0)
    igst_tax = models.FloatField(default=0)
    utgst_tax = models.FloatField(default=0)
    cess_tax = models.FloatField(default=0)
    apmc_tax = models.FloatField(default=0)
    remarks = models.CharField(max_length=256, default='')
    json_data = models.JSONField(null=True, blank=True, default=dict)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PENDING_LINE_ITEMS'

    round_fields = ['quantity']


class PurchaseApprovals(TenantBaseModel):  #PRApprovals
    pending_po = ForeignKey(PendingPO,on_delete=models.CASCADE, related_name='pending_poApprovals', blank=True, null=True)
    purchase_number = models.PositiveIntegerField() #WH Specific Inc Number
    purchase_type = models.CharField(max_length=32, default='PO')
    configName = models.CharField(max_length=64, default='')
    approval_type = models.CharField(max_length=64, default='')
    product_category = models.CharField(max_length=64, default='')
    pr_user = ForeignKey(User,on_delete=models.CASCADE, related_name='PurchaseApproval_WarehouseUser')
    level = models.CharField(max_length=64, default='')
    validated_by = models.TextField(default='')
    status = models.CharField(max_length=32, default='')
    remarks = models.TextField(default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PURCHASE_APPROVALS'

class PurchaseApprovalMails(TenantBaseModel):  #PRApprovalMails
    pr_approval = ForeignKey(PurchaseApprovals,on_delete=models.CASCADE)
    email = models.EmailField(max_length=64)
    level  = models.CharField(max_length=64, default='')
    hash_code = models.CharField(max_length=256, default='')
    status = models.CharField(max_length=32, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "PURCHASE_APPROVAL_MAILS"
    
class POHeader(TenantBaseModel):
    warehouse = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    po_number = models.CharField(max_length=64, default='', db_index=True)
    po_reference = models.CharField(max_length=128, default='', db_index=True)
    supplier = ForeignKey(SupplierMaster,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    po_type = models.CharField(max_length=32, default='normal')
    status = models.CharField(max_length=32, db_index=True)
    expected_date = models.DateField(blank=True, null=True)
    creation_date = models.DateTimeField(auto_now_add=True, db_index=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PO_HEADER'
        unique_together = ('po_number', 'warehouse', 'po_reference')

    def __str__(self):
        return str(self.id)

class PurchaseOrder(TenantBaseModel):
    
    history = CustomHistoricalRecords()
    history_disable = True
    order_id = models.PositiveIntegerField(db_index=True)
    po_number = models.CharField(max_length=64, default='', db_index=True)
    open_po = ForeignKey(OpenPO,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    received_quantity =models.FloatField(default=0)
    received_free_quantity = models.FloatField(default=0)
    saved_quantity = models.FloatField(default=0)
    saved_free_quantity = models.FloatField(default=0)
    intransit_quantity = models.FloatField(default=0)
    discrepancy_quantity = models.FloatField(default=0)
    po_date = models.DateTimeField(blank=True, null=True)
    ship_to = models.CharField(max_length=256, default='')
    status = models.CharField(max_length=32, db_index=True)
    reason = models.TextField(blank=True, null=True)
    prefix = models.CharField(max_length=32, default='')
    remarks = models.TextField(default='')
    po_type = models.CharField(max_length=32, default='normal')
    expected_date = models.DateField(blank=True, null=True)
    remainder_mail = models.IntegerField(default=0)
    payment_received = models.FloatField(default=0)
    priority = models.IntegerField(default=0)
    pcf = models.FloatField(default=1)
    line_reference = models.CharField(max_length=64, blank=True, null=True)
    creation_date = models.DateTimeField(auto_now_add=True, db_index=True)
    updation_date = models.DateTimeField(auto_now=True)
    cancelled_quantity = models.FloatField(default=0)
    rtv_quantity = models.FloatField(default=0)

    class Meta:
        db_table = 'PURCHASE_ORDER'
        index_together = (
            ('order_id', 'open_po'),
            ('order_id', 'open_po', 'received_quantity'),
            ('po_number', 'open_po'), ('creation_date', ),
            ('po_number', ), ('status', 'open_po')
        )
        permissions = [
            ('update_purchaseorder', 'Update Purchase Order'),
        ]

    round_fields = ['received_quantity', 'saved_quantity', 'intransit_quantity', 'discrepancy_quantity']

    def __str__(self):
        return str(self.id)
