import datetime
import logging
import os
import time
import copy
import re
import jwt
import sentry_sdk

import pandas as pd
from decimal import Decimal, ROUND_DOWN
from functools import wraps
from collections import OrderedDict
from requests import Session

from django.core.files import File
from django.db import models as django_models
from django.db.models import Su<PERSON>, BigInteger<PERSON>ield

from rest_framework import serializers

from .models import *
from .choices import CURRENCY_CHOICES

from wms.settings import base

from boto3.session import Session

from .middleware.uuid_grenerate import request_id_local
from wms_base.loggers.handlers import setup_aync_logging
OPENSEARCH_APPLOG_INDEX = getattr(settings, 'OPENSEARCH_APPLOG_INDEX', 'local-logs')


METABASE_SITE_URL = getattr(settings, 'METABASE_SITE_URL', None)
METABASE_SECRET_KEY = getattr(settings, 'METABASE_SECRET_KEY', None)



logger_boto3_session = None

AJAX_DATA = {
  "draw": 0,
  "recordsTotal": 0,
  "recordsFiltered": 0,
  "aaData": [
  ],
  "card_details": [  
  ]
}

serial_no_const, sku_code_const, batch_no_const, active_or_inactive_const, sku_mandatory_const, source_zone_const, source_location_const, source_loc_const, destination_zone_const, destination_location_const, destination_loc_const, batch_reference_const = 'Serial Number', 'SKU Code', 'Batch Number', 'Status(Active/Inactive)', 'SKU Code*', 'Source Zone*', 'Source Location*', 'Source Location', 'Destination Zone*', 'Destination Location*', 'Destination Location', 'Batch Reference'

sku_ref_const = 'SKU Reference'
warehouse_const = 'Warehouse Name'
sku_list_const = 'SKU List'
wms_code_const = 'WMS Code'
wms_code_const_req = 'WMS Code *'
sku_desc_const = 'SKU Description'
sku_group_const = 'SKU Group'
sku_category_const = 'SKU Category'
sku_class_const = 'SKU Class'
sku_type_const = 'SKU Type'
ean_number_const = 'EAN Number'
product_desc_const = 'Product Description'
batch_no_mandatory_const = 'Batch Number*'

threshold_qty_const = 'Threshold Quantity'

supplier_id_const = 'Supplier ID'
supplier_Id_const = 'Supplier Id'
supplier_name_const = 'Supplier Name'
supplier_code_const = 'Supplier Code'
po_number_const = 'PO Number'
po_reference_const = 'PO Reference'
receipt_number_const = 'Receipt Number'
receipt_date_const = 'Receipt Date'
receipt_summary_const = 'Receipt Summary'
put_zone_const = 'Put Zone'

qty_const_req = 'Quantity *'
order_qty_const = 'Order Quantity'
ordered_qty_const = 'Ordered Quantity'
received_qty_const = 'Received Quantity'
reserved_qty_const = 'Reserved Quantity'
physical_qty_const = 'Physical Quantity'
physical_qty_mandatory_const = 'Physical Quantity*'
total_qty_const = 'Total Quantity'
picked_qty_const = 'Picked Quantity'

customer_id_const = 'Customer ID'
phone_no_const = 'Phone Number'

from_date_const = 'From Date'
to_date_const = 'To Date'
order_id_const = 'Order ID'
order_date_const = 'Order Date'
dispatch_summary_const = 'Dispatch Summary'
product_sku_code_const = 'Product SKU Code'
product_sku_qty_const = 'Product SKU Quantity'
location_wise_sku_const = 'Location Wise SKU'
material_sku_const = 'Material SKU Code'
material_sku_quantity_const = 'Material SKU Quantity'

sku_wise_stock = 'SKU Wise Stock'
last_name_const = 'Last Name'
customer_name_const = 'Customer Name'
customer_id_const = 'Customer ID *'
carton_id_const = 'LPN Number'
first_name_const = 'First Name *'
email_const = 'Email *'
password_const = 'Password *'
phone_num_const = 'Phone No. *'
username_const = 'User Name *'
manufactured_date_const = 'Manufactured Date'
expiry_date_const = 'Expiry Date'
reason_const = 'Reason*'
quantity_const = 'Quantity*'
inspection_lot_number = 'Inspection Lot Number'
vendor_batch_no_const = 'Vendor Batch Number'
stock_status_const = 'Status(OnHold, Available, Reserved, Rejected, Obsolete, Blocked, Consumed)'

put_sequence_const = 'Put sequence*'
get_sequence_const = 'Get sequence*'
zone_type_const = 'Zone Type(Sellable, Non_Sellable, Outbound_Staging, inbound_staging, Cross_Dock, Sorting, Drop_Ship)'
restrict_1_loc_to_1_sku_const = 'Restrict 1 Location to 1 SKU(Yes, No)'
unique_batch_restriction_const = 'Unique Batch Restriction(Yes, No)'
lpn_managed_const = 'LPN Managed(Yes, No)'
allowed_inbound_storage_type = 'REC, PUT, REPACK'
allowed_outbound_storage_type = 'pre_invoice, packing, sorting'
allowed_non_sell_storage_type = 'bulk_area, reject_area, qc_area, wip_area'

market_place_const = 'Market Place'

attribute_value_const = 'Attribute Value'


# date format constants
DEFAULT_DATE_FORMAT = "%Y-%m-%d"
DEFAULT_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
DATATABLE_DATE_FORMAT = "%d %b, %Y"
DATATABLE_DATETIME_FORMAT = "%d %b, %Y %I:%M %p"


NOW = datetime.datetime.now()
SKU_DATA = {
    'user': '', 'sku_code': '', 'wms_code': '',
    'sku_desc': '', 'sku_group': '', 'sku_type': '', 'mix_sku': '',
    'sku_category': '', 'sku_class': '', 'threshold_quantity': 0, 'max_norm_quantity': 0, 'color': '',
    'mrp': 0,'status': 1, 'online_percentage': 0, 'qc_check': 1, 'sku_brand': '', 'sku_size': '',
    'style_name': '','price': 0,'ean_number': 0, 'load_unit_handle': 'unit', 'zone_id': None,
    'hsn_code': '', 'product_type': '','sub_category': '', 'pick_group': '', 'cost_price': 0,
    'qc_eligible': 0, 'scan_picking': 0, 'image_url': '', 'length': 0, 'breadth': 0, 'height':0,
    'weight' : '','measurement_type': '', 'sale_through': '', 'shelf_life': 0, 'enable_serial_based': 0,
    'block_options': '', 'customer_shelf_life': 0,'batch_based': 0, 'gl_code': 0, 'sku_reference': '',
    'minimum_shelf_life': 0,'make_or_buy':0, 'receipt_tolerance':0, 'dispensing_enabled': 0, 'allow_price_override' : 0,
    'is_barcode_required': 0, 'invoice_group': '', 'seller_id': None, 'pick_and_sort': 0, 'mandate_scan': False
}

CYCLE_COUNT_EXCEL_HEADERS_KEYMAP = {
    'SKU Code': 'sku',
    'Location': 'location',
    physical_qty_const: 'quantity',
    batch_no_const: 'batch_no',
    'MRP': 'mrp',
    'Manufactured Date(YYYY-MM-DD)': 'manufactured_date',
    'Expiry Date(YYYY-MM-DD)': 'expiry_date',
    'Weight': 'weight',
    'Price': 'unit_price',
    'Adjustment Type(Scrap)': 'adjustment_type',
    'Reason': 'reason',
    'Serial Number': 'serial_number',
}

SKU_STOCK_DATA = {'sku_id': '', 'total_quantity': 0,
            'online_quantity': 0, 'offline_quantity': 0}

SUPPLIER_DATA = {'name': '', 'address': '',
            'phone_number': '', 'email_id': '',
            'status': 1, 'creation_date': NOW}

MACHINE_MASTER_DATA = {'master_code': '','master_name': '',
                       'model_number': '', 'serial_number': '',
                       'brand': '','status': 1,'creation_date':NOW}

ISSUE_DATA = {'issue_title': '', 'priority': '', 'status': 'Active',
            'issue_description': '', 'creation_date': NOW}

SUPPLIER_SKU_DATA = {'supplier_id': '', 'supplier_type': '',
                     'sku': '', 'supplier_code': '', 'preference': '', 'moq': '', 'price': '',
                     'costing_type': 'Price Based','markup_percentage':0, 'lead_time': 0,
                     'margin_percentage': 0, 'gatekeeper_margin' : 0
                     }


UPLOAD_ORDER_DATA = {'order_id': '', 'title': '','user': '',
             'sku_id': '', 'status': 1, 'shipment_date': NOW, 'creation_date': NOW}


LOC_DATA = {'zone_id': '', 'location': '',
            'max_capacity': 0, 'fill_sequence': 0, 'pick_sequence': 0,
            'filled_capacity': 0, 'status': 1, 'creation_date': NOW}
ZONE_DATA = {'user': '', 'zone': '', 'restrict_one_location_to_one_sku': 0, 'creation_date': NOW}

ORDER_DATA = {'order_id': '', 'sku_id': '', 'title': '',
              'quantity': '', 'status': 1, 'creation_date': NOW}

RETURN_DATA = {'order_id': '', 'return_id': '', 'return_date': '',
              'quantity': '', 'status': 1, 'creation_date': NOW}

PALLET_FIELDS = {'pallet_code': '', 'quantity': 0, 'status': 1, 'creation_date': NOW}

CONFIRM_SALES_RETURN = {'order_id':'','sku_id':''}

CANCEL_ORDER_HEADERS = OrderedDict([('','id'),(order_id_const,'order_id'),(sku_code_const,'sku__sku_code'),(customer_id_const,'customer_id')])

PO_SUGGESTIONS_DATA = {'supplier_id': '', 'sku_id':'', 'order_quantity': '',
                       'price': 0, 'status': 1, 'creation_date': NOW}
PO_DATA = {'open_po_id': '', 'status': '', 'received_quantity': 0, 'creation_date': NOW}

ORDER_SHIPMENT_DATA = {'shipment_number': '', 'shipment_date': '', 'truck_number': '', 'shipment_reference': '', 'status': 1, 'creation_date': NOW, 'driver_id': None}

SKU_FIELDS = ( (('WMS SKU Code *', 'wms_code',60), ('Product Description *', 'sku_desc',256)),
               ((sku_type_const, 'sku_type',60), (sku_class_const, 'sku_class',60)),
               ((sku_category_const, 'sku_category',60), (sku_group_const, 'sku_group')),
               (('Put Zone *', 'zone_id',60), ('Threshold Quantity *', 'threshold_quantity')),
               (('Status', 'status',11), ))

QUALITY_CHECK_FIELDS = {'purchase_order_id': '', 'accepted_quantity': 0,'rejected_quantity': 0, 'putaway_quantity': 0, 'status': 'qc_pending', 'reason': '', 'creation_date': NOW}

ORDER_PACKAGING_FIELDS = {'order_shipment_id':'', 'package_reference': '', 'status': 1, 'creation_date': NOW}

SHIPMENT_INFO_FIELDS = {'order_shipment_id':'', 'order': '', 'shipping_quantity': '', 'status': 1,'order_packaging_id': '', 'creation_date': NOW }

ADD_SKU_FIELDS = ( (('WMS SKU Code *', 'wms_code',60), ('Product Description *', 'sku_desc',256)),
                   ((sku_type_const, 'sku_type',60), (sku_class_const, 'sku_class',60)),
                   ((sku_category_const, 'sku_category',60), (sku_group_const, 'sku_group')),
                   (('Put Zone *', 'zone_id',60), ('Threshold Quantity *', 'threshold_quantity')),
                   (('Status', 'status',11), ))


RAISE_ISSUE_FIELDS = ( ('Issue Title', 'issue_title'),
               ('Priority', 'priority'),
               ('Issue Description', 'issue_description'), )

UPDATE_ISSUE_FIELDS = ( (('Issue Title', 'issue_title',60), ('Priority', 'priority',32)),
                    (('Issue Description', 'issue_description',256), ('Status', 'status',11)),
                      (('Resolved Description', 'resolved_description'),),   )


RESOLVED_ISSUE_FIELDS = ( (('Issue Title', 'issue_title',60), ('Priority', 'priority',32)),
                    (('Issue Description', 'issue_description',256), ('Status', 'status',11)),
                      (('Resolved Description', 'resolved_description'),),   )

TAX_TYPE_ATTRIBUTES = {
    'inter_state': 'Inter State',
    'intra_state': 'Intra State'
}

SUPPLIER_FIELDS = ( (('Supplier Id *', 'id',60), ('Supplier Name *', 'name',256)),
                    ((email_const, 'email_id',64), (phone_num_const, 'phone_number',10)),
                    (('Address *', 'address'), ('Status', 'status',11)), )

MACHINE_MASTER_FIELDS = ( (('Machine ID *', 'id', 60), ('Machine Name *', 'machine_name',128)),
                          (('Model Number *', 'model_number', 128), ('Serial Number *', 'serial_number', 128)),
                          (('Brand *', 'brand', 128), ('Status *' 'status', 11)))

SKU_SUPPLIER_FIELDS = ( (('Supplier ID *', 'supplier_id',60), (wms_code_const_req, 'wms_id','')),
                        ((supplier_code_const,'supplier_code'), ('Priority *', 'preference',32) ),
                        (('MOQ', 'moq',256,0), ('Price', 'price'), ) )

RAISE_PO_FIELDS = ( ((supplier_id_const, 'supplier_id',11), ('PO Name', 'po_name',30)),
                    (('Ship To', 'ship_to',''), ) )

RAISE_PO_FIELDS1 = OrderedDict([(wms_code_const_req,'wms_code'), (supplier_code_const, 'supplier_code'), (qty_const_req,'order_quantity'),('Price','price')])

MOVE_INVENTORY_FIELDS = ( ((wms_code_const_req,'wms_code'),('Source Location *','source_loc')),
                          (('Destination Location *','dest_loc'),(qty_const_req,'quantity')), )

ADJUST_INVENTORY_FIELDS = ( ((wms_code_const_req,'wms_code'),('Location *','location')),
                            (('Physical Quantity *','quantity'),('Reason','reason')),)

MOVE_INVENTORY_UPLOAD_FIELDS = [
    sku_mandatory_const, source_location_const, destination_location_const,
    'Batch REF/NO','Status(Available, OnHold, Consumed, Locked, Reserved, Rejected, Obsolete, Blocked)', carton_id_const,
    quantity_const, serial_no_const, reason_const, 'Task Creation(Y/N)'
    ]

SUPPLIER_HEADERS = [supplier_Id_const, supplier_name_const, 'Address', 'Email', 'Phone No.']

SALES_RETURN_HEADERS = ['Return ID', 'Return Date', sku_code_const, product_desc_const, market_place_const, 'Quantity']

SALES_RETURN_TOGGLE = ['Return ID', sku_code_const, product_desc_const, 'Shipping Quantity', 'Return Quantity', 'Damaged Quantity' ]

RETURN_DATA_FIELDS = ['sales-check', 'order_id', 'sku_code', 'customer_id', 'shipping_quantity', 'return_quantity', 'damaged_quantity', 'delete-sales']

SUPPLIER_SKU_HEADERS = [supplier_Id_const, wms_code_const, 'Preference', 'MOQ','Price']

MARKETPLACE_SKU_HEADERS = [wms_code_const, 'Flipkart SKU', 'Snapdeal SKU', 'Paytm SKU', 'Amazon SKU', 'HomeShop18 SKU', 'Jabong SKU', 'Indiatimes SKU', 'Myntra SKU', 'Voonik SKU', 'Campus Sutra SKU', 'Flipkart Description', 'Snapdeal Description', 'Paytm Description', 'HomeShop18 Description', 'Jabong Description', 'Indiatimes Description', 'Amazon Description']

PURCHASE_ORDER_HEADERS = ['PO Name', 'PO Date(MM-DD-YYYY)', supplier_id_const, 'WMS SKU Code', 'Quantity', 'Price', 'Ship TO']

LOCK_FIELDS = ['', 'Inbound', 'Outbound', 'Inbound and Outbound']

LOCATION_FIELDS = ( (('Zone ID *', 'zone_id'),('Location *', 'location')),
                    (('Capacity', 'max_capacity'), ('Put Sequence', 'fill_sequence')),
                    ( ('Get Sequence', 'pick_sequence'), ('Status', 'status')),
                    (('Location Lock', 'lock_status'), (sku_group_const, 'location_group')) )

USER_FIELDS = ( ((username_const, 'username'),('Name *', 'first_name')),
                (('Groups', 'groups'),),)

ADD_USER_FIELDS = ( ((username_const, 'username'),(first_name_const, 'first_name')),
                    ((last_name_const, 'last_name'), ('Email', 'email')),
                    ((password_const, 'password'), ('Re-type Password', 're_password')),)

WAREHOUSE_USER_FIELDS = ( ((username_const, 'username'),(first_name_const, 'first_name')),
                          ((last_name_const, 'last_name'), (phone_no_const, 'phone_number')),
                          (('Email', 'email'), (password_const, 'password')),
                          (('Re-type Password', 're_password'), ('Country', 'country')),
                          (('State', 'state'), ('City', 'city')),
                          (('Address', 'address'), ('Pincode', 'pin_code')),
                        )

WAREHOUSE_UPDATE_FIELDS = ( (('User Name', 'username'),('First Name', 'first_name')),
                          ((last_name_const, 'last_name'), (phone_no_const, 'phone_number')),
                          (('Email', 'email'), ('Country', 'country')),
                          (('State', 'state'), ('City', 'city')),
                          (('Address', 'address'), ('Pincode', 'pin_code')),
                        )

ADD_GROUP_FIELDS = ( (('Group Name *', 'group'),('Permissions', 'permissions')),)

SHIPMENT_FIELDS = ( (('Shipment Number *', 'shipment_number'),('Shipment Date *', 'shipment_date')),
                    (('Truck Number *', 'truck_number'), ('Shipment Reference *', 'shipment_reference')),
                    ((customer_id_const, 'customer_id'), (market_place_const, 'marketplace')) )



CREATE_ORDER_FIELDS = ( ((customer_id_const, 'customer_id'), (customer_name_const, 'customer_name')),
                    (('Telephone', 'telephone'),('Shipment Date *','shipment_date')),
                    (('Address', 'address'), ('Email', 'email_id'))  )

CREATE_ORDER_FIELDS1 = OrderedDict([('SKU Code/WMS Code *','sku_id'),('Quantity','quantity'), ('Invoice Amount', 'invoice_amount')])

SALES_RETURN_FIELDS = ( (('Return Tracking ID', 'scan_return_id'), (sku_code_const, 'return_sku_code')), )

MARKETPLACE_SKU_FIELDS = {'marketplace_code': '', 'sku_id': '', 'description': '',
                          'sku_type': '', 'creation_date': NOW}

MARKET_LIST_HEADERS = [market_place_const, 'SKU', 'Description']

MARKETPLACE_LIST = ['Flipkart', 'Snapdeal', 'Paytm', 'Amazon', 'Shopclues', 'HomeShop18', 'Jabong', 'Indiatimes']

ORDER_HEADERS = [order_id_const, 'Title', sku_code_const, 'Quantity','Shipment Date(yyyy-mm-dd)']

REPORT_FIELDS = ( ((from_date_const, 'from_date'),(to_date_const, 'to_date')),
                    ((sku_code_const, 'sku_code'), (sku_category_const, 'sku_category')),
                    ((sku_type_const, 'sku_type'), (sku_class_const, 'sku_class')) )

SKU_LIST_REPORTS_DATA = {('sku_list_form','reportsTable','SKU List Filters','sku-list', 1, 2, 'sku-report') : ([sku_code_const, wms_code_const, sku_group_const, sku_type_const, sku_category_const, sku_class_const, put_zone_const, threshold_qty_const],( ((sku_code_const, 'sku_code'), (sku_category_const, 'sku_category')), ((sku_type_const, 'sku_type'), (sku_class_const, 'sku_class')),((wms_code_const,'wms_code'),))),}

LOCATION_WISE_FILTER = {('location_wise_form', 'locationTable', 'Location Wise Filters', 'location-wise', 3, 4, 'location-report') : (['Location', sku_code_const, wms_code_const, product_desc_const, 'Zone', receipt_number_const, receipt_date_const, 'Quantity'], ( ((sku_code_const, 'sku_code'), (sku_category_const, 'sku_category')), ((sku_type_const, 'sku_type'), (sku_class_const, 'sku_class')), (('Location','location'), ('Zone','zone_id')),((wms_code_const, 'wms_code'),), )),}

SUPPLIER_WISE_POS = {('supplier_wise_form', 'suppliertable', 'Supplier Wise Filters', 'supplier-wise', 1, 2, 'supplier-report'): ([order_date_const, po_number_const, supplier_name_const, wms_code_const, 'Design', ordered_qty_const, received_qty_const, 'Status'], ((('Supplier', 'supplier'),), )),}

GOODS_RECEIPT_NOTE = {('receipt_note_form', 'receiptTable', 'Goods Receipt Filter', 'receipt-note', 11, 12, 'po-report'): ([po_number_const, supplier_id_const, supplier_name_const, total_qty_const], ( ((from_date_const, 'from_date'),(to_date_const, 'to_date')), ((po_number_const, 'open_po'), (wms_code_const,'wms_code') ), )),}

RECEIPT_SUMMARY = {('receipt_summary_form', 'summaryTable', receipt_summary_const, 'summary-wise', 5, 6, 'receipt-report'): (['Supplier', po_reference_const, wms_code_const, 'Description', received_qty_const], ( ((from_date_const, 'from_date'),(to_date_const, 'to_date')), ((wms_code_const, 'wms_code'), ('Supplier', 'supplier')),((sku_code_const,'sku_code'),), )),}

DISPATCH_SUMMARY = {('dispatch_summary_form', 'dispatchTable', dispatch_summary_const, 'dispatch-wise', 13, 14, 'dispatch-report'): ([order_id_const, wms_code_const, 'Description', 'Location', 'Quantity', picked_qty_const, 'Date', 'Time'], ( ((from_date_const, 'from_date'),(to_date_const, 'to_date')), ((wms_code_const, 'wms_code'),(sku_code_const,'sku_code') )) ),}

SKU_WISE_STOCK = {('sku_wise_form','skustockTable','SKU Wise Stock Summary','sku-wise', 1, 2, 'sku-wise-report') : ([sku_code_const, wms_code_const, product_desc_const, sku_category_const, total_qty_const],( ((sku_code_const, 'sku_code'), (sku_category_const, 'sku_category')), ((sku_type_const, 'sku_type'), (sku_class_const, 'sku_class')),((wms_code_const,'wms_code'),))),}

SKU_WISE_PURCHASES =  {('sku_wise_purchase','skupurchaseTable','SKU Wise Purchase Orders','sku-purchase-wise', 1, 2, 'sku-wise-purchase-report') : (['PO Date', 'Supplier', sku_code_const, order_qty_const, received_qty_const, receipt_date_const, 'Status'],( (('WMS SKU Code', 'wms_code'),),)),}

SALES_RETURN_REPORT = {('sales_return_form','salesreturnTable','Sales Return Reports','sales-report', 1, 2, 'sales-return-report') : ([sku_code_const, order_id_const, customer_id_const, 'Return Date', 'Status', 'Quantity'],( ((sku_code_const, 'sku_code'), (wms_code_const, 'wms_code')), ((order_id_const, 'order_id'), (customer_id_const, 'customer_id')),(('Date','creation_date'),))),}

INVENTORY_ADJUST_REPORT = {('inventory_adjust_form','inventoryadjustTable','Inventory Adjustment Reports','adjustment-report', 1, 2, 'inventory-adjust-report') : ([sku_code_const, 'Location', 'Quantity', 'Date', 'Remarks'],( ((from_date_const, 'from_date'),(to_date_const, 'to_date')), ((sku_code_const, 'sku_code'), (wms_code_const, 'wms_code')), (('Location', 'location'),),   )),}

INVENTORY_AGING_REPORT = {('inventory_aging_form','inventoryagingTable','Inventory Aging Reports','aging-report', 1, 2, 'inventory-aging-report') : ([sku_code_const, sku_desc_const, sku_category_const, 'Location', 'Quantity', 'As on Date(Days)'],( ((from_date_const, 'from_date'),(to_date_const, 'to_date')), ((sku_code_const, 'sku_code'), (sku_category_const, 'sku_category')),   )),}

LOCATION_HEADERS = ['Zone*', 'Location*', 'Capacity*', put_sequence_const, get_sequence_const, active_or_inactive_const, 'Carton Capacity']

ZONE_HEADERS = [
    'Zone ID*', zone_type_const, restrict_1_loc_to_1_sku_const, 'Subzone Type', unique_batch_restriction_const, lpn_managed_const, 'Check Digit (Active/Inactive)'
]

SUBZONE_HEADERS = [
    'Sub Zone ID*', zone_type_const, put_sequence_const,
    get_sequence_const, restrict_1_loc_to_1_sku_const, unique_batch_restriction_const, lpn_managed_const
]

ZONE_HEADER_MAPPING = OrderedDict((
    ('Zone ID*', 'zone'),
    (zone_type_const, 'zone_type'),
    (restrict_1_loc_to_1_sku_const, 'restrict_one_location_to_one_sku'),
    ('Subzone Type', 'storage_type'),
    (unique_batch_restriction_const, 'unique_batch_restriction'),
    (lpn_managed_const, 'lpn_managed'),
    ('Check Digit (Active/Inactive)', 'is_check_digit')
))

SUBZONE_HEADER_MAPPING = OrderedDict((
    ('Sub Zone ID*', 'sub_zone'),
    (zone_type_const, 'zone_type'),
    (put_sequence_const, 'put_sequence'),
    (get_sequence_const, 'get_sequence'),
    (restrict_1_loc_to_1_sku_const, 'restrict_one_location_to_one_sku'),
    (unique_batch_restriction_const, 'unique_batch_restriction'),
    (lpn_managed_const, 'lpn_managed')
))

STAGING_ROUTE_HEADERS = ['Transaction Type', 'Attribute', attribute_value_const, put_zone_const, 'Staging Type', 'Staging Location']
STAGING_ROUTE_HEADER_MAPPING = OrderedDict((
    ('Transaction Type', 'transaction'),
    ('Attribute', 'attribute_name'),
    (attribute_value_const, 'attribute_value'),
    (put_zone_const, 'put_zone'),
    ('Staging Type', 'stage'),
    ('Staging Location', 'staging_location')
))

LOCATION_HEADERS_MAPPING = OrderedDict((
                            ('Zone*', 'zone'),
                            ('Location*', 'location'),
                            ('Capacity*', 'capacity'),
                            (put_sequence_const, 'put_sequence'),
                            (get_sequence_const, 'get_sequence'),
                            (active_or_inactive_const, 'status'),
                            ('Carton Capacity', 'carton_capacity'),
                        ))

SKU_HEADERS = [
    sku_code_const, sku_desc_const, sku_group_const,
    'SKU Type(Options: FG, RM, Expense, WIP,Consumables,Spare Parts)', sku_category_const,
    sku_class_const, 'SKU Brand', 'Style Name', 'SKU Size', 'Size Type', put_zone_const, 'Seller Id', 'Cost Price',
    'Selling Price','MRP Price','Image Url','Min Norm Quantity', 'Max Norm Quantity', 'UOM','Color',
    ean_number_const, 'HSN Code', 'Sub Category','Combo Flag', 'Block For PO','GL Code',
    'Batch Based(Options: Enable, Disable)', 'Dispensing Enabled(Options: true, false)',
    'Status', 'Length', 'Breadth', 'Height', 'Weight', 'Tax Name', 'Product Shelf Life', 'Customer Shelf Life',
    sku_ref_const,'Scan Status(Options: Scannable, Non-Scannable)', 'Mandate Scan(Options: Enable, Disable)', 'Minimum Shelf Life','Make Or Buy',
    'Serialized(Options: Enable, Disable)', 'QC Required(Options: Yes, No)', "Receipt Tolerance",
    'Barcoding Required(Options: Yes, No)', 'Invoice Group', "Pick and Sort(Options: Enable, Disable)",
]

TAX_HEADERS = ['Tax Name', 'Tax Type(inter state / intra state)', 'Min Amount', 'Max Amount', 'SGST Tax', 'CGST Tax', 'IGST Tax', 'CESS Tax', 'APMC Tax', 'Reference ID']
HSN_HEADERS = ['HSN Code*', 'Description', 'Tax Name*']

EXCEL_HEADERS = [receipt_number_const, receipt_date_const,  'WMS SKU', 'Location', 'Quantity', 'Receipt Type']
EXCEL_RECORDS = ('receipt_number', 'receipt_date', 'wms_code', 'location', 'wms_quantity', 'receipt_type')

SKU_EXCEL = ('wms_code', 'sku_desc', 'sku_group', 'sku_type', 'sku_category', 'sku_class', 'zone_id', 'threshold_quantity', 'status')

PICKLIST_FIELDS = { 'order_id': '', 'picklist_number': '', 'reserved_quantity': '', 'picked_quantity': 0, 'remarks': '', 'status': 'open'}
PICKLIST_HEADER = ('ORDER ID', wms_code_const, 'Title', 'Zone', 'Location', reserved_qty_const, picked_qty_const)

PICKLIST_HEADER1 = (wms_code_const, 'Title','Zone', 'Location', reserved_qty_const, picked_qty_const,'')

PRINT_PICKLIST_HEADERS = (wms_code_const, 'Title', 'Zone', 'Location', reserved_qty_const, picked_qty_const,'Stock Left')

PICKLIST_EXCEL = OrderedDict(( (wms_code_const, 'wms_code'), ('Title', 'title'), ('Zone', 'zone'), ('Location', 'location'),
                               (reserved_qty_const, 'reserved_quantity')))

PROCESSING_HEADER = (wms_code_const, 'Title', 'Zone', 'Location', reserved_qty_const, picked_qty_const, '')

SKU_SUPPLIER_MAPPING = OrderedDict([(supplier_id_const,'supplier_id'),(sku_code_const,'sku__wms_code'),(supplier_code_const, 'supplier_code'),('Priority','preference'),('MOQ','moq')])

SUPPLIER_MASTER_HEADERS = OrderedDict([(supplier_id_const,'id'),('Name','name'),('Address','address'),(phone_no_const,'phone_number'),('Email','email_id'),('Status','status')])

MACHINE_MASTER_HEADERS = OrderedDict([('Machine ID', 'id'),('Machine Code', 'machine_code'), ('Machine Name', 'machine_name'), ('Model Name', 'model_name'), (serial_no_const, 'serial_number'), ('Band', 'band'),('Status', 'status')])

STOCK_DET = ([('0','receipt_number'),('1','receipt_date'),('2','sku_id__sku_code'),('3','sku_id__wms_code'),('4','sku_id__sku_desc'),('5','location_id__zone'),('6','location_id__location'),('7','quantity')])

ORDER_DETAIL_HEADERS = OrderedDict([(order_id_const,'order_id'),(sku_code_const,'sku_id__sku_code'),('Title','title'),('Product Quantity','quantity'),('Shipment Date','shipment_date')])

PICK_LIST_HEADERS = OrderedDict([('Picklist ID','picklist_number'),('Picklist Note','remarks'),('Date','creation_date')])

PO_SUGGESTIONS = OrderedDict([('0','creation_date'),('1','supplier_id'),('2','sku_id'),('3','order_quantity'),('4','price'),('5','status')])

RECEIVE_PO_HEADERS = OrderedDict([(order_id_const,'order_id'),(order_date_const,'creation_date'),(supplier_id_const,'open_po_id__supplier_id__id'),(supplier_name_const,'open_po_id__supplier_id__name')])

STOCK_DETAIL_HEADERS = OrderedDict([(sku_code_const,'sku_id__sku_code'),(wms_code_const,'sku_id__wms_code'),(product_desc_const,'sku_id__sku_desc'),('Quantity','quantity')])

CYCLE_COUNT_HEADERS = OrderedDict([(wms_code_const,'sku_id__wms_code'),('Zone','location_id__zone'),('Location','location_id__location'),('Quantity','quantity')])

BATCH_DATA_HEADERS = OrderedDict([(sku_code_const,'sku__sku_code'),('Title','title'),(total_qty_const,'quantity')])

PUT_AWAY = OrderedDict([(po_number_const,'open_po_id'),(order_date_const,'creation_date'),(supplier_id_const,'open_po_id__supplier_id__id'),(supplier_name_const,'open_po_id__supplier_id__name')])

SKU_MASTER_HEADERS = OrderedDict(
    [(sku_code_const, 'wms_code'), (ean_number_const, 'ean_number'), (product_desc_const, 'sku_desc'),
     (sku_type_const, 'sku_type'), (sku_category_const, 'sku_category'), (sku_class_const, 'sku_class'),
     ('Pick Group', 'pick_group'), ('Zone', 'zone_id'), ('Creation Date', 'creation_date'), ('Updation Date', 'updation_date'),
     ('Combo Flag', 'relation_type'), ('Status', 'status'),('Created By','created_by'),('Updated By','updated_by') ,('MRP', 'mrp'), ('HSN Code', 'hsn_code'),
     ('Tax Type', 'product_type')])

QUALITY_CHECK = OrderedDict([('Purchase order_id_const','purchase_order__order_id'),(supplier_id_const,'purchase_order__open_po__supplier_id'),(supplier_name_const,  'purchase_order__open_po__supplier__name'),(total_qty_const,'purchase_order__received_quantity')])

SHIPMENT_INFO = OrderedDict([('',id),(customer_id_const,'order__customer_id'),(customer_name_const,'order__customer_name')])

CYCLE_COUNT_FIELDS = {'cycle': '', 'sku_id': '', 'location_id': '',
                   'quantity': '', 'seen_quantity': '',
                   'status': 1, 'creation_date': '', 'updation_date': ''}

INVENTORY_FIELDS =  {'cycle_id': '', 'adjusted_location': '',
                   'adjusted_quantity': '', 'reason': 'Moved Successfully',
                    'creation_date': '', 'updation_date': ''}

REPLENISHMENT_EXCEL_HEADERS = [sku_mandatory_const, source_zone_const, source_loc_const, destination_zone_const, destination_loc_const, 'Min Qty*', 'Max Qty*']

NTE_REPLENISHMENT_EXCEL_HEADERS = [sku_mandatory_const, source_zone_const, source_loc_const, destination_zone_const, destination_loc_const, active_or_inactive_const]

REPLENISHMENT_EXCEL_MAPPING = OrderedDict(((sku_mandatory_const,'sku_code'),
                                            (source_zone_const,'from_zone'),
                                            (source_loc_const,'from_location'),
                                            (destination_zone_const, 'zone'),
                                            (destination_loc_const, 'location'),
                                            ('Min Qty*','min_qty'),
                                            ('Max Qty*', 'max_qty')))

NTE_REPLENISHMENT_EXCEL_MAPPING = OrderedDict(((sku_mandatory_const,'sku_code'),
                                                (source_zone_const,'from_zone'),
                                                (source_loc_const,'from_location'),
                                                (destination_zone_const,'zone'),
                                                (destination_loc_const,'location'),
                                                (active_or_inactive_const, 'status')))

BOM_UPLOAD_EXCEL_HEADERS = [
    product_sku_code_const, material_sku_const, 'Material Quantity',
    'Wastage Percentage', 'Value Percentage Share', 'Unit of Measurement',
    'Location','Nested(yes / no)', 'Type(ASSEMBLY / COMBO)', 'Status(Active/Inactive)'
]

JOB_ORDER_EXCEL_HEADERS = [
    'JO Type(Standard JO, Non Standard JO, Material to Material)',product_sku_code_const, product_sku_qty_const,
    material_sku_const, material_sku_quantity_const, batch_no_const, 'Location', 'Auto Pick(True/False)'
]

JOB_ORDER_EXCEL_HEADERS_KEYMAP = {
    'JO Type(Standard JO, Non Standard JO, Material to Material)' : 'jo_type',
    product_sku_code_const : 'product_code',
    product_sku_qty_const : 'product_quantity',
    material_sku_const : 'material_code',
    material_sku_quantity_const : 'material_quantity',
    batch_no_const : 'batch_no',
    'Location' : 'location',
    'Auto Pick(True/False)': 'auto_pick'
}

MOVE_INVENTORY_EXCEL_HEADERS_KEYMAP = {
    sku_mandatory_const: 'sku_code',
    source_location_const: 'source_loc',
    destination_location_const: 'dest_loc',
    serial_no_const: 'serial_number',
    'Batch REF/NO' : 'batch_ref_no',
    'Status(Available, OnHold, Consumed, Locked, Reserved, Rejected, Obsolete, Blocked)': 'source_status',
    carton_id_const : 'lpn_number',
    quantity_const: 'quantity',
    reason_const: 'reason',
    'Task Creation(Y/N)': 'task_creation',
    'Task Creation(Y/N)*': 'task_creation'
}

JOB_ORDER_EXCEL_MAPPING = OrderedDict((('jo_type', 0),('product_code', 1), ('product_quantity', 2), ('material_code', 3), ('material_quantity', 4),
                            ('batch_no', 5), ('location', 6), ('carton_id',7)))

PUTAWAY_HEADERS = ['WMS CODE', 'Location', 'Original Quantity', 'Putaway Quantity', '']


BATCH_CREATION_EXCEL_MAPPING = OrderedDict(
    (
        (sku_mandatory_const, 'sku_code'),
        ('Location*', 'location'),
        (carton_id_const, 'lpn_number'),
        (physical_qty_mandatory_const, 'quantity'), 
        (batch_no_mandatory_const, 'batch_no'),
        (batch_reference_const, 'batch_reference'),
        (vendor_batch_no_const, 'vendor_batch_number'),
        ('Inspection Lot Number', 'inspection_lot_number'),
        ('Best Before Date(YYYY-MM-DD)', 'best_before_date'),
        ('Retest Date(YYYY-MM-DD)', 'retest_date'),
        ('Reevaluation date(YYYY-MM-DD)', 'reevaluation_date'),
        ('MRP', 'mrp'),
        ('Manufactured Date(YYYY-MM-DD)', 'manufactured_date'),
        ('Expiry Date(YYYY-MM-DD)', 'expiry_date'),
        ('Weight', 'weight'),
        ("Price", 'unit_price'),
        ("Adjustment Type(Scrap)", "adjustment_type"),
        ('Reason*', 'reason'), 
        (serial_no_const, 'serial_number'),
        (stock_status_const, 'stock_status')
    )
)
ADJUST_INVENTORY_EXCEL_MAPPING = OrderedDict(((sku_mandatory_const, 'sku_code'),
                                              ('Location*', 'location'),
                                              (carton_id_const, 'lpn_number'),
                                              (physical_qty_mandatory_const, 'quantity'), 
                                              (batch_no_const, 'batch_no'),
                                              (batch_reference_const, 'batch_reference'),
                                              ("Price", 'unit_price'),
                                              ("Adjustment Type(Scrap)", "adjustment_type"),
                                              ('Reason*', 'reason'), 
                                              (serial_no_const, 'serial_number'),
                                              (stock_status_const, 'stock_status')))

CYCLE_COUNT_CREATION_HEADERS = OrderedDict(((sku_code_const, 'sku_code'),
                                            ('Location' , 'location'),
                                            ('Batch Number/Ref', 'batch_no'),
                                            ('Stock Status', 'stock_status'))) 

CONFIRM_CYCLE_COUNT_HEADERS = ['Cycle Count ID', sku_code_const, 'Batch Number/Ref', 'Seen Quantity', 'Reason']

CONFIRM_CYCLE_COUNT_MAPPING = OrderedDict((('Cycle Count ID', 'cycle'),
                                            (sku_code_const, 'sku_code'),
                                            ('Batch Number/Ref', 'batch_no_or_reference'),
                                            ('Seen Quantity', 'seen_quantity'),
                                            ('Reason', 'reason')))

MOVE_INVENTORY_EXCEL_MAPPING = OrderedDict((
                                            (sku_mandatory_const, 'wms_code'),
                                            (source_location_const, 'source'),
                                            (destination_location_const, 'destination'),
                                            ('Quantity*', 'quantity'), ('Source Carton ID', 'carton_id'),
                                            ('Destination Carton ID', 'dest_carton'),
                                            (batch_no_const, 'batch_no'),
                                            ('Reason*', 'reason'),
                                            (serial_no_const,'serial_number'),
                                            (stock_status_const, 'stock_status')))

FORECAST_MASTER_EXCEL_HEADERS = ['Forecast Name',sku_code_const,'Frequency(Daily, Weekly, Monthly)', 'Frequency Value', 'Value', 'Year']

FORECAST_MASTER_EXCEL_MAPPING = OrderedDict((
                                            ('Forecast Name','forecast_name'),
                                            (sku_code_const, 'sku_code'),
                                            ('Frequency(Daily, Weekly, Monthly)', 'frequency_type'),
                                            ('Frequency Value', 'frequency_value'),
                                            ('Value', 'value'),
                                            ('Year', 'year')
                                        ))

SKU_PACK_MASTER_EXCEL_HEADERS = [sku_mandatory_const, 'Pack ID/UOM*', 'Pack Quantity/UOM Conversion*', 'Purchase UOM (Yes/No)', 'Sales UOM (Yes/No)', active_or_inactive_const]
SKU_PACK_MASTER_EXCEL_MAPPING = OrderedDict((
                                            (sku_mandatory_const,'sku_code'),
                                            ('Pack ID/UOM*', 'pack_id'),
                                            ('Pack Quantity/UOM Conversion*', 'pack_qty'),
                                            ('Purchase UOM (Yes/No)', 'purchase_uom'),
                                            ('Sales UOM (Yes/No)', 'sales_uom'),
                                            (active_or_inactive_const, 'status'),
                                        ))


UOM_MASTER_EXCEL_MAPPING = OrderedDict((
    ('UOM Code *', 'uom_code'),
    ('UOM Description *', 'uom_description'),
    ('UOM Class*', 'uom_class'),
    ('Conversion Factor*', 'conversion_factor'),
    ('Decimals*', 'decimal'),
    ('Base UOM', 'base_uom')
))

LPN_MASTER_EXCEL_MAPPING = OrderedDict((
                                        ("LPN Type*", 'carton_type'),
                                        ("LPN*", 'lpn_number')
))

BATCH_CREATION_HEADERS = OrderedDict([(sku_code_const, 'sku_code'), 
                                      (batch_no_const, 'batch_no'), 
                                      ('Batch Reference', 'batch_reference'),
                                      ('MRP', 'mrp'),
                                      (manufactured_date_const, 'manufactured_date'),
                                      (expiry_date_const, 'expiry_date'),
                                      ('Best Before Date', 'best_before_date'),
                                      ('Re-Test Date', 'retest_date'),
                                      ('Re-Evaluation Date', 'reevaluation_date'),
                                      ('Weight', 'weight'),
                                      ('Vendor Batch Number', 'vendor_batch_no'),
                                      (inspection_lot_number, 'inspection_lot_number'),
                                      ('Batch Key', 'batch_key')])


BACK_ORDER_TABLE = [ wms_code_const, ordered_qty_const, 'Stock Quantity', 'Transit Quantity', 'Procurement Quantity']

BACK_ORDER_RM_TABLE = [ wms_code_const, ordered_qty_const, 'Stock Quantity', 'Transit Quantity', ' Procurement Quantity']

BACK_ORDER_HEADER = [supplier_name_const, wms_code_const, 'Title', 'Quantity', 'Price']

QC_WMS_HEADER = ['Purchase Order', 'Quantity', 'Accepted Quantity', 'Rejected Quantity']

REJECT_REASONS = ['Color Mismatch', 'Price Mismatch', 'Wrong Product', 'Package Damaged', 'Product Damaged', 'Others']

QC_SERIAL_FIELDS = {'quality_check_id': '', 'serial_number_id': '', 'status': '','reason': '', 'creation_date': NOW}

RAISE_JO_HEADERS = OrderedDict([(product_sku_code_const, 'product_code'), ('Product SKU Description', 'description'),
                                (product_sku_qty_const, 'product_quantity'),
                                (material_sku_const, 'material_code'), (material_sku_quantity_const, 'material_quantity'),
                                ('Material SKU Description','material_desc'),('Measurement Type', 'measurement_type')])

JO_PRODUCT_FIELDS = {'product_quantity': 0, 'received_quantity': 0, 'job_code': 0, 'jo_reference': '','status': 'open', 'product_code_id': '',
                     'creation_date': NOW}

JO_MATERIAL_FIELDS = {'material_code_id': '', 'job_order_id': '', 'material_quantity': '', 'status': 1, 'creation_date': NOW}

RAISE_JO_TABLE_HEADERS = ['JO Reference', 'Creation Date']

RM_CONFIRMED_HEADERS = ['Job Code ', 'Creation Date', 'Order Type']

MATERIAL_PICKLIST_FIELDS = {'jo_material_id': '', 'status': 'open', 'reserved_quantity': 0, 'picked_quantity': 0, 'creation_date': NOW}

MATERIAL_PICK_LOCATIONS = {'material_picklist_id': '', 'stock_id': '', 'quantity': 0, 'status': 1, 'creation_date': NOW}

RECEIVE_JO_TABLE = [' Job Code', 'Creation Date', 'Receive Status']

RECEIVE_JO_TABLE_HEADERS = ['WMS CODE', 'JO Quantity', received_qty_const, 'Stage']

GRN_HEADERS = ('WMS CODE', order_qty_const, received_qty_const)

PUTAWAY_JO_TABLE_HEADERS = ['  Job Code', 'Creation Date']

PUTAWAY_HEADERS = ['WMS CODE', 'Location', 'Original Quantity', 'Putaway Quantity', '']

CUSTOMER_MASTER_HEADERS = [' Customer ID', customer_name_const, 'Email', phone_no_const, 'Address', 'Status']

CUSTOMER_FIELDS = ( ((customer_id_const, 'id',60), ('Customer Name *', 'name',256)),
                    ((email_const, 'email_id',64), (phone_num_const, 'phone_number',10)),
                    (('Address *', 'address'), ('Status', 'status',11)), )

CUSTOMER_DATA = {'name': '', 'address': '', 'phone_number': '', 'email_id': '', 'status': 1, 'creation_date': NOW}

PRODUCTION_STAGES = {'Apparel': ['', 'Raw Material Inspection', 'Fabric Washing', 'Finishing'], 'Default': ['', 'Raw Material Inspection',
                     'Fabric Washing', 'Finishing']}

STATUS_TRACKING_FIELDS = {'status_id': '', 'status_type': '', 'status_value': '', 'creation_date': NOW}

BOM_TABLE_HEADERS = [product_sku_code_const, product_desc_const]

COMBO_SKU_EXCEL_HEADERS = [sku_code_const, 'Combo SKU']

ADD_BOM_HEADERS = OrderedDict([(material_sku_const, 'material_sku'), ('Material Quantity', 'material_quantity'),
                               ('Unit of Measurement', 'unit_of_measurement')])

ADD_BOM_FIELDS = {'product_sku_id': '', 'material_sku_id': '', 'material_quantity': 0, 'unit_of_measurement': '', 'creation_date': NOW}

UOM_FIELDS = ['KGS', 'UNITS', 'METERS', 'SHEETS']

MAIL_REPORTS = { 'sku_list': [sku_list_const], 'location_wise_stock': [location_wise_sku_const], 'receipt_note': [receipt_summary_const], 'dispatch_summary': [dispatch_summary_const], 'sku_wise': [sku_wise_stock] }

MAIL_REPORTS_DATA = {'Raise PO': 'raise_po', 'Receive PO': 'receive_po', 'Orders': 'order', 'Dispatch': 'dispatch', 'RTV': 'rtv_mail'}

REPORTS_DATA = {sku_list_const: 'sku_list', location_wise_sku_const: 'location_wise_stock', receipt_summary_const: 'receipt_note', dispatch_summary_const: 'dispatch_summary', sku_wise_stock: 'sku_wise'}

SKU_CUSTOMER_FIELDS = ( ((customer_id_const, 'customer_id',60), ('Customer Name *', 'customer_name',256)),
                        (('SKU Code *','sku_code'), ('Price *', 'price'), ) )

CUSTOMER_SKU_DATA = {'customer_id': '', 'sku_id': '', 'price': 0, 'customer_sku_code': ''}

CUSTOMER_SKU_MAPPING_HEADERS = OrderedDict([(customer_id_const,'customer_name__id'),(customer_name_const,'customer_name__name'),
                                            (sku_code_const,'sku__sku_code'),('Price','price')])

SHOPCLUES_EXCEL = {'order_id': 1, 'quantity': 5, 'title': 3, 'invoice_amount': 20, 'address': 11, 'customer_name': 10,
                   'marketplace': 'Shopclues', 'sku_code': 15}

FLIPKART_EXCEL = {'order_id': 6, 'quantity': 14, 'title': 2, 'invoice_amount': 17, 'address': 22, 'customer_name': 21,
                  'marketplace': 'Flipkart', 'sku_code': 8}

FLIPKART_EXCEL1 = {'order_id': 6, 'quantity': 14, 'title': 2, 'invoice_amount': 16, 'address': 21, 'customer_name': 20,
                  'marketplace': 'Flipkart', 'sku_code': 8}

PAYTM_EXCEL1 = {'order_id': 0, 'quantity': 21, 'title': 12, 'invoice_amount': 20, 'address': 7, 'customer_name': 3, 'marketplace': 'Paytm',
               'sku_code': 14}

PAYTM_EXCEL2 = {'order_id': 13, 'quantity': 10, 'title': 1, 'invoice_amount': 9, 'address': 22, 'customer_name': 18, 'marketplace': 'Paytm',
                'sku_code': 3}

FLIPKART_FA_EXCEL = {'order_id': 1, 'quantity': 13, 'title': 8, 'invoice_amount': 9, 'address': 20, 'customer_name': 29,
                     'marketplace': 'Flipkart FA', 'sku_code': 7}

SNAPDEAL_EXCEL = {'order_id': 3, 'title': 1, 'invoice_amount': 13, 'customer_name': 8, 'marketplace': 'Snapdeal', 'sku_code': 4,
                  'shipment_date': 17}

SNAPDEAL_EXCEL1 = {'order_id': 3, 'title': 2, 'invoice_amount': 14, 'customer_name': 9, 'marketplace': 'Snapdeal', 'sku_code': 5,
                  'shipment_date': 8}

AMAZON_FA_EXCEL = {'title': 4, 'invoice_amount': 14, 'marketplace': 'Amazon FA', 'sku_code': 3, 'quantity': 7}

SNAPDEAL_FA_EXCEL = {'title': 4, 'invoice_amount': 6, 'marketplace': 'Snapdeal FA', 'sku_code': 3, 'quantity': 5}

ORDER_DEF_EXCEL = {'order_id': 0, 'quantity': 3, 'title': 1, 'shipment_date': 4,'sku_code': 2, 'marketplace': ''}

JABONG_EXCEL = {'order_id': 1, 'title': 7, 'invoice_amount': 14, 'marketplace': 'Jabong', 'sku_code': 5, 'quantity': 9}

INDIA_TIMES_EXCEL = {'order_id': 2, 'invoice_amount': 16, 'address': 8, 'customer_name': 7,
                     'marketplace': 'Indiatimes', 'sku_code': 15, 'telephone': 12}

HOMESHOP18_EXCEL = {'order_id': 0, 'invoice_amount': 10, 'address': 18, 'customer_name': 4, 'marketplace': 'HomeShop18',
                    'title': 6, 'sku_code': 8, 'quantity': 9, 'telephone': 22}

AMAZON_EXCEL = {'order_id': 1, 'invoice_amount': 25, 'address': [17,18,19], 'customer_name': 8, 'marketplace': 'Amazon',
                'title': 11, 'sku_code': 10, 'quantity': 12, 'telephone': 9, 'email_id': 7}

AMAZON_EXCEL1 = {'order_id': 1, 'invoice_amount': 11, 'address': [17,18,19], 'customer_name': 16, 'marketplace': 'Amazon',
                        'title': 8, 'sku_code': 7, 'quantity': 9, 'telephone': 6, 'email_id': 4}

ASKMEBAZZAR_EXCEL = {'order_id': 0, 'invoice_amount': 14, 'address': 27, 'customer_name': 9, 'marketplace': 'Ask Me Bazzar',
                    'title': 30, 'sku_code': 29, 'quantity': 12, 'telephone': 10}

FLIPKART_FA_EXCEL1 = {'order_id': 16, 'invoice_amount': 15, 'marketplace': 'Flipkart FA', 'title': 0, 'sku_code': 2, 'quantity': 9}

CAMPUS_SUTRA_EXCEL = {'order_id': 2, 'invoice_amount': 14, 'marketplace': 'Campus Sutra', 'sku_code': 6, 'quantity': 13, 'customer_name': 3,
                      'address': 5, 'telephone': 10, 'email_id': 7, 'shipment_date': 1}

LIMEROAD_EXCEL = {'order_id': 0, 'invoice_amount': 8, 'marketplace': 'Lime Road', 'sku_code': 3, 'quantity': 9, 'customer_name': 10,
                      'address': 12, 'telephone': 11,  'shipment_date': 1}

MYNTRA_EXCEL = {'invoice_amount': 14, 'marketplace': 'Myntra', 'sku_code': 2, 'quantity': 9, 'title': 7}

COMBO_SKU_HEADERS = ['Combo SKU Code', 'Description']

ADJUST_INVENTORY_EXCEL_HEADERS = [wms_code_const, 'Location', physical_qty_const, 'Reason']

EXCEL_REPORT_MAPPING = {'dispatch_summary': 'get_dispatch_data', 'sku_list': 'get_sku_filter_data', 'location_wise': 'get_location_stock_data',
                        'goods_receipt': 'get_po_filter_data', 'receipt_summary': 'get_receipt_filter_data',
                        'sku_stock': 'print_sku_wise_data', 'sku_wise_purchases': 'sku_wise_purchase_data',
                        'supplier_wise': 'get_supplier_details_data', 'sales_report': 'get_sales_return_filter_data',
                        'inventory_adjust_report': 'get_adjust_filter_data', 'inventory_aging_report': 'get_aging_filter_data',
                        'rm_picklist_report': 'get_rm_picklist_data'}

SKU_GROUP_FIELDS = {'group': '', 'user': '', 'creation_date': NOW}

LOCATION_GROUP_FIELDS = {'group': '', 'location_id': '', 'creation_date': NOW}

RAISE_ST_FIELDS = ( ((warehouse_const, 'warehouse_name',60), ), )

RAISE_ST_FIELDS1 = OrderedDict([(wms_code_const_req,'wms_code'), (qty_const_req,'order_quantity'),
                                ('Price','price')])

OPEN_STOCK_HEADERS = [warehouse_const, total_qty_const]

OPEN_ST_FIELDS = {'warehouse_id': '', 'order_quantity': 0, 'price': 0, 'sku_id': '', 'status': 1, 'creation_date': NOW}

STOCK_TRANSFER_FIELDS = {'order_id': '', 'invoice_amount': 0, 'quantity': 0, 'shipment_date': NOW, 'st_po_id': '', 'sku_id': '', 'status': 1}

STOCK_TRANSFER_HEADERS = [warehouse_const, 'Stock Transfer ID', sku_code_const, 'Quantity']

VIEW_STOCK_TRANSFER = OrderedDict([(wms_code_const,'wms_code'), ('Quantity','quantity'), ('Price','price')])

ST_ORDER_FIELDS = {'picklist_id': '', 'stock_transfer_id': ''}

WAREHOUSE_HEADERS = ['Username', 'Name', 'Email', 'City']

ADD_USER_DICT = {'username': '', 'first_name': '', 'last_name': '', 'password': '', 'email': ''}

ADD_WAREHOUSE_DICT = {'user_id': '', 'city': '', 'is_active': 1, 'country': '', u'state': '', 'pin_code': '', 'address': '',
                      'phone_number': '', 'prefix': '', 'location': '', 'place_of_supply': ''}


ADD_DISCOUNT_FIELDS = ( (('SKU Code *','sku_code'),('SKU Discount *','sku_discount')),
                            (('Category *','category'),('Category Discount *','category_discount')),)

DISCOUNT_HEADERS = [sku_code_const, sku_category_const, 'SKU Discount', 'Category Discount']

VENDOR_HEADERS = ['Vendor ID', 'Name', 'Address', phone_no_const, 'Email', 'Status']

VENDOR_FIELDS = ( (('Vendor ID *', 'vendor_id',60), ('Vendor Name *', 'name',256)),
                    ((email_const, 'email_id',64), (phone_num_const, 'phone_number',10)),
                    (('Address *', 'address'), ('Status', 'status',11)), )

VENDOR_DATA = {'vendor_id': '', 'name': '', 'address': '', 'phone_number': '', 'email_id': '', 'status': 1, 'creation_date': NOW}

RWO_FIELDS = {'vendor_id': '', 'job_order_id': '', 'status': 1, 'creation_date': NOW}

RWO_PURCHASE_FIELDS = {'purchase_order_id': '', 'rwo_id': '', 'creation_date': NOW}

SHIPMENT_STATUS = ['Dispatched', 'In Transit', 'Out for Delivery', 'Delivered']

USER_ROLES = [
    'Catalog Incharge', 'Customer Success Incharge', 'Inbound Incharge', 'Inventory Incharge',
    'Order Management Incharge', 'Outbound Incharge', 'Packing Incharge', 'Picking Incharge', 'QC Incharge',
    'Production Incharge', 'Purchase Incharge', 'Putaway Incharge', 'Shipment Incharge', 'Warehouse Manager',
    'PO Approver','Inventory Approver', 'Dispense Manager', 'Pick and Pass', 'STOCKONE_ALL_PERMS','Gate Manager',
    'BA to SA Assignment Based on Destination Zone/Subzone', 'Sorting Incharge', 'Order Approver', 'ARS Incharge',
]

CONFIG_INPUT_DICT = {'email': 'email', 'report_freq': 'report_frequency',
                     'scan_picklist_option': 'scan_picklist_option',
                     'data_range': 'report_data_range', 'imei_limit': 'imei_limit',
                     'invoice_remarks': 'invoice_remarks',
                     'invoice_declaration': 'invoice_declaration',
                     'pos_remarks': 'pos_remarks',
                     'raisepo_terms_conditions': 'raisepo_terms_conditions',
                     'invoice_marketplaces': 'invoice_marketplaces', 'serial_limit': 'serial_limit',
                     'extra_view_order_status': 'extra_view_order_status',
                     'bank_option_fields': 'bank_option_fields',
                     'invoice_types': 'invoice_types',
                     'mode_of_transport': 'mode_of_transport',
                     'terms_of_payment': 'terms_of_payment',
                     'shelf_life_ratio': 'shelf_life_ratio',
                     'auto_expire_enq_limit': 'auto_expire_enq_limit',
                     'sales_return_reasons': 'sales_return_reasons',
                     'rtv_prefix_code': 'rtv_prefix_code',
                     'discrepency_prefix': 'discrepency_prefix',
                     'weight_integration_name': 'weight_integration_name',
                     'delivery_challan_terms_condtions': 'delivery_challan_terms_condtions',
                     'order_prefix': 'order_prefix',
                     'st_po_prefix': 'st_po_prefix',
                     'pending_pr_prefix': 'pending_pr_prefix',
                     'location_min_norm': 'location_min_norm',
                     'bulk_zones_list': 'bulk_zones_list',
                     'pick_zones_list': 'pick_zones_list',
                     'view_orders_alert' : 'view_orders_alert',
                     'picklist_reasons' : 'picklist_reasons',
                     'grn_rejection_reasons' : 'grn_rejection_reasons',
                     'ba_to_sa_logic': 'ba_to_sa_logic',
                     'ba_to_sa_type': 'ba_to_sa_type',
                     'express_putaway': 'express_putaway',
                     'putaway_strategy': 'putaway_strategy',
                     'network_type': 'network_type',
                     'receive_higher_quantity' : 'receive_higher_quantity',
                     'invoice_type' : 'invoice_type',
                     'putaway_sa_time_from':'putaway_sa_time_from',
                     'putaway_sa_time_to':'putaway_sa_time_to',
                     'cycle_count_allowance_value':'cycle_count_allowance_value',
                     'saved_weighing_scale': 'saved_weighing_scale',
                     'assign_task_count':'assign_task_count',
                     'assign_task_count_type': 'assign_task_count_type',
                     'sku_inv_price_tolerance': 'sku_inv_price_tolerance',
                     'grn_inv_price_tolerance': 'grn_inv_price_tolerance',
                     'picklist_priority':'picklist_priority',
                     'sort_by': 'sort_by',
                     'auto_po_frequency': 'auto_po_frequency',
                     'batosa_assign_task_count': 'batosa_assign_task_count',
                     'closing_stock_zones': 'closing_stock_zones',
                     'po_tolerance' : 'po_tolerance',
                     'rm_return_logic': 'rm_return_logic',
                     'replenishment_task_creation': 'replenishment_task_creation',
                     'group_by_dest_details': 'group_by_dest_details',
                     'nte_picklist_strategy': 'nte_picklist_strategy',
                     'replenishment_pick_drop_user': 'replenishment_pick_drop_user',
                     'nte_pick_drop_user': 'nte_pick_drop_user',
                     'replenishment_picklist_strategy': 'replenishment_picklist_strategy',
                     'multi_scanner': 'multi_scanner',
                     'style_based_asn_summary': 'style_based_asn_summary',
                     'qr_code_format': 'qr_code_format',
                     'batch_key_prefix' : 'batch_key_prefix',
                     'group_replenishment_task_count': 'group_replenishment_task_count',
                     'nte_selected_source_zone': 'nte_selected_source_zone',
                     'nte_selected_destination_zone': 'nte_selected_destination_zone',
                     'asn_inv_price_tolerance': 'asn_inv_price_tolerance',
                     'allow_multi_customer_lpn_scan_at_invoice': 'allow_multi_customer_lpn_scan_at_invoice',
                     'cluster_type_filtering': 'cluster_type_filtering',
                     'sort_picklist_by_scanned_location': 'sort_picklist_by_scanned_location',
                     'enable_order_view_v2': 'enable_order_view_v2', #Temporary: Will be removed once v2 is stable
                     'zone_wise_manual_assignment': 'zone_wise_manual_assignment',
}
TAX_VALUES = [{'tax_name': 'Inter State', 'tax_value': 'inter_state'},
              {'tax_name': 'Intra State', 'tax_value': 'intra_state'}]
LABEL_KEYS = ["NOTIFICATION_LABEL", "MASTERS_LABEL", "INBOUND_LABEL", "PRODUCTION_LABEL", "STOCK_LABEL",
              "OUTBOUND_LABEL", "SHIPMENT_LABEL",
              "OTHERS_LABEL", "UPLOADS", "REPORTS", "CONFIGURATIONS", "PAYMENT_LABEL", 'MASTERS_VIEW_LABEL']
PERMISSION_DICT = OrderedDict((
    # Notifications
    ("NOTIFICATION_LABEL", (("Notifications", "view_pushnotifications"),)),
    # Masters
    ("MASTERS_LABEL", (("SKU Master Edit", "add_skumaster"), ("Location Master", "add_locationmaster"),
                       ("Supplier Master Edit", "add_suppliermaster"), ("Source SKU Mapping Edit", "add_skusupplier"),
                       ("Project Master", "add_corporatemaster"), ("Carton Master Edit", "add_cartontypes"),
                       ("Reseller Corporate Mapping", "add_corpresellermapping"),
                       ("Customer Master Edit", "add_customermaster"), ("Customer SKU Mapping", "add_customersku"),
                       ("BOM Master", "add_bommaster"), ("Staff Master", "add_staffmaster"),
                       ("Vendor Master", "add_vendormaster"), ("Discount Master", "add_categorydiscount"),
                       ("Custom SKU Template", "add_productproperties"), ("Size Master", "add_sizemaster"),
                       ('Pricing Master Edit', 'add_pricemaster'), ('Network Master', 'add_networkmaster'),
                       ('Tax Master', 'add_taxmaster'), ('T&C Master', 'add_tandcmaster'),
                       ('Seller Master', 'add_sellermaster'), ('Seller Margin Mapping', 'add_sellermarginmapping'),
                       ('Staff Master', 'add_staffmaster'), ('Notification Master', 'add_pushnotifications'),
                       ('Cluster SKU Mapping', 'add_clusterskumapping'),
                       ('Inventory Norm Master', 'add_replenushmentmaster'),
                       )),

    # Inbound
    ("INBOUND_LABEL", (("Raise PO", "add_openpo"), ("Confirm PO", "change_openpo"), ("Cancel PO", "delete_purchaseorder"),
                       ("Receive PO", "add_purchaseorder"), ("Generate GRN", "change_purchaseorder"),
                       ("Quality Check", "add_qualitycheck"),
                       ("Putaway Confirmation", "add_polocation"), ("Sales Returns", "add_orderreturns"),
                       ("Returns Putaway", "add_returnslocation"),
                       ("RTV", "add_returntovendor"), ("Seller Invoices", "add_sellerpo"),
                       ("Supplier Invoices", "change_sellerposummary"), ("GRN Edit", "delete_sellerposummary"),
                       ("View PendingPO", "view_pendingpo"), ("Add PendingPO", "add_pendingpo"),
                       ("Change PendingPO", "change_pendingpo"),
                       ("View PendingPR", "view_pendingpr"), ("Add PendingPR", "add_pendingpr"),
                       ("Change PendingPR", "change_pendingpr"),
                       ("Approve Service GRN DOA", "change_mastersdoa"),
                       ("Pending Invoice Mismatches", "change_pocreditnote"),
                       ("Approve Source SKU Mapping DOA", "approve_source_sku_doa"),
                       ("Approve SKU Master DOA", "approve_sku_master_doa"),
                       ("Approve Service Master DOA", "approve_service_master_doa"),
                       ("Approve Otheritems Master DOA", "approve_otheritems_master_doa"),
                       ("Approve Inventory Adjustment", "approve_inventory_adjustment"),
                       ("Approve Manual Test", "approve_manual_test"),
                       ("View Manual Test Approval", "view_manual_test_approval"),
                       ("Material Planning", "change_replenushmentmaster"),
                       ("Create ASN", "add_asnsummary"),
                       ("Update Purchase Order", "update_purchaseorder"),
                       ("Create Scheduled Cycle Count", "add_cyclecountschedule"),
                       ("Run Cycle Count Schedule", "change_cyclecountschedule"),
                       ("New UOM","add_uomdetail"),
                       ("Delete UOM","delete_uomdetail")
                       )),

    # Production
    ("PRODUCTION_LABEL", (("Raise Job order", "add_jomaterial"), ("RM Picklist", "add_materialpicklist"),
                          ("Receive Job Order", "add_joborder"), ("Job Order Putaway", "add_rmlocation"))),

    # Stock Locator
    ("STOCK_LABEL", (("Stock Summary", "add_skustock"), ("Stock Detail", "add_stockdetail"),
                     ("Vendor Stock", "add_vendorstock"),
                     ("Cycle Count", "add_cyclecount"), ("Move Inventory", "change_inventoryadjustment"),
                     ("Inventory Adjustment", "add_inventoryadjustment"),
                     ("Warehouse Stock", "add_usergroups"), ("IMEI Tracker", "add_poimeimapping"),
                     ("Seller Stock", "add_sellerstock"), ("View BA to SA", "view_skuclassification"),
                     ("Calculate BA to SA", "add_skuclassification"),
                     ("Confirm BA to SA", "change_skuclassification"))),

    # Outbound
    ("OUTBOUND_LABEL", (("Create Orders", "add_orderdetail"), ("View Orders", "add_picklist"), ("Cancel Picklist", "delete_picklist"),
                        ("Pull Confirmation", "add_picklistlocation"), ("Enquiry Orders", "add_enquirymaster"),
                        ("Customer Invoices", "add_sellerordersummary"), ("Manual Orders", "add_manualenquiry"),
                        ("Shipment Info", "add_shipmentinfo"), ("Create Stock Transfer", "add_stocktransfer"),
                        ('Create Manual Test', 'add_consumptiondata'), ('Closing Stock', 'change_consumptiondata'),
                        ('Cancel Picklist/Packlist' , 'change_sellerordersummary'),
                        ("Add Sorting","add_staginginfo"),("Edit Sorting","change_staginginfo")
                        )),

    # Shipment Info
    ("SHIPMENT_LABEL", ("Shipment Info", "add_shipmentinfo")),

    # Others
    ("OTHERS_LABEL", (("Raise Stock Transfer", "add_openst"), ("Create Stock Transfer", "add_stocktransfer"),
                      ('Upload Closing Stock', 'change_consumptiondata'), ('Upload Opening Stock', 'change_closingstock'),)),

    # Payment
    ("PAYMENT_LABEL", (("PAYMENTS", "add_paymentsummary"),)),

    # Uploads
    ("UPLOADS", (('Create Orders', 'add_orderdetail'), ('SKU Master', 'add_skumaster'),
                 ('Stock Detail', 'add_stockdetail'), ('Supplier Master', 'add_suppliermaster'),
                 ('add_skusupplier', 'add_skusupplier'), ('add_locationmaster', 'add_locationmaster'),
                 ('Raise PO', 'add_openpo'),
                 ('change_inventoryadjustment', 'change_inventoryadjustment'),
                 ('add_bommaster', 'add_bommaster'),
                 ('add_inventoryadjustment', 'add_inventoryadjustment'),
                 ('add_vendormaster', 'add_vendormaster'),
                 ('add_customermaster', 'add_customermaster'),
                 ('add_orderreturns', 'add_orderreturns'), ('add_pricemaster', 'add_pricemaster'),
                 ('add_networkmaster', 'add_networkmaster'), ('add_orderlabels', 'add_orderlabels'),
                 ('add_jomaterial', 'add_jomaterial'),
                 ('Intermediate Orders', 'add_intermediateorders'),
                 ('add_sellerstocktransfer', 'add_sellerstocktransfer'),
                 ('add_substitutionsummary', 'add_substitutionsummary'),
                 ('add_targetmaster', 'add_targetmaster'),
                 ('add_enquirymaster', 'add_enquirymaster'),
                 ('add_clusterskumapping', 'add_clusterskumapping'),
                 ('Upload Adjusted Consumption', 'change_consumptiondata'),
                 ('Upload Opening Stock', 'change_closingstock'),
                 )),
    ("REPORTS", (('SKU List Report', 'view_skumaster'), ('Location Wise Filter Report', 'view_locationmaster'),
                 ('Goods Receipt Note Report', 'view_sellerposummary'), ('Receipt Summary Report', 'view_polocation'),
                 ('Dispatch Summary Report', 'view_picklist'), ('SKU Wise Stock Report', 'view_stockdetail'),
                 ('SKU Wise PO Report', 'view_openpo'), ('Supplier Wise PO Report', 'view_purchaseorder'),
                 ('Sales Return Report', 'view_orderreturns'),
                 ('Inventory Adjustment Report', 'view_inventoryadjustment'),
                 ('Inventory Aging Report', 'view_cyclecount'), ('Stock Summary Report', 'change_stockdetail'),
                 ('Daily Production Report', 'view_statustracking'), ('Order Summary Report', 'view_orderdetail'),
                 ('Open JO Report', 'view_openjo'), ('Seller Invoice Detail Report', 'view_sellerpo'),
                 ('RM Picklist Report', 'view_materialpicklist'), ('Stock Ledger Report', 'view_stockstats'),
                 ('Shipment Report', 'view_ordershipment'), ('RTV Report', 'view_returntovendor'),
                 ('Current Stock Report', 'view_skudetailstats'),
                 ('Inventory Value Report', 'delete_skudetailstats'),
                 ('Stock Cover Report', 'add_skudetailstats'),
                 ('MoveInventory Report', 'view_moveinventory'),
                 ('Bulk To Retail Report', 'view_substitutionsummary'),
                 ('Consumption Report', 'view_consumption'),
                 ('Closing Stock Report', 'view_closingstock'),
                 ('Approval PO Report', 'view_pendingpo'),
                 )),
    # Master Edit Access
    ("MASTERS_VIEW_LABEL", (('SKU Master View', 'view_skumaster'),
                            ('Supplier Master View', 'view_suppliermaster'),
                            ('Source SKU Mapping View', 'view_skusupplier'),
                            ('Pricing Master View', 'view_pricemaster'),
                            ('Customer Master View', 'view_customermaster')
                            )),

    # Uploaded POs
    ("UPLOADPO_LABEL", (("uploadedPOs", "add_orderuploads"),)),
    # Configurations
    ("CONFIGURATIONS", (("Configutaions", "add_miscdetail"),)),

))

# Company logo names
COMPANY_LOGO_PATHS = {'TranceHomeLinen': 'trans_logo.jpg', 'Subhas_Publishing': 'book_publications.png',
                      'sm_admin': 'sm-brand.jpg', 'corp_attire': 'corp_attire.jpg',
                      'aidin_technologies': 'aidin_tech.jpg', 'nutricane': 'nutricane.jpg',
                      '72Networks': '72networks.png'}
ISO_COMPANY_LOGO_PATHS = {'aidin_technologies': 'iso_aidin_tech.jpg'}
LEFT_SIDE_COMPNAY_LOGO = {'Skinstore': 'skin_store.png'}

RECEIVE_OPTIONS = OrderedDict((('One step Receipt + Qc', 'receipt-qc'), ('Two step Receiving', '2-step-receive')))

MAIL_REPORTS = {'sku_list': [sku_list_const], 'location_wise_stock': [location_wise_sku_const],
                'receipt_note': [receipt_summary_const], 'dispatch_summary': [dispatch_summary_const],
                'shipment_report': ['Shipment Report'],
                'sku_wise': [sku_wise_stock]}

STYLE_DETAIL_HEADERS = OrderedDict(((sku_code_const, 'wms_code'), (sku_desc_const, 'sku_desc'), ('Size', 'sku_size'),
                                    ('1-Day Stock', 'physical_stock'), ('3-Day Stock', 'all_quantity')
                                    ))

# Configurations
PICKLIST_OPTIONS = {'Scan SKU': 'scan_sku', 'Scan SKU Location': 'scan_sku_location', 'Scan Serial': 'scan_serial',
                    'Scan Label': 'scan_label',
                    'Scan None': 'scan_none'}

ORDER_HEADERS_d = OrderedDict(
    (('Unit Price', 'unit_price'), ('Amount', 'amount'), ('Tax', 'tax'), ('Total Amount', 'total_amount'),
     ('Remarks', 'remarks'), ('Discount', 'discount'), ('Discount Percentage', 'discount_percentage'),
     ('Price Ranges', 'price_ranges')))

BARCODE_OPTIONS = {sku_code_const: 'sku_code', 'Embedded SKU Code in Serial': 'sku_serial', ean_number_const: 'sku_ean',
                   'SKU PACK': 'sku_pack'}

# Configurtions Mapping
REMAINDER_MAIL_ALERTS = OrderedDict((('po_remainder', 'PO Remainder'),))

batch_constant, exp_date_const = 'Batch No', 'Expiry Date'
RECEIVE_PO_MANDATORY_FIELDS = OrderedDict((
    ('Invoice Price', 'buy_price'),
    ('Rejected Quantity', 'rejected_quantity'),
    ('SKU Weight', 'weight'),
    ('GRN File Upload', 'grn_file_upload'),
    (put_zone_const, 'put_zone'),
    ('Recommended Zone', 'recommended_zone'),
    (sku_ref_const, 'sku_reference'),
    ('No of Containers', 'no_of_containers')
))

SR_GRN_MANDATORY_FIELDS = OrderedDict((
    (put_zone_const, 'put_zone'),
))

ADDITIONAL_BATCH_ATTRIBUTES = OrderedDict((
    (batch_constant, 'batch_number'),
    (manufactured_date_const, 'manufactured_date'),
    (exp_date_const, 'expiry_date'),
    ('MRP', 'mrp'),
    ('Vendor Batch No', 'vendor_batch_number'),
    ('Retest Date', 'retest_date'),
    ('Re Evaluation Date', 'reevaluation_date'),
    ('Best Before Date', 'best_before_date'),
    (inspection_lot_number, 'inspection_lot_number'),
    (batch_reference_const, 'batch_reference'),
    ('Weight', 'weight')

))

DISPLAY_BATCH_ATTRIBUTES = ADDITIONAL_BATCH_ATTRIBUTES

#Attributes to be shown in picking screen in mobile
PICKING_SCREEN_ATTRIBUTES = OrderedDict((
    ('Batch Number', 'batch_display_key'),
    (exp_date_const, 'expiry_date'),
    ('MRP', 'mrp'),
    ('Brand', 'sku_brand'),
    ('Pick Quantity in Base UOM', 'picked_base_uom_qty'),
    ('UOM', 'sku_measurement_type'),
    ('Zone', 'zone'),
    ('Sub Zone', 'sub_zone'),
    ('Location', 'location'),
    ('Weight', 'weight'),
    ('MRP from Order','mrp_from_order'),
    ('Sales Price from Order','sale_price_from_order')
))

PRE_SELECTED_PICKING_SCREEN_ATTRIBUTES = ['batch_display_key','expiry_date',
                                          'mrp','sku_brand',
                                          'picked_base_uom_qty',
                                          'sku_measurement_type',
                                          'zone','sub_zone',
                                          'location','weight']

#Attributes to be shown in picking summary screen in mobile
PICKING_SUMMARY_ATTRIBUTES = OrderedDict((
    ('Order Reference', 'order_reference'),
    ('Order Type', 'order_type'),
    ('Customer Name', 'customer_name'),
))

NON_MANDATORY_BATCH_ATTRIBUTES = ADDITIONAL_BATCH_ATTRIBUTES

PO_HEADER_FIELDS = OrderedDict((
    ('Free Quantity', 'free_quantity'),
))

PO_MANDATORY_FIELDS = PO_HEADER_FIELDS

ASN_FIELDS = OrderedDict((
    ('GRN Reference', 'grn_reference'),
    ('LR Number', 'lr_number'),
    ('Carrier Name', 'carrier_name'),
    ('Document Number', 'invoice_number'),
    ('Document Date', 'invoice_date'),
    ('TCS', 'tcs'),
    ('Discount Amount', 'discount_amount'),
    ('Additional Cost', 'additional_cost'),
    ('Remarks', 'remarks'),
    ('Upload File', 'upload_file'),
    ('Total ASN Value excl. tax', 'value_without_tax'),
    ('Total ASN Value incl. tax', 'value_with_tax'),
    ('Invoice Free Qty', 'invoice_free_quantity'),
    ('Supplier Name', 'supplier_name'),
    ('PO Line Ref', 'po_line_reference'),
    ('P UOM', 'unit'),
    ('UOM', 'base_uom'),
    ('Pack', 'pack_uom'),
    ('Tax', 'tax_percent'),
    ('Cess Tax', 'cess_tax'),
    ('Total Tax', 'total_tax'),
    ('Scheme Discount', 'scheduled_percent'),
    ('Scheme Amount', 'scheduled_amount'),
    ('Cash Discount', 'cash_discount_percent'),
    ('Cash Amount', 'cash_discount_amount'),
    ('Tax Amount', 'tax_amount'),
    ('Margin', 'margin'),
    ('Price', 'price'),
    ('CN Amount', 'cn_amount'),
    ('GateKeeper Margin', 'gatekeeper_margin'),
    ('Supplier Invoice Tax%', 'supplier_invoice_tax'),
    ('Document Value', 'invoice_value')
    ))

ASN_MANDATORY_FIELDS = ASN_FIELDS

ORDER_HOLD_OPTIONS = OrderedDict((
    ('Manual Order Hold', 'manual_order_hold'),
    ('Automated Order Hold', 'automated_order_hold'),
))

CYCLE_COUNT_TYPES = OrderedDict((
    ('Short Cycle Count', 'short_cycle_count'),
    ('Scheduled cycle count', 'scheduled_cycle_count'),
    ('Unscheduled cycle count', 'unscheduled_cycle_count'),
    ('Audit Cycle Count', 'audit_cycle_count'),
    ('Adhoc Cycle count', 'adhoc_cycle_count'),
))


INVOICE_TYPES = OrderedDict((('3 Inch', '3_inch'), ('A4', 'a4')))
PICKLIST_PRIORITY_TYPES = OrderedDict((
    ('Order Creation Time', 'od_creation_time'),
    ('Picklist Generation', 'picklist_generation'),
    ('Expected Delivery Time', 'exp_delivery'),
    ('ETA', 'eta'),
    ('Route ID', 'trip_id'),
    ('Manual Picker Assignment', 'manual_picker_assignment'),
))
TYPES_OF_SORTING = OrderedDict((('Order','order'),('Customer','customer')))
PICKING_STRATEGY_DICT = OrderedDict((('Regular/Bulk Picking','regular_picking'),('Pack while pick','pack_while_pick'), ('Cluster Picking', 'cluster_picking'), ('Pick & Pass', 'pick_and_pass'), ('Pick and Sort','pick_and_sort')))
INCO_TERMS = OrderedDict(( ('EXW', 'EXW'), ('DAP', 'DAP'), ('DDP', 'DDP'), ('CIP', 'CIP'),
                           ('DPU', 'DPU'), ('FCA', 'FCA'), ('CPT', 'CPT'), ('FAS', 'FAS'),
                           ('FOB', 'FOB'), ('CFR', 'CFR'), ('CIF', 'CIF') ))
AUTO_CONFIRM_PO_OPTIONS = OrderedDict(( ('PO Approval', 'po_approval'), ('Min Max', 'min_max_planning') ))

REPLENISHMENT_OPTIONS = OrderedDict(( ('BA to SA', 'ba_to_sa'), ('NTE', 'nte'), ('EXPIRED', 'expired') ))

RESTRICTED_SKU_HEADERS = [wms_code_const, put_zone_const, threshold_qty_const, 'Load Unit Handling(Options: Enable, Disable)']

USER_SKU_EXCEL = {'warehouse_user': SKU_HEADERS, 'marketplace_user': SKU_HEADERS,
                  'customer': SKU_HEADERS, 'WH': RESTRICTED_SKU_HEADERS, 'DIST': RESTRICTED_SKU_HEADERS}

ATTRIBUTE_PRICING_EXCEL_MAPPING = OrderedDict((
    ('attribute_type', 0),
    ('attribute_value', 1),
    ('price_type', 2),
    ('min_unit_range', 3), ('max_unit_range', 4),
    ('price', 5), ('discount', 6)
))

SKU_DEF_EXCEL = OrderedDict((('wms_code', 0), ('sku_desc', 1), ('sku_group', 2), ('sku_type', 3),
                             ('sku_category', 4), ('pick_group', 5), ('sku_class', 6), ('sku_brand', 7),
                             ('style_name', 8),
                             ('sku_size', 9), ('size_type', 10), ('zone_id', 11), ('cost_price', 12), ('price', 13),
                             ('mrp', 14),('image_url', 15), ('threshold_quantity', 16),
                             ('max_norm_quantity', 17),
                             ('measurement_type', 18),
                             ('color', 19),('ean_number', 20),
                             ('hsn_code', 21),
                             ('sub_category', 22),('combo_flag', 23),
                             ('block_options', 24),
                             ('batch_based', 25),('status', 26),('length', 27), ('breadth', 28),
                             ('height', 29), ('weight', 30), ('product_type', 31), ('shelf_life', 32), ('customer_shelf_life', 33), 
                             ('sku_reference', 34),('scan_picking',35), ('minimum_shelf_life',36), ('make_or_buy',37), ('enable_serial_based',38),
                             ('qc_eligible',39), ('receipt_tolerance', 40), ('is_barcode_required', 41)
                             ))

USER_SKU_EXCEL_MAPPING = {'warehouse_user': SKU_DEF_EXCEL, 'marketplace_user': SKU_DEF_EXCEL,
                          'customer': SKU_DEF_EXCEL}

LOAD_UNIT_HANDLE_DICT = {'enable': 'pallet', 'disable': 'unit'}

PRICING_MASTER_ATTRIBUTE_HEADERS = ['Attribute Name', attribute_value_const, 'Selling Price Type', 'Min Unit Range',
                                    'Max Unit Range', 'Price', 'Discount']

EASYOPS_STOCK_HEADERS = OrderedDict([('Product Name', 'sku_desc'), ('Sku', 'wms_code'), ('Vendor Sku', 'wms_code'),
                                     ('Stock', 'stock_count'), ('Purchase Price', 'purchase_price')])
CENTRAL_ORDER_EXCEL = OrderedDict((
    ('original_order_id', 0), ('batch_number', 1), ('batch_date', 2),
    ('branch_id', 3), ('branch_name', 4), ('loan_proposal_id', 5),
    ('loan_proposal_code', 6), ('client_code', 7), ('client_id', 8),
    ('customer_name', 9), ('address1', 10),
    ('address2', 11), ('landmark', 12), ('village', 13), ('district', 14),
    ('state', 15), ('pincode', 16), ('mobile_no', 17), ('alternative_mobile_no', 18),
    ('sku_code', 19), ('model', 20), ('unit_price', 21),
    ('cgst', 22), ('sgst', 23), ('igst', 24),
    ('total_price', 25), ('location', 26)))

LOAD_UNIT_HANDLE_DICT = {'enable': 'pallet', 'disable': 'unit'}

MIX_SKU_ATTRIBUTES = {'no_mix': 'No Mix', 'mix_group': 'Mix within Group'}

PENDING_PUTAWAY_HEADERS = OrderedDict([('GRN Number', 'grn_number'), ('Receipt Type', 'po_type'), 
        (receipt_date_const, 'receipt_date'),
        (po_number_const, 'po_number'),
        (po_reference_const, 'po_reference'),
        (sku_code_const, 'sku_code'), (sku_desc_const, 'sku_desc'),(sku_ref_const, 'sku_reference'),('SKU Brand', 'sku_brand'),
        ('Size', 'sku_size'), ('UOM', 'uom'),
        (batch_constant, 'batch_no'), (sku_category_const, 'sku_category'), ('Sub Category', 'sub_category'),
        ('SKU Weight', 'sku_weight'),
        ('MRP', 'mrp'), ('Quantity', 'quantity'),
        ('Manufactured Date', 'manufactured_date'), (exp_date_const, 'expiry_date'), 
        (supplier_Id_const, 'supplier_id'), (supplier_name_const, 'supplier_name'),
        ('Putaway Zone', 'putaway_zone'), ('Putaway Location', 'putaway_location'),
        (serial_no_const, 'serial_number')])

BLIND_GRN_VALIDATIONS = OrderedDict((
    ('Validate Qty', 'validate_quantity'),
    ))

PICKLIST_LABEL_CONTENT_OPTIONS = OrderedDict((
    ("Order Line Reference", "order_line_reference"),
    ("Picklist ID", "picklist_id"),
))

CONFIG_DEF_DICT = {'receive_options': dict(RECEIVE_OPTIONS),
                   'mail_options': MAIL_REPORTS_DATA,
                   'mail_reports': MAIL_REPORTS, 'style_detail_headers': STYLE_DETAIL_HEADERS,
                   'picklist_options': PICKLIST_OPTIONS,
                   'order_headers': ORDER_HEADERS_d, 'barcode_generate_options': BARCODE_OPTIONS,
                   'rem_mail_alerts': REMAINDER_MAIL_ALERTS,
                   'receive_po_mandatory_fields': RECEIVE_PO_MANDATORY_FIELDS,
                   'receive_po_editable_fields': RECEIVE_PO_MANDATORY_FIELDS,
                   'sr_grn_mandatory_fields': SR_GRN_MANDATORY_FIELDS,
                   'sr_grn_editable_fields': SR_GRN_MANDATORY_FIELDS,
                   'invoice_options': dict(INVOICE_TYPES),
                   'picklist_priority_options': dict(PICKLIST_PRIORITY_TYPES),
                   'sort_by_options': dict(TYPES_OF_SORTING),
                   'picking_strategy':dict(PICKING_STRATEGY_DICT), 
                   'inco_terms': INCO_TERMS,
                   'auto_confirm_po_options': AUTO_CONFIRM_PO_OPTIONS,
                   'replenishment_options': REPLENISHMENT_OPTIONS,
                   'additional_batch_attributes': ADDITIONAL_BATCH_ATTRIBUTES,
                   'display_batch_attributes': DISPLAY_BATCH_ATTRIBUTES,
                   'picking_screen_attributes' : PICKING_SCREEN_ATTRIBUTES,
                   'picking_summary_attributes': PICKING_SUMMARY_ATTRIBUTES,
                   'non_mandatory_batch_attributes': NON_MANDATORY_BATCH_ATTRIBUTES,
                   'po_header_fields': PO_HEADER_FIELDS,
                   'po_mandatory_fields': PO_MANDATORY_FIELDS,
                   'asn_fields': ASN_FIELDS,
                   'asn_mandatory_fields': ASN_MANDATORY_FIELDS,
                   'order_hold_options': ORDER_HOLD_OPTIONS,
                   'blind_grn_validations': BLIND_GRN_VALIDATIONS,
                   'pre_selected_picking_screen_attributes': PRE_SELECTED_PICKING_SCREEN_ATTRIBUTES,
                   'cycle_count_types': CYCLE_COUNT_TYPES,
                   'picklist_label_content_options': PICKLIST_LABEL_CONTENT_OPTIONS,
                   }

CONFIG_SWITCHES_DICT = {'use_imei': 'use_imei', 'tally_config': 'tally_config', 'show_mrp': 'show_mrp',
                        'stock_display_warehouse': 'stock_display_warehouse', 'seller_margin': 'seller_margin',
                        'hsn_summary': 'hsn_summary',
                        'gst_summary': 'gst_summary',
                        'send_message': 'send_message', 'order_management': 'order_manage', 'back_order': 'back_order',
                        'display_customer_sku': 'display_customer_sku', 'pallet_switch': 'pallet_switch',
                        'receive_process': 'receive_process',
                        'no_stock_switch': 'no_stock_switch', 'show_disc_invoice': 'show_disc_invoice',
                        'production_switch': 'production_switch', 'sku_sync': 'sku_sync',
                        'display_remarks_mail': 'display_remarks_mail',
                        'async_picklist_confirmation': 'async_picklist_confirmation',
                        'stock_sync': 'stock_sync', 'float_switch': 'float_switch',
                        'automate_invoice': 'automate_invoice',
                        'show_distributer': 'show_distributer',
                        'async_picklist_generation': 'async_picklist_generation',
                        'picking_location_copy': 'picking_location_copy',
                        'async_order_creation': 'async_order_creation',
                        'show_order_type': 'show_order_type',
                        'pos_switch': 'pos_switch', 'create_seller_order': 'create_seller_order',
                        'marketplace_model': 'marketplace_model', 'decimal_limit': 'decimal_limit', 'print_no_rows': 'print_no_rows',
                        'batch_switch': 'batch_switch', 'decimal_limit_price': 'decimal_limit_price',
                        'view_order_status': 'view_order_status', 'label_generation': 'label_generation',
                        'grn_scan_option': 'grn_scan_option',
                        'show_imei_invoice': 'show_imei_invoice', 'style_headers': 'style_headers',
                        'picklist_sort_by': 'picklist_sort_by',
                        'bag_label': 'bag_label',
                        'barcode_generate_opt': 'barcode_generate_opt', 'online_percentage': 'online_percentage','idle_timeout': 'idle_timeout',
                        'mail_alerts': 'mail_alerts',
                        'detailed_invoice': 'detailed_invoice', 'invoice_titles': 'invoice_titles',
                        'show_image': 'show_image', 'repeat_po': 'repeat_po',
                        'auto_generate_backorder': 'auto_generate_backorder',
                        'auto_po_switch': 'auto_po_switch',
                        'loc_serial_mapping_switch': 'loc_serial_mapping_switch',
                        'internal_mails': 'Internal Emails', 'increment_invoice': 'increment_invoice',
                        'create_shipment_type': 'create_shipment_type',
                        'auto_allocate_stock': 'auto_allocate_stock', 'priceband_sync': 'priceband_sync',
                        'generic_wh_level': 'generic_wh_level',
                        'create_order_po': 'create_order_po', 'calculate_customer_price': 'calculate_customer_price',
                        'carton_label':'carton_label',
                        'shipment_sku_scan': 'shipment_sku_scan', 'disable_brands_view': 'disable_brands_view',
                        'sellable_segregation': 'sellable_segregation', 'display_styles_price': 'display_styles_price',
                        'display_sku_cust_mapping': 'display_sku_cust_mapping',
                        'disable_categories_view': 'disable_categories_view',
                        'is_portal_lite': 'is_portal_lite',
                        'show_purchase_history': 'show_purchase_history',
                        'auto_raise_stock_transfer': 'auto_raise_stock_transfer',
                        'inbound_supplier_invoice': 'inbound_supplier_invoice', 'customer_dc': 'customer_dc',
                        'central_order_mgmt': 'central_order_mgmt',
                        'invoice_based_payment_tracker': 'invoice_based_payment_tracker',
                        'inbound_supplier_invoice': 'inbound_supplier_invoice', 'customer_dc': 'customer_dc',
                        'receive_po_invoice_check': 'receive_po_invoice_check',
                        'mark_as_delivered': 'mark_as_delivered',
                        'order_exceed_stock': 'order_exceed_stock',
                        'sku_pack_config': 'sku_pack_config',
                        'location_types' : 'location_types',
                        'central_order_reassigning': 'central_order_reassigning',
                        'po_sub_user_prefix': 'po_sub_user_prefix',
                        'combo_allocate_stock': 'combo_allocate_stock',
                        'dispatch_qc_check': 'dispatch_qc_check',
                        'unique_mrp_putaway': 'unique_mrp_putaway',
                        'sku_less_than_threshold': 'sku_less_than_threshold',
                        'block_expired_batches_picklist': 'block_expired_batches_picklist',
                        'generate_delivery_challan_before_pullConfiramation': 'generate_delivery_challan_before_pullConfiramation',
                        'non_transacted_skus': 'non_transacted_skus',
                        'allow_rejected_serials': 'allow_rejected_serials',
                        'update_mrp_on_grn': 'update_mrp_on_grn',
                        'mandate_sku_supplier': 'mandate_sku_supplier',
                        'brand_categorization': 'brand_categorization',
                        'purchase_order_preview': 'purchase_order_preview',
                        'picklist_sort_by_sku_sequence': 'picklist_sort_by_sku_sequence',
                        'stop_default_tax': 'stop_default_tax',
                        'supplier_mapping': 'supplier_mapping',
                        'show_mrp_grn': 'show_mrp_grn',
                        'display_dc_invoice': 'display_dc_invoice',
                        'display_order_reference': 'display_order_reference',
                        'enable_pending_approval_pos': 'enable_pending_approval_pos',
                        'enable_pending_approval_prs': 'enable_pending_approval_prs',
                        'mandate_invoice_number': 'mandate_invoice_number',
                        'display_parts_allocation': 'display_parts_allocation',
                        'auto_generate_receive_qty': 'auto_generate_receive_qty',
                        'sku_packs_invoice': 'sku_packs_invoice',
                        'mandate_ewaybill_number': 'mandate_ewaybill_number',
                        'lpn_level_drop_in_picking': 'lpn_level_drop_in_picking',
                        'po_or_pr_edit_permission_approver': 'po_or_pr_edit_permission_approver',
                        'stock_auto_receive': 'stock_auto_receive',
                        'attributes_sync': 'attributes_sync',
                        'tax_master_sync': 'tax_master_sync',
                        'supplier_sync': 'supplier_sync',
                        'enable_margin_price_check': 'enable_margin_price_check',
                        'receive_po_inv_value_qty_check': 'receive_po_inv_value_qty_check',
                        'central_admin_level_po': 'central_admin_level_po',
                        'sku_attribute_grouping_key': 'sku_attribute_grouping_key',
                        'auto_putaway_grn': 'auto_putaway_grn',
                        'eom_consumption_configuration_plant': 'eom_consumption_configuration_plant',
                        'location_sku_mapping': 'location_sku_mapping',
                        'slotted_order': 'slotted_order',
                        'short_close_order': 'short_close_order',
                        'alternative_location_for_partial_pick': 'alternative_location_for_partial_pick',
                        'allow_reserved_in_move_inventory': 'allow_reserved_in_move_inventory',
                        'create_cycle_count_for_short_close': 'create_cycle_count_for_short_close',
                        'mobile_version_number': 'mobile_version_number',
                        'sourcesku_sync':'sourcesku_sync',
                        'membership_discount_value': 'membership_discount_value',
                        'return_po_qty': 'return_po_qty',
                        'einvoice': 'einvoice',
                        'trigger_invoice_callback' : 'trigger_invoice_callback',
                        'stock_allocate': 'stock_allocate',
                        'packing_switch': 'packing_switch',
                        'bulk_picking':'bulk_picking',
                        'sorting':'sorting',
                        'staging_area': 'staging_area',
                        'update_order_check': 'update_order_check',
                        'stock_supplier_id': 'stock_supplier_id',
                        'invoice_type' : 'invoice_type',
                        'inbound_packing': 'inbound_packing',
                        'asn_packing': 'asn_packing',
                        'draft_asn': 'draft_asn',
                        'multi_session': 'multi_session',
                        'enable_dispense': 'enable_dispense',
                        'ignore_packing_instock': 'ignore_packing_instock',
                        'cancel_po': 'cancel_po',
                        'cancel_picklist_packlist': 'cancel_picklist_packlist',
                        'show_picklist_data' : 'show_picklist_data',
                        'export_invoice' : 'export_invoice',
                        'bin_picking': 'bin_picking',
                        'scan_mandatory_while_picking' : 'scan_mandatory_while_picking',
                        'scan_first_sku_mandatory' : 'scan_first_sku_mandatory',
                        'restrict_location_to_one_sku': 'restrict_location_to_one_sku',
                        'expense_item_putaway': 'expense_item_putaway',
                        'enable_fast_picking': 'enable_fast_picking',
                        'sku_pack_create_order': 'sku_pack_create_order',
                        'merge_picking': 'merge_picking',
                        'apparel_ship_label': 'apparel_ship_label',
                        'mrp_based_picking': 'mrp_based_picking',
                        'full_carton_picking': 'full_carton_picking',
                        'same_supplier_invoice_check': 'same_supplier_invoice_check',
                        'picklist_priority':'picklist_priority',
                        'additional_cost_in_wac':'additional_cost_in_wac',
                        'spoc_warehouse': 'spoc_warehouse',
                        'sale_order_types': 'sale_order_types',
                        'issuance_dept_types':'issuance_dept_types',
                        'sort_by': 'sort_by',
                        'scan_sku_mandatory':'scan_sku_mandatory',
                        'mandate_first_sku_scan':'mandate_first_sku_scan',
                        'show_mrp_raise_po': 'show_mrp_raise_po',
                        'prefill_batch_number': 'prefill_batch_number',
                        'auto_grn_for_salesreturn' : 'auto_grn_for_salesreturn',
                        'nodocument_salesreturn' : 'nodocument_salesreturn',
                        'auto_credit_note' : 'auto_credit_note',
                        'split_by_pick_group':'split_by_pick_group',
                        'free_qty_in_grn': 'free_qty_in_grn',
                        'outbound_staging_area': 'outbound_staging_area',
                        'auto_shipment' : 'auto_shipment',                        
                        'min_max_planning': 'min_max_planning',
                        'back_to_back_po': 'back_to_back_po',
                        'stock_ledger': 'stock_ledger',
                        'enable_inventory_approval': 'enable_inventory_approval',
                        'enable_empty_location_cycle_count' : 'enable_empty_location_cycle_count',
                        'auto_trigger_jo': 'auto_trigger_jo',
                        'gate_management': 'gate_management',
                        'location_based_move_inventory': 'location_based_move_inventory',
                        'lpn_movement': 'lpn_movement',
                        'drop_multiple_lpns_putaway': 'drop_multiple_lpns_putaway',
                        'prefill_suggested_location_in_putaway': 'prefill_suggested_location_in_putaway',
                        'combo_details_in_inventory_callback': 'combo_details_in_inventory_callback',
                        'schedule_cycle_past_picking': 'schedule_cycle_past_picking',
                        'schedule_cycle_past_putaway': 'schedule_cycle_past_putaway',
                        'reuse_carton':'reuse_carton',
                        'release_carton': 'release_carton',
                        'inbound_staging_lanes' : 'inbound_staging_lanes',
                        'create_cycle_count_for_short_close_batosa': 'create_cycle_count_for_short_close_batosa',
                        'order_wise_inv':'order_wise_inv',
                        'invoice_price_from_pricing_master': 'invoice_price_from_pricing_master',
                        'inbound_scan_sku_options': 'inbound_scan_sku_options',
                        'jo_batch_level_picking': 'jo_batch_level_picking',
                        'subject_to_retest': 'subject_to_retest',
                        'block_min_shelf_life_stock' : 'block_min_shelf_life_stock',
                        'maximum_takeoff_weight': 'maximum_takeoff_weight',
                        'picklist_tolerance': 'picklist_tolerance',
                        'restrict_stock_creation': 'restrict_stock_creation',
                        'picklist_tolerance_type': 'picklist_tolerance_type',
                        'dispensing_short_close': 'dispensing_short_close',
                        'rtv_for_blocked_stock': 'rtv_for_blocked_stock',
                        'complete_batch_mapping': 'complete_batch_mapping',
                        'restrict_movement_in_wip': 'restrict_movement_in_wip',
                        'dispensing_tolerance_enabled': 'dispensing_tolerance_enabled',
                        'zone_mandatory_for_picklist_generation': 'zone_mandatory_for_picklist_generation',
                        'consumble_quantity_while_grn': 'consumble_quantity_while_grn',
                        'receive_higher_fg_quantity': 'receive_higher_fg_quantity',
                        'enable_weighing': 'enable_weighing',
                        'enable_purchase_uom': 'enable_purchase_uom',
                        'pack_uom_based_lpn_split': 'pack_uom_based_lpn_split',
                        'enable_sales_uom': 'enable_sales_uom',
                        'scan_carton_in_invoice': 'scan_carton_in_invoice',
                        'mobile_invoice_type': 'mobile_invoice_type',
                        'packing_data_in_packing_screen': 'packing_data_in_packing_screen',
                        'packing_in_progress_data_in_packing_screen': 'packing_in_progress_data_in_packing_screen',
                        'order_reference_as_lpn_number': 'order_reference_as_lpn_number',
                        'picklist_strategies': 'picklist_strategies',
                        'material_return_after_jogrn' : 'material_return_after_jogrn',
                        'consume_extra_stock_while_rm_consumption': 'consume_extra_stock_while_rm_consumption',
                        'location_scan_mandatory' : 'location_scan_mandatory',
                        'min_max_po_type': 'min_max_po_type',
                        'blind_grn': 'blind_grn',
                        'lpn_putaway_execution' : 'lpn_putaway_execution',
                        'show_grn_details_in_putaway' : 'show_grn_details_in_putaway',
                        'bulk_putaway' : 'bulk_putaway',
                        'lpn_restriction' : 'lpn_restriction',
                        'invoice_level' : 'invoice_level',
                        'prefill_qty_in_picking': 'prefill_qty_in_picking',
                        'stock_selection_strategy' : 'stock_selection_strategy',
                        'grn_rejected_docs_mandatory' : 'grn_rejected_docs_mandatory',
                        'restrict_putaway_override': 'restrict_putaway_override',
                        'allow_batch_edit_in_asn_to_grn': 'allow_batch_edit_in_asn_to_grn',
                        'allow_unitprice_gt_mrp' : 'allow_unitprice_gt_mrp',
                        'allow_future_manufactured_dates' : 'allow_future_manufactured_dates',
                        'allow_past_expiry_dates' : 'allow_past_expiry_dates',
                        'enable_batch_key' : 'enable_batch_key',
                        'auto_po_cancellation': 'auto_po_cancellation',
                        'hide_asn_rejected_accepted_qty': 'hide_asn_rejected_accepted_qty',
                        'asn_receive_qty_as_invoiced_qty': 'asn_receive_qty_as_invoiced_qty',
                        'lpn_scan_in_picking':'lpn_scan_in_picking',
                        'validate_unique_id_in_packing': 'validate_unique_id_in_packing',
                        'remove_button_in_packing': 'remove_button_in_packing',
                        'scan_options_in_packing': 'scan_options_in_packing',
                        'task_assignment_priority': 'task_assignment_priority',
                        'allow_ba_to_sa_partial_picklist': 'allow_ba_to_sa_partial_picklist',
                        'ba_to_sa_block_expired_batches_picklist': 'ba_to_sa_block_expired_batches_picklist',
                        'file_mandatory_for_reject_qty': 'file_mandatory_for_reject_qty',
                        'scandit_license_key': 'scandit_license_key',
                        'auto_pick_jo': 'auto_pick_jo',
                        'lpn_wise_invoice_preview': 'lpn_wise_invoice_preview',
                        'price_application': 'price_application',
                        'order_expiration_date': 'order_expiration_date',
                        'auto_cancel_open_expired_orders': 'auto_cancel_open_expired_orders',
                        'subzone_mapping' : 'subzone_mapping',
                        'asn_approval': 'asn_approval',
                        'print_saved_invoice': 'print_saved_invoice',
                        'single_scan_location' : 'single_scan_location',
                        'restrict_override_drop_location' : 'restrict_override_drop_location',
                        'create_cycle_count_for_short_close_joborder' : 'create_cycle_count_for_short_close_joborder',
                        'scan_fixed_number_of_sku_in_picking':'scan_fixed_number_of_sku_in_picking',
                        'enable_route_master': 'enable_route_master',
                        'update_lpn_details':'update_lpn_details',
                        'lpn_based_move_inventory':'lpn_based_move_inventory',
                        'restricts_number_of_skus_in_a_lpn': 'restricts_number_of_skus_in_a_lpn',
                        'default_filter_in_invoice_and_packing': 'default_filter_in_invoice_and_packing',
                        'auto_generate_einvoice': 'auto_generate_einvoice',
                        'user_sub_zone_mapping': 'user_sub_zone_mapping',
                        'force_zone_removal': 'force_zone_removal',
                        'one_user_one_zone_restriction': 'one_user_one_zone_restriction',
                        'max_skus_count_in_packing':'max_skus_count_in_packing',
                        'auto_generate_eway_bill': 'auto_generate_eway_bill',
                        'sku_limit_for_invoice': 'sku_limit_for_invoice',
                        'allow_po_update_with_open_asn_grn': 'allow_po_update_with_open_asn_grn',
                        'auto_calculate_short_quantity' : 'auto_calculate_short_quantity',
                        'allow_po_update_with_open_asn_grn': 'allow_po_update_with_open_asn_grn',
                        'enable_inbound_staging_lanes': 'enable_inbound_staging_lanes',
                        'max_skus_count_in_packing':'max_skus_count_in_packing',
                        'consolidate_packing': 'consolidate_packing',
                        'dynamic_bin_mapping_level': 'dynamic_bin_mapping_level',
                        'dynamic_replenishment_mapping_level': 'dynamic_replenishment_mapping_level',
                        'auto_generate_consolidated_eway_bill': 'auto_generate_consolidated_eway_bill',
                        'move_inventory_task_count': 'move_inventory_task_count',
                        'restrict_invoice_at_picking': 'restrict_invoice_at_picking',
                        'enable_invoice_value_in_asn': 'enable_invoice_value_in_asn',
                        'enable_invoice_value_in_grn': 'enable_invoice_value_in_grn',
                        'batch_attr_prefill_date': 'batch_attr_prefill_date',
                        'override_picklist_priority': 'override_picklist_priority',
                        'grn_tax_source': 'grn_tax_source',
                        'grn_trigger_count': 'grn_trigger_count',
                        'close_manifest': 'close_manifest',
                        'allow_close_manifest': 'allow_close_manifest',
                        'save_lpn_details_in_invoice':'save_lpn_details_in_invoice',
                        'mandate_sku_scan_at_picking': 'mandate_sku_scan_at_picking',
                        'cycle_time_in_picking' : 'cycle_time_in_picking',
                        'location_single_scan_in_picking': 'location_single_scan_in_picking',
                        'unique_id_scan_in_asn': 'unique_id_scan_in_asn',
                        'enable_order_type_selection_in_mobile': 'enable_order_type_selection_in_mobile',
                        'eff_rate_in_rtv': 'eff_rate_in_rtv',
                        'enable_control_tower': 'enable_control_tower',
                        'incremental_batch_prefix': 'incremental_batch_prefix',
                        'show_create_manifest': 'show_create_manifest',
                        'manifest_filters': 'manifest_filters',
                        'disable_location_scan': 'disable_location_scan',
                        'all_batch_cycle_count_on_short_pick': 'all_batch_cycle_count_on_short_pick',
                        'show_inventory_in_cycle_count': 'show_inventory_in_cycle_count',
                        'allow_skip_cycle_count': 'allow_skip_cycle_count',
                        'mandate_cycle_count_assignment_by_zone': 'mandate_cycle_count_assignment_by_zone',
                        'mandate_tripid_for_picklist' : 'mandate_tripid_for_picklist',
                        'invoice_split_on_invoice_group':'invoice_split_on_invoice_group',
                        'staging_location_routing': 'staging_location_routing',
                        'show_summary_in_picking': 'show_summary_in_picking',
                        'rapid_picklist_confirmation': 'rapid_picklist_confirmation',
                        'connect_electron': 'connect_electron',
                        'allowed_special_characters': 'allowed_special_characters',
                        'MOBILE INVOICE': 'MOBILE INVOICE',
                        'CREDIT NOTE': 'CREDIT NOTE',
                        'sr_grn_packing': 'sr_grn_packing',
                        'hold_grn_po_types': 'hold_grn_po_types',
                        'inspection_lot_inv_hold_after_putaway': 'inspection_lot_inv_hold_after_putaway',
                        'customer_level_packing': 'customer_level_packing',
                        'material_request': 'material_request',
                        'restrict_expired_orders' : 'restrict_expired_orders',
                        'skip_location_suggestions_in_lpn_drop': 'skip_location_suggestions_in_lpn_drop',
                        'show_inventory_in_picking': 'show_inventory_in_picking',
                        'cancel_task_in_picking': 'cancel_task_in_picking',
                        'manifest_creation_grouping': 'manifest_creation_grouping',
                        'gate_pass_validation_during_grn' : 'gate_pass_validation_during_grn',
                        'margin_value_validation': 'margin_value_validation',
                        'direct_dispatch_of_orders': 'direct_dispatch_of_orders',
                        'gate_pass_file_upload': 'gate_pass_file_upload',
                        'invoice_callback_at_order_level': 'invoice_callback_at_order_level',
                        'allow_grn_for_rtv_qty': 'allow_grn_for_rtv_qty',
                        'update_batch_details_config' : 'update_batch_details_config',
                        'intermediate_drop_in_pick_and_pass': 'intermediate_drop_in_pick_and_pass',
                        'allocation_options' : 'allocation_options',
                        'allow_jo_partial_picklist': 'allow_jo_partial_picklist',
                        'sku_serialisation': 'sku_serialisation',
                        'location_capacity_calculation': 'location_capacity_calculation',
                        'enable_seller': 'enable_seller',
                        'invoice_to_picklist_quantity':'invoice_to_picklist_quantity',
                        'po_type_level_packing' : 'po_type_level_packing',
                        'new_dashboard_apis':'new_dashboard_apis',
                        'reason_for_cycle_count': 'reason_for_cycle_count',
                        'hide_grn_fields':'hide_grn_fields',
                        'sr_grn_lpn_restriction': 'sr_grn_lpn_restriction',
                        'seller_usage_calculation': 'seller_usage_calculation',
                        'serial_number_mapping': 'serial_number_mapping',
                        'auto_drop_after_picking': 'auto_drop_after_picking',
                        'enable_standard_dashboards': 'enable_standard_dashboards',
                        'validate_doc_num_per_fy': 'validate_doc_num_per_fy',
                        'dock_scheduling': 'dock_scheduling',
                        'dock_scheduling_handling_unit': 'dock_scheduling_handling_unit',
                        'mandate_serial_scan_at_invoice': 'mandate_serial_scan_at_invoice',
                        'full_lpn_invoice_at_order_fulfill': 'full_lpn_invoice_at_order_fulfill',
                        'mandate_allocation_for_picklist_generation': 'mandate_allocation_for_picklist_generation',
                        'minimum_doc_time': 'minimum_doc_time', 'check_digit_limit': 'check_digit_limit',
                        'image_expansion': 'image_expansion',
                        'sales_return_check_in': 'sales_return_check_in',
                        'merge_stock_ids_in_invoice': 'merge_stock_ids_in_invoice',
                        'validate_customer_po_number' : 'validate_customer_po_number',
                        'max_days_for_material_transactions': 'max_days_for_material_transactions',
                        'enable_invoice_value_tolerance': 'enable_invoice_value_tolerance',
                        'manual_stock_selection_for_picklist' : 'manual_stock_selection_for_picklist',
                        "sorting_for_external_system": "sorting_for_external_system",
                        'picklist_label_content': 'picklist_label_content',
                        'allow_grn_line_cancel': 'allow_grn_line_cancel',
                        'asn_grn_from_po_header': 'asn_grn_from_po_header',
                        'hide_asn_fields': 'hide_asn_fields',
                        'validate_order_type': 'validate_order_type',
                        'mrp_tolerance': 'mrp_tolerance',
                        'allow_partial_grn_in_asn': 'allow_partial_grn_in_asn',
                        'auto_putaway': 'auto_putaway',
                        'image_expansion': 'image_expansion',
                        'cancel_open_delivery_lines': 'cancel_open_delivery_lines',
                        'manual_wave' : 'manual_wave',
                        'zone_mandatory_for_jo_picklist_generation': 'zone_mandatory_for_jo_picklist_generation',
                        'enable_staging_locator_in_grn': 'enable_staging_locator_in_grn',
                        'source_lpn_as_destination_lpn_in_picking': 'source_lpn_as_destination_lpn_in_picking',
                        'enable_outbound_qc' : 'enable_outbound_qc',
                        'sku_quantity_in_qc' : 'sku_quantity_in_qc',
                        'enable_check_digit': 'enable_check_digit',
                        'pigeon_hole_sorting': 'pigeon_hole_sorting',
                        'invoice_preview_view': 'invoice_preview_view',
                        'allow_asn_creation_without_batch': 'allow_asn_creation_without_batch',
                        'check_digit_delimiter': 'check_digit_delimiter',
                        'lpn_type_suggestion': 'lpn_type_suggestion',
                        'task_based_move_inventory': 'task_based_move_inventory',
                        'move_inventory_approval': 'move_inventory_approval',
                        'restrict_sku_batch_mixing': 'restrict_sku_batch_mixing',
                        'update_inventory_expiry_status': 'update_inventory_expiry_status',
                        'order_approval': 'order_approval',
                        'old_putaway_suggestions': 'old_putaway_suggestions',
                        'enable_skip_task': 'enable_skip_task',
                        'update_inventory_expiry_status': 'update_inventory_expiry_status',
                        'restrict_to_single_order_invoice': 'restrict_to_single_order_invoice',
                        'putaway_class_category': 'putaway_class_category',
                        'restrict_default_location_suggestion_for_pigeon_hole_sorting': 'restrict_default_location_suggestion_for_pigeon_hole_sorting',
                        'suspend_cancel_orders': 'suspend_cancel_orders',
                        'line_level_picklist_cancellation': 'line_level_picklist_cancellation',
                        'flag_order_level_discrepancy' : 'flag_order_level_discrepancy',
                        'line_level_order_cancellation' : 'line_level_order_cancellation',
                    }

CONFIG_ORDER_TYPE_DICT = {
    'restrict_partial_picklist': 'restrict_partial_picklist',
    'auto_generate_picklist': 'auto_generate_picklist',
    'allow_partial_picklist': 'allow_partial_picklist',
    'order_all_items_picklist_generation': 'order_all_items_picklist_generation',
    'customer_shelf_life': 'customer_shelf_life',
    'restrict_partial_invoice': 'restrict_partial_invoice',
    'order_cancel_on_invoice': 'order_cancel_on_invoice',
    'auto_invoice': 'auto_invoice',
    'invoice_price_as_per_buyprice': 'invoice_price_as_per_buyprice',
    'packing_mandatory_for_invoice': 'packing_mandatory_for_invoice',
    'manual_assignment': 'manual_assignment',
    'outbound_staging_lanes' : 'outbound_staging_lanes',
    'auto_confirm_picklist': 'auto_confirm_picklist',
    'cancel_open_order_at_picklist_generation' : 'cancel_open_order_at_picklist_generation',
    'cancel_open_order_at_picklist_confirmation' : 'cancel_open_order_at_picklist_confirmation',
    'label_based_picking': 'label_based_picking',
    'max_days_for_material_transactions': 'max_days_for_material_transactions',
    'approval_order_types': 'approval_order_types',
    'auto_allocate_sale_order': 'auto_allocate_sale_order',
    'hybrid_task_assignment': 'hybrid_task_assignment',
}

SKU_GROUP_FIELDS = {'group': '', 'user': '', 'creation_date': NOW}

REPORTS_DATA = {sku_list_const: 'sku_list', location_wise_sku_const: 'location_wise_stock', receipt_summary_const: 'receipt_note',
                dispatch_summary_const: 'dispatch_summary', sku_wise_stock: 'sku_wise',
                'Shipment Report': 'shipment_report'}
SUMMARY_INTER_STATE_STATUS = {0: 'intra_state', 1: 'inter_state', '2': 'default'}
PERMISSION_IGNORE_LIST = ['session', 'webhookdata', 'swxmapping', 'userprofile', 'useraccesstokens', 'contenttype',
                          'user',
                          'permission', 'group', 'logentry', 'corsmodel', 'subzonemapping']
STAGES_FIELDS = {'stage_name': '', 'user': ''}

# Myntra Invoice Address based on username
MYNTRA_BANGALORE_ADDRESS = 'Myntra Jabong India Pvt Ltd \n Survey Numbers 231, 232 and 233, Soukya Road,\n Samethanahalli Village,\n\
                            Anugondanahalli Hobli, Hoskote Taluk,\n Bangalore;-560087 Karnataka\n GSTIN: 29AAACQ3774A2ZI'

MYNTRA_JABONG_ADDRESS = 'Jabong Marketplace\n DTDC Facility Premise No. 79/2,79/1A & 78/6 Dasanpura Village,\nDasanpura Hobli, Bangalore,\
                         North Taluk,Bangalore,Karnataka,562162\n GSTIN: 29AAACQ3774A2ZI'

MYNTRA_MUMBAI_ADDRESS = 'Myntra jabong India Pvt Ltd.\nKsquare Industrial Park, Warehouse 4\n\
                         Before Padgha Toll naka Nashik-Mumbai Highway \nNear Pushkar Mela Hotel Rahul Narkhede,\n\
                         Padgha Bhiwandi - 421101, Maharashtra\n\
                         TIN:27461499703'
MYNTRA_BULK_ADDRESS = 'MYNTRA DESIGNS PVT LTD\nKsquare Industrial Park, Warehouse 4\n\
                         Before Padgha Toll naka Nashik-Mumbai Highway \nNear Pushkar Mela Hotel Rahul Narkhede,\n\
                         Padgha-Bhiwandi\nTin# 27590747736'

JABONG_ADDRESS = 'Myntra jabong India Pvt Ltd.\nKsquare Industrial Park,\
                         Before Padgha Toll naka Nashik-Mumbai Highway, \nNear Pushkar Mela Hotel Rahul Narkhede,\n\
                         Padgha Bhiwandi - 421101, Maharashtra\n\
                         TIN:27461499703'
USER_CHANNEL_ADDRESS = {'campus_sutra:myntra': MYNTRA_BANGALORE_ADDRESS, 'adam_clothing:myntra': MYNTRA_MUMBAI_ADDRESS,
                        'adam_clothing1:myntra': MYNTRA_MUMBAI_ADDRESS,
                        'adam_clothing1:myntra:bulk': MYNTRA_BULK_ADDRESS,
                        'adam_clothing1:jabong': JABONG_ADDRESS, 'campus_sutra:jabong': MYNTRA_JABONG_ADDRESS
                        }

TOP_COMPANY_LOGO_PATHS = {'Konda_foundation': 'dr_reddy_logo.png', 'acecraft': 'acecraft.jpg'}
DECLARATIONS = {
    'default': 'We declare that this invoice shows actual price of the goods described inclusive of taxes and that all particulars are true and correct.',
    'TranceHomeLinen': 'Certify that the particulars given above are true and correct and the amount indicated represents the price actually charged and that there is no flow of additional consideration directly or indirectly.\n Subject to Banglore Jurisdication'}

SKU_NAME_FIELDS_MAPPING = OrderedDict((('Brand', 'sku_brand'), ('Category', 'sku_category')))
PO_RECEIPT_TYPES = ['Purchase Order', 'Buy & Sell', 'Hosted Warehouse']
STATUS_DICT = {1: True, 0: False}
PO_ORDER_TYPES = {'SR': 'po_receipt', 'VR': 'Vendor Receipt', 'HW': 'Hosted Warehouse', 'BS': 'Buy & Sell',
                  'SP': 'Sampling'}
BARCODE_ADDRESS_DICT = {
    'adam_clothing1': 'Adam Exports 401, 4th Floor,\n Pratiek Plazza, S.V.Road,\n Goregaon West, Mumbai - 400062.\n MADE IN INDIA',
    'scholar_clothing': 'Scholar Clothing Co. <br/> Karnataka - India', 'bcbs_retail': 'Scholar Clothing Co.',
    'bcgs_retail': 'Scholar Clothing Co.', 'SSRVM_RETAIL': 'Scholar Clothing Co.',
    'stjohns_retail': 'Scholar Clothing Co.',
    'narayana_retail': 'Scholar Clothing Co.', 'vps_retail': 'Scholar Clothing Co.',
    'christ_retail': 'Scholar Clothing Co.',
    'sindhihebbal_retail': 'Scholar Clothing Co.', 'stjosephs_retail': 'Scholar Clothing Co.'}

OPEN_ST_FIELDS = {'warehouse_id': '', 'order_quantity': 0, 'price': 0, 'sku_id': '', 'status': 1,
                  'creation_date': NOW}

PO_TEMP_JSON_DEF = {"scan_sku": "", "weight": "", "lr_number": "", "display_approval_button": "false",
                    "remainder_mail": "0", "exp_date": "", "carrier_name": "", "id": "", "unit": "",
                    "supplier_id": "", "expected_date": "", "discount_percentage": "", "buy_price": "0",
                    "cess_percent": "0", "price": "0", "sku_index": "0", "invoice_date": "",
                    "po_quantity": "0", "new_sku": "", "wms_code": "", "remarks": "", "invoice_number": "",
                    "tax_percent": "0", "invoice_value": "", "mrp": "0", "mfg_date": "", "batch_no": "",
                    "quantity": "0", "apmc_percent": "0"}

DEPARTMENT_TYPES_MAPPING = OrderedDict(
    [('ALLDE', 'All Department'), ('COVID', 'COVID-19'), ('MOLBI', 'Molecular Biology'),
     ('IMMUN', 'Immunochemistry'), ('BIOCHE', 'Bio Chemistry'),
     ('CLITRL', 'Clinical Trial'), ('CLITRP', 'Clinical Trial Project'),
     ('RE&DE', 'Research & Development'), ('MEDGE', 'Medical Genetics'),
     ('IFADE', 'IFA'), ('MOLPA', 'Molecular Pathology'), ('NACOP', 'NACO'),
     ('ACCES', 'Accession'), ('MICRO', 'Microbiology'), ('ELISA', 'Elisa'),
     ('SPCHE', 'Special Chemistry'), ('ANACH', 'Analytical Chemistry'),
     ('COAGU', 'Coagulation'), ('HEMAT', 'Hematology'), ('SEROL', 'Serology'),
     ('HISTO', 'Histopathology'), ('HLADE', 'HLA'), ('REPOR', 'Reports'),
     ('QUAAU', 'Quality Assurance'), ('CUSCA', 'Customer Care'), ('LOGIS', 'Logistics'),
     ('HOMVO', 'Home Visit'), ('COLCO', 'Collection Centers'), ('HUBDE', 'HUB'),
     ('HEALC', 'Health Checkup'), ('SCMMM', 'Supply Chain/Materials Management'),
     ('ADMIN', 'Administration'), ('HRDDE', 'Human Resources'),
     ('ACCFI', 'Account & Finance'), ('MARKE', 'Marketing'),
     ('ITTEC', 'Information Technology'), ('LEGAL', 'Legal Department'),
     ('SECRE', 'Secretrial Department'), ('SALES', 'Sales Department'),
     ('CLPAT', 'Clinical Pathology'), ('WELLN', 'Wellness'), ('IMMUNO', 'Immunoassay'),
     ('HEADW', 'Head Office - Worli'), ('MCGMP', 'MCGM - Project'), ('Tulsiani 01', 'Local ILD 1'),
     ('RADIO', 'Radiology'), ('R&DGE', 'R&D - Genetics'), ('GENET', 'Genetics'), ('LENA', 'LENA'), ('NEHA', 'NEHA')])

PRODUCT_CATEGORIES = ['Kits&Consumables', 'Services', 'Assets', 'OtherItems']
CURRENCY_CODES = [code[0] for code in CURRENCY_CHOICES]
BACKFLUSH_JO_TYPES = ['Standard JO', 'Non Standard JO','Returnable JO', 'BA_TO_SA']

WMS_FIELDS = {
    "Order Reference": "order_reference",
    "Customer ID": "customer_id",
}
MILKBASKET_BULK_ZONE = 'BULK ZONE'

SIZES_LIST = ['S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'FREE SIZE']

SUB_CATEGORIES = OrderedDict((('mens_polo', 'MENS POLO'), ('ladies_polo', 'LADIES POLO'),
                              ('round_neck', 'ROUND NECK'), ('hoodie', 'HOODIE'), ('jackets', 'JACKETS'),
                              ('henley', 'HENLEY'), ('laptop_bags', 'LAPTOP BAGS'),
                              ('gym_bags', 'GYM BAGS'), ('pant', 'PANT'),
                              ('belts', 'BELTS'), ('ear_phone', 'EAR PHONE'),
                              ('v_neck', 'V NECK'), ('polo', 'POLO'), ('chinese_collar', 'CHINESE COLLAR'),
                              ('bags', 'BAGS')
                              ))
REPLENISHMNENT_DATA = {'classification': '', 'size': '', 'max_days': 0, 'min_days': 0,
                       'creation_date': datetime.datetime.now()}

CYCLE_COUNT_FIELDS = {'cycle': '', 'sku_id': '', 'location_id': '',
                      'quantity': '', 'seen_quantity': '',
                      'status': 1, 'creation_date': '', 'updation_date': ''}

SKU_PACK_DATA = {'sku_id': '', 'pack_id': '', 'pack_quantity': '', 'creation_date': datetime.datetime.now(), 'status': 1}

SKU_MASTER_API_MAPPING = OrderedDict((
    ('skus', 'skus'), ('sku_code', 'sku_code'), ('sku_desc', 'sku_desc'),
    ('sku_brand', 'sku_brand'), ('sku_category', 'sku_category_name'),
    ('price', 'price'), ('sub_category', 'sub_category'),
    ('mrp', 'mrp'), ('sku_class', 'sku_class'),
    ('style_name', 'style_name'), ('status', 'status'), ('hsn_code', 'hsn_code'),
    ('ean_number', 'ean_number'), ('threshold_quantity', 'threshold_quantity'),('max_norm_quantity','max_norm_quantity'),
    ('color', 'color'), ('cost_price', 'cost_price'),
    ('measurement_type', 'measurement_type'), ('sku_size', 'sku_size'),
    ('mix_sku', 'mix_sku'), ('sku_type', 'sku_type'), ('attributes', 'sku_options'),
    ('child_skus', 'child_skus'),
    ('shelf_life', 'product_shelf_life'), ('customer_shelf_life', 'customer_shelf_life'), ('batch_based', 'batch_based'),
    ('image_url', 'image_url'), ('length', 'length'), ('height', 'height'), ('weight', 'weight'),
    ('breadth', 'breadth'),('pick_group', 'pick_group'), ('sku_reference', 'sku_reference'),
    ('cgst', 'cgst'), ('sgst', 'sgst'),('igst', 'igst'), ('cess', 'cess'),
    ('json_data', 'json_data'), ('scan_picking','scan_picking'), ('minimum_shelf_life','minimum_shelf_life'),('make_or_buy','make_or_buy'),('qc_eligible','qc_eligible'),
    ('serialized','serialized'), ('receipt_tolerance', 'receipt_tolerance'), ('zone_id', 'zone_id'), ('dispensing_enabled', 'dispensing_enabled'),
    ('is_barcode_required', 'is_barcode_required'), ('invoice_group', 'invoice_group'), ('seller_id', 'seller_id'),
    ('pick_and_sort', 'pick_and_sort'), ('mandate_scan', 'mandate_scan')
))

USER_MASTER_HEADERS = [first_name_const, 'UserName *', 'Status (Active/InActive) *', email_const, phone_no_const, password_const, 'Retype Password *', 'Roles']
USER_MASTER_HEADERS_MAPPING = {
    first_name_const: 'first_name', 'UserName *': 'username', 'Status (Active/InActive) *': 'status', email_const: 'email_id',
    phone_no_const: 'phone_number', password_const: 'password', 'Retype Password *': 're_password', 'Roles': 'groups',
    'send_invite': 'send_invite', 'password_reset_required': 'password_reset_required', 'warehouse': 'warehouse',
}

APPROVAL_CONFIG_DATA = {'Free Quantity Ratio Validation': ["invoice_free_quantity_ratio<po_free_quantity_ratio"], 
                        'Free Quantity Absolute Validation':["invoice_free_quantity<po_free_quantity"],
                        'Shelf Life Validation': ["sku_remaining_shelf_life<sku_minimum_shelf_life"],
                        'Margin Validation': ["sku_margin<gatekeeper_margin"],
                        'MRP Validation': ["invoice_mrp>po_mrp"]}

BATCH_MASTER_DATE_FIELDS = {'manufactured_date': 'Mfg. Date', 'expiry_date': 'Exp. Date',
                            'batch_number':'Batch No'}

def fn_timer(function):
    @wraps(function)
    def function_timer(*args, **kwargs):
        t0 = time.time()
        result = function(*args, **kwargs)
        t1 = time.time()
        print(('Time taken to run %s: %s seconds' %
               (function.__name__, str(t1-t0))
               ))
        return result
    return function_timer

def create_table_data(headers, data):
    table_data = '<table class="table"><tbody><tr class="active">'
    for header in headers:
        table_data += "<th>%s</th>" % header
    table_data += "</tr>"

    for zone, location in data.items():
        for key, value in location.items():
            table_data += "<tr>"
            table_data += "<td>%s</td>" % zone
            table_data += "<td>%s</td>" % key
            table_data += "<td>%s</td>" % value[0]
            table_data += "<td>%s</td>" % value[1]
            table_data += "</tr>"
    table_data += "</tbody></table>"

    return table_data

def create_reports_table(headers, data):
    table_data = '<table class="table"><tbody><tr class="active">'
    for header in headers:
        table_data += "<th>%s</th>" % header
    table_data += "</tr>"

    for item in data:
        table_data += "<tr>"
        if isinstance(item, dict):
            item = item.values()

        for dat in item:
            table_data += "<td style='text-align:center;'>%s</td>" % dat
        table_data += "</tr>"
    table_data += "</tbody></table>"

    return table_data

def create_po_reports_table(headers, data, user_profile, supplier):
    order_date = (str(NOW).split(' ')[0]).split('-')
    order_date.reverse()
    table_data = "<center>" + user_profile.company_name + "</center>"
    table_data += "<center>" + user_profile.user.username + "</center>"
    table_data += "<center>" + user_profile.location + "</center>"
    table_data += "<table style='padding-bottom: 10px;text-align:right;'><tr><th>Pending Purchase Orders till date:</th><th>" + "-".join(order_date) + "</th></tr>"
    table_data += "<tr><th>Supplier Name:</th><th>" + supplier + "</th></tr></table>"
    table_data += '<table class="table"><tbody><tr class="active">'
    for header in headers:
        table_data += "<th>%s</th>" % header
    table_data += "</tr>"

    for item in data:
        table_data += "<tr>"
        if isinstance(item, dict):
            item = item.values()
        for dat in item:
            table_data += "<td style='text-align:center;'>%s</td>" % dat
        table_data += "</tr>"
    table_data += "</tbody></table>"

    return table_data

def create_mail_table(headers, data):
    table_data = '<table style="border: 1px solid black;border-collapse: collapse;"><tbody><tr>'
    for header in headers:
        table_data += '<th style="border: 1px solid black;">%s</th>' % header
    table_data += '</tr>'

    for item in data:
        table_data += "<tr>"
        for dat in item:
            table_data += "<td style='border: 1px solid black;'>%s</td>" % dat
        table_data += "</tr>"
    table_data += "</tbody></table>"

    return table_data

def current_year():
    return date.today().year

class ForeignKey(models.ForeignKey):
    def db_type(self, connection):
        """ Adds support for foreign keys to big integers as primary keys.
        """
        rel_field = self.remote_field.get_related_field()
        if (isinstance(rel_field, BigAutoField) or
                (not connection.features.related_fields_match_type and
                isinstance(rel_field, (BigIntegerField, )))):
            return BigIntegerField().db_type(connection=connection)
        return super(ForeignKey, self).db_type(connection)

class TruncatedFloatField(models.FloatField):
    def get_db_prep_value(self, value, connection, prepared=False):
        if value is not None:
            number = Decimal(value)
            precision = Decimal('0.0000')
            value = number.quantize(precision ,rounding=ROUND_DOWN)
            value = super().get_db_prep_value(value, connection, prepared)
        return value


class BigAutoField(models.fields.AutoField):
    def db_type(self, connection):
        if 'mysql' in connection.__class__.__module__:
            return 'bigint AUTO_INCREMENT'
        elif 'postgresql' in connection.__class__.__module__:
            return 'bigserial'
        return super(BigAutoField, self).db_type(connection)
    
def frame_user_permissions(user, codename_list):
    all_roles = user.roles.all()
    role_ids = all_roles.values_list('id', flat=True)
    roles_perms_list = list(Permission.objects.filter(role__id__in=role_ids, codename__in=codename_list).values_list('codename', flat=True).distinct())
    user_permissions = list(user.user_permissions.filter(codename__in=codename_list).values_list('codename', flat=True))
    
    return roles_perms_list, user_permissions


def get_permission(user, codename, roles=None, user_perms_list=None):
    all_roles = user.roles.all()
    role_ids = all_roles.values_list('id', flat=True)
    roles_perms_list = set(Permission.objects.filter(role__id__in=role_ids).values_list('codename', flat=True).distinct())

    if codename in roles_perms_list:
        return True

    user_permissions = set(user.user_permissions.values_list('codename', flat=True))
    if codename in user_permissions:
        return True

    return False

def get_sku_filter_data(search_params, user):
    temp_data = copy.deepcopy(AJAX_DATA)
    results_data = []
    search_parameters = {}

    start_index = search_params.get('start', 0)
    stop_index = start_index + search_params.get('length', 0)

    cmp_data = ('sku_code', 'wms_code', 'sku_category', 'sku_type', 'sku_class')
    for data in cmp_data:
        if data in search_params:
            search_parameters['%s__%s' % (data, 'iexact')] = search_params[data]

    search_parameters['user'] = user.id
    sku_master = SKUMaster.objects.filter(**search_parameters)

    temp_data['recordsTotal'] = len(sku_master)
    temp_data['recordsFiltered'] = len(sku_master)

    if stop_index:
        sku_master = sku_master[start_index:stop_index]

    zone = ''
    for data in sku_master:
        if data.zone:
            zone = data.zone.zone

        temp_data['aaData'].append(OrderedDict(( (sku_code_const, data.sku_code), (wms_code_const, data.wms_code), (sku_group_const, data.sku_group),
                                    (sku_type_const, data.sku_type), (sku_category_const, data.sku_category), (sku_class_const, data.sku_class),
                                    (put_zone_const, zone), (threshold_qty_const, data.threshold_quantity) )))

    return temp_data


def get_location_stock_data(search_params, user):
    results_data = copy.deepcopy( AJAX_DATA )
    total_quantity = 0
    search_parameters = {}
    search_mapping = {'sku_code': 'sku__sku_code__iexact', 'sku_category': 'sku__sku_category__iexact',
                      'sku_type': 'sku__sku_type__iexact', 'sku_class': 'sku__sku_class__iexact', 'zone': 'location__zone__zone__iexact',
                      'location': 'location__location__iexact', 'wms_code': 'sku__wms_code'}

    results_data['draw'] = search_params.get('draw', 1)
    for key, value in search_mapping.items():
        if key in search_params:
            search_parameters[value] = search_params[key]

    start_index = search_params.get('start', 0)
    stop_index = start_index + search_params.get('length', 0)

    stock_detail = []
    search_parameters['quantity__gt'] = 0
    search_parameters['sku__user'] = user.id
    if search_parameters:
        stock_detail = StockDetail.objects.exclude(receipt_number=0).filter(**search_parameters)
        total_quantity = stock_detail.aggregate(Sum('quantity'))['quantity__sum']

    results_data['recordsTotal'] = len(stock_detail)
    results_data['recordsFiltered'] = len(stock_detail)

    if stop_index:
        stock_detail = stock_detail[start_index:stop_index]

    for data in stock_detail:
        results_data['aaData'].append(OrderedDict(( (sku_code_const, data.sku.sku_code), (wms_code_const, data.sku.wms_code),
                                                    (product_desc_const, data.sku.sku_desc), ('Zone', data.location.zone.zone),
                                                    ('Location', data.location.location), ('Quantity', data.quantity),
                                                    (receipt_number_const, data.receipt_number),
                                                    (receipt_date_const, str(data.receipt_date).split('+')[0]) )))
    return results_data, total_quantity

def get_receipt_filter_data(search_params, user):
    search_parameters = {}
    lis = ['open_po__supplier__name', 'order_id', 'open_po__sku__wms_code', 'open_po__sku__sku_desc', 'received_quantity']
    temp_data = copy.deepcopy( AJAX_DATA )
    temp_data['draw'] = search_params.get('draw')

    start_index = search_params.get('start', 0)
    stop_index = start_index + search_params.get('length', 0)

    if 'from_date' in search_params:
        search_parameters['creation_date__gt'] = search_params['from_date']
    if 'to_date' in search_params:
        search_parameters['creation_date__lt'] = search_params['to_date']

    if 'supplier' in search_params:
        search_parameters['open_po__supplier__id__iexact'] = search_params['supplier']
    if 'wms_code' in search_params:
        search_parameters['open_po__sku__wms_code__iexact'] = search_params['wms_code']
    if 'sku_code' in search_params:
        search_parameters['open_po__sku__sku_code__iexact'] = search_params['sku_code']

    purchase_order = []
    search_parameters['open_po__sku__user'] = user.id
    search_parameters['status__in'] = ['grn-generated', 'location-assigned', 'confirmed-putaway']

    purchase_order = PurchaseOrder.objects.filter(**search_parameters)
    if search_params.get('order_term'):
        order_data = lis[search_params['order_index']]
        if search_params['order_term'] == 'desc':
            order_data = "-%s" % order_data

        purchase_order = purchase_order.order_by(order_data)

    temp_data['recordsTotal'] = len(purchase_order)
    temp_data['recordsFiltered'] = len(purchase_order)

    if stop_index:
        purchase_order = purchase_order[start_index:stop_index]
    for data in purchase_order:
        po_reference = '%s%s_%s' %(data.prefix, str(data.creation_date).split(' ')[0].replace('-', ''), data.order_id)
        temp_data['aaData'].append(OrderedDict(( (po_reference_const, po_reference), (wms_code_const, data.open_po.sku.wms_code), ('Description', data.open_po.sku.sku_desc),
                                    ('Supplier', '%s (%s)' % (data.open_po.supplier.name, data.open_po.supplier_id)),
                                    (receipt_number_const, data.open_po_id), (received_qty_const, data.received_quantity) )))
    return temp_data


def get_dispatch_data(search_params, user):
    lis = ['order__order_id', 'order__sku__wms_code', 'order__sku__sku_desc', 'stock__location__location', 'picked_quantity', 'picked_quantity', 'updation_date', 'updation_date']
    temp_data = copy.deepcopy( AJAX_DATA )
    search_parameters = {}

    if 'from_date' in search_params:
        search_params['from_date'] = datetime.datetime.combine(search_params['from_date'], datetime.time())
        search_parameters['updation_date__gt'] = search_params['from_date']
    if 'to_date' in search_params:
        search_params['to_date'] = datetime.datetime.combine(search_params['to_date']  + datetime.timedelta(1), datetime.time())
        search_parameters['updation_date__lt'] = search_params['to_date']
    if 'wms_code' in search_params:
        search_parameters['stock__sku__wms_code'] = search_params['wms_code']
    if 'sku_code' in search_params:
        search_parameters['stock__sku__sku_code'] = search_params['sku_code']

    start_index = search_params.get('start', 0)
    stop_index = start_index + search_params.get('length', 0)

    search_parameters['status__contains'] = 'picked'
    search_parameters['stock__gt'] = 0
    search_parameters['order__user'] = user.id

    picklist = Picklist.objects.filter(**search_parameters)
    if search_params.get('order_term'):
        order_data = lis[search_params['order_index']]
        if search_params['order_term'] == 'desc':
            order_data = "-%s" % order_data
        picklist = picklist.order_by(order_data)

    temp_data['recordsTotal'] = len(picklist)
    temp_data['recordsFiltered'] = len(picklist)

    if stop_index:
        picklist = picklist[start_index:stop_index]

    for data in picklist:
        picked_quantity = data.picked_quantity
        if data.stock.location.zone.zone == 'DEFAULT':
            picked_quantity = 0
        date = get_local_date(user, data.updation_date).split(' ')

        temp_data['aaData'].append(OrderedDict(( (order_id_const, data.order.order_id), (wms_code_const, data.stock.sku.wms_code),
                                                 ('Description', data.stock.sku.sku_desc), ('Location', data.stock.location.location),
                                                 ('Quantity', data.picked_quantity), (picked_qty_const, picked_quantity),
                                                 ('Date', ' '.join(date[0:3])), ('Time', ' '.join(date[3:5]))  )))

    return temp_data

def sku_wise_purchase_data(search_params, user):
    from itertools import chain
    data_list = []
    received_list = []
    temp_data = copy.deepcopy( AJAX_DATA )
    search_parameters = {}

    if 'wms_code' in search_params:
        search_parameters['open_po__sku__wms_code'] = search_params['wms_code']

    start_index = search_params.get('start', 0)
    stop_index = start_index + search_params.get('length', 0)
    search_parameters['open_po__sku__user'] = user.id
    purchase_orders = PurchaseOrder.objects.filter(**search_parameters)
    temp_data['recordsTotal'] = len(purchase_orders)
    temp_data['recordsFiltered'] = len(purchase_orders)
    for data in purchase_orders:
        total_quantity = 0
        receipt_date = ''
        temp = ''
        if data.received_quantity == 0:
            status = 'Yet to Receive'
        elif int(data.open_po.order_quantity) - int(data.received_quantity) > 0:
            status = 'Partially Received'
            receipt_date = str(data.updation_date).split('+')[0]
        else:
            status = 'Received'
            receipt_date = str(data.updation_date).split('+')[0]
        temp = OrderedDict(( ('PO Date', str(data.po_date).split('+')[0]), ('Supplier', data.open_po.supplier.name),
                             (sku_code_const, data.open_po.sku.wms_code), (order_qty_const, data.open_po.order_quantity),
                             (received_qty_const, data.received_quantity), (receipt_date_const, receipt_date), ('Status', status) ))
        temp['Rejected Quantity'] = 0
        rejected = QualityCheck.objects.filter(purchase_order_id=data.id, po_location__location__zone__user=user.id).\
                                        aggregate(Sum('rejected_quantity'))['rejected_quantity__sum']
        if rejected:
            temp['Rejected Quantity'] = rejected
        if status == 'Received':
            received_list.append(temp)
        else:
            data_list.append(temp)

    data_list = list(chain(data_list, received_list))
    if stop_index:
        data_list = data_list[start_index:stop_index]
    temp_data['aaData'] = list(chain(temp_data['aaData'], data_list))

    return temp_data

def get_po_filter_data(search_params, user):
    lis = ['order_id', 'open_po__supplier_id', 'open_po__supplier__name', 'received_quantity']
    search_parameters = {}
    start_index = search_params.get('start', 0)
    stop_index = start_index + search_params.get('length', 0)

    temp_data = copy.deepcopy( AJAX_DATA )
    temp_data['draw'] = search_params.get('draw')

    if 'from_date' in search_params:
        search_parameters['creation_date__gte'] = search_params['from_date']
    if 'to_date' in search_params:
        search_parameters['creation_date__lte'] = search_params['to_date']

    if 'open_po' in search_params and search_params['open_po']:
        temp = re.findall('\d+', search_params['open_po'])
        if temp:
            search_parameters['order_id'] = temp[-1]

    if 'wms_code' in search_params:
        search_parameters['open_po__sku__wms_code__iexact'] = search_params['wms_code']


    search_parameters['open_po__sku__user'] = user.id
    result_values = ['order_id', 'open_po__supplier_id', 'open_po__supplier_id__name', 'prefix']
    purchase_order = PurchaseOrder.objects.exclude(status='').filter(**search_parameters).values(*result_values).distinct()
    if 'order_term' not in search_params:
        search_params['order_term'] = 0
    if search_params['order_term']:
        order_data = lis[search_params['order_index']]
        if search_params['order_term'] == 'desc':
            order_data = "-%s" % order_data
        purchase_order = PurchaseOrder.objects.exclude(status='').filter(**search_parameters).order_by(order_data).\
                                               values(*result_values).distinct()

    temp_data['recordsTotal'] = len(purchase_order)
    temp_data['recordsFiltered'] = len(purchase_order)

    if stop_index:
        purchase_order = purchase_order[start_index:stop_index]

    for data in purchase_order:
        total_quantity = 0
        results = PurchaseOrder.objects.filter(order_id=data['order_id'],open_po__sku__user=user.id)
        for result in results:
            total_quantity += result.received_quantity
        po_number = '%s%s_%s' %(data['prefix'], str(result.creation_date).split(' ')[0].replace('-', ''), data['order_id'])
        temp_data['aaData'].append(OrderedDict(( (po_number_const, po_number), (supplier_id_const, data['open_po__supplier_id']),
                                                 (supplier_name_const, data['open_po__supplier_id__name']), (total_qty_const, total_quantity),
                                                 ('DT_RowClass', 'results'), ('DT_RowAttr', {'data-id': data['order_id']}) )))
    return temp_data

USE_CLOUDWATCH = os.environ.get('USE_CLOUDWATCH') == 'TRUE'
USE_OPENSEARCH = os.environ.get('USE_OPENSEARCH') == 'TRUE'
LOG_GROUP = os.environ.get('LOG_GROUP_NAME', 'stockone_dev_logs')
if os.environ.get('AWS_ID') != '':
    CLOUDWATCH_AWS_ID = os.environ.get('AWS_ID')
    CLOUDWATCH_AWS_KEY = os.environ.get('AWS_KEY')
    AWS_DEFAULT_REGION = 'ap-south-1' # Be sure to update with your AWS region
    logger_boto3_session = Session(
        aws_access_key_id=CLOUDWATCH_AWS_ID,
        aws_secret_access_key=CLOUDWATCH_AWS_KEY,
        region_name=AWS_DEFAULT_REGION,
    )

SCENTRY = os.environ.get('SCENTRY') == 'TRUE'

class RequestIDFilter(logging.Filter):
    def filter(self, record):
        record.request_id = getattr(request_id_local, 'request_id', 'unknown')
        return True

class LogCustomHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.pattern = "Traceback (most recent call last):"

    def emit(self, record):
        try:
            msg = self.format(record)
            if self.pattern in msg:
                self.log_to_sentry(msg)
        except Exception:
            self.handleError(record)

    def log_to_sentry(self, msg):
        sentry_sdk.capture_message(msg)

def init_logger(log_file, index_name=OPENSEARCH_APPLOG_INDEX):
    if USE_CLOUDWATCH:
        logfile = log_file
        log = logging.getLogger('watchtower')
        request_id_filter = RequestIDFilter()
        log.addFilter(request_id_filter)
        return log
    else:
        logfile = os.path.abspath(log_file)
        handler = logging.handlers.RotatingFileHandler(logfile, maxBytes=20971520, backupCount=50)
        log = logging.getLogger(logfile)
    formatter = logging.Formatter(
        '%(request_id)s: %(asctime)s.%(msecs)d: %(filename)s: %(lineno)d: %(funcName)s: %(levelname)s: %(message)s', "%Y%m%dT%H%M%S")

    if SCENTRY:
        # Add custom handler to the logger for the specific pattern
        custom_handler = LogCustomHandler()
        log.addHandler(custom_handler)

    if USE_OPENSEARCH:
        log = setup_aync_logging(log, index_name)
        request_id_filter = RequestIDFilter()
        log.addFilter(request_id_filter)
        return log

    handler.setFormatter(formatter)
    log.addHandler(handler)
    log.setLevel(logging.DEBUG)
    request_id_filter = RequestIDFilter()
    log.addFilter(request_id_filter)

    return log

def cloudwatch_logger(log_file):
    """
        Back off logger for cloudwatch
    """
    if USE_CLOUDWATCH:
        logfile = log_file
        log = logging.getLogger('watchtower')
        request_id_filter = RequestIDFilter()
        log.addFilter(request_id_filter)
        return log
    else:
        logfile = os.path.abspath(log_file)
        handler = logging.handlers.RotatingFileHandler(logfile, maxBytes=20971520, backupCount=50)
        log = logging.getLogger(logfile)
        formatter = logging.Formatter(
        '%(request_id)s: %(asctime)s.%(msecs)d: %(filename)s: %(lineno)d: %(funcName)s: %(levelname)s: %(message)s', "%Y%m%dT%H%M%S")
        handler.setFormatter(formatter)
        log.addHandler(handler)
        log.setLevel(logging.DEBUG)
        request_id_filter = RequestIDFilter()
        log.addFilter(request_id_filter)
        return log



def get_git_current_version_number():
    version_number= ""
    version_number =  os.environ.get('VERSION_NUMBER', "")
    if version_number:
        return version_number
    try:
        current_path= os.getcwd()
        current_path= current_path.split("/API_WMS/wms")
        git_path=""
        if current_path:
            git_path= current_path[0]
        else:
            git_path= current_path
        repo=git.Repo(git_path)
        try:
            git_version_str= subprocess.Popen(["git", "describe", "--tags", "--abbrev=0"], stdout=subprocess.PIPE).communicate()
        except:
            git_version_str= subprocess.Popen(["/usr/bin/git", "describe", "--tags", "--abbrev=0"], stdout=subprocess.PIPE).communicate()
        if git_version_str:
            version_number = str(git_version_str[0][:-1]).split("'")[1]
            #version_number= git_version_str[0][1:-1]
        #tags = sorted(repo.tags, key=lambda t: t.commit.committed_datetime)
        #git_version_obj = tags[-1]
        #git_version_obj = repo.git.tag('--contains')
        #if git_version_obj:
        #    version_number= git_version_obj[1:]
    except Exception as e:
        #log.info(e)
        pass
    return version_number


class ApplicationConstant:
    JOBSTATUS = (
        (0, "Running"),
        (1, "Completed"),
        (2, "Failed"),
        (3, "Invalid"),
        (4, "Inprogress")
    )

class ChoiceField(serializers.ChoiceField):

    def to_representation(self, obj):
        if obj == '' and self.allow_blank:
            return obj
        return self._choices[obj]
    def to_internal_value(self, data):
        # To support inserts with the value
        if data == '' and self.allow_blank:
            return ''

        for key, val in self._choices.items():
            if val == data:
                return key
        self.fail('invalid_choice', input=data)


def folder_check(path):
    ''' Check and Create New Directory '''
    if not os.path.exists(path):
        os.makedirs(path)
    return True

def rename_dict_keys(key_mapping , data_list,reverse=False):
    """ Rename keys of data list """
    df = pd.DataFrame(data_list)
    mapping = dict(key_mapping)
    if reverse:
        for k,v in key_mapping.items():
            mapping[v] = k
    df.rename(columns=mapping, inplace=True)
    return df.to_dict(orient='records')


def generate_file_from_df(dataframe, name):
    dataframe.to_csv(
        name,
        index=False
    )
    # Step 2: Create a Django File object from the CSV file
    csv_file = File(open(name, 'rb'))
    return csv_file

def format_key_string(key_string):
    return key_string.replace('_', ' ').title()

reverse_stock_choice_mapping = {
    'OnHold': 0, 'Available': 1, 'Consumed': 2, 'Reserved': 3,
    'Rejected': 4, 'Obsolete': 5, 'Blocked': 6,
    'Expired': 7, 'Near_Expired': 8
}


def get_token(payload):
    return jwt.encode(payload, METABASE_SECRET_KEY, algorithm="HS256")


def signed_public_dashboard(warehouse, dashboard_id, send_params = True):
    if METABASE_SECRET_KEY:
        params = {"warehouse": warehouse} if send_params else {}

        payload = {
            "resource": {"dashboard": int(dashboard_id)},
            "params": params
        }

        iframeUrl = METABASE_SITE_URL + "/embed/dashboard/" + get_token(payload) + "#bordered=true"
        return iframeUrl
    else: 
        return None

def is_int(value):
    try:
        int(value)
        return True
    except ValueError:
        return False

def is_float(value):
    try:
        float(value)
        return True
    except ValueError:
        return False
