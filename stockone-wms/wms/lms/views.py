"""Application Code For LMS,  Which Handles all task Management"""
from __future__ import unicode_literals
from django.db import transaction
from django.views.generic import ListView
from django.db.models import F, Q, CharField
from .models import EmployeeMaster, TaskMaster, PutawayTask,ManualAssignment

# Create your views here.
from django.http import HttpResponse, JsonResponse, HttpResponseBadRequest
from django.db.models import Subquery, OuterRef, FloatField, DurationField, ExpressionWrapper
from django.db.models import Sum, Count, Max, Min, IntegerField, Case, Value, When
from django.contrib.postgres.aggregates import StringAgg, ArrayAgg

import json
from copy import deepcopy
import random
import datetime
import pytz
from django.utils import timezone
from datetime import timedelta
from collections import defaultdict
import pandas as pd

from wms_base.models import User, UserProfile
from outbound.models import (
    Picklist, OrderDetail, CustomerOrderSummary,
    SellerOrderSummary, StagingInfo, PickAndPassStrategy, CustomerAttributes
)
from inbound.models import POLocation
from inventory.models import (
    CycleCount, SKUPackMaster, StockDetail, LocationMaster, BAtoSADetail
)

from inventory.views.cycle_count.cycle_count import get_cyclecount_data

from quality_control.models import QualityControl
from core_operations.views.common.main import (
    get_misc_value, get_sku_pack_repr, truncate_float,
    get_multiple_misc_options, get_multiple_misc_values, WMSListView,
    save_models_objs, get_sku_ean_numbers, get_decimal_value,
    get_company_id, get_uom_decimals, get_warehouse, get_misc_options_list,
    get_batch_key_dict, get_user_time_zone, get_local_date_known_timezone
)
from core.models import (
    TempJson, MiscDetailOptions, EANNumbers
)
from inbound.views.common.constants import INBOUND_STAGING_LANES_MAPPING
from inbound.views.putaway.suggestions import save_remaining_putaway_quantity
from inbound.views.putaway.mobile import get_putaway_data, get_jo_putaway_data
from inbound.views.quality_check.common import fetch_pending_qc_quantity

from outbound.views.picklist.helpers import fetch_serial_data

from inbound.views.putaway.pending_putaway import PendingPutawaySet
from operator import itemgetter
from django.db.models.functions import Cast

from wms_base.wms_utils import init_logger, frame_user_permissions, get_permission
log = init_logger('logs/lms.log')

JO_TYPES = ['Standard JO', 'Non Standard JO', 'Returnable JO']
BACKFLUSH_JO_TYPES = JO_TYPES + ['NTE', 'BA_TO_SA']

no_task_const, otp_const, task_const, user_not_exists_const, failed_const = 'No Task Available', 'OTP Verified', 'Task Completed', 'User Not Exists', 'Failed To Assign tasks'
class lmsemployees(ListView):
    def get(self, *args, **kwargs):
        search = self.request.GET.get('search', None)
        group = self.request.GET.get('group', None)
        admin_user = kwargs['admin_user']
        employees = EmployeeMaster.objects.filter(
            location=admin_user,
            status=True
        )
        if search:
            employees = employees.filter(user__username__contains=search)

        if group:
            employees = employees.filter(user__groups__name__contains=group)

        return JsonResponse(self.queryset_to_list(employees), safe=False)

    def remove_unnecessary_data(self, skudict):
        result = {}
        for key, value in skudict.items():
            if isinstance(value, (str, int, float)):
                result[key] = value
            else:
                continue

        return result

    def queryset_to_list(self, employees):
        res = []
        for row in employees:
            res.append(
                self.object_to_dict(row)
            )
        return res

    def object_to_dict(self, row):
        newrow = row.__dict__
        clean_row = self.remove_unnecessary_data(newrow)
        clean_row.update({
            'employee': row.user.username,
            'location': row.location.username,
        })
        return clean_row

class lmstasks(WMSListView):
    def create_task(self, wh_user_id, task_ref_id, task_ref_type, priority=0, task_eta=None, group_type="",order_type="", reference_number=""):
        task = TaskMaster(
            warehouse_id=wh_user_id,
            task_ref_type=task_ref_type,
            task_ref_id= task_ref_id,
            group_type= group_type,
            order_type=order_type,
            reference_number= reference_number
        )
        if priority:
            task.priority = priority
        if task_eta:
            task.eta = task_eta

        task.save()
        return task.id

    def get_summarized_view(self, task):
        resp = []
        task_ref_type = task.task_ref_type
        task_ref_id = task.task_ref_id
        if task_ref_type == 'Picklist':
            refobj = Picklist.objects.filter(id=task_ref_id)
            if not refobj.exists():
                return resp
            refobj= refobj.first()
            order_number = refobj.order.original_order_id
            pickidslist = []
            
            for row in OrderDetail.objects.filter(original_order_id = order_number, user =task.warehouse.id):
                pickidslist.extend(list(row.picklist_set.exclude(status='open').values_list('id', flat=True)))
            
            datas = Picklist.objects.filter(id__in=pickidslist)
            for data in datas:
                location, zone, sequence, slot_from, slot_to = self.fetch_location_and_date_fields(data)
                resp.append({
                    'id': data.id,
                    'sku_code': data.order.sku.sku_code,
                    'sku_desc': data.order.sku.sku_desc,
                    'sku_size': data.order.sku.sku_size,
                    'sku_image': self.request.build_absolute_uri('/') + data.order.sku.image_url,
                    'picklist_number': data.picklist_number,
                    'order_id': data.order.original_order_id,
                    'order_reference': data.order.order_reference,
                    'reserved_quantity': data.reserved_quantity,
                    'picked_quantity': data.picked_quantity,
                    'location': location,
                    'sequence': sequence,
                    'zone': zone,
                    'order_type': data.order.order_type,
                    'slot_from': slot_from,
                    'slot_to': slot_to,
                    'promised_time': data.order.promised_time,
                    'customer_name': data.order.customer_name
                })
            resp = sorted(resp, key=itemgetter('sequence'))
            return resp
        return resp

    def fetch_location_and_date_fields(self, data):
        '''Fetch Location, Zone and Date Fields'''
        try:
            location = data.stock.location.location
            sequence = data.stock.location.pick_sequence
            zone = data.stock.location.zone.zone
        except Exception:
            sequence = 0
            location, zone = '', ''

        try:
            customer_data = data.order.customerordersummary_set.filter()
            if customer_data:
                customer_data = customer_data[0]
                shipment_slot = customer_data.shipment_time_slot.split('-')
                slot_from = shipment_slot[0].strip()
                slot_to = shipment_slot[1].strip()
        except Exception:
            slot_from, slot_to = '', ''
            
        return location, zone, sequence, slot_from, slot_to
    
    def get_aggreagated_view(self, tasks):
        response_arr = []
        picklist_aggregate = {}
        tasks = tasks.filter(employee=None)
        picklist_ids = tasks.values_list('task_ref_id', flat=True)
        picklist_aggregate['Picklist'] = get_picklist_aggregation(picklist_ids)
        response_arr.append(picklist_aggregate)
        return response_arr

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        admin_user = self.warehouse
        request_user = self.request.user
        aggregated_view = self.request.GET.get('aggregated' , None)
        data_view = self.request.GET.get('data' , None)
        task_type = self.request.GET.get('task_ref_type', None)
        summary = self.request.GET.get('summary', None)
        task_id = self.request.GET.get('task_id', None)
        if not summary:
            tasks = TaskMaster.objects.filter(warehouse=admin_user).exclude(status=1)
        else:
            tasks = TaskMaster.objects.filter(status=1, warehouse=admin_user)

        if tasks.exists() and not summary:
            tasks = tasks.order_by('eta', 'priority')
        if tasks.exists() and task_type:
            tasks = tasks.filter(task_ref_type=task_type)
        if data_view or summary:
            tasks = tasks.filter(employee=request_user.employee)
        if summary and tasks.exists():
            try:
                if task_id:
                    tasks = tasks.filter(id=task_id)
            except Exception:
                pass
            tasks = tasks.order_by('-updation_date')
            tasks = tasks.first()
            return JsonResponse(self.get_summarized_view(tasks), safe=False)
        if tasks.exists() and aggregated_view:
            return JsonResponse(self.get_aggreagated_view(tasks), safe=False)    
        return JsonResponse(self.queryset_to_list(tasks, data=data_view), safe=False)

    def put(self, *args, **kwargs):
        log.info("Current request user name %s" % (self.request.user))
        self.set_user_credientials()
        request_user = self.request.user
        request_data = self.request.GET
        admin_user =  self.warehouse
        task_ref_id = request_data.get('task_ref_id', None)
        status = request_data.get('status', None)
        task_ref_type = request_data.get('task_ref_type', None)
        if not task_ref_type:
            return HttpResponseBadRequest('Task Type Is Required')
        try:
            task = TaskMaster.objects.filter(
                task_ref_type=task_ref_type, 
                warehouse=admin_user,
                status=False,
                employee=None
            )
            if task_ref_id:
                task = task.filter(task_ref_id=task_ref_id)
            if not task.exists():
                return HttpResponseBadRequest(no_task_const)
        except Exception:
            return HttpResponseBadRequest(no_task_const)

        if status:
            tasks = TaskMaster.objects.filter(
                task_owner=task.task_owner,
                employee=task.employee,
                task_ref_type=task.task_ref_type
            )
            if not tasks.exists():
                return HttpResponseBadRequest('Task Doesn`t Exist')
            if int(status) == 1:
                tasks.update(status=0)
                save_models_objs(tasks)
            return JsonResponse({'status': 'Task Updated'})
        else:
            if TaskMaster.objects.filter(employee=request_user.employee, status=False).exists():
                return HttpResponseBadRequest('Please Complete Your Existing task')    
            task = task.annotate(empty_priority = Case(When(priority=0, then=0),
                default=1, output_field=IntegerField())).order_by('task_priority', '-empty_priority', 'eta', 'priority')
            if self.assign_tasks_to_user(task, request_user.employee):
                return JsonResponse({'status': 'task Assigned'})
            else:
                return HttpResponseBadRequest(failed_const)    

    def send_email_task(self, tasks):
        send_to = []
        subject = "OTP Generated For tasks"
        send_to.append(tasks[0].employee.user.email)

        tasklist = self.queryset_to_list(tasks, keys_to_have=['task_ref_id', 'task_ref_type', 'otp'])
        df = pd.DataFrame(tasklist)
        body = df.to_html()

        send_mail(send_to, subject, body)

    def post(self, *args, **kwargs):
        log.info("POST Request with user name %s and arguments %s, %s" % (str(self.request.user),str(args),str(kwargs)))
        self.set_user_credientials()
        request_user = self.request.user
        request_data = json.loads(self.request.body)
        task_ref_id = request_data.get('task_id', None)
        task_ref_type = request_data.get('task_type', None)
        employee_id = request_data.get('user', None)
        if employee_id == None or task_ref_id == None or task_ref_type == None:
            return HttpResponseBadRequest('Missing Parameters')

        for row in task_ref_id:
            if row.get('can_assign', False):
                taskmaster, created = TaskMaster.objects.get_or_create(
                    task_owner=request_user.employee,
                    task_ref_id=row.get('id'),
                    task_ref_type=task_ref_type,
                    status=False
                )
                taskmaster.employee_id = employee_id
                taskmaster.start_time = datetime.datetime.now()
                taskmaster.save()

        return HttpResponse('Tasks Created Successfully')

    def remove_unnecessary_data(self, skudict):
        result = {}
        for key, value in skudict.items():
            if isinstance(value, (str, int, float)):
                result[key] = value
            elif isinstance(value, (datetime.datetime)):
                result[key] = value.strftime('%d/%m/%Y %H:%M:%S')
            else:
                continue

        return result

    def sortdata(self, e):
        task = e['task_data']
        return task['sequence']

    def queryset_to_list(self, employees, keys_to_have=None, data=None):
        res = []
        for row in employees:
            res.append(
                self.object_to_dict(row, keys_to_have, data=data)
            )
        def sortdata(e):
            task = e['task_data']
            return task['sequence']
        try:
            res = sorted(res, key=sortdata)
        except Exception:
            pass

        return res

    def get_task_data(self, row):
        task_ref_type = row.task_ref_type
        task_ref_id = row.task_ref_id
        slot_from = ''
        slot_to = ''
        if task_ref_type == 'Picklist':
            data = Picklist.objects.filter(id=task_ref_id).values('order__sku__scan_picking','order__sku__image_url','order__sku__sku_code','order__sku__sku_desc','order__sku__sku_size',
                                                                    'picklist_number','reserved_quantity',
                                                                    'order__original_order_id','order__order_reference','order__order_type','order__promised_time','order__customer_name',
                                                                    'location__location','location__pick_sequence',
                                                                    'location__zone__zone', 'stock__carton__bin_number'
                                                                    ).annotate(slot_timing_string = Subquery(CustomerOrderSummary.objects.filter(order = OuterRef('order'),).values('shipment_time_slot')))
            data = list(data)
            try:
                scan_to_pick_flag = data[0]['order__sku__scan_picking']
                scan_to_pick = True if scan_to_pick_flag == 1 else False
            except Exception:
                scan_to_pick = False
            image_url = data[0]['order__sku__image_url']
            if "http" not in image_url:
                image_url = self.request.build_absolute_uri('/') + data[0]['order__sku__image_url']

            try:
                if data[0]['slot_timing_string']:
                    slot_from = data[0]['slot_timing_string'].split('-')[0].strip()
                    slot_to = data[0]['slot_timing_string'].split('-')[1].strip()
            except Exception:
                pass
            return {
                'id': task_ref_id,
                'scan_to_pick': scan_to_pick,
                'sku_code': data[0]['order__sku__sku_code'],
                'sku_desc': data[0]['order__sku__sku_desc'],
                'sku_image': image_url,
                'sku_size': data[0]['order__sku__sku_size'],
                'picklist_number': data[0]['picklist_number'],
                'order_id': data[0]['order__original_order_id'],
                'order_reference': data[0]['order__order_reference'],
                'quantity': data[0]['reserved_quantity'],
                'location': data[0]['location__location'],
                'sequence': data[0]['location__pick_sequence'],
                'zone': data[0]['location__zone__zone'],
                'order_type': data[0]['order__order_type'],
                'slot_from': slot_from,
                'slot_to': slot_to,
                'promised_time': data[0]['order__promised_time'],
                'customer_name': data[0]['order__customer_name'],
                'carton_number': data[0]['stock__carton__bin_number']
            }
        return {}

    def object_to_dict(self, row, keys_to_have=None, data=None):
        newrow = row.__dict__
        clean_row = self.remove_unnecessary_data(newrow)
        clean_row['task_data'] = self.get_task_data(row)
        if row.employee:
            clean_row.update({
                'employee': row.employee.user.username
            })
        if row.task_owner_id:
            clean_row.update({
                'task_owner': row.task_owner.user.username
            })
        if keys_to_have:
            new_row = {}
            for key in keys_to_have:
                new_row[key] = clean_row.get(key, None)
            return new_row

        return clean_row

    def update_task(self, status, task_id=None, task_ref_id=None, task_ref_type=None):
        tasks = TaskMaster.objects.filter()
        if task_id:
            tasks = tasks.filter(id=task_id)
        if task_ref_id and task_ref_type:
            tasks = tasks.filter(task_ref_id=task_ref_id, task_ref_type=task_ref_type)

        tasks.update(status=status, updation_date=datetime.datetime.now(datetime.timezone.utc))

    def get_tasks_for_user(self, user, task_id=None, ref_id=None, ref_type=None, status=False):
        tasks = TaskMaster.objects.filter(
            employee__user=user
        )
        if task_id:
            tasks.filter(id=task_id)
        if ref_id:
            tasks.filter(task_ref_id=ref_id)
        if ref_type:
            tasks.filter(task_ref_type=ref_type)
        if status:
            tasks.filter(status=status)

        return self.queryset_to_list(tasks)

    def prepare_task_details_as_list(self, tasks):
        '''
        Prepare task details
        '''
        ret_arr = []
        for row in tasks:
            status = "Unassigned"
            if row.status == None:
                status = 'Assigned'
            if row.status == False:
                status = otp_const
            if row.status == True:
                status = task_const
            ret_arr.append({
                'task_ref_id': row.task_ref_id,
                'task_ref_type': row.task_ref_type,
                'task_id': row.id,
                'status': status,
                'assigned_to': row.employee.user.username,
            })
        return ret_arr

    def prepare_Task_details_as_dict(self, tasks):
        '''
        Prepare task details as dict
        '''
        ret_arr = {}
        for row in tasks:
            status = "Unassigned"
            if row.status == None:
                status = 'Assigned'
            if row.status == False:
                status = otp_const
            if row.status == True:
                status = task_const
            ret_arr[row.task_ref_id] = {
                'task_ref_id': row.id,
                'task_ref_type': row.task_ref_type,
                'task_id': row.id,
                'status': status,
                'assigned_to': row.employee.user.username,
            }
        return ret_arr

    def get_task_and_assigned_user(self, ref_id, ref_type, status=None, user=None, as_list=None, as_dict=None):
        if isinstance(ref_id, list):
            tasks = TaskMaster.objects.filter(
                task_ref_id__in=ref_id,
                task_ref_type=ref_type
            ).order_by('-creation_date')
        else:
            tasks = TaskMaster.objects.filter(
                task_ref_id=ref_id,
                task_ref_type=ref_type
            ).order_by('-creation_date')
        if user:
            tasks = tasks.filter(employee__user=user)

        if not status:
            tasks = tasks.exclude(status=1)
        else:
            tasks = tasks.filter(status=status)
        if tasks.exists():
            if as_list:
                ret_arr = self.prepare_task_details_as_list(tasks)
                return True, ret_arr

            if as_dict:
                ret_arr = self.prepare_Task_details_as_dict(tasks)
                return True, ret_arr
            else:
                status = 'Unassigned'
                if tasks[0].status == None:
                    status = 'Assigned'
                if tasks[0].status == False:
                    status = otp_const
                if tasks[0].status == True:
                    status = task_const

                return True, {
                    'task_ref_id': tasks[0].id,
                    'status': status,
                    'assigned_to': tasks[0].employee.user.username,
                }
        else:
            return False, False

def queryset_to_list(request, employees, keys_to_have=None, data=None, task_type="", warehouse_id="", sequence=False):
    log.info("Querytolist Request user %s with data %s and keys %s" % (str(request.user),str(data),str(keys_to_have)))
    if task_type=="Putaway":
        res = get_putaway_data(request, employees, task_type, warehouse_id=warehouse_id)
    elif task_type=="sales_return_putaway":
        res = PendingPutawaySet().get_sr_putaway_line_level_data(request, employees, warehouse_id=warehouse_id)
    elif task_type == "JOPutaway":
        res = get_jo_putaway_data(request, employees, task_type, warehouse_id)
    elif task_type == 'cp_putaway':
        res = PendingPutawaySet().get_cp_putaway_line_level_data(request, employees, warehouse_id=warehouse_id)
    elif task_type == "cyclecount":
        res = get_cyclecount_data(request, employees, warehouse_id=warehouse_id)
    elif task_type in ("batosa_putaway", "nte_putaway"):
        res = PendingPutawaySet().get_batosa_putaway_line_level_data(request, employees, task_type, warehouse_id=warehouse_id)
    else:
        res = new_get_task_data(request, employees, task_type, priority_sequence=sequence)
        
    def sortdata(e):
        task = e['task_data']
        return task['sequence']
    try:
        if not sequence:
            res = sorted(res, key=sortdata)
    except Exception:
        pass
    return res

def get_pack_sizes_for_sku(user, sku_codes):
    '''
    Get pack sixe for sku
    '''
    sku_pack_details = dict(SKUPackMaster.objects.filter(sku__user=user.id, sku__sku_code__in=sku_codes, status=1).values(
        sku_code=F('sku__sku_code')).annotate(pack_qty=Max('pack_quantity')).values_list('sku_code', 'pack_qty'))
    return sku_pack_details

def get_picklist_tolerance(user, sku_codes, picklist_tolerance_config):
    '''
    Get picklist tolerance
    '''
    tolerance_percentage, tolerance_type, sku_pack_details = 0, '', {}
    
    tolerance_type_config = picklist_tolerance_config.get('picklist_tolerance_type', '')
    tolerance_percentage = picklist_tolerance_config.get('picklist_tolerance')
    if tolerance_type_config not in ['false', False, '', 'null', 'None']:
        tolerance_type = tolerance_type_config

    if tolerance_type == 'default' and tolerance_percentage not in ['false', '', 'None', 'null', None]:
        tolerance_percentage = float(tolerance_percentage)
    else:
        tolerance_percentage = 0

    if tolerance_type == 'pack_size_tolarance':
        sku_pack_details = get_pack_sizes_for_sku(user, sku_codes)

    return tolerance_type, tolerance_percentage, sku_pack_details

def get_picklist_tolerance_cal_extra_quantity(item_quantity, sku_code, tolerance_type, tolerance_percenetage, sku_pack_details={}, is_total=True):
    '''
    Get picklist_tolerance quantity
    '''
    tolerance_quantity = 0
    if tolerance_type == 'pack_size_tolarance':
        sku_max_qty = sku_pack_details.get(sku_code, '')
        if sku_max_qty:
            remaining_pack_qty = item_quantity%sku_max_qty
            if remaining_pack_qty > 0:
                tolerance_quantity = sku_max_qty - remaining_pack_qty
    elif tolerance_percenetage:
        tolerance_quantity = (item_quantity/100)*tolerance_percenetage
    if is_total:
        tolerance_quantity += item_quantity
    return tolerance_quantity

def get_and_prepare_task_details(all_tasks_values):
    '''
    Get and prepare task details
    '''
    employee_id, order_type, task_ref_ids, all_task_dicts = None, '', [], {}
    for each_task_row in all_tasks_values:
        if not employee_id:
            employee_id = each_task_row["employee_id"]
        task_ref_ids.append(int(each_task_row["task_ref_id"]))
        all_task_dicts[each_task_row["task_ref_id"]] = {
                "task_ref_type": each_task_row["task_ref_type"],
                "id": each_task_row["id"],
                "task_ref_id": each_task_row["task_ref_id"],
                "warehouse_id": each_task_row["warehouse_id"],
                "employee_id": each_task_row["employee_id"],
                "priority": each_task_row["priority"],
                "employee": each_task_row["employee__user__username"],
                "eta": each_task_row["eta"].strftime('%d/%m/%Y %H:%M:%S') if each_task_row["eta"] else "",
                "creation_date": each_task_row["creation_date"].strftime('%d/%m/%Y %H:%M:%S') if each_task_row["creation_date"] else "",
                "updation_date": each_task_row["updation_date"].strftime('%d/%m/%Y %H:%M:%S') if each_task_row["updation_date"] else "",
                }
        order_type = each_task_row.get('order_type', '')
    return employee_id, order_type, task_ref_ids, all_task_dicts

def get_auto_invoice_and_staging_area(user, misc_dict, order_type):
    '''
    Get auto invoice and staging area
    '''
    outbound_staging_area = False
    if misc_dict.get("outbound_staging_area", "false") != 'false':
        misc_option_keys = ['auto_invoice']
        misc_options = get_multiple_misc_options(misc_option_keys, order_type.lower(), user)
        auto_invoice = misc_options.get('auto_invoice', 'false')
        if auto_invoice == 'false':
            outbound_staging_area =True
    return outbound_staging_area

def get_picklist_and_unique_values(task_ref_ids, order_type, merge_picking, bulk_picking):
    '''
    Get picklist and unique values
    '''
    order_references, picklist_numbers, pending_sku_codes, pending_line_count, customer_refs = set(), [], [], [], set()
    pending_reserved_quantity = 0
    all_picklists = list(Picklist.objects.filter(id__in=task_ref_ids).values('reference_number', 'sku__sku_code', 'reserved_quantity', 'picklist_number', 'order_type', 'stock__batch_detail__batch_no', 'location__location', 'order__sku_code', 'classification', 'pick_type', 'reference_id', 'order__customer_identifier__customer_reference'))
    is_combo_exist = False
    for picklist in all_picklists:
        if picklist['classification'] == 'combo':
            is_combo_exist = True
            break
    for picklist in all_picklists:
        order_references.add(picklist['reference_number'])
        pending_sku_codes.append(picklist['sku__sku_code'])
        picklist_numbers.append(picklist['picklist_number'])
        customer_refs.add(picklist['order__customer_identifier__customer_reference'])
        pending_reserved_quantity += picklist['reserved_quantity']
        order_type = picklist['order_type']
        if picklist['pick_type'] in ['cluster_picking']:
            merge_task_key = (picklist['sku__sku_code'], picklist['stock__batch_detail__batch_no'], picklist['location__location'], picklist['classification'])
        elif merge_picking == 'true':
            if is_combo_exist:
                merge_task_key = (picklist['sku__sku_code'], picklist['stock__batch_detail__batch_no'], picklist['location__location'], picklist['reference_id'])
            else:
                merge_task_key = (picklist['sku__sku_code'], picklist['stock__batch_detail__batch_no'], picklist['location__location'])
        else:
            merge_task_key = (picklist['sku__sku_code'], picklist['stock__batch_detail__batch_no'], picklist['location__location'], picklist['reference_number'], picklist['order__sku_code'])
        if merge_task_key not in pending_line_count:
            pending_line_count.append(merge_task_key)

    no_of_orders = len(order_references)
    if no_of_orders > 1:
        bulk_picking = True

    return order_references, picklist_numbers, pending_sku_codes, pending_line_count, pending_reserved_quantity, order_type, bulk_picking, is_combo_exist, customer_refs

def get_total_task_details(user, show_picklist_data, task_ids, merge_picking, pending_line_count, is_combo_exist):
    '''
    Get total task details
    '''
    total_picked_quantity, total_picklist_quantity, total_tasks, picked_tasks, pick_type = 0, 0, 0, 0, ''
    if not task_ids: return total_picked_quantity, total_picklist_quantity, total_tasks, picked_tasks, pick_type
    if show_picklist_data != 'Task':
        totals = list(Picklist.objects.filter(id__in=task_ids, user=user.id).values('user', 'pick_type').annotate(pick_qty = Sum('picked_quantity'), reserved_qty = Sum('reserved_quantity'),picklist_qty= Sum(Case(When(remarks='alternative picklist',then=Value(0)), default=F('picklist_quantity'),output_field=FloatField()))))
        if not len(totals):
            return total_picked_quantity, total_picklist_quantity, total_tasks, picked_tasks, pick_type
        totals=totals[0]
        total_picked_quantity = totals['pick_qty']
        total_picklist_quantity = totals['picklist_qty']
        pick_type = totals['pick_type']
    else:
        picklist_objects = Picklist.objects.filter(id__in=task_ids, user=user.id)
        pick_data = picklist_objects.first()
        pick_type = pick_data.pick_type if pick_data else ''
        merge_task_val = ['sku__sku_code',  'picklist_number', 'pick_type', 'stock__batch_detail__batch_no','location__location']
        if merge_picking == 'false':
            merge_task_val.append('order__order_reference')
            merge_task_val.append('order__sku_code')
        if pick_type in ['cluster_picking']:
            merge_task_val.append('classification')
        if is_combo_exist:
            merge_task_val.append('reference_id')
        picklist_objects = picklist_objects.values(*merge_task_val).distinct()
        total_tasks = picklist_objects.count()
        picked_tasks = total_tasks - len(set(pending_line_count))

    return total_picked_quantity, total_picklist_quantity, total_tasks, picked_tasks, pick_type

def get_dispensing_tolerance(misc_dict):
    '''
    Get dispensing tolerance config
    '''
    dispensing_tolerance_enabled = False
    dispensing_tolerance_enabled_misc = misc_dict.get('dispensing_tolerance_enabled', 'false')
    if dispensing_tolerance_enabled_misc not in ['false', False, '', None]:
        dispensing_tolerance_enabled = True
    return dispensing_tolerance_enabled

def get_single_order_picklist_details(merge_picking, order_picklist_list,sale_price_from_order):
    if merge_picking == 'false':
        order_picklist_list.append('sku_id')
    if sale_price_from_order == "true":
        order_picklist_list.extend(['order__unit_price','order__customerordersummary__cgst_tax','order__customerordersummary__cess_tax',
                                    'order__customerordersummary__igst_tax','order__customerordersummary__sgst_tax'])
    order_picklist_dict = {
        'skuid': F('sku'), 'scan_picking':F('sku__scan_picking'), 'serial_sku' : F('sku__enable_serial_based'),
        'sku_image':F('sku__image_url'), 'skucode':F('sku__sku_code'), 'sku_desc':F('sku__sku_desc'),
        'sku_size':F('sku__sku_size'), 'mfg_date': F('stock__batch_detail__manufactured_date'),
        'exp_date':F('stock__batch_detail__expiry_date'), 'batch_no':F('stock__batch_detail__batch_no'),
        'original_order_id' :F('order__original_order_id'), 'order_reference': F('reference_number'),
        'ordertype' : F('order_type'), 'promised_time' : F('order__promised_time'), 'customer_reference': F('order__customer_identifier__customer_reference'),
        'order_ordertype' : F('order_type'), 'batch_reference':F('stock__batch_detail__batch_reference'),
        'slot_from' : F('order__slot_from'), 'slot_to' : F('order__slot_to'), 'sub_zone': F('location__sub_zone__zone'),
        'customer_name' : F('order__customer_name'),'order_mrp':F('order__mrp'), 'mandate_scan' : F('sku__mandate_scan'),
        'trip_id' : F('order__trip_id'), 'pick_sequence' : F('location__pick_sequence'), 'zone' : F('location__zone__zone'),
        'mrp': F('stock__batch_detail__mrp'), 'sku_dispensing_enabled': F('sku__dispensing_enabled'),
        'sku_user': F('sku__user'), 'sku_brand': F('sku__sku_brand'), 'weight': F('stock__batch_detail__weight'),
        'sku_uom': F('sku__measurement_type'), 'order_pack_uom_quantity': F('order__json_data__pack_uom_quantity'),
        'stock_lpn': F('stock__lpn_number'), 'pack_id' : F('order__json_data__pack_id'),
    }
    return order_picklist_list, order_picklist_dict

def get_pagination_details(request, order_type, data_result_values):
    if request.GET.get('limit') and order_type != 'full_carton_pick':
        try:
            limit = int(request.GET.get('limit' , 50))
            offset = int(request.GET.get('offset' , 0))
        except Exception:
            limit = 50
            offset = 0
        offset = offset * limit
        data_result_values = data_result_values[offset : offset+limit]
    return data_result_values

def get_cluster_picking_details(warehouse, employee_id, picklist_ids):
    '''
    Retrieve cluster picking details for a given warehouse and picklist IDs.

    Args:
        warehouse (Warehouse): The warehouse object.
        picklist_ids (list): A list of picklist IDs.

    Returns:
        tuple: A tuple containing two dictionaries:
            - cluster_details: A dictionary containing the cluster name and sub-cluster details.
            - picklist_sub_cluster_details: A dictionary containing the sub-cluster details for each picklist ID.
    '''
    cluster_details, picklist_sub_cluster_details, temp_json_details = {}, {}, {}
    picklist_details = list(Picklist.objects.filter(id__in=picklist_ids, user=warehouse.id).values('id', 'reference_number', 'picklist_number', 'json_data'))
    if picklist_details:
        picklist_record = picklist_details[0]
        cluster_details['cluster_name'] = picklist_record.get('json_data', {}).get('cluster_name', '')
        picklist_number = picklist_record.get('picklist_number', '')
        temp_json_objects = list(TempJson.objects.filter(warehouse_id=warehouse.id, model_id=picklist_number, model_name='cluster_picking', model_reference=str(employee_id)).values_list('model_json', flat=True))
        if temp_json_objects:
            temp_json_details = json.loads(temp_json_objects[0].replace("'", '"'))

    cluster_details['sub_cluster_details'] = {}
    for picklist_record in picklist_details:
        json_data = picklist_record.get('json_data', {}) or {}
        sub_cluster_name = json_data.get('sub_cluster_name', '')
        if temp_json_details.get(sub_cluster_name, {}):
            picklist_sub_cluster_data = cluster_details['sub_cluster_details'][sub_cluster_name] = temp_json_details[sub_cluster_name]
        else:
            picklist_sub_cluster_data = cluster_details['sub_cluster_details'][sub_cluster_name] = {
                'name': sub_cluster_name,
                'lpn_type': json_data.get('sub_cluster_lpn_type', ''),
                'lpn_numbers': [],
                'current_lpn': ''
            }
        picklist_sub_cluster_details[str(picklist_record['id'])] = picklist_sub_cluster_data
    return cluster_details, picklist_sub_cluster_details

def get_sku_pack_dict(user, sku_codes):
    sku_pack_dict = {}
    sku_pack_qtys_objs = SKUPackMaster.objects.filter(sku__sku_code__in=sku_codes, sku__user=user.id, status=1). \
        order_by('-pack_quantity'). \
        values('pack_quantity', 'pack_id', 'sku__sku_code')
    for sku_pack_qtys_obj in sku_pack_qtys_objs:
        sku_pack_dict.setdefault(sku_pack_qtys_obj['sku__sku_code'], [])
        sku_pack_dict[sku_pack_qtys_obj['sku__sku_code']].\
            append({'pack_id': sku_pack_qtys_obj['pack_id'],
                    'pack_quantity': sku_pack_qtys_obj['pack_quantity']})
    return sku_pack_dict

def get_unique_picklist_details(data_result_values):
    sku_codes, picklist_numbers, sku_uoms, batch_nos, serial_skus, picklist_ids = [], [], [], set(), set(), set()
    for data in data_result_values:
        picklist_numbers.append(data['picklist_number'])
        sku_codes.append(data['skucode'])
        sku_uoms.append(data['sku_uom'])
        batch_nos.add(data['batch_no'])
        if data['serial_sku']:
            serial_skus.add(data['skucode'])
        pick_ids = data['picklist__row_id'].split(",")
        picklist_ids.update(pick_ids)
    return sku_codes, picklist_numbers, sku_uoms, batch_nos, serial_skus, picklist_ids

def get_time_zone(user):
    user_details = UserProfile.objects.get(user_id=user.id)
    time_zone = 'Asia/Calcutta'
    if user_details.timezone:
        time_zone = user_details.timezone 
    return time_zone

def get_date_formats(each_picklist, time_zone):
    slot_from, slot_to, manufactured_date, expiry_date = '', '', '', ''
    try:
        manufacture_date = each_picklist['mfg_date'].astimezone(pytz.timezone(time_zone))
        manufactured_date = datetime.datetime.strftime(manufacture_date, "%Y-%m-%d")
    except Exception:
        pass
    try:
        expiry_date = each_picklist['exp_date'].astimezone(pytz.timezone(time_zone))
        expiry_date = datetime.datetime.strftime(expiry_date, "%Y-%m-%d")
    except Exception:
        pass
    try:
        if each_picklist.get('slot_from'):
            slot_from = each_picklist['slot_from'].astimezone(pytz.timezone(time_zone)).strftime("%H:%M")
        if each_picklist.get('slot_to'):
            slot_to = each_picklist['slot_to'].astimezone(pytz.timezone(time_zone)).strftime("%H:%M")
    except Exception:
        pass
    return slot_from, slot_to, manufactured_date, expiry_date

def get_pack_id(sku_pack_dict, sku_code):
    pack_id = ''
    if sku_pack_dict.get(sku_code, []):
        pack_id = sku_pack_dict.get(sku_code, [])[0].get('pack_id', '')
    return pack_id

def get_picklist_tolerance_quantity(picklist_tolerance_type, dispensing_tolerance_enabled, each_picklist, tolerance_percentage, reserved_quantity, sku_code, sku_pack_details):
    picklist_tolerance_quantity = 0
    if (picklist_tolerance_type in ['default']) or (not dispensing_tolerance_enabled) or (dispensing_tolerance_enabled and each_picklist.get('sku_dispensing_enabled', 0) in [1] and picklist_tolerance_type in ['pack_size_tolarance']):
        picklist_tolerance_quantity = get_picklist_tolerance_cal_extra_quantity(reserved_quantity, sku_code, picklist_tolerance_type,
            tolerance_percentage, sku_pack_details=sku_pack_details, is_total=False)
    return picklist_tolerance_quantity

def prepare_final_data(order_type, final_data_dict, data, each_picklist, task_data, reserved_quantity):
    if order_type == 'full_carton_pick':
        if final_data_dict.get(each_picklist.get('carton_bin_number')):
            final_data_dict[each_picklist.get('carton_bin_number')]['carton_data'].append(task_data)
            final_data_dict[each_picklist.get('carton_bin_number')]['task_data']['quantity'] += each_picklist['reserved_quantity']
            final_data_dict[each_picklist.get('carton_bin_number')]['task_data']['id'] = str(final_data_dict[each_picklist.get('carton_bin_number')]['task_data']['id']) + ',' + str(each_picklist["picklist__row_id"])
        else:
            final_data_dict[each_picklist.get('carton_bin_number')] = data
            final_data_dict[each_picklist.get('carton_bin_number')]['carton_data'] = [task_data]
            final_data_dict[each_picklist.get('carton_bin_number')]['task_data'] = {
            'id': str(each_picklist["picklist__row_id"]),
            'picklist_number': each_picklist['picklist_number'],
            'quantity': reserved_quantity,
            'location': each_picklist['location__location'],
            'sequence': each_picklist['pick_sequence'],
            'zone': each_picklist['zone'],
            'order_type': each_picklist['ordertype'],
            'carton_number': each_picklist.get('carton_bin_number'),
            'sku_desc' : each_picklist.get('carton_bin_number')
            }
    else:
        final_data_dict[data['id']] = data
    return final_data_dict

def get_sale_uom_details(pack_uom_quantity, reserved_quantity, maximum_pick_quantity):
    if pack_uom_quantity > 1:
        maximum_pick_quantity = reserved_quantity = reserved_quantity / pack_uom_quantity
    return reserved_quantity, maximum_pick_quantity

def get_cluster_details(user, data_result_values, employee_id, task_ref_ids):
    """
    Retrieves cluster details and picklist sub-cluster details based on the provided parameters.

    Parameters:
    user (str): The user for whom the cluster details are retrieved.
    data_result_values (list): A list of data result values.
    employee_id (int): The employee ID.
    task_ref_ids (list): A list of task reference IDs.

    Returns:
    tuple: A tuple containing the pick type, cluster details, and picklist sub-cluster details.
    """
    cluster_details, picklist_sub_cluster_details, pick_type = {}, {}, ''
    if data_result_values:
        pick_type = data_result_values[0].get('pick_type', '')
        if pick_type == 'cluster_picking':
            cluster_details, picklist_sub_cluster_details = get_cluster_picking_details(user, employee_id, task_ref_ids)
    return pick_type, cluster_details, picklist_sub_cluster_details

def get_scan_to_pick_flag(each_picklist):
    '''
    Get scan to pick flag
    '''
    try:
        scan_to_pick_flag = each_picklist['scan_picking']
        scan_to_pick = True if scan_to_pick_flag == 1 else False
    except Exception:
        scan_to_pick = False
    return scan_to_pick

def get_image_url(request, each_picklist):
    """
    Get the image URL for a picklist item.

    Args:
        request (HttpRequest): The HTTP request object.
        each_picklist (dict): The picklist item.

    Returns:
        str: The image URL.
    """
    image_url = each_picklist.get('sku_image', '')
    if image_url and "http" not in image_url:
        image_url = request.build_absolute_uri('/') + each_picklist['sku_image']
    return image_url

def get_picklist_specific_fields(pick_type, combo, order_picklist_dict, order_picklist_list):
    """
    Get specific fields for a picklist based on the pick type.

    Args:
        pick_type (str): The type of picklist.
        order_picklist_dict (dict): The dictionary containing the picklist fields.
        order_picklist_list (list): The list of picklist fields.

    Returns:
        None
    """
    if pick_type == 'cluster_picking' or combo:
        order_picklist_list.append('classification')
        order_picklist_dict['classification_'] = F('classification')
    if combo:
        order_picklist_list.append('reference_id')
    return order_picklist_dict, order_picklist_list

@get_warehouse
def new_get_task_data(request, all_tasks_objs, task_type='', warehouse='', priority_sequence=False):
    '''
    Get new task details
    '''
    user = warehouse
    if priority_sequence:
        all_tasks_values = list(all_tasks_objs.order_by('priority').values(
            "task_ref_type", "task_ref_id", "id", "order_type", "employee__user__username",
            "warehouse_id", "employee_id", "priority", "eta", "creation_date", "updation_date"
        ))
    else:
        all_tasks_values = list(all_tasks_objs.values(
            "task_ref_type", "task_ref_id", "id", "order_type", "employee__user__username",
            "warehouse_id", "employee_id", "priority", "eta", "creation_date", "updation_date"
        ))
    task_data = []
    final_data_dict, bin_detail_dict, customer_data_dict = {}, {}, {}
    total_picked_quantity, total_picklist_quantity = 0, 0
    bulk_picking = False
    task_filters = {}
    if task_type:
        task_filters['task_ref_type'] = task_type

    #get and prepare task details
    employee_id, order_type, task_ref_ids, all_task_dicts = get_and_prepare_task_details(all_tasks_values)

    misc_types = [
        "merge_picking", "outbound_staging_area", "picklist_tolerance_type", "picklist_tolerance",
        "dispensing_tolerance_enabled", "show_picklist_data", "decimal_limit", "decimal_limit_price",
        "picking_screen_attributes"
    ]
    misc_dict = get_multiple_misc_values(misc_types, user.id)
    misc_detail_options_filters = {
        'misc_detail__user': warehouse.id,
        'misc_detail__misc_type': 'picklist_reasons',
        'status': 1
    }
    reasons = list(MiscDetailOptions.objects.filter(**misc_detail_options_filters).values_list('misc_value', flat=True))
    merge_picking = misc_dict.get("merge_picking", "false")
    show_picklist_data = misc_dict.get("show_picklist_data","Quantity")
    picking_screen_attributes = misc_dict.get("picking_screen_attributes","").split(',') if misc_dict.get("picking_screen_attributes","") else []
    sale_price_from_order = 'true' if 'sale_price_from_order' in picking_screen_attributes else "false"

    #dispensing enabled configuration
    dispensing_tolerance_enabled = get_dispensing_tolerance(misc_dict)
    outbound_staging_area, decimal_limit, decimal_limit_price = False, 4, 2
    if all_tasks_values and all_tasks_values[0]["task_ref_type"]== "Picklist":
        company_id = get_company_id(user)
        decimal_limit = misc_dict.get('decimal_limit', 0)
        decimal_limit_price = misc_dict.get('decimal_limit_price', 0)
        if all_tasks_values[0]["order_type"] != 'ST':
            outbound_staging_area = get_auto_invoice_and_staging_area(user, misc_dict, order_type)

            #get picklist details
            order_references, picklist_numbers, pending_sku_codes, pending_line_count, pending_reserved_quantity, order_type, bulk_picking, is_combo_exist, customer_refs = get_picklist_and_unique_values(task_ref_ids, order_type, merge_picking, bulk_picking)

            task_ids = list(TaskMaster.objects.filter(warehouse_id = user.id, employee_id=employee_id, reference_number__in=picklist_numbers,**task_filters).values_list('task_ref_id', flat=True))

            already_droped_cartons = get_already_dropped_cartons(order_references, user, bulk_picking)
            
            customer_data_dict = get_customer_data(user, customer_refs)

            #get total task details
            total_picked_quantity, total_picklist_quantity, total_tasks, picked_tasks, pick_type = get_total_task_details(user, show_picklist_data, task_ids, merge_picking, pending_line_count, is_combo_exist)

            no_of_orders = len(order_references)
            order_picklist_list = ['picklist_number', 'pick_type', 'location__location', 'location__check_digit']
            if no_of_orders == 1 or merge_picking == 'false':
                order_picklist_list, order_picklist_dict = get_single_order_picklist_details(merge_picking, order_picklist_list,sale_price_from_order)
            else:
                order_picklist_dict = {
                    'skuid': F('sku'), 'scan_picking':F('sku__scan_picking'), 'serial_sku' : F('sku__enable_serial_based'),
                    'sku_image':F('sku__image_url'), 'skucode':F('sku__sku_code'), 'sku_desc':F('sku__sku_desc'),
                    'sku_size':F('sku__sku_size'), 'mfg_date': F('stock__batch_detail__manufactured_date'),
                    'exp_date':F('stock__batch_detail__expiry_date'), 'batch_no':F('stock__batch_detail__batch_no'),  
                    'ordertype' : F('order_type'), 'mrp': F('stock__batch_detail__mrp'), 'mandate_scan': F('sku__mandate_scan'),
                    'batch_reference':F('stock__batch_detail__batch_reference'), 'trip_id' : F('order__trip_id'),
                    'pick_sequence' : F('location__pick_sequence'), 'zone' : F('location__zone__zone'), 'sub_zone': F('location__sub_zone__zone'),
                    'sku_user': F('sku__user'), 'skubrand': F('sku__sku_brand'), 'weight': F('stock__batch_detail__weight'),'customer_reference': StringAgg('order__customer_identifier__customer_reference', delimiter=',', distinct=True),
                    'sku_dispensing_enabled': F('sku__dispensing_enabled'), 'sku_uom': F('sku__measurement_type'), 'order_pack_uom_quantity': F('order__json_data__pack_uom_quantity'),
                    'pick_from_lpn_number': F('stock__lpn_number'), 'stock_lpn': F('stock__lpn_number'), 'pack_id' : F('order__json_data__pack_id'),
                }

            order_picklist_dict, order_picklist_list = get_picklist_specific_fields(pick_type, is_combo_exist, order_picklist_dict, order_picklist_list)

            data_result_values = Picklist.objects.filter(id__in=task_ref_ids, status='open').order_by("location__pick_sequence").values(*order_picklist_list, **order_picklist_dict).annotate(
                reserved_quantity=Sum('reserved_quantity'), picklist__row_id = StringAgg(Cast('id', CharField()),distinct=True, delimiter=','), stock_available_qty=Sum('stock__quantity')
            )

        if data_result_values and priority_sequence:
            data_result_values = sorted(
                data_result_values,
                key=lambda each_picklist: get_priority_for_picklist(each_picklist, all_task_dicts)
            )
        pick_type, cluster_details, picklist_sub_cluster_details = get_cluster_details(user, data_result_values, employee_id, task_ref_ids)

        data_result_values = get_pagination_details(request, order_type, data_result_values)

        sku_codes, picklist_numbers, sku_uoms, batch_nos, serial_skus, picklist_ids = get_unique_picklist_details(data_result_values)
        
        serial_numbers_data = fetch_serial_data(user, picklist_numbers[0], picklist_ids, serial_skus) if serial_skus and len(set(picklist_numbers)) == 1 else {}

        ean_sku_dict = get_sku_ean_numbers(sku_codes, [all_tasks_values[0]["warehouse_id"]])
        batch_key_dict = get_batch_key_dict(user, batch_nos)
        time_zone = request.timezone if request.timezone else get_time_zone(user)
        picklist_tolerance_type, tolerance_percentage, sku_pack_details = get_picklist_tolerance(user, sku_codes, misc_dict)

        #Decimal Round off Based on UOM
        uom_decimals = get_uom_decimals(company_id, uom_codes = sku_uoms)
        
        for each_picklist in  data_result_values:
            uom_decimal_limit = uom_decimals.get(each_picklist.get('sku_uom'))
            round_off = uom_decimal_limit or decimal_limit
            slot_from= ""
            slot_to = ""
            sales_uom_enabled = 0
            sale_price_gst = 0

            slot_from, slot_to, manufactured_date, expiry_date = get_date_formats(each_picklist, time_zone)
            scan_to_pick = get_scan_to_pick_flag(each_picklist)
            bin_detail = bin_detail_dict.get(each_picklist.get('order_id'), {})
            image_url = get_image_url(request, each_picklist)
            reserved_quantity = each_picklist['reserved_quantity']
            temp_reference = all_task_dicts.get(str(str(each_picklist["picklist__row_id"]).split(',')[0]), {})
            sku_code = each_picklist.get('skucode', '')
            
            maximum_pick_quantity = reserved_quantity
            picklist_tolerance_quantity = get_picklist_tolerance_quantity(picklist_tolerance_type, dispensing_tolerance_enabled, each_picklist, tolerance_percentage, reserved_quantity, sku_code, sku_pack_details)
            maximum_pick_quantity += picklist_tolerance_quantity
            order_reference, original_order_id = each_picklist.get('order_reference', ''), each_picklist.get('original_order_id', '')
            batch_no, batch_reference = each_picklist.get("batch_no"), each_picklist.get("batch_reference", '')
            batch_display_key = batch_reference or batch_no
            pack_repr, pack_id, pack_uom_quantity = '', '', 1
            base_uom_quantity = reserved_quantity
            if each_picklist['order_pack_uom_quantity'] and each_picklist['pack_id']:
                sales_uom_enabled = 1
                pack_id = each_picklist['pack_id']
                pack_uom_quantity = each_picklist['order_pack_uom_quantity']
                sku_pack_dict = [{
                    'pack_id': pack_id,
                    'pack_quantity': pack_uom_quantity,
                }]
                pack_repr = get_sku_pack_repr(
                    sku_pack_dict, reserved_quantity
                )
                reserved_quantity, maximum_pick_quantity = get_sale_uom_details(pack_uom_quantity, reserved_quantity, maximum_pick_quantity)
            reserved_quantity =  truncate_float(reserved_quantity, round_off)
            stock_available_qty = truncate_float(each_picklist.get('stock_available_qty', 0), round_off)
            picklist_tolerance_quantity = truncate_float(picklist_tolerance_quantity, round_off)
            maximum_pick_quantity = truncate_float(maximum_pick_quantity, round_off)
            pending_reserved_quantity = truncate_float(pending_reserved_quantity, round_off)
            total_picked_quantity = truncate_float(total_picked_quantity, round_off)
            total_picklist_quantity = truncate_float(total_picklist_quantity, round_off)
            total_unfulfilled_quantity = total_picklist_quantity - total_picked_quantity - pending_reserved_quantity
            total_unfulfilled_quantity = truncate_float(total_unfulfilled_quantity, round_off)

            picklist_row_cluster_data = {}
            for picklist_row_id in str(each_picklist["picklist__row_id"]).split(','):
                if picklist_row_id in picklist_sub_cluster_details:
                    picklist_row_cluster_data = picklist_sub_cluster_details[picklist_row_id]
                    break
            if sale_price_from_order == 'true':
                cgst_tax = each_picklist.get('order__customerordersummary__cgst_tax', 0) or 0
                sgst_tax = each_picklist.get('order__customerordersummary__sgst_tax', 0) or 0
                igst_tax = each_picklist.get('order__customerordersummary__igst_tax', 0) or 0
                cess_tax = each_picklist.get('order__customerordersummary__cess_tax', 0) or 0
                total_tax_percentage = cgst_tax + sgst_tax + igst_tax + cess_tax
                
                # Calculate sale price with GST
                unit_price = each_picklist.get('order__unit_price', 0) or 0
                sale_price_gst = unit_price * (1 + (total_tax_percentage / 100))
                sale_price_gst = truncate_float(sale_price_gst, decimal_limit_price)

            task_data = {
                    'id': each_picklist["picklist__row_id"],
                    'scan_to_pick': scan_to_pick,
                    'sku_code': sku_code,
                    'sku_desc': each_picklist.get('sku_desc', ''),
                    'sku_image': image_url,
                    'sku_size': each_picklist.get('sku_size', ''),
                    'combo_flag' : (each_picklist.get('classification', '') == 'combo'),
                    'ean_numbers' : ean_sku_dict.get(each_picklist.get('skucode'), []),
                    'picklist_number': each_picklist['picklist_number'],
                    'order_id': original_order_id,
                    'order_reference': order_reference,
                    'order_display_key': order_reference or original_order_id,
                    'quantity': reserved_quantity,
                    'picklist_tolerance_quantity': picklist_tolerance_quantity,
                    'maximum_pick_quantity': maximum_pick_quantity,
                    'base_uom_quantity': base_uom_quantity,
                    'sales_uom_quantity': pack_uom_quantity,
                    'sales_uom_enabled' : sales_uom_enabled,
                    'location': each_picklist['location__location'],
                    'check_digit': each_picklist['location__check_digit'],
                    'batch_number': batch_no,
                    'batch_keys': batch_key_dict.get(batch_no, []),
                    'batch_reference': batch_reference,
                    'batch_display_key': batch_display_key,
                    "manufactured_date": manufactured_date,
                    "expiry_date": expiry_date,
                    'sequence': each_picklist['pick_sequence'],
                    'zone': each_picklist['zone'],
                    'sub_zone': each_picklist.get('sub_zone', ''),
                    'order_type': each_picklist['ordertype'],
                    'slot_from': slot_from,
                    'slot_to': slot_to,
                    'stock_lpn': each_picklist.get('stock_lpn', ''),
                    'promised_time': each_picklist.get('promised_time'),
                    'customer_name': each_picklist.get('customer_name', ''),
                    'customer_reference': each_picklist.get('customer_reference', ''),
                    'customer_attributes' : customer_data_dict.get(each_picklist.get('customer_reference')) or {},
                    'pick_type': each_picklist['pick_type'],
                    'bin_detail': bin_detail,
                    'trip_id': each_picklist.get('trip_id'),
                    'pack_repr': pack_repr,
                    'pack_id': pack_id,
                    'sku_measurement_type': each_picklist.get('sku_uom', ''),
                    'mrp': each_picklist.get('mrp', 0),
                    'order_mrp': each_picklist.get('order_mrp',0),
                    'sale_price_gst':sale_price_gst,
                    'sku_brand': each_picklist.get('sku_brand', ''),
                    'weight': each_picklist.get('weight', ''),
                    "pending_line_count": len(pending_line_count),
                    "total_tasks": total_tasks,
                    "picked_tasks": picked_tasks,
                    "already_droped_cartons" : already_droped_cartons,
                    "sub_cluster_details": picklist_row_cluster_data,
                    "pick_from_lpn_number": each_picklist.get('pick_from_lpn_number', ''),
                    "serial_numbers": serial_numbers_data.get((sku_code, each_picklist['location__location'], batch_no or '', each_picklist.get('stock_lpn', '') or ''), []),
                    "serial_based": each_picklist.get('serial_sku', 0),
                    "is_full_lpn": False,
                    "mandate_scan" : each_picklist.get('mandate_scan', False),
                }
            if each_picklist.get('stock_lpn', '') and reserved_quantity == stock_available_qty:
                task_data['is_full_lpn'] = True
            data = {
                "id": temp_reference.get("id", ""),
                "warehouse_id": temp_reference.get("warehouse_id", ""),
                "employee_id": temp_reference.get("employee_id", ""),
                "task_ref_id": temp_reference.get("task_ref_id", ""),
                "task_ref_type": temp_reference.get("task_ref_type", ""),
                "priority": temp_reference.get("priority", ""),
                "eta": temp_reference.get("eta", ""),
                "reasons": reasons,
                "bulk_picking": bulk_picking,
                "outbound_staging_area": outbound_staging_area,
                "creation_date": temp_reference.get("creation_date", ""),
                "updation_date": temp_reference.get("updation_date", ""),
                "employee": temp_reference.get("employee", ""),
                "pending_reserved_quantity": pending_reserved_quantity,
                "pending_sku_count": len(set(pending_sku_codes)),
                "total_picked_quantity": total_picked_quantity,
                "total_picklist_quantity": total_picklist_quantity,
                "total_unfulfilled_quantity": total_unfulfilled_quantity,
                "task_data":task_data,
                "cluster_details": cluster_details
            }
            final_data_dict = prepare_final_data(order_type, final_data_dict, data, each_picklist, task_data, reserved_quantity)
    return list(final_data_dict.values())

def get_already_dropped_cartons(order_references, user, bulk_picking=False):
    '''
    Get Dropped Cartons from staging_info
    '''
    if not order_references:
        return []
    filters = {
        'user': user.id,
        'status': 0,
        'location__zone__storage_type': 'pre_invoice',
        'segregation': 'outbound_staging'
    }
    if bulk_picking:
        filters['order_reference__in'] = order_references
    else:
        filters['order_reference'] = list(order_references)[0]
    already_droped_cartons = list(StagingInfo.objects.filter(**filters).values_list('carton_no',flat=True))
    
    return already_droped_cartons

def get_customer_data(user, customer_refs):
    '''
    Get Customer Data
    '''
    customer_data_dict = defaultdict(dict)
    if customer_refs:
        customer_data_list = list(CustomerAttributes.objects.filter(customer__user=user.id, customer__customer_reference__in=customer_refs).values_list('customer__customer_reference', 'attribute_name', 'attribute_value'))
        for customer_attrs in customer_data_list:
            customer_data_dict[customer_attrs[0]][customer_attrs[1]] = customer_attrs[2]
    return customer_data_dict

def get_priority_for_picklist(each_picklist, all_task_dicts):
    picklist_row_id = str(each_picklist.get("picklist__row_id", "")).split(',')[0]
    priority = all_task_dicts.get(str(picklist_row_id), {}).get("priority", 0)
    return priority
        
class lmstasks1(WMSListView):
    def get_summarized_view(self, task):
        task_ref_type = task.task_ref_type
        if task_ref_type == 'Picklist':
            resp = []
            order_type = ''
            total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity = 0, 0, 0, 0
            task_ref_no = task.reference_number
            picklist_ids = list(TaskMaster.objects.filter(reference_number=task_ref_no, employee__user=self.request.user, warehouse_id=task.warehouse_id)
                                                    .values_list('task_ref_id', flat=True).distinct())
            if not picklist_ids:
                return resp

            company_id = get_company_id(task.warehouse)
            decimal_limit = get_decimal_value(task.warehouse_id)
            if task.order_type != 'ST':
                values_list = {
                    'row_id' : F('id'), 'picklistnumber' : F('picklist_number'), 'reserved_qty': F('reserved_quantity'),
                    'picked_qty': F('total_picked'), 'scan_picking' : F('sku__scan_picking'), 'image_url' : F('sku__image_url'),
                    'sku_code' : F('sku__sku_code'), 'sku_desc' : F('sku__sku_desc'), 'sku_size' : F('sku__sku_size'),
                    'pick_group' : F('sku__pick_group'), 'original_order_id' : F('order__original_order_id'),
                    'order_reference' : F('reference_number'), 'promised_time' : F('order__promised_time'),
                    'original_quantity' : F('order__original_quantity'), 'trip_id' : F('order__trip_id'), 
                    'carton_bin_number' : F('stock__carton__bin_number'), 'order_ordertype': F('order_type'),
                    'ordertype' : F('order_type'), 'customer_name' : F('order__customer_name'), 'json_data' : F('order__json_data'),
                    'sku_brand': F('sku__sku_brand'), 'weight': F('sku__weight'), 'sku_uom': F('sku__measurement_type'),
                    'picklist_qty': F('picklist_quantity'), 'remark_': F('remarks')
                }
                picklist_values = list(Picklist.objects.filter(id__in=picklist_ids, sku__user = task.warehouse_id)
                    .exclude(status='open').exclude(reason='Short closure (No Physical Stock)')
                    .values('sku__sku_code').annotate(total_picked = Sum('picked_quantity')).values('total_picked',**values_list)\
                    .annotate(slot_timing_string = Subquery(CustomerOrderSummary.objects.filter(order = OuterRef('order'),)\
                    .values('shipment_time_slot'))))

                if not picklist_values:
                    return resp
                
                slot_from, slot_to = self.fetch_slot_date_values(picklist_values)
            
            #fetch unique picklist data
            order_type, sku_uoms, order_references, sku_codes, order_quantity_dict = self.fetch_unique_picklist_values(picklist_values, order_type)

            #Decimal Round off Based on UOM
            uom_decimals = get_uom_decimals(company_id, uom_codes = sku_uoms)

            #fetch unique skupack data
            sku_pack_dict = self.fetch_unique_pack_master_data(sku_codes, task)

            #prepare summarized picklist data
            final_data_dict, total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity = self.prepare_summarized_picklist_data(picklist_values, order_type, sku_pack_dict, order_quantity_dict, slot_from, slot_to, total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity)
            
            #prepare carton related data
            resp = self.prepare_carton_related_data(order_type, final_data_dict)

            #prepare final summarized data
            resp = self.prepare_final_summarized_data(resp, uom_decimals, total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity, decimal_limit)
            return resp
        return []
    
    def fetch_slot_date_values(self, picklist_values):
        '''Fetch Slot From and to Datetime'''
        slot_from, slot_to = '', ''
        try:
            if picklist_values:
                picklist_values = picklist_values[0]
                slot_timing = picklist_values.get('slot_timing_string','') or ''
                if slot_timing:
                    slot_from = slot_timing.split('-')[0].strip()
                    slot_to = slot_timing.split('-')[1].strip()
        except Exception:
            slot_from, slot_to = '', ''
        
        return slot_from, slot_to
    
    def fetch_unique_picklist_values(self, picklist_values, order_type):
        '''Prepare Unique Picklist Values'''
        order_quantity_dict = {}
        order_references, sku_uoms, sku_codes = [], [], []
        for each_row in picklist_values:
            sku_uoms.append(each_row.get('sku_uom'))
            order_type = each_row.get('ordertype', '')
            sku_code = each_row.get('sku_code','')
            if (each_row['order_reference'], sku_code) not in order_references:
                order_quantity_dict.setdefault(sku_code, 0)
                order_quantity_dict[sku_code] += each_row.get('original_quantity') or 0
                order_references.append((each_row['order_reference'],sku_code))
                sku_codes.append(sku_code)
        
        return order_type, sku_uoms, order_references, sku_codes, order_quantity_dict
    
    def fetch_unique_pack_master_data(self, sku_codes, task):
        '''Function to fetch and prepare unique sku pack data'''
        sku_pack_qtys_objs = SKUPackMaster.objects.filter(sku__sku_code__in=sku_codes, sku__user=task.warehouse_id, status=1). \
            order_by('-pack_quantity'). \
            values('pack_quantity', 'pack_id', 'sku__sku_code')
        sku_pack_dict = {}
        for sku_pack_qtys_obj in sku_pack_qtys_objs:
            sku_pack_dict.setdefault(sku_pack_qtys_obj['sku__sku_code'], [])
            sku_pack_dict[sku_pack_qtys_obj['sku__sku_code']].\
                append({'pack_id': sku_pack_qtys_obj['pack_id'],
                        'pack_quantity': sku_pack_qtys_obj['pack_quantity']})
        
        return sku_pack_dict
    
    def prepare_summarized_picklist_data(self, picklist_values, order_type, sku_pack_dict, order_quantity_dict, slot_from, slot_to, total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity):
        '''Function to prepare Summarized Picklist Data'''
        final_data_dict = {}
        for each_row in picklist_values:
            sku_code = each_row.get('sku_code','')
            image_url = each_row['image_url']
            if image_url and 'http' not in str(image_url):
                image_url = self.request.build_absolute_uri('/') + each_row['image_url']
            image_url = image_url.replace("https://udaan.azureedge.net/products", "https://ud-img.azureedge.net/w_120/u/products")
            if order_type == 'full_carton_pick':
                key = (sku_code, each_row.get("carton_bin_number"))
            else:
                key = sku_code
            base_uom_quantity = each_row["total_picked"]
            sales_uom_enabled, sales_uom_quantity = 0, 0
            if each_row.get('json_data') != None:
                sales_uom_quantity = each_row.get('json_data', {}).get('pack_uom_quantity', 1) or 1
                if each_row.get('json_data', {}).get('pack_uom_quantity') not in [None,'']:
                    sales_uom_enabled = 1
            if sales_uom_quantity:
                each_row["reserved_qty"] = each_row["reserved_qty"] / sales_uom_quantity
                each_row["total_picked"] = each_row["total_picked"] / sales_uom_quantity
                each_row['picked_qty'] = each_row['picked_qty'] / sales_uom_quantity
            pack_id = ''
            if sku_pack_dict.get(sku_code, []):
                pack_id = sku_pack_dict.get(sku_code, [])[0].get('pack_id', '')
            if key in final_data_dict:
                final_data_dict[key]["picked_quantity"] += each_row["total_picked"]
                final_data_dict[key]["reserved_quantity"] += each_row["reserved_qty"]
                if each_row.get('best_bin_number',''):
                    final_data_dict[key]['bin_info'].setdefault(each_row.get('best_bin_number',''), 0)
                    final_data_dict[key]['bin_info'][each_row.get('best_bin_number','')] += each_row["total_picked"]
                total_picked_quantity += each_row["total_picked"]
                total_reserved_quantity += each_row['reserved_qty']
            else:
                final_data_dict[key]={
                        'id': each_row["row_id"],
                        'picklist_number': each_row['picklistnumber'],
                        'sku_code': sku_code,
                        'sku_desc': each_row['sku_desc'],
                        'sku_size': each_row['sku_size'],
                        'pick_group': each_row.get('pick_group'),
                        'sku_image': image_url,
                        'order_id': each_row['original_order_id'],
                        'order_reference': each_row['order_reference'],
                        'order_quantity': order_quantity_dict.get(sku_code, 0),
                        'reserved_quantity': each_row['reserved_qty'],
                        'picked_quantity': each_row.get('picked_qty'),
                        'order_type': each_row.get('ordertype', 'ST'),
                        'order_ordertype': each_row.get('order_ordertype', ''),
                        'trip_id': each_row.get('trip_id', ""),
                        'picklist_quantity': each_row.get('picklist_qty', ''),
                        'slot_from': slot_from,
                        'slot_to': slot_to,
                        'promised_time': each_row.get('promised_time'),
                        'customer_name': each_row.get('customer_name'),
                        'bin_number': each_row.get('best_bin_number',''),
                        'bin_type': each_row.get('best_bin_type', ''),
                        'sku_measurement_type': each_row.get('sku_uom', ''),
                        'base_uom_quantity' : base_uom_quantity,
                        'sales_uom_quantity' : sales_uom_quantity,
                        'sales_uom_enabled': sales_uom_enabled,
                        'pack_id' : pack_id
                        }
                total_order_quantity += order_quantity_dict.get(each_row['sku_code'], 0)
                total_picked_quantity += each_row.get('picked_qty')
                total_reserved_quantity += each_row['reserved_qty']
                if each_row.get('remark_') != 'alternative picklist':
                    total_picklist_quantity += each_row.get('picklist_qty', 0)
                
                final_data_dict = self.prepare_final_data_dict_extra_details(each_row, final_data_dict, key)
        
        return final_data_dict, total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity

    def prepare_final_data_dict_extra_details(self, each_row, final_data_dict, key):
        '''Extra Details Preparation'''
        if each_row.get('best_bin_number',''):
            final_data_dict[key]['bin_info'] = {each_row.get('best_bin_number',''): each_row['total_picked']}
        if each_row.get('json_data'):
            if isinstance(each_row.get('json_data'), dict):
                final_data_dict[key]['aux_data'] = each_row.get('json_data')
        
        return final_data_dict

    def prepare_carton_related_data(self, order_type, final_data_dict):
        '''Prepare Carton Related Data'''
        if order_type == 'full_carton_pick':
            response_dict = {}
            for key, value in final_data_dict.items():
                if key[1] in response_dict:
                    response_dict[key[1]]['picked_quantity'] += value['picked_quantity']
                    response_dict[key[1]]['reserved_quantity'] += value['reserved_quantity']
                    response_dict[key[1]]['carton_data'].append(value)
                else:
                    response_dict[key[1]] = {
                    "carton_number" : key[1],
                    "order_type" : "full_carton_pick",
                    "picked_quantity" : value['picked_quantity'],
                    "reserved_quantity" : value['reserved_quantity'],
                    "carton_data" : [value]
                    }
            resp = response_dict.values()
            resp = sorted(resp, key = itemgetter('carton_number'))
        else:
            resp = final_data_dict.values()
            resp = sorted(resp, key = itemgetter('sku_code'))
        
        return resp
    
    def prepare_final_summarized_data(self, resp, uom_decimals, total_order_quantity, total_picked_quantity, total_reserved_quantity, total_picklist_quantity, decimal_limit):
        '''Final Summarized Data'''
        if resp[0]:
            resp[0]['total_order_quantity'] = truncate_float(total_order_quantity, decimal_limit)
            resp[0]['total_picked_quantity'] = truncate_float(total_picked_quantity, decimal_limit)
            resp[0]['total_reserved_quantity'] = truncate_float(total_reserved_quantity, decimal_limit)
            resp[0]['total_picklist_quantity'] = truncate_float(total_picklist_quantity, decimal_limit)
            resp[0]['total_unfulfilled_quantity'] = truncate_float(total_picklist_quantity - total_picked_quantity, decimal_limit)

        for res in resp:
            uom_decimal_limit = uom_decimals.get(res.get('sku_uom'))
            round_off = uom_decimal_limit or decimal_limit
            res['order_quantity'] =  truncate_float(res.get('order_quantity', 0), round_off)
            res['picked_quantity'] = truncate_float(res.get('picked_quantity', 0), round_off)
            res['reserved_quantity'] = truncate_float(res.get('reserved_quantity', 0), round_off)
            res['picklist_quantity'] = truncate_float(res.get('picklist_quantity', 0), round_off)
        return resp

    def get_userwise_tasks(self, tasks, employee_id, employee_name, warehouse_id, offset=0, limit=50):
        pack_data = {}
        task_reference_numbers=list(tasks.values_list("reference_number", flat=True).distinct().annotate(reference_number_int=Cast('reference_number', IntegerField())).order_by("-reference_number_int")[offset:limit+offset])
        task_data = list(tasks.filter(reference_number__in=task_reference_numbers).order_by('updation_date').values('task_ref_id','reference_number','updation_date','start_time'))
        task_ids,invoice_numbers = [],[]
        picking_time = 0
        for task in task_data:
            task_ids.append(task['task_ref_id'])
            if task['updation_date'] and task['start_time']:
                picking_time = (task['updation_date'] - task['start_time']).total_seconds()
        picking_time = max(picking_time, 0)
        picked_lines = len(set(Picklist.objects.filter(id__in=task_ids).values_list('reference_id','sku_id','stock__batch_detail_id').distinct()))
        if self.line_level == 'true':
            q_filters = Q()
            q_filters |= Q(picklist_id__in=task_ids)
            combo_pick_data = []
            combo_order_ids = list(Picklist.objects.filter(id__in=task_ids, classification='combo').values_list('order_id', flat=True).distinct())
            if combo_order_ids:
                q_filters |= Q(order_id__in=combo_order_ids)
                combo_pick_data = Picklist.objects.filter(order_id__in=combo_order_ids, classification='combo', picklist_number=self.picklist_number).exclude(status='short_closed').values('picklist_number', 'sku__sku_code', 'picked_quantity')
            summaryobjs = SellerOrderSummary.objects.filter(q_filters)
            object_values = list(summaryobjs.select_related().values('order__order_reference', 'order__sku', 'order__original_order_id', 'order__customer_name', 'order__order_type','order__shipment_date' ,'picklist__status', 'picklist__picklist_number', 'creation_date', 'updation_date', 'picklist__id', 'order__trip_id' ,"order__sku__pick_group", "picklist__stock_id").order_by('-creation_date'))
            sos_ids = dict(summaryobjs.filter(order_status_flag="processed_orders").values_list('id','picklist__picklist_number'))
            if sos_ids:
                pack_data = self.fetch_stock_detail_lpn_data(sos_ids, pack_data, warehouse_id)

            pack_data = self.fetch_seller_order_lpn_data(pack_data, summaryobjs, combo_pick_data)
            invoice_numbers = self.fetch_picklist_invoice_data(warehouse_id, task_reference_numbers, summaryobjs)
        else:
            pick_objs = Picklist.objects.filter(id__in= task_ids)
            object_values = list(pick_objs.select_related().values('order__order_reference', 'order__sku', 'order__original_order_id', 'order__customer_name', 'order__order_type','order__shipment_date' ,'status', 'picklist_number', 'creation_date', 'updation_date', 'id', 'order__trip_id' ,"order__sku__pick_group").annotate(sos_creation_date=Min('updation_date')).annotate(sos_updation_date=Max('updation_date')).order_by('-updation_date'))

        dropped_lpns = self.get_staging_info_details(warehouse_id, object_values)
        userwise_orderlist = {}
        try:
            admin_user= User.objects.get(id=warehouse_id)
        except Exception:
            admin_user= self.request.user
        picklist_number = ''
        for row in object_values:
            if self.line_level == 'true':
                picklist_number = row['picklist__picklist_number']
                picklist_status = row['picklist__status']
                picklist_id = row['picklist__id']
                userwise_orderlist = self.prepare_header_level_data(row, userwise_orderlist, employee_id, employee_name, warehouse_id, admin_user, picklist_number, picklist_status, picklist_id)
                converted_data, sku_data = self.prepare_final_lpn_and_sku_details(row, pack_data, dropped_lpns)
                userwise_orderlist[picklist_number].update({'lpn_details': converted_data, 'sku_details': sku_data,
                                                            'invoice_details': invoice_numbers})
            else:
                picklist_number = row['picklist_number']
                picklist_status = row['status']
                picklist_id = row['id']
                row.update({'creation_date': row['sos_creation_date'], 'updation_date': row['sos_updation_date']})
                userwise_orderlist = self.prepare_header_level_data(row, userwise_orderlist, employee_id, employee_name, warehouse_id, admin_user, picklist_number, picklist_status, picklist_id)
        for picklist_data in userwise_orderlist.values():
            picklist_data['order_reference'] = list(set(filter(None, picklist_data['order_reference'])))
            picklist_data['order_reference'] = ','.join(picklist_data['order_reference'])
        if self.misc_dict.get('cycle_time_in_picking','false') == 'true':
            if userwise_orderlist.get(picklist_number,{}).get('items'):
                userwise_orderlist[picklist_number]['avg_picking_time'] = round(picking_time/picked_lines, 2) if picked_lines else 0
            if userwise_orderlist.get(picklist_number,{}):
                userwise_orderlist[picklist_number]['today_picking_time'] = self.get_user_average_picking_time(employee_id, warehouse_id, task_ids)
        final_response = userwise_orderlist.values()

        return list(final_response)

    def get_staging_info_details(self, warehouse_id, object_values):
        '''
        Get staging info details
        '''
        order_references = [row['order__order_reference'] for row in object_values]
        return list(StagingInfo.objects.filter(order_reference__in=order_references, status=0, segregation='outbound_staging', user_id=warehouse_id).values_list('carton_no', flat=True).distinct())

    
    def fetch_stock_detail_lpn_data(self, sos_ids, pack_data, warehouse_id):
        stock_objs = StockDetail.objects.filter(receipt_number__in=sos_ids.keys(),lpn_number__isnull=False, receipt_type__in=['so_picking', 'so_dispense'], sku__user=warehouse_id).select_related('sku')
        for stock in stock_objs:
            picklist_no = sos_ids.get(stock.receipt_number)
            if picklist_no not in pack_data:
                pack_data[picklist_no] = []
            
            pack_data[picklist_no].append({
                'lpn_number': stock.lpn_number,
                'sku_code' : stock.sku.sku_code,
                'picked_quantity': stock.quantity
            })
        
        return pack_data
    
    def fetch_seller_order_lpn_data(self, pack_data, summaryobjs, combo_pick_data):
        seller_objs = summaryobjs.values('picklist__picklist_number', 'lpn_number', 'picklist__sku__sku_code', 'quantity', 'picklist__stock_id')
        for sos in seller_objs:
            # Skip combo parent picklist <> sos mapping record
            if sos.get('picklist__stock_id') is None:
                continue
            picklist_no = sos.get('picklist__picklist_number')
            if picklist_no not in pack_data:
                pack_data[picklist_no] = [{
                    'lpn_number': sos.get('lpn_number'),
                    'sku_code' : sos.get('picklist__sku__sku_code'),
                    'picked_quantity': sos.get('quantity')
                }]
            else:
                pack_data[picklist_no].append({
                    'lpn_number': sos.get('lpn_number'),
                    'sku_code' : sos.get('picklist__sku__sku_code'),
                    'picked_quantity': sos.get('quantity')
                })

        # Frame Combo child picked data
        for combo in combo_pick_data:
            picklist_no = combo.get('picklist_number')
            pack_data.setdefault(picklist_no, []).append({
                'lpn_number': '',
                'sku_code' : combo.get('sku__sku_code'),
                'picked_quantity': combo.get('picked_quantity'),
            })
        
        return pack_data
    
    def fetch_picklist_invoice_data(self, warehouse_id, reference_numbers, summaryobjs):
        '''
        Fetch Picklist Invoice Data
        '''
        picklist_data = Picklist.objects.filter(user_id=warehouse_id,picklist_number__in =reference_numbers,status='open')
        if  picklist_data.exists():
            return []
        else:
            invoice_numbers = []
            invoice_data = list(summaryobjs.values('invoice_number', 'picklist__picklist_number',
                            'financial_year','invoice_reference').distinct())
            for data in invoice_data:

                datatable_dict = {
                "invoice_number":data.get('invoice_number','') ,
                "picklist_number" :str(data.get('picklist__picklist_number', '')),
                "financial_year" :data.get('financial_year',''),
                "invoice_reference":data.get('invoice_reference','')
                }
                invoice_numbers.append(datatable_dict)     
            return invoice_numbers  

                        
    def prepare_header_level_data(self, row, userwise_orderlist, employee_id, employee_name, warehouse_id, admin_user, picklist_number, picklist_status, picklist_id):
        '''Prepare Header Level Data'''
        if picklist_number in userwise_orderlist:
            userwise_orderlist[picklist_number]['items'] += 1
            userwise_orderlist[picklist_number]['order_reference'].append(row['order__order_reference'])
        else:
            userwise_orderlist[picklist_number] = {
            'employee_id' : employee_id,
            'employee_name':employee_name,
            'warehouse_id' : warehouse_id,
            'order_reference' : [row['order__order_reference']],
            'items' : 1,
            'pick_group': row['order__sku__pick_group'],
            'customer_name' : row['order__customer_name'],
            'order_type' : row['order__order_type'],
            'trip_id': row.get("order__trip_id", ""),
            'status' : picklist_status,
            'picklist_number' : picklist_number,
            'updation_date' : get_local_date_known_timezone(self.time_zone, row["updation_date"]) if row['updation_date']  else "",
            'creation_date' : get_local_date_known_timezone(self.time_zone, row["creation_date"]) if row['creation_date']  else "",
            'shipment_date':  get_local_date_known_timezone(self.time_zone, row["order__shipment_date"]) if row['order__shipment_date']  else "",
            'task_ref_id' : picklist_id
            }
        
        return userwise_orderlist

    def prepare_final_lpn_and_sku_details(self, row, pack_data, dropped_lpns):
        converted_data, sku_data = {}, {}
        for item in pack_data.get(row['picklist__picklist_number'], []):
            lpn_number, sku_code, picked_quantity = item['lpn_number'], item['sku_code'], item['picked_quantity']
            if lpn_number:
                is_dropped = True if lpn_number in dropped_lpns else False
                if (existing := next((i for i in converted_data.get(lpn_number, []) if i['sku_code'] == sku_code), None)):
                    existing['picked_quantity'] += picked_quantity
                else:
                    converted_data.setdefault(lpn_number, []).append({'sku_code': sku_code, 'picked_quantity': picked_quantity, 'is_dropped': is_dropped})
            else:
                if (existing := next((i for i in sku_data.get(sku_code, []) if i['sku_code'] == sku_code), None)):
                    existing['picked_quantity'] += picked_quantity
                else:
                    sku_data.setdefault(sku_code, []).append({'sku_code': sku_code, 'picked_quantity': picked_quantity})

        final_sku_data = []
        for item in sku_data.values():
            if isinstance(item, list):
                item = item[0]
            final_sku_data.append(item)
        return converted_data, final_sku_data

    def get_assigned_sub_zones(self):
        """
        Retrieves a list of assigned sub-zones based on the admin user and group type.

        Returns:
            list: A list of assigned sub-zones.
        """
        zones = list(LocationMaster.objects.filter(sub_zone__user=self.admin_user.id, sub_zone__zone__in=self.group_type).values_list('zone_id', flat=True).distinct())
        all_subzones = list(LocationMaster.objects.filter(sub_zone__user=self.admin_user.id, zone_id__in=zones).values_list('sub_zone_id', flat=True).distinct())
        return all_subzones

    def get_pick_and_pass_picker_picklist_count(self, reference_type, extra_params=None):
        """
        Returns the count of picklists for the pick and pass picker.

        Args:
            pick_objs (QuerySet): A queryset of pick objects.

        Returns:
            int: The count of picklists for the pick and pass picker.
        """

        if extra_params is None:
            extra_params = {}

        picklist_filters = extra_params.get('picklist_filters', {})
        picklist_excludes = extra_params.get('picklist_excludes', {})

        # Get all assigned subzones
        all_subzones = self.get_assigned_sub_zones()

        # Get all open picklist numbers for the user in the specified subzones
        all_open_picklist_numbers = list(Picklist.objects.filter(
            **picklist_filters,
            user_id=self.admin_user.id,
            status='open',
            location__sub_zone__in=all_subzones,
            pick_type='pick_and_pass'
        ).exclude(**picklist_excludes).values_list('picklist_number', flat=True).distinct())
        all_open_picklist_numbers = [str(ele) for ele in all_open_picklist_numbers]

        # Get reference numbers for open pick and pass strategy in the specified subzones
        open_picklist_numbers = PickAndPassStrategy.objects.filter(
            warehouse_id=self.admin_user.id,
            sub_zone__in=all_subzones,
            status='open',
            reference_type=reference_type
        ).values_list('reference_number', flat=True).distinct()

        # Get reference numbers for completed pick and pass strategy
        completed_picklist_numbers = PickAndPassStrategy.objects.filter(
            warehouse_id=self.admin_user.id,
            sub_zone__zone__in=self.group_type,
            reference_type=reference_type
        ).values('reference_number').annotate(
            total_count=Count('id'),
            completed_count=Count('id', filter=Q(status='completed'))
        ).filter(total_count=F('completed_count')).values_list('reference_number', flat=True).distinct()

        # Combine all open picklist numbers and remove duplicates by converting to set
        combined_picklist_numbers = set(all_open_picklist_numbers).union(open_picklist_numbers)

        # Subtract completed picklist numbers from combined picklist numbers
        final_picklist_numbers = combined_picklist_numbers.difference(completed_picklist_numbers)

        return list(final_picklist_numbers)

    def get_tasks_aggregation(self, task_type ,task_ids, status_count=""):
        if task_type == 'picklist' or task_type=='st_picklist':
            picklist_filters= {'id__in': task_ids}
            if status_count=="open":
                picklist_filters["status"]= "open"
                picklist_filters["reserved_quantity__gt"]= 0
            # pickobjs = Picklist.objects.filter(**picklist_filters).exclude(Q(order__order_type__in=BACKFLUSH_JO_TYPES) | Q(pick_type='pick_and_pass'))
            # aggr_obj = pickobjs.aggregate(qty=Sum('reserved_quantity'), picklist=Count('picklist_number', distinct=True))
            aggr_obj = Picklist.objects.filter(**picklist_filters).exclude(Q(order__order_type__in=BACKFLUSH_JO_TYPES) | Q(pick_type='pick_and_pass')).aggregate(picklist=Count('picklist_number', distinct=True))
            # aggr_obj['qty'] = truncate_float(aggr_obj['qty'] or 0, self.decimal_limit)
            if task_type == 'picklist' and self.task_sub_type == "pick_and_pass":
                extra_params = {'picklist_excludes': {'order_type__in': BACKFLUSH_JO_TYPES}}
                total_picklist_numbers = self.get_pick_and_pass_picker_picklist_count('so_picking', extra_params=extra_params)
                aggr_obj['picklist'] = len(total_picklist_numbers)
            return dict(aggr_obj)

        elif task_type == 'jo_picklist':
            picklist_filters= {'id__in': task_ids, 'order_type__in': JO_TYPES}
            if status_count == "open":
                picklist_filters["status"] = "open"
                picklist_filters["reserved_quantity__gt"] = 0
            # pickobjs = Picklist.objects.filter(**picklist_filters)
            aggr_obj =  Picklist.objects.filter(**picklist_filters).aggregate(items=Sum('reserved_quantity'), orders=Count('picklist_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'batosa_picking':
            aggr_obj = {'item': 0, 'SKU': 0}
            picklist_filters= {'id__in': task_ids, 'order_type':'BA_TO_SA'}
            pickobjs = Picklist.objects.filter(**picklist_filters)
            if pickobjs.exists():
                aggr_obj = pickobjs.aggregate(item=Sum('reserved_quantity'), SKU=Count('picklist_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'batosa_putaway':
            aggr_obj = {'item': 0, 'SKU': 0}
            putobjs = POLocation.objects.filter(id__in=task_ids, putaway_type = 'ba_to_sa', status=1)
            if putobjs.exists():
                aggr_obj = putobjs.aggregate(item=Sum('quantity'), SKU=Count('sku_id', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'cyclecount':
            cycleobjs = CycleCount.objects.filter(id__in=task_ids)
            aggr_obj = cycleobjs.aggregate(items=Count('id'), tasks=Count('cycle', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'grn': #po_putaway
            putobjs = POLocation.objects.filter(id__in=task_ids, status=1)
            aggr_obj = putobjs.aggregate(items=Count('purchase_order__open_po__sku'), orders=Count('seller_po_summary__grn_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'cancelled':
            cancobjs = POLocation.objects.filter(id__in=task_ids, status=1)
            aggr_obj = cancobjs.aggregate(items=Count('picklist__order__sku'), orders=Count('picklist__order__order_reference', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'cancelled_picklist':
            canc_objs = POLocation.objects.filter(id__in=task_ids, picklist__isnull=False, status=1)
            aggr_obj = canc_objs.aggregate(items=Count('picklist__sku'), orders=Count('picklist__reference_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'sales_return': #SalesReturns Putaway
            sales_return_objs = POLocation.objects.filter(id__in=task_ids, purchase_order__isnull=True, status=1)
            aggr_obj = sales_return_objs.aggregate(items=Count('seller_po_summary__sales_return_batch__sales_return_sku'), orders=Count('seller_po_summary__sales_return', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'po_grn':
            qc_objs = QualityControl.objects.filter(id__in=task_ids, transaction_type="after_grn", status__in=[0,1])
            aggr_obj = qc_objs.aggregate(items=Count('transaction_id'), orders=Count('reference_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'sr_grn':
            qc_objs = QualityControl.objects.filter(id__in=task_ids, transaction_type="after_sr_grn", status__in=[0,1])
            aggr_obj = qc_objs.aggregate(items=Count('transaction_id'), orders=Count('reference_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'jo_grn':
            qc_objs = QualityControl.objects.filter(id__in=task_ids, transaction_type="after_jo_grn", status__in=[0,1])
            aggr_obj = qc_objs.aggregate(items=Count('transaction_id'), orders=Count('reference_number', distinct=True))
            return dict(aggr_obj)
        elif task_type == 'jo': #JOb Order Putaway
            jo_putaway_objs = POLocation.objects.filter(id__in=task_ids, job_order_id__isnull=False, status=1)
            aggr_obj = jo_putaway_objs.aggregate(items=Count('job_order__product_code__sku_code'), orders=Count('seller_po_summary__grn_number', distinct=True))
            return dict(aggr_obj)
        else:
            return dict({})

    def check_user_permission(self,roles_perms_list, user_permissions, task):
        if task in roles_perms_list:
            return True
        
        if task in user_permissions:
            return True

        return False

    def get_cycle_tasks_data(self, cycletasks, employee_id):
        filter_dict = {}
        if self.group_type:
            if self.subzone_mapping != 'false':
                filter_dict = {"location__sub_zone__zone__in": self.group_type}
            else:
                filter_dict = {"location__zone__zone__in": self.group_type}

        cycle_cycle_open_tasks = cycletasks.filter(Q(employee__isnull=True) | Q(employee=employee_id[0]), status = 1, **filter_dict)
        return cycle_cycle_open_tasks

    def get_aggreagated_dict(self, tasks, cycletasks, aggregated=False, putaway_tasks=None, batosa_picking=None, batosa_putaway=None, admin_user=''):
        final_response = {}
        misc_options_types = ['manual_assignment', 'label_based_picking', 'hybrid_task_assignment']
        misc_options_dict = get_misc_options_list(misc_options_types, admin_user, to_lower_case=False)
        manual_assignment_order_types = misc_options_dict.get('manual_assignment', [])
        label_based_picking_order_types = misc_options_dict.get('label_based_picking', [])
        hybrid_assignment_order_types = misc_options_dict.get('hybrid_task_assignment', [])
        self.subzone_mapping = self.misc_dict.get('subzone_mapping', 'false')

        task_permission_dict = {
                'jo_picklist': 'add_picklist',
                'picklist' : 'add_picklist',
                'create_grn' : 'add_sellerposummary',
                'cyclecount' : 'add_cyclecount', 
                'st_picklist': 'add_stocktransfer',
                'putaway' : 'change_polocation',
                'move_inventory': 'add_moveinventory',
                'inventory' : 'view_stockdetail',
                'batosa_picking': 'add_picklist',
                'batosa_putaway': 'add_sellerposummary',
                'quality_control' : 'add_qualitycontrol',
                'outbound_qc' : 'view_so_qc',
                'picklistlocation' : 'add_picklistlocation',
                'add_orderpackaging': 'add_orderpackaging',
                'show_create_manifest': 'show_create_manifest'

                }

        #UserBased Permission Dict
        all_task_types = {
            'picklist': ['picklist','st_picklist','jo_picklist'],
            'create_grn' : 'create_grn',
            'putaway': ['grn', 'cancelled_picklist', 'returns', 'sales_return', 'jo'],
            'cyclecount' : 'cyclecount',
            'move_inventory' : 'move_inventory',
            'inventory':'inventory',
            'quality_control' : ['po_grn', 'jo_grn', 'sr_grn']
            }
        user_task_types = {}
        codename_list = [task_permission_dict.get(task) for task in task_permission_dict]
        roles_perms_list, user_permissions = frame_user_permissions(self.request.user, codename_list)

        for task_type, task_value in all_task_types.items():
            user_task_types = self.get_user_task_types(user_task_types, task_value, roles_perms_list, user_permissions, task_permission_dict)

        #Applying Filters and Fetching OpenTasks.
        open_tasks_dict, pick_filter_params, qc_filter_params, open_tasks = self.prepare_task_filters_and_open_tasks(tasks, user_task_types, cycletasks, putaway_tasks, batosa_picking, batosa_putaway)

        #Framing Data for all TaskTypes with the status 
        priority = 1
        for task_type, task_value in user_task_types.items():
            iter_list = [('open', open_tasks_dict.get(task_type, open_tasks))]
            #Fetching All, Running, Closed tasks Objects
            iter_list = self.get_all_running_closed_tasks(task_type, aggregated, iter_list, tasks, putaway_tasks, cycletasks, batosa_picking, batosa_putaway, pick_filter_params)

            #Names To Display in Dashboard
            dashboard_key_dict = {
                    'jo_picklist': 'Job Order',
                    'st_picklist':'Stock Transfer',
                    'picklist' : 'Sale Order', 'grn':'GRN',
                    'cancelled_picklist' : 'Cancelled',
                    'returns': 'Returns',
                    'sales_return' : 'Sales Return',
                    'batosa_picking': 'Picking',
                    'batosa_putaway': 'Putaway',
                    'po_grn' : 'PO GRN',
                    'sr_grn' : 'SR GRN',
                    'jo_grn' : 'JO GRN',
                    'jo' : 'Job Order',
                    }

            #To Filter based on TaskRefType (Ids mights be same in Another models)
            filter_key_dict = {
                    'grn':'Putaway',
                    'cancelled_picklist': 'CancelledPutaway',
                    'sales_return' : 'SalesReturnPutaway',
                    'jo' : 'JOPutaway',
                    'picklist' : 'Picklist',
                    'batosa_picking': 'Picklist',
                    'batosa_putaway': 'BATOSAPutaway',
                    'po_grn' : 'QualityCheck',
                    'sr_grn' : 'QualityCheck',
                    'jo_grn' : 'QualityCheck',
                    'jo_picklist': 'Picklist',
                    'st_picklist':'Picklist'
            }

            #Final API Data Framing
            for task_status, tasks_list in iter_list:
                temp_dict = {}
                if isinstance(task_value, list) and task_status == 'open':
                    extra_params = {
                        'filter_key_dict': filter_key_dict,
                        'dashboard_key_dict': dashboard_key_dict,
                        'manual_assignment_order_types': manual_assignment_order_types,
                        'label_based_picking_order_types': label_based_picking_order_types,
                        'hybrid_assignment_order_types': hybrid_assignment_order_types,
                    }
                    temp_dict = self.get_open_tasks_aggregated_dict(temp_dict, task_status, task_value, tasks_list, extra_params=extra_params)
                    temp_dict['priority'] = priority
                    final_response.update({task_type : temp_dict})
                else:
                    if task_type == 'cyclecount':
                        task_ids = list(tasks_list.values_list('id', flat=True))
                    elif task_type == 'picklist':
                        task_ids = self.get_open_picklist_task_ids(tasks_list, manual_assignment_order_types, label_based_picking_order_types, hybrid_assignment_order_types)
                    else:
                        task_ids = list(tasks_list.values_list('task_ref_id', flat=True))

                    temp = self.get_tasks_aggregation(task_type, task_ids, task_status)
                    temp_dict.update({task_status: temp, 'priority' : priority})
                    final_response.update({task_type : temp_dict})
            priority+=1
        packing_switch = self.check_user_permission(roles_perms_list, user_permissions, 'add_orderpackaging')
        if packing_switch:
            final_response.update({'packing': {}})
        manifest_permission = self.check_user_permission(roles_perms_list, user_permissions,'show_create_manifest')
        if manifest_permission:
            final_response.update({'manifest': {}})
        so_qc = self.check_user_permission(roles_perms_list, user_permissions, 'view_so_qc')
        if so_qc:
            final_response.setdefault('quality_control', {}).update({'so_qc': {}})
            final_response.setdefault('quality_control', {}).setdefault('open', {}).update({'SO QC': 0})
        return final_response

    def get_user_task_types(self, user_task_types, task_value, roles_perms_list, user_permissions, task_permission_dict):
        """
        Retrieves the task types for a given user based on their permissions and task values.

        Args:
            user_task_types (dict): A dictionary containing the user's task types.
            task_value (list or str): The task value(s) to be processed.
            roles_perms_list (list): A list of roles and permissions.
            user_permissions (list): A list of user permissions.
            task_permission_dict (dict): A dictionary mapping tasks to their corresponding permissions.

        Returns:
            dict: A dictionary containing the updated user task types.

        """

        task_mapping_dict = {
            'grn': 'putaway',
            'cancelled_picklist': 'putaway',
            'returns': 'putaway',
            'po_grn': 'quality_control',
            'jo_grn': 'quality_control',
            'sr_grn': 'quality_control',
        }
        task_type_mapping_dict = {
            'st_picklist': 'picklist',
            'jo_picklist': 'picklist',
            'batosa_picking': 'replenishment',
            'batosa_putaway': 'replenishment',
        }
        if isinstance(task_value, list):
            for task in task_value:
                task = task_mapping_dict.get(task, task)
                permission = self.check_user_permission(roles_perms_list, user_permissions, task_permission_dict.get(task))
                manual_picker_perm = self.check_user_permission(roles_perms_list, user_permissions,'add_picklistlocation')
                if task == 'picklist' and (permission == True or manual_picker_perm == True):
                    user_task_types.update({'picklist' : ['picklist']})
                if not permission:
                    continue
                if task in task_type_mapping_dict:
                    user_task_types.setdefault(task_type_mapping_dict[task], []).append(task)
                elif task == 'putaway':
                    #'returns' (Old Sales Return Putaway!!Add this Putaway dict to Show in Dashboard)
                    user_task_types.update({'putaway' : ['grn', 'cancelled_picklist', 'sales_return', 'jo']})
                    break
                elif task == 'quality_control':
                    user_task_types.update({'quality_control' : ['po_grn', 'jo_grn', 'sr_grn']})
                    break

        elif isinstance(task_value, str):
            permission = self.check_user_permission(roles_perms_list, user_permissions, task_permission_dict.get(task_value))
            if permission:
                user_task_types.update({task_value : task_value })

        return user_task_types


    def prepare_task_filters_and_open_tasks(self, tasks, user_task_types, cycletasks, putaway_tasks, batosa_picking, batosa_putaway):
        """
        Prepare task filters and open tasks based on the given parameters.

        Args:
            tasks (QuerySet): The queryset of tasks.
            user_task_types (list): The list of user task types.
            cycletasks (QuerySet): The queryset of cycle tasks.
            putaway_tasks (QuerySet): The queryset of putaway tasks.
            batosa_picking (QuerySet): The queryset of batosa picking tasks.
            batosa_putaway (QuerySet): The queryset of batosa putaway tasks.

        Returns:
            tuple: A tuple containing the following:
                - open_tasks_dict (dict): A dictionary containing open tasks for different task types.
                - pick_filter_params (dict): A dictionary containing pick filter parameters.
                - qc_filter_params (dict): A dictionary containing quality control filter parameters.
                - open_tasks (QuerySet): The queryset of open tasks.
        """

        open_tasks_dict, pick_filter_params, qc_filter_params = {}, {}, {}
        open_tasks = tasks.filter(employee=None, status = False)

        if 'cyclecount' in user_task_types:
            employee_id = EmployeeMaster.objects.filter(user=self.request.user.id).values_list('id', flat=True)
            cycle_open_tasks = self.get_cycle_tasks_data(cycletasks, employee_id)
            open_tasks_dict.update({'cyclecount' : cycle_open_tasks})

        if 'putaway' in user_task_types:
            staging_filter = {}
            if self.misc_dict.get('enable_inbound_staging_lanes') == 'true':
                stock_data = list(StockDetail.objects.filter(
                    receipt_type='grn_packing', quantity__gt=0, location__zone__storage_type='PUT', sku__user=self.admin_user.id
                    ).values_list('receipt_number', flat=True))
                staging_filter['task_ref_id__in'] = stock_data
                cancelled_loc_ids = list(putaway_tasks.filter(employee=None, status = False, task_ref_type = 'CancelledPutaway').values_list('task_ref_id', flat=True))
                staging_filter['task_ref_id__in'].extend(cancelled_loc_ids)
            putaway_open_tasks = putaway_tasks.filter(employee=None, status = False, **staging_filter)
            open_tasks_dict.update({'putaway' : putaway_open_tasks})

        if 'replenishment' in user_task_types:
            batosa_picking_open_tasks = batosa_picking.filter(status=False)
            batosa_putaway_open_tasks = batosa_putaway.filter(status=False)
            open_tasks_dict.update({'replenishment' : {'batosa_picking': batosa_picking_open_tasks,'batosa_putaway': batosa_putaway_open_tasks}})

        if 'quality_control' in user_task_types:
            inbound_staging_lanes = self.misc_dict.get('inbound_staging_lanes', '')

            staging_lanes_mapping = INBOUND_STAGING_LANES_MAPPING
            qc_staging = staging_lanes_mapping.get('qc', {}).get('name', '')
            qc_filter_params = {'task_ref_type' : 'QualityCheck'}
            if 'qc' in inbound_staging_lanes.split(','):
                pending_jo_qc_dict = fetch_pending_qc_quantity(qc_staging, 'jo_grn', self.admin_user.id)
                jo_transact_ids = [each[0] for each in list(pending_jo_qc_dict.keys())]
                pending_po_qc_dict = fetch_pending_qc_quantity(qc_staging, 'po_grn', self.admin_user.id)
                po_transact_ids = [each[0] for each in list(pending_po_qc_dict.keys())]
                transact_ids = jo_transact_ids + po_transact_ids
                qc_ids = list(QualityControl.objects.filter(transaction_id__in=transact_ids).values_list('id', flat=True))
                qc_filter_params['task_ref_id__in'] = qc_ids
            qc_open_tasks = open_tasks.filter(**qc_filter_params)
            open_tasks_dict.update({'quality_control' : qc_open_tasks})

        if 'picklist' in user_task_types:
            if self.cluster:
                cluster_filters = {
                    'user_id': self.admin_user.id,
                    'reserved_quantity__gt': 0,
                    'status': 'open',
                }
                picklist_query = Picklist.objects.filter(**cluster_filters)
            if self.group_type:
                pick_filter_params.update({'group_type__in':self.group_type})
            if self.order_type:
                pick_filter_params.update({'order_type__in':self.order_type})
            if self.cluster:
                reference_numbers = list(picklist_query.filter(json_data__cluster_name=self.cluster).values_list('picklist_number', flat=True).distinct())
                pick_filter_params.update({'reference_number__in':reference_numbers})
            if pick_filter_params:
                open_tasks = open_tasks.filter(**pick_filter_params)

        return open_tasks_dict, pick_filter_params, qc_filter_params, open_tasks


    def get_all_running_closed_tasks(self, task_type, aggregated, iter_list, tasks, putaway_tasks, cycletasks, batosa_picking, batosa_putaway, pick_filter_params):
        """
        Get all running and closed tasks based on the task type.

        Args:
            task_type (str): The type of task. Possible values are 'putaway', 'cyclecount', 'replenishment', or any other value.
            aggregated (bool): Flag indicating whether the tasks should be aggregated.
            iter_list (list): The list to store the task iterations.
            tasks (QuerySet): The queryset of all tasks.
            putaway_tasks (QuerySet): The queryset of putaway tasks.
            cycletasks (QuerySet): The queryset of cycle count tasks.
            batosa_picking (QuerySet): The queryset of batosa picking tasks.
            batosa_putaway (QuerySet): The queryset of batosa putaway tasks.
            pick_filter_params (dict): Additional filter parameters for running tasks.

        Returns:
            list: The updated iter_list with all the task iterations.
        """

        if aggregated:
            return iter_list
        if task_type == 'putaway':
            filter_params = {}
            running_tasks = putaway_tasks.filter(status=False).exclude(employee=None)
            closed_tasks = putaway_tasks.filter(status=1)
            if filter_params:
                running_tasks = running_tasks.filter(**filter_params)
            iter_list+=[('all', tasks), ('running',running_tasks), ('closed',closed_tasks)]

        elif task_type == 'cyclecount':
            filter_params = {}
            running_tasks = cycletasks.filter(status=1).exclude(employee=None)
            closed_tasks = cycletasks.filter(status__in =['checked', 'completed'])
            if filter_params:
                running_tasks = running_tasks.filter(**filter_params)
            iter_list+=[('all', cycletasks), ('running',running_tasks), ('closed',closed_tasks)]

        elif task_type == 'replenishment':
            batosa_picking_running_tasks = batosa_picking.filter(status=False).exclude(employee=None)
            batosa_putaway_running_tasks = batosa_putaway.filter(status=False).exclude(employee=None)
            batosa_picking_closed_tasks = batosa_picking.filter(status=1)
            batosa_putaway_closed_tasks = batosa_putaway.filter(status=1)
            iter_list+=[('all', batosa_picking), ('running', batosa_picking_running_tasks), ('closed', batosa_picking_closed_tasks)]
            iter_list+=[('all', batosa_putaway), ('running', batosa_putaway_running_tasks), ('closed', batosa_putaway_closed_tasks)]
        else:
            running_tasks = tasks.filter(status=False).exclude(employee=None)
            closed_tasks = tasks.filter(status=1)
            if pick_filter_params:
                running_tasks = running_tasks.filter(**pick_filter_params)
            iter_list+=[('all', tasks), ('running',running_tasks), ('closed',closed_tasks)]

        return iter_list

    def get_st_manual_picklist_task_ids(self, manual_assignment_order_types, tasks_list, filter_key_dict, task, filter_dict, exclude_dict, manual_task_ids, hybrid_assignment_order_types):
        """
        Get the task IDs for manual picklists based on the given parameters.

        Parameters:
        - manual_assignment_order_types (list): List of order types for manual assignment.
        - tasks_list (QuerySet): QuerySet of tasks.
        - filter_key_dict (dict): Dictionary containing filter keys for tasks.
        - task (str): Task type.
        - filter_dict (dict): Dictionary containing additional filters for tasks.
        - exclude_dict (dict): Dictionary containing exclude filters for tasks.
        - manual_task_ids (list): List of task IDs for manual picklists.
        - hybrid_assignment_order_types (list): List of order types for hybrid assignment.

        Returns:
        - exclude_dict (dict): Updated exclude filters.
        - manual_task_ids (list): List of task IDs for manual picklists.
        """
        if 'STOCKTRANSFER' in manual_assignment_order_types or 'STOCKTRANSFER' in hybrid_assignment_order_types:
            manual_assignment_filters = {
                'warehouse_id': self.admin_user.id,
                'reference_type': 'picklist',
                'employee__user': self.request.user.id,
                'status': 0
            }
            if self.zone_wise_manual_assignment and self.group_type:
                manual_assignment_filters['zones__in'] = self.group_type
            manual_picklists = list(ManualAssignment.objects.filter(**manual_assignment_filters).values_list('reference_number', flat=True))
            manual_task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task], reference_number__in=manual_picklists, **filter_dict).values_list('task_ref_id', flat=True))
            exclude_dict['order_type__in'].extend(manual_assignment_order_types)
        return exclude_dict, manual_task_ids

    def get_open_tasks_aggregated_dict(self, temp_dict, task_status, task_value, tasks_list, extra_params=None):
        """
        Get the aggregated dictionary of open tasks.

        Args:
            temp_dict (dict): The dictionary to store the aggregated tasks.
            task_status (str): The status of the tasks.
            task_value (list): The list of task values.
            tasks_list (list): The list of tasks.
            extra_params (dict, optional): Additional parameters. Defaults to None.

        Returns:
            dict: The aggregated dictionary of open tasks.
        """
        if extra_params is None:
            extra_params = {}
        status_count = ''
        task_ids = []
        filter_key_dict = extra_params.get('filter_key_dict', {})
        dashboard_key_dict = extra_params.get('dashboard_key_dict', {})
        manual_assignment_order_types = extra_params.get('manual_assignment_order_types', [])
        label_based_picking_order_types = extra_params.get('label_based_picking_order_types', [])
        hybrid_assignment_order_types = extra_params.get('hybrid_assignment_order_types', [])

        temp_dict[task_status] = {}
        manual_assignment_filters = {
            'warehouse_id': self.admin_user.id,
            'reference_type': 'picklist',
            'status': 0
        }
        if self.zone_wise_manual_assignment and self.group_type:
            manual_assignment_filters['zones__in'] = self.group_type
        for task in task_value:
            if task in ['batosa_picking', 'batosa_putaway']:
                task_ids = list(tasks_list.get(task).filter(task_ref_type=filter_key_dict[task]).values_list('task_ref_id', flat=True))
            else:
                task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task]).values_list('task_ref_id', flat=True))
            exclude_dict = {}
            filter_dict = {}
            hybrid_picklists = []
            if task == 'picklist':
                manual_task_ids = []
                exclude_dict= {'order_type__in':['STOCKTRANSFER', 'ST'] + BACKFLUSH_JO_TYPES + label_based_picking_order_types}
                status_count = 'open'
                if manual_assignment_order_types or hybrid_assignment_order_types:
                    manual_picklists = list(ManualAssignment.objects.filter(employee__user=self.request.user.id, **manual_assignment_filters).values_list('reference_number',flat=True))
                    manual_task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task],reference_number__in=manual_picklists).exclude(**exclude_dict).values_list('task_ref_id', flat=True))
                    exclude_dict['order_type__in'].extend(manual_assignment_order_types)
                if hybrid_assignment_order_types:
                    hybrid_picklists = list(ManualAssignment.objects.filter(**manual_assignment_filters).values_list('reference_number',flat=True).distinct())
                if not ('all' in manual_assignment_order_types or 'all' in label_based_picking_order_types):
                    task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task]).exclude(**exclude_dict).exclude(reference_number__in=hybrid_picklists).values_list('task_ref_id', flat=True))
                task_ids.extend(manual_task_ids)
            if task == 'st_picklist':
                manual_task_ids  = []
                filter_dict = {'order_type':'STOCKTRANSFER'}
                status_count = 'open'
                task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task], **filter_dict).values_list('task_ref_id', flat=True))
                exclude_dict['order_type__in'] = label_based_picking_order_types
                exclude_dict, manual_task_ids = self.get_st_manual_picklist_task_ids(manual_assignment_order_types, tasks_list, filter_key_dict, task, filter_dict, exclude_dict, manual_task_ids, hybrid_assignment_order_types)
                if hybrid_assignment_order_types:
                    hybrid_picklists = list(ManualAssignment.objects.filter(**manual_assignment_filters).values_list('reference_number',flat=True).distinct())
                if not ('all' in manual_assignment_order_types or 'all' in label_based_picking_order_types):
                    task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task],**filter_dict).exclude(**exclude_dict).exclude(reference_number__in=hybrid_picklists).values_list('task_ref_id', flat=True))
                task_ids.extend(manual_task_ids)
            if task == 'jo_picklist':
                task_ids = []
                filter_dict = {'order_type__in': JO_TYPES}
                status_count = 'open'
                exclude_dict = {'order_type__in': label_based_picking_order_types}
                if manual_assignment_order_types or hybrid_assignment_order_types:
                    manual_picklists = list(ManualAssignment.objects.filter(employee__user=self.request.user.id, **manual_assignment_filters).values_list('reference_number',flat=True))
                    manual_task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task],reference_number__in=manual_picklists,**filter_dict).values_list('task_ref_id', flat=True))
                    exclude_dict['order_type__in'].extend(manual_assignment_order_types)
                if hybrid_assignment_order_types:
                    hybrid_picklists = list(ManualAssignment.objects.filter(**manual_assignment_filters).values_list('reference_number',flat=True).distinct())
                if not ('all' in manual_assignment_order_types or 'all' in label_based_picking_order_types):
                    task_ids = list(tasks_list.filter(task_ref_type=filter_key_dict[task],**filter_dict).exclude(**exclude_dict).exclude(reference_number__in=hybrid_picklists).values_list('task_ref_id', flat=True))
                task_ids.extend(manual_task_ids)
            aggr_dict = self.get_tasks_aggregation(task, task_ids, status_count)
            temp_dict[task] = {task_status: aggr_dict}
            if task == 'picklist' or task == 'st_picklist':
                temp_dict[task_status].update({dashboard_key_dict[task] : aggr_dict['picklist']})
            elif task in ['batosa_picking', 'batosa_putaway']:
                temp_dict[task_status].update({dashboard_key_dict[task] : aggr_dict['SKU']})
            else:
                temp_dict[task_status].update({dashboard_key_dict[task] : aggr_dict['orders']})
        return temp_dict

    def get_open_picklist_task_ids(self, tasks_list, manual_assignment_order_types, label_based_picking_order_types, hybrid_assignment_order_types):
        """
        Returns a list of open picklist task IDs.

        Args:
            tasks_list (QuerySet): A queryset of tasks.
            manual_assignment_order_types (bool): A flag indicating whether manual assignment order types are present.

        Returns:
            list: A list of task IDs.

        """

        task_ids, manual_ref_ids, hybrid_picklists = [], [], []
        exclude_dict = {'order_type__in': label_based_picking_order_types}
        manual_assignment_filters = {
            'warehouse_id': self.admin_user.id,
            'reference_type': 'picklist',
            'status': 0
        }
        if self.zone_wise_manual_assignment and self.group_type:
            manual_assignment_filters['zones__in'] = self.group_type
        if manual_assignment_order_types or hybrid_assignment_order_types:
            manual_picklists = list(ManualAssignment.objects.filter(employee__user=self.request.user.id, **manual_assignment_filters).values_list('reference_number', flat=True))
            if manual_picklists:
                manual_ref_ids = list(Picklist.objects.filter(picklist_number__in=manual_picklists,user_id=self.admin_user.id,status='open').values_list('id',flat=True))
            exclude_dict['order_type__in'].extend(manual_assignment_order_types)
        if hybrid_assignment_order_types:
            hybrid_picklists = list(ManualAssignment.objects.filter(**manual_assignment_filters).values_list('reference_number', flat=True).distinct())
        if not ('all' in manual_assignment_order_types or 'all' in label_based_picking_order_types):
            task_ids = list(tasks_list.exclude(**exclude_dict).exclude(reference_number__in=hybrid_picklists).values_list('task_ref_id', flat=True))
        task_ids.extend(manual_ref_ids)
        return task_ids

    def user_exists_check(self, user):
        return EmployeeMaster.objects.filter(id=user).exists()

    def task_exists_check(self, tasks, task):
        return tasks.filter(task_ref_id=task)

    def get_pending_task_details(
            self, request_user, admin_user, search_params, task_type, cycle_tasks, tasks, batosa_picking, nte_picking, nte_putaway
        ):
        final_response = []
        task_ref_type = 'Picklist' if task_type in ['picklist', 'batosa_picking', 'nte_picking'] else ''
        if task_type == 'cyclecount':
            tasks = cycle_tasks.filter(status=1)
        elif task_type == 'batosa_picking':
            tasks = batosa_picking.filter(status=False)
        elif task_type == 'nte_picking':
            tasks = nte_picking.filter(status=False)
        elif task_type == 'all':
            if not tasks.exists():
                tasks = cycle_tasks.filter(status=1)
        if not self.fast_putaway:
            tasks = tasks.filter(employee=request_user.employee)
        if task_ref_type:
            tasks = tasks.filter(task_ref_type=task_ref_type)
        if tasks.exists():
            filter_params = {}
            if "grn_number" in search_params:
                filter_params["reference_number"] = search_params["grn_number"]
            elif "return_id" in search_params:
                filter_params["reference_number"] = search_params["return_id"]
            elif "sku_code" in  search_params:
                filter_params["carton_sku__icontains"] = search_params["sku_code"]
            elif "ean_numbers" in search_params:
                sku_codes = EANNumbers.objects.filter(
                    ean_number__icontains=str(search_params["ean_numbers"]),
                    sku__user=admin_user.id
                ).values_list('sku__sku_code', flat=True).distinct()
                if not sku_codes:
                    return []
                filter_params["carton_sku__in"] = sku_codes
            if filter_params:
                tasks= tasks.filter(**filter_params)
            final_response = queryset_to_list(self.request, tasks, data=True, task_type=(task_ref_type or task_type), warehouse_id=admin_user.id, sequence=self.sequence)
        return final_response

    def get_task_info(self, user, admin_user, tasks, task_type, offset, limit):
        '''
        Get task info
        '''
        error = ''
        employee = EmployeeMaster.objects.get(id = user)
        if task_type != 'cyclecount':
            filters_dict = {'task_ref_type': task_type}
            if user :
                filters_dict.update({'employee_id': user})
            tasks = tasks.filter(**filters_dict)
            final_response = self.get_userwise_tasks(tasks, user, employee.user.username, admin_user.id, offset=offset, limit=limit)
        else:
            tasks = tasks.filter(employee_id = user).order_by('-updation_date')
            final_response = get_cyclecount_data(self.request, tasks, employee_id = employee.id,warehouse_id = admin_user.id, offset=offset, limit=limit)
        if not tasks:
            error = "No Tasks Found"
            final_response = []
        return final_response, error

    def get_task_details_for_user(self, search_params, tasks, cycle_tasks, putaway_tasks, batosa_picking, batosa_putaway, nte_picking, nte_putaway, is_picklist_tasks):
        '''
        Get task details
        '''
        final_response, error = [], ""
        summary = self.request.GET.get('summary' , 'false')
        self.task_sub_type = self.request.GET.get('task_sub_type', '')
        if self.dashboard_check:
            #Dashboard API
            final_response = self.get_aggreagated_dict(tasks, cycle_tasks, aggregated=True, putaway_tasks=putaway_tasks, \
                        batosa_picking= batosa_picking, batosa_putaway = batosa_putaway, admin_user=self.admin_user)
            return final_response, error

        elif self.status == 'pending' and self.user and (
            tasks.exists() or cycle_tasks.exists() or batosa_picking.exists() or nte_picking.exists()
        ):
            #Get Pending Tasks
            if self.picklist_number:
                tasks = tasks.filter(reference_number=self.picklist_number)
            if is_picklist_tasks:
                tasks = tasks.filter(status=False)
            if self.user_exists_check(self.user):
                final_response = self.get_pending_task_details(
                    self.request_user, self.admin_user, search_params, self.task_type, cycle_tasks, tasks, batosa_picking, 
                    nte_picking, nte_putaway
                )
            else:
                error = user_not_exists_const
            return final_response, error

        elif ((self.user and self.task_type) or summary == "true") and tasks.exists():
            #Picking Summary or User Wise History
            if self.picklist_number and self.task_type == 'Picklist':
                tasks = tasks.filter(reference_number=self.picklist_number)
            if self.user_exists_check(self.user):
                if summary == 'true':
                    self.task_type = 'Picklist'       
                final_response, error = self.get_task_info(self.user, self.admin_user, tasks, self.task_type, self.offset, self.limit)
                if not final_response:
                    error = "All Items are Unfulfilled"
            else:
                error = user_not_exists_const
            return final_response, error
        elif self.user and tasks.exists():
            if self.user_exists_check(self.user):
                tasks = tasks.filter(employee__id=self.user)
                final_response = self.get_aggreagated_dict(tasks, cycle_tasks, putaway_tasks=putaway_tasks,admin_user=self.admin_user)
            else:
                error = user_not_exists_const
            return final_response, error
        return final_response, error

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_user = self.request.user
        self.admin_user = self.warehouse
        self.user = self.request.GET.get('user' , None)
        self.status = self.request.GET.get('status' , None)
        self.task_type = self.request.GET.get('type' , None)
        self.line_level = self.request.GET.get('line_level','false')
        self.picklist_number = self.request.GET.get('picklist_number','')
        # self.decimal_limit = get_decimal_value(self.warehouse.id)
        group_type_str = self.request.GET.get('zone', "")
        self.group_type = group_type_str.split(",") if group_type_str else []
        order_type_str = self.request.GET.get('order_type','')
        search_params = self.request.GET
        self.order_type = order_type_str.split(",") if order_type_str else []
        self.cluster = self.request.GET.get('cluster_name', '')
        self.sequence = int(self.request.GET.get('sequence', False))
        try:
            self.fast_putaway = int(self.request.GET.get('fast_putaway', 0))
        except Exception:
            self.fast_putaway = 0
        self.task = self.request.GET.get('task' , None)
        try:
            self.limit = int(self.request.GET.get('limit' , 50))
            self.offset = int(self.request.GET.get('offset' , 0))
        except Exception:
            self.limit = 50
            self.offset = 0
        self.offset = self.offset*self.limit
        self.dashboard_check = self.request.GET.get("aggregated" , False)

        # Initializing empty querysets to allow the use of .exists() directly
        cycle_tasks = CycleCount.objects.none()
        batosa_picking = nte_picking = BAtoSADetail.objects.none()

        putaway_tasks, batosa_putaway, nte_putaway, is_picklist_tasks = None, None, None, False

        misc_types = [
            'inbound_staging_lanes', 'enable_inbound_staging_lanes', 'subzone_mapping', 'cycle_time_in_picking',
            'zone_wise_manual_assignment'
        ]
        self.misc_dict = get_multiple_misc_values(misc_types, self.admin_user.id)
        
        self.zone_wise_manual_assignment = bool(self.misc_dict.get('zone_wise_manual_assignment', 'false') == 'true')

        self.time_zone = get_user_time_zone(self.admin_user)

        if self.task_type and self.task_type.lower().endswith("putaway"):
            filter_dict = {'warehouse': self.admin_user, 'status': False}
            if self.task_type == "batosa_putaway":
                filter_dict.update({'task_ref_type': 'BATOSAPutaway'})
                if self.request.GET.get('replenishment_drop'):
                    filter_dict.update({'employee_id': self.user})
            if self.task_type == "nte_putaway":
                filter_dict.update({'task_ref_type': 'NTEPutaway'})
                if self.request.GET.get('replenishment_drop'):
                    filter_dict.update({'employee_id': self.user})

            tasks = PutawayTask.objects.filter(**filter_dict)

        elif self.task_type and self.task_type.lower()=="cyclecount":
            tasks = CycleCount.objects.filter(sku__user=self.admin_user.id)
        else:
            tasks = TaskMaster.objects.filter(warehouse=self.admin_user).exclude(order_type__in =['BA_TO_SA','ST','NTE'])
            batosa_picking = TaskMaster.objects.filter(warehouse=self.admin_user, order_type = 'BA_TO_SA')
            batosa_putaway = PutawayTask.objects.filter(warehouse=self.admin_user, task_ref_type = 'BATOSAPutaway', status=False)
            nte_picking = TaskMaster.objects.filter(warehouse=self.admin_user, order_type = 'NTE')
            nte_putaway = PutawayTask.objects.filter(warehouse=self.admin_user, task_ref_type = 'NTEPutaway', status=False)
            cycle_tasks = CycleCount.objects.filter(sku__user=self.admin_user.id)
            putaway_tasks = PutawayTask.objects.filter(warehouse=self.admin_user, status=False).exclude(task_ref_type__in = ['BATOSAPutaway','NTEPutaway'])
            is_picklist_tasks = True
        final_response, error = self.get_task_details_for_user(search_params, tasks, cycle_tasks, putaway_tasks, batosa_picking, batosa_putaway, nte_picking, nte_putaway, is_picklist_tasks)
        if error:
            return JsonResponse({'errors':error},status=400)
        return JsonResponse(final_response, safe=False)

    def assign_related_tasks_to_user(self, task_id, employee=None):
        log.info("Task ID Received To Attach to User Task %s" % task_id)
        tasks = TaskMaster.objects.filter(id=task_id)
        task = tasks[0]
        cur_employee = employee
        pick_sequence = 0
        employee, old_tasks, zones, allpickids = None, False, None, []
        allpickids = list(Picklist.objects.filter(picklist_number=task.reference_number, user=task.warehouse_id)\
                        .values_list('id', flat=True))
        completed_tasks = TaskMaster.objects.filter(task_ref_id__in=allpickids, employee__isnull = False,
                task_ref_type="Picklist").order_by("-updation_date").values('employee_id','zones','status', 'priority', 'task_ref_id')

        for row in completed_tasks:
            zones = str(row['zones']).split(",") if row['zones'] else ""
            if zones and task.group_type not in zones:
                continue
            if ((row['employee_id']==cur_employee.id) or (row['employee_id']!=cur_employee.id and not row['status'])):
                employee = row['employee_id']
                old_tasks = True
                if row['status'] and row['priority'] and not pick_sequence:
                     pick_sequence = Picklist.objects.filter(id=row['task_ref_id']).values_list('location__pick_sequence', flat=True).first()
                break
        if pick_sequence:
            task_ref_ids = [task.task_ref_id] + list(completed_tasks.filter(status=False).values_list('task_ref_id', flat=True))
            combined_ids = list(
                            Picklist.objects.filter(id__in = task_ref_ids, picklist_number=task.reference_number, user=task.warehouse_id).annotate(
                                sequence_order=Case(
                                    When(location__pick_sequence__gte=pick_sequence, then=Value(0)),
                                    default=Value(1),
                                    output_field=IntegerField()
                                )
                            )
                            .order_by('sequence_order', 'location__pick_sequence')
                            .values_list('id', flat=True)
                            .distinct()
                        )
            for priority, picklist_id in enumerate(combined_ids, start=1):
                if picklist_id:
                    TaskMaster.objects.filter(
                        task_ref_id=picklist_id,
                        warehouse_id=task.warehouse_id,
                        task_ref_type="Picklist"
                    ).update(priority=priority)
        if employee:
            self.assign_tasks_to_user(tasks, employee, old_tasks=old_tasks, zones=zones)

    def assign_tasks_to_user(self, tasks, employee_id, old_tasks=False, zones=None):
        if old_tasks and zones:
            tasks = tasks.filter(group_type__in=zones)
            zones =  ','.join(map(str, zones))
        tasks.update(employee_id=employee_id,start_time=datetime.datetime.now(), zones=zones)
        return True

    def get_user_average_picking_time(self, user, warehouse, current_task_ids):

        now = timezone.now().astimezone(pytz.timezone('UTC'))
        start_of_today = now.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_today = start_of_today + timedelta(days=1)

        # Annotate queryset
        filters = {
            'employee': user,
            'warehouse': warehouse,
            'task_ref_type': 'Picklist',
            'start_time__isnull' : False,
            'updation_date__isnull' : False,
            'start_time__lt': end_of_today,
            'updation_date__gt': start_of_today,
            'status': True
        }
        picking_data = TaskMaster.objects.filter(start_time__lt=F('updation_date')).filter(**filters).exclude(order_type__in=['BA_TO_SA', 'ST', 'NTE']).exclude(task_ref_id__in=current_task_ids)
        pick_ids = set(picking_data.values_list('task_ref_id',flat=True))
        average_picking_time = picking_data.values('reference_number').annotate(
            # Calculate the portion of start_time that is today
            start_time_today=Min(Case(
                When(start_time__lt=start_of_today, then=start_of_today),
                default=F('start_time'),
                output_field=DurationField()
            )),
            # Calculate the portion of updation_time that is today
            end_time_today=Max(Case(
                When(updation_date__gt=end_of_today, then=end_of_today),
                default=F('updation_date'),
                output_field=DurationField()
            ))
        ).annotate(
            # Calculate today's time duration
            today_duration=ExpressionWrapper(
                F('end_time_today') - F('start_time_today'),
                output_field=DurationField()
            )
        ).aggregate(
            total_picking_time=Sum('today_duration')
        )
        picked_lines = len(set(Picklist.objects.filter(id__in=pick_ids).values_list('reference_id','sku_id','stock__batch_detail_id').distinct()))
        return round((average_picking_time['total_picking_time'].total_seconds())/picked_lines, 2) if picked_lines and average_picking_time['total_picking_time'] else 0

class lmstaskscreate(WMSListView):

    def get_task_informations(self, task_type, task_assign, tasks):
        '''
        Get task_ref_id, task_ref_type, task_warehouse_id, reference_number
        '''
        task = task_assign.__dict__
        if task_type != 'cyclecount':
            task_ref_id = task['task_ref_id']
            task_ref_type = task['task_ref_type']
            task_warehouse_id = task['warehouse_id']
            reference_number = [task['reference_number']]
            order_type = task['order_type']
            if order_type in ['BA_TO_SA', 'NTE']:
                tasks_count = get_misc_value('batosa_assign_task_count', task_warehouse_id)
                try:
                    tasks_count = int(tasks_count)
                except Exception:
                    tasks_count = 1
                reference_number = list(tasks.values_list('reference_number', flat = True))[0: tasks_count]
                reference_number = set(reference_number)
        else:
            task_ref_id = task['id']
            task_ref_type = task_type
            task_warehouse_id = task_assign.sku.user
            reference_number = task_assign.cycle
        return task_ref_id, task_ref_type, task_warehouse_id, reference_number

    def assign_picklist_task_to_user(self, tasks, task_assign, task_ref_id, task_warehouse_id, reference_number, employee, running_task):
        '''
        Assign picklist task to user
        '''
        if self.misc_dict.get('sku_picking') == 'true':
            task_assign.employee = employee
            task_assign.start_time = datetime.datetime.now()
            task_assign.save()
            return True, reference_number
        else:
            log.info("Assigning Of task of ID :: %s" % task_ref_id)
            if not running_task.exists():
                if self.zone_wise_manual_assignment:
                    tasks = tasks.filter(reference_number__in=reference_number)
                else:
                    tasks = TaskMaster.objects.filter(
                        reference_number__in = reference_number,
                        warehouse_id = task_warehouse_id,
                        employee=None,
                        status=False
                    )
                zones =''
                filter_params = {}
                if self.cluster or self.sequence:
                    cluster_filters = {
                        'user_id': task_warehouse_id,
                        'reserved_quantity__gt': 0,
                        'status': 'open',
                        'picklist_number__in': reference_number
                    }
                    picklist_query = Picklist.objects.filter(**cluster_filters)
                if self.group_type:
                    filter_params['group_type__in'] = self.group_type
                    zones =  ','.join(map(str, self.group_type))
                if self.order_type:
                    filter_params['order_type__in'] = self.order_type
                if self.cluster:
                    filter_params['reference_number__in'] = list(picklist_query.filter(json_data__cluster_name=self.cluster).values_list('picklist_number', flat=True).distinct())
                if self.sequence:
                    if self.cluster:
                        picklist_query = picklist_query.filter(json_data__cluster_name=self.cluster)
                    combined_ids = list(
                            picklist_query.annotate(
                                sequence_order=Case(
                                    When(location__pick_sequence__gte=self.sequence, then=Value(0)),
                                    default=Value(1),
                                    output_field=IntegerField()
                                )
                            )
                            .order_by('sequence_order', 'location__pick_sequence')
                            .values_list('id', flat=True)
                            .distinct()
                        )
                    for priority, picklist_id in enumerate(combined_ids, start=1):
                        if picklist_id:
                            TaskMaster.objects.filter(
                                task_ref_id=picklist_id,
                                warehouse_id=task_warehouse_id,
                                task_ref_type="Picklist"
                            ).update(priority=priority)
                if filter_params:
                    tasks = tasks.filter(**filter_params)
                tasks.update(employee=employee,start_time=datetime.datetime.now(), zones=zones)
                if (task_assign.order_type in self.manual_assignment_order_types or 'all' in self.manual_assignment_order_types) or (task_assign.order_type in self.hybrid_assignment_order_types or 'all' in self.hybrid_assignment_order_types):
                    manual_assignment_filters = {
                        'warehouse_id': task_warehouse_id,
                        'reference_type': 'picklist',
                        'employee__user': self.request.user.id,
                        'status': 0,
                        'reference_number': reference_number
                    }
                    if self.zone_wise_manual_assignment and self.group_type:
                        manual_assignment_filters['zones__in'] = self.group_type
                    manual_picklists = ManualAssignment.objects.filter(**manual_assignment_filters)
                    if manual_picklists.exists():
                        manual_picklists.update(status=2)
                return True, reference_number
        return False, reference_number

    def assign_tasks_to_user(self, tasks, employee, running_task, task_type=''):

        task_assign = tasks.first()
        task_ref_id, task_ref_type, task_warehouse_id, reference_number = self.get_task_informations(task_type, task_assign, tasks)

        if task_ref_type == "Picklist" and reference_number:
            return self.assign_picklist_task_to_user(tasks, task_assign, task_ref_id, task_warehouse_id, reference_number, employee, running_task)

        elif task_ref_type == 'cyclecount' and reference_number:
            cycle_filters = {'sku__user' : task_warehouse_id, 'status':1, 'employee': None}
            if self.group_type:
                if self.misc_dict.get('subzone_mapping', 'false') != 'true':
                    cycle_filters.update({'location__zone__zone__in': self.group_type})
                else:
                    cycle_filters.update({'location__sub_zone__zone__in': self.group_type}) 
            cycle_count_objs = CycleCount.objects.filter(**cycle_filters)
            if cycle_count_objs.exists():
                miscs = ['assign_task_count', 'assign_task_count_type']
                misc_dict = get_multiple_misc_values(miscs, task_warehouse_id)
                tasks_count = misc_dict.get('assign_task_count', 1)
                task_count_type = misc_dict.get('assign_task_count_type', 'cycle')
                try:
                    tasks_count = int(tasks_count)
                except Exception:
                    tasks_count = 1
                order_by_list = ['-json_data__priority', 'location__pick_sequence', 'cycle'] 
                value_key, filter_key = 'cycle', 'cycle__in'
                if task_count_type == 'sku':
                    value_key, filter_key = 'sku', 'sku__in'
                get_cycle_tasks = list(cycle_count_objs.values_list(value_key, flat=True).order_by(*order_by_list).distinct())
                unique_value_keys = set()
                for cycle_task in get_cycle_tasks:
                    unique_value_keys.add(cycle_task)
                    if len(unique_value_keys) == tasks_count:
                        break
                
                log.info("Assigning Of task of ID :: %s" % task_ref_id)
                if not running_task.exists():
                    if tasks_count:
                        filter_dict = {filter_key: unique_value_keys}
                        tasks = cycle_count_objs.filter(**filter_dict)
                        tasks.update(employee=employee, start_time=datetime.datetime.now())
                    return True, reference_number

        return False, reference_number

    def check_picklist_available_for_assigned_task(self, running_task):
        '''
        Check picklist available for assigned task
        '''
        task_ref_ids = list(running_task.values_list('task_ref_id', flat=True))

        picklist_ids_query = Picklist.objects.filter(id__in=task_ref_ids, status__in=["open", "batch_open"], reserved_quantity__gt=0).values_list(Cast(str('id'),output_field=CharField()), flat=True)
        picklist_ids = list(picklist_ids_query)
        invalid_tasks_ids = set(task_ref_ids) - set(picklist_ids)

        if invalid_tasks_ids:
            running_task.filter(task_ref_id__in=invalid_tasks_ids).update(status=1)
            running_task = running_task.filter(status=False)

        return len(invalid_tasks_ids) != len(task_ref_ids)

    def get_task_check(self):
        task_check = False
        if self.task_ref_type in ["picklist", 'st_picklist', 'jo_picklist']:
            picklist_filter_params = {}
            if self.pick_pass_picklist_number:
                picklist_filter_params = {'reference_number': self.pick_pass_picklist_number}
            running_task = TaskMaster.objects.filter(
                    employee=self.request_user.employee, warehouse=self.admin_user, status=False, **picklist_filter_params
                ).exclude(order_type__in = BACKFLUSH_JO_TYPES)
            if running_task.exists():
                task_check = self.check_picklist_available_for_assigned_task(running_task)
        elif self.task_ref_type == "cyclecount":
            running_task = CycleCount.objects.filter(
                employee=self.request_user.employee, sku__user=self.admin_user.id, status=1)
            if running_task.exists():
                task_check= True
        elif self.task_ref_type == "batosa_picking":
            running_task = TaskMaster.objects.filter(
                employee=self.request_user.employee, warehouse=self.admin_user, status=False, order_type = 'BA_TO_SA')
            if running_task.exists():
                task_check= True
        elif self.task_ref_type == "nte_picking":
            running_task = TaskMaster.objects.filter(
                employee=self.request_user.employee, warehouse=self.admin_user, status=False, order_type = 'NTE')
            if running_task.exists():
                task_check= True
        return task_check, running_task

    def get_filtered_pick_ids(self, picklist_filters, manual_assignment_filters):
        """
        Get filtered pick IDs based on the provided filters.
        Args:
            picklist_filters (dict): Filters for the picklist.
            manual_assignment_filters (dict): Filters for manual assignment.
        Returns:
            list: List of filtered pick IDs.
        """
        manual_priority = False
        pick_ids, manual_pick_ids, manual_picklists, hybrid_picklists = [], [], [], []
        exclude_pick_types = {}
        exclude_dict = {'order_type__in': self.label_based_picking_order_types}

        if not self.pick_and_pass_user:
            exclude_pick_types['pick_type'] = 'pick_and_pass'

        # Manual Assigned Picklists
        if self.manual_assignment_order_types or self.hybrid_assignment_order_types:
            picklist_priority = self.misc_dict.get('picklist_priority', '')
            if picklist_priority == 'manual_picker_assignment':
                manual_priority = True
            manual_picklists = list(ManualAssignment.objects
                .filter(**manual_assignment_filters)
                .values_list('reference_number', flat=True)
                .distinct()
            )
            if manual_picklists:
                manual_pick_ids = list(Picklist.objects
                    .filter(**picklist_filters, picklist_number__in=manual_picklists)
                    .exclude(**exclude_pick_types)
                    .values_list('id', flat=True)
                )
            exclude_dict['order_type__in'].extend(self.manual_assignment_order_types)
        # Hybrid Assigned Picklists
        if self.hybrid_assignment_order_types:
            manual_assignment_filters.pop('employee__user', None)
            hybrid_picklists = list(ManualAssignment.objects
                .filter(**manual_assignment_filters)
                .values_list('reference_number', flat=True)
                .distinct()
            )
        # Exclude Manual and Hybrid Assigned Picklists to fetch Auto Assigning Picklists
        if not (manual_priority and manual_pick_ids) and not ('all' in self.manual_assignment_order_types or 'all' in self.label_based_picking_order_types):
            pick_ids = list(Picklist.objects
                .filter(**picklist_filters)
                .exclude(**exclude_dict)
                .exclude(**exclude_pick_types)
                .exclude(picklist_number__in=hybrid_picklists)
                .values_list('id', flat=True)
            )

        pick_ids.extend(manual_pick_ids)
        return pick_ids

    def get_zone_wise_filter_pick_ids(self, picklist_filters, manual_assignment_filters):
        """
        Get filtered pick IDs based on the provided filters.
        Args:
            picklist_filters (dict): Filters for the picklist.
            manual_assignment_filters (dict): Filters for manual assignment.
        Returns:
            list: List of filtered pick IDs.
        """
        manual_priority = False
        pick_ids, manual_pick_ids, manual_picklists, hybrid_picklists = [], [], [], []
        exclude_pick_types = {}
        exclude_dict = {'order_type__in': self.label_based_picking_order_types}

        if not self.pick_and_pass_user:
            exclude_pick_types['pick_type'] = 'pick_and_pass'

        # Manual Assigned Picklists
        assigned_zone = 'location__sub_zone__zone' if self.misc_dict.get('user_sub_zone_mapping', 'false') == 'true' else 'location__zone__zone'
        if self.manual_assignment_order_types or self.hybrid_assignment_order_types:
            picklist_priority = self.misc_dict.get('picklist_priority', '')
            if picklist_priority == 'manual_picker_assignment':
                manual_priority = True
            manual_picklists = dict(ManualAssignment.objects
                .filter(**manual_assignment_filters)
                .values('reference_number').annotate(zones=ArrayAgg('zones', distinct=True, filter=Q(zones__gt='')))
                .values_list('reference_number', 'zones')
            )
            if manual_picklists:
                manual_picklists_data = list(Picklist.objects
                    .filter(**picklist_filters, picklist_number__in=manual_picklists)
                    .exclude(**exclude_pick_types)
                    .values_list('id', 'picklist_number', assigned_zone)
                )
                for pick_id, picklist_number, pick_zone in manual_picklists_data:
                    if manual_picklists.get(str(picklist_number)) and pick_zone not in manual_picklists.get(str(picklist_number)):
                        continue
                    manual_pick_ids.append(pick_id)
            exclude_dict['order_type__in'].extend(self.manual_assignment_order_types)
        # Hybrid Assigned Picklists
        if self.hybrid_assignment_order_types:
            manual_assignment_filters.pop('employee__user', None)
            hybrid_picklists = dict(ManualAssignment.objects
                .filter(**manual_assignment_filters)
                .values('reference_number').annotate(zones=ArrayAgg('zones', distinct=True, filter=Q(zones__gt='')))
                .values_list('reference_number', 'zones')
            )
        # Exclude Manual and Hybrid Assigned Picklists to fetch Auto Assigning Picklists
        if not (manual_priority and manual_pick_ids) and not ('all' in self.manual_assignment_order_types or 'all' in self.label_based_picking_order_types):
            pick_data = list(Picklist.objects
                .filter(**picklist_filters)
                .exclude(**exclude_dict)
                .exclude(**exclude_pick_types)
                .values_list('id', 'picklist_number', assigned_zone)
            )
            for pick_id, picklist_number, pick_zone in pick_data:
                if (not hybrid_picklists.get(str(picklist_number))) or pick_zone in hybrid_picklists.get(str(picklist_number)):
                    continue
                pick_ids.append(pick_id)

        pick_ids.extend(manual_pick_ids)
        return pick_ids

    def get_picklist_tasks(self, tasks, picklist_number):
        """
        Get SO, RTV Picklist tasks.
        """

        filter_params = {}
        manual_assignment_filters = {
            'warehouse_id': self.admin_user.id,
            'reference_type': 'picklist',
            'employee__user': self.request.user.id,
            'status': 0,
        }
        if self.zone_wise_manual_assignment and self.group_type:
            manual_assignment_filters['zones__in'] = self.group_type
        picklist_filters = {
            'status': 'open',
            'reserved_quantity__gt': 0,
            'user_id': self.admin_user.id,
            'reference_model__in': ['OrderDetail', 'ReturnToVendor'],
        }
        if picklist_number:
            manual_assignment_filters['reference_number'] = picklist_number
            picklist_filters['picklist_number'] = picklist_number
        if self.group_type:
            filter_params['group_type__in'] = self.group_type
            if self.misc_dict.get('user_sub_zone_mapping') == 'true':
                picklist_filters['location__sub_zone__zone__in'] = self.group_type
            else:
                picklist_filters['location__zone__zone__in'] = self.group_type
        if self.order_type:
            filter_params['order_type__in'] = self.order_type
            picklist_filters['order_type__in'] = self.order_type
        if self.cluster:
            cluster_filters = {
                'user_id': self.admin_user.id,
                'reserved_quantity__gt': 0,
                'status': 'open',
            }
            picklist_query = Picklist.objects.filter(**cluster_filters)
        if self.cluster:
            picklist_filters['picklist_number__in'] = list(picklist_query.filter(json_data__cluster_name=self.cluster).values_list('picklist_number', flat=True).distinct())

        if self.zone_wise_manual_assignment:
            pick_ids = self.get_zone_wise_filter_pick_ids(picklist_filters, manual_assignment_filters)
        else:
            pick_ids = self.get_filtered_pick_ids(picklist_filters, manual_assignment_filters)
        filter_params['task_ref_id__in'] = pick_ids
        tasks = tasks.exclude(order_type__in = BACKFLUSH_JO_TYPES)
        exclude_params = {'order_type' : 'ST'}
        if filter_params:
            tasks= tasks.filter(**filter_params).exclude(**exclude_params)
        return tasks

    def get_jo_picklist_tasks(self, tasks):
        '''
        Get jo picklist tasks
        '''

        picklist_filters = {
            'status': 'open',
            'reserved_quantity__gt': 0,
            'user_id': self.admin_user.id,
            'reference_model': 'JOMaterial',
        }
        manual_assignment_filters = {
            'warehouse_id': self.admin_user.id,
            'reference_type': 'picklist',
            'employee__user': self.request.user.id,
            'status': 0,
        }

        pick_ids = self.get_filtered_pick_ids(picklist_filters, manual_assignment_filters)
        filter_params = {'order_type__in': JO_TYPES}
        if self.group_type:
            filter_params.update({'group_type__in':self.group_type})
        tasks = tasks.filter(task_ref_id__in=pick_ids, **filter_params)
        return tasks
    
    def get_cycle_count_filter_for_task_assignment(self):
        cycle_count_filter_dict = {}
        if self.group_type:
            if self.misc_dict.get('subzone_mapping', 'false') != 'true':
                cycle_count_filter_dict['location__zone__zone__in'] = self.group_type
            else:
                cycle_count_filter_dict['location__sub_zone__zone__in'] = self.group_type
        return cycle_count_filter_dict

    def get_replenishment_tasks(self, tasks):
        filter_type = 'BA_TO_SA' if self.task_ref_type == 'batosa_picking' else 'NTE'
        subzone_mapping = self.misc_dict.get('subzone_mapping', '')

        #Get Source Filters based on Destination Zone / Subzone Filters
        if filter_type == 'BA_TO_SA' and self.dest_zones:
            pick_ids = get_tasks_by_dest_filters(self.warehouse, subzone_mapping, self.dest_zones)
        else:
            filter_dict = {
                'status' : 'open',
                'reserved_quantity__gt' : 0,
                'user' : self.admin_user.id,
                'order_type' : filter_type
            }
            if self.group_type:
                if self.pick_and_pass_user:
                    filter_dict['location__sub_zone__zone__in'] = self.group_type
                else:
                    filter_dict['location__zone__zone__in'] = self.group_type
            pick_ids=list(
                Picklist.objects.filter(**filter_dict).values_list("id", flat=True)
            )
        
        filter_dict = {'task_ref_id__in': pick_ids, 'order_type': filter_type}

        if self.group_type:
            filter_dict['group_type__in'] = self.group_type
        if self.order_type:
            filter_dict['order_type__in'] = self.order_type
        
        tasks= tasks.filter(**filter_dict)
        return tasks

    def get_tasks_using_request_task_type(self, reference_number= None):
        error, tasks = '', None
        cycle_count_filter_dict = self.get_cycle_count_filter_for_task_assignment()
        try:
            if self.task_ref_type.lower() == "putaway":
                tasks = PutawayTask.objects.filter(
                    warehouse=self.admin_user,
                    status=False,
                    employee=None
                ).select_for_update()
            elif self.task_ref_type.lower() == "cyclecount":
                tasks = CycleCount.objects.filter(
                        sku__user=self.admin_user.id,
                        status=1,
                        employee=None,
                        **cycle_count_filter_dict
                ).select_for_update()
            else:
                filter_task_type = 'Picklist' if self.task_ref_type in ['picklist', 'batosa_picking', 'nte_picking', 'jo_picklist'] else self.task_ref_type
                task_filters = {
                    'task_ref_type': filter_task_type,
                    'warehouse': self.admin_user,
                    'status': False,
                    'employee': None
                }
                if reference_number:
                    task_filters['reference_number'] = reference_number
                tasks = TaskMaster.objects.filter(**task_filters).select_for_update()
                if self.task_ref_type=="picklist":
                    tasks = self.get_picklist_tasks(tasks, reference_number)
                elif self.task_ref_type=="st_picklist":
                    filter_params = {'order_type' : 'ST'}
                    if self.group_type:
                        filter_params.update({'group_type__in':self.group_type})
                    tasks= tasks.filter(**filter_params)
                elif self.task_ref_type == "jo_picklist":
                    tasks = self.get_jo_picklist_tasks(tasks)
                elif self.task_ref_type in ("batosa_picking", 'nte_picking'):
                    tasks = self.get_replenishment_tasks(tasks)
            if not tasks.exists():
                error = no_task_const
        except Exception:
            error = no_task_const
        return tasks, error

    def assign_cyclecount_tasks(self, running_task, response_check, tasks):
        allow_multiple_tasks_ina_location, error = True, ''
        if not allow_multiple_tasks_ina_location:
            ongoing_locations = CycleCount.objects.filter(status=1, employee_isnull=False, sku__user=self.admin_user.id)\
                    .values_list('location__location', flat=True)
            if ongoing_locations:
                tasks = tasks.exclude(location__location__in=ongoing_locations)
        filter_dict = {}
        if self.group_type:
            if self.misc_dict.get('subzone_mapping', 'false') != 'true':
                filter_dict['location__zone__zone__in'] = self.group_type
            else:
                filter_dict['location__sub_zone__zone__in'] = self.group_type
        tasks = tasks.filter(**filter_dict)
        tasks = tasks.order_by('-json_data__priority', 'location__pick_sequence', 'cycle')
        assigned_task, reference_number = self.assign_tasks_to_user(tasks, self.request_user.employee, running_task, 'cyclecount')
        if assigned_task:
            response_check =True
        else:
            if not allow_multiple_tasks_ina_location and ongoing_locations:
                error = 'Cycle Counting is in progress'
            else:
                error = failed_const
        return tasks, assigned_task, reference_number, error, response_check

    def assign_putaway_tasks(self, tasks):
        final_response, error = {}, ''
        po_loc = POLocation.objects.filter(id=self.task_ref_id, status="1").select_for_update()
        quantity = float(self.request_data.get('quantity', 0))
        if po_loc.exists():
            remaining_quanaity = po_loc[0].quantity-quantity
            if remaining_quanaity>=0:
                tasks.filter(task_ref_id=self.task_ref_id).update(employee=self.request_user.employee, start_time=datetime.datetime.now())
                task = PutawayTask.objects.filter(employee=self.request_user.employee, status=False)
                if task.exists():
                    final_response = {"status": "Success", "reference_number":self.task_ref_id}
                    if remaining_quanaity:
                        save_remaining_putaway_quantity(po_loc, quantity, remaining_quanaity)
                else:
                    error = no_task_const
            else:
                error = 'Picked Quantity is not available'
        else:
            error = 'Putaway already confirmed'
        return final_response, error


    def put(self, *args, **kwargs):
        log.info("Put Request with user name %s and arguments %s, %s" % (str(self.request.user),str(args),str(kwargs)))
        self.set_user_credientials()
        self.request_user = self.request.user
        self.request_data = json.loads(self.request.body)
        self.admin_user = self.warehouse
        self.task_ref_type = self.request_data.get('type', None)
        self.task_ref_id = self.request_data.get('task_ref_id', None)
        group_type_str = self.request_data.get('zone', "")
        self.group_type = group_type_str.split(",") if group_type_str else []
        order_type_str = self.request_data.get('order_type','')
        self.order_type = order_type_str.split(",") if order_type_str else []
        self.lpn_number = self.request_data.get('lpn_number', None)
        self.request_picklist_number = self.request_data.get('picklist_number', None)
        self.cluster =  self.request_data.get('cluster_name', '')
        self.sequence = int(self.request_data.get('sequence', 0))
        if not self.task_ref_type:
            return HttpResponseBadRequest('task Type Is Required')

        misc_types = [
            'picklist_priority', 'subzone_mapping', 'user_sub_zone_mapping',
            'sku_picking', 'zone_wise_manual_assignment'
        ]
        self.misc_dict = get_multiple_misc_values(misc_types, self.admin_user.id)

        self.zone_wise_manual_assignment = bool(self.misc_dict.get('zone_wise_manual_assignment', 'false') == 'true')
        
        self.assign_task_by_dest_details = get_permission(self.request_user, 'ba_to_sa_assignment_based_on_destination_details')
        self.dest_zones = []
        if self.assign_task_by_dest_details and self.task_ref_type == 'batosa_picking' and self.group_type:
            self.dest_zones = self.group_type
            self.group_type = []
        
        self.pick_and_pass_user = get_permission(self.request_user, 'pick_and_pass')

        order_by_list = ['task_priority', '-empty_priority', 'eta', 'priority', 'id']
        if self.misc_dict.get('picklist_priority') == 'trip_id':
            order_by_list = ['task_priority', '-empty_priority', 'priority', 'eta', 'id']

        self.pick_pass_picklist_number = None
        if self.task_ref_type in ["picklist"] and self.request_picklist_number:
            self.pick_pass_picklist_number = self.request_picklist_number

        #checking unassigned tasks
        misc_options_types = ['manual_assignment', 'label_based_picking', 'hybrid_task_assignment']
        misc_options_dict = get_misc_options_list(misc_options_types, self.admin_user, to_lower_case=False)
        self.manual_assignment_order_types = misc_options_dict.get('manual_assignment', [])
        self.label_based_picking_order_types = misc_options_dict.get('label_based_picking', [])
        self.hybrid_assignment_order_types = misc_options_dict.get('hybrid_task_assignment', [])
        if self.task_ref_type.lower() not in ["putaway"]:
            task_check, running_task = self.get_task_check()
            if task_check:
                final_response = queryset_to_list(self.request, running_task, data=True, task_type=self.task_ref_type, warehouse_id=self.admin_user.id)
                return JsonResponse(final_response, safe=False)
        try:
            response_check, reference_number = False, ""
            with transaction.atomic("default"):
                tasks, error = self.get_tasks_using_request_task_type(reference_number=self.pick_pass_picklist_number)
                if error:
                    return HttpResponseBadRequest(no_task_const)

                final_response = []
                if self.task_ref_type in ["picklist", "st_picklist", "batosa_picking", "nte_picking", "jo_picklist"]:
                    tasks = tasks.annotate(empty_priority = Case(When(priority=0, then=0),default=1, output_field=IntegerField())).order_by(*order_by_list)
                    assigned_task, reference_number = self.assign_tasks_to_user(tasks, self.request_user.employee, running_task, self.task_ref_type)
                    if assigned_task:
                        response_check =True
                    else:
                        return HttpResponseBadRequest(failed_const)

                elif self.task_ref_type=="cyclecount":
                    tasks, assigned_task, reference_number, error, response_check = self.assign_cyclecount_tasks(running_task, response_check, tasks)
                    if error:
                        return HttpResponseBadRequest(error)

                elif self.task_ref_type in ['Putaway', 'batosa_putaway', 'nte_putaway']:
                    final_response, error = self.assign_putaway_tasks(tasks)
                    if error:
                        return HttpResponseBadRequest(error)
                    return JsonResponse(final_response, safe=False)

            if response_check and reference_number:
                if self.task_ref_type != 'cyclecount':
                    task = TaskMaster.objects.filter(reference_number__in = reference_number, employee=self.request_user.employee, status=False, task_ref_type="Picklist").order_by("priority")
                else:
                    task = CycleCount.objects.filter(employee=self.request_user.employee, status=1, sku__user = self.admin_user.id)

                if task.exists():
                    final_response = queryset_to_list(self.request, task, data=True, task_type=self.task_ref_type, warehouse_id=self.admin_user.id, sequence=self.sequence)
                return JsonResponse( final_response, safe=False)
        except Exception as e:
            log.info("Task assign error  %s" % str(e))
            return HttpResponseBadRequest('Concurrent Task assign Exception')

@get_warehouse
def aggregated_replenishment_count(request, warehouse:User):
    final_response = {}
    task_sub_type = request.GET.get('task_sub_type', '')
    group_type_str = request.GET.get('zone', "")
    input_group_type = group_type_str.split(",") if group_type_str else []

    #Required Misc Details
    misc_types = ['replenishment_pick_drop_user', 'replenishment_options', 'subzone_mapping', 'nte_pick_drop_user']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)

    #Get Configs Data
    subzone_mapping = misc_dict.get("subzone_mapping", '')
    assign_task_by_dest_details = get_permission(request.user, 'ba_to_sa_assignment_based_on_destination_details')

    #Get Replenishment Options
    replenishment_options = misc_dict.get('replenishment_options', [])
    if replenishment_options:
        replenishment_options = replenishment_options.split(',')

    #Exlcuding BA to SA Replenishment Drop Tasks to Staging
    po_loc_filters = {
        'sku__user': warehouse.id, 
        'location__zone__storage_type__in': ['replenishment_staging_area', 'nte_staging_area'],
        'status': 1
    }
    exclude_rep_drop_tasks = list(POLocation.objects.filter(**po_loc_filters).values_list('id', flat=True))

    for option in replenishment_options:
        group_type = input_group_type

        if option == 'ba_to_sa':
            putaway_type = 'BATOSAPutaway'
            user_type = misc_dict.get('replenishment_pick_drop_user', '')
        elif option == 'nte':
            putaway_type = 'NTEPutaway'
            user_type = misc_dict.get('nte_pick_drop_user', '')

        dest_zones = []
        if assign_task_by_dest_details and option == 'ba_to_sa':
            dest_zones = group_type
            group_type = []

        batosa_picking_filters = {'warehouse' : warehouse, 'order_type' :option.upper()}
        if group_type:
            batosa_picking_filters.update(group_type__in = group_type)

        #Get Source Filters based on Destination Zone / Subzone Filters
        if dest_zones:
            filter_tasks = get_tasks_by_dest_filters(warehouse, subzone_mapping, dest_zones)
            batosa_picking_filters.update({'task_ref_id__in': filter_tasks})

        batosa_picking = TaskMaster.objects.filter(**batosa_picking_filters)

        putaway_filter_dict = {'warehouse': warehouse, 'task_ref_type': putaway_type}
        if user_type and user_type == 'single_user':
            putaway_filter_dict.update({'employee': request.user.employee})

        batosa_putaway = PutawayTask.objects.exclude(task_ref_id__in = exclude_rep_drop_tasks).filter(**putaway_filter_dict)

        batosa_picking_open_tasks = batosa_picking.filter(status=False)
        batosa_putaway_open_tasks = batosa_putaway.filter(status=False)
        iter_dict = {'pick': batosa_picking_open_tasks,'drop': batosa_putaway_open_tasks}
        temp_dict = {}
        temp_dict['open'] = {}
        for type, tasks in iter_dict.items():
            temp_dict[type] = {}
            task_ids = list(tasks.values_list('task_ref_id', flat=True))
            task_dict, aggr_dict = get_replenishment_tasks_aggregation(
                type, task_ids, warehouse, group_type, task_sub_type, option
            )
            temp_dict[type].update({'open': aggr_dict})
            if task_dict:
                temp_dict['open'].update({type : task_dict['SKU']})
            else:
                temp_dict['open'].update({type : temp_dict['SKU']})
        
        final_response.update({option : temp_dict})

    return JsonResponse({'message': final_response}, status = 200)


def get_replenishment_tasks_aggregation(task_type, task_ids, warehouse, group_type, task_sub_type, option):
    if task_type == 'pick':
        aggr_obj, task_dict = {'item': 0, 'SKU': 0}, {'item': 0, 'SKU': 0}
        picklist_filters= {'id__in': task_ids}
        pickobjs = Picklist.objects.filter(**picklist_filters)
        if pickobjs.exists():
            aggr_obj = pickobjs.aggregate(item=Sum('reserved_quantity'), SKU=Count('sku_id', distinct=True))
            task_dict = pickobjs.aggregate(item=Sum('reserved_quantity'), SKU=Count('picklist_number', distinct=True))
            if task_sub_type == "pick_and_pass" and option == 'nte':
                lms = lmstasks1()
                lms.admin_user = warehouse
                lms.group_type = group_type
                extra_params = {'picklist_filters': {'order_type__in': ['NTE']}}
                total_picklist_numbers = lms.get_pick_and_pass_picker_picklist_count('nte_replenishment', extra_params=extra_params)
                aggr_obj['SKU'] = len(total_picklist_numbers)

        return dict(task_dict), dict(aggr_obj)

    elif task_type == 'drop':
        aggr_obj = {'item': 0, 'SKU': 0}
        putobjs = POLocation.objects.filter(id__in=task_ids, status=1)
        if putobjs.exists():
            aggr_obj = putobjs.aggregate(item=Sum('quantity'), SKU=Count('sku_id', distinct=True))
        return dict(aggr_obj), dict(aggr_obj)

def get_tasks_by_dest_filters(warehouse, subzone_mapping, dest_zones):
    '''
    Get Source Picklist Details
    '''
    filter_dict = {'sku__user': warehouse.id, 'transact_type': 'BA_TO_SA', 'status__in': [1, 5]}
    if subzone_mapping == 'true':
        filter_dict.update({'dest_location__sub_zone__zone__in': dest_zones})
    else:
        filter_dict.update({'dest_zone__zone__in': dest_zones})

    ba_to_sa_ids = list(BAtoSADetail.objects.filter(**filter_dict).values_list('id', flat=True))

    picklist_ids = list(
        Picklist.objects.filter(reference_id__in = ba_to_sa_ids, status = 'open').values_list('id', flat=True)
    )
    return picklist_ids



@get_warehouse
def aggregated_picking_count(request, warehouse: User):
    """
    returns the aggregated picking count for the given warehouse
    """

    zone = request.GET.get('zone', '')
    group_type_str = zone.split(",") if zone else []
    order_type = request.GET.get('order_type', '')
    order_types = order_type.split(",") if order_type else []
    misc_options_types = ['manual_assignment', 'label_based_picking', 'hybrid_task_assignment']
    misc_options_dict = get_misc_options_list(misc_options_types, warehouse, to_lower_case=False)
    misc_types = ['zone_wise_manual_assignment']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    task_sub_type = request.GET.get('task_sub_type', '')

    task_filters = {
        'warehouse': warehouse,
        'status': False,
        'employee': None,
        'task_ref_type': 'Picklist'
    }
    if group_type_str:
        task_filters['group_type__in'] = group_type_str
    if order_types:
        task_filters['order_type__in'] = order_types
    tasks = TaskMaster.objects.filter(**task_filters).exclude(order_type__in =['BA_TO_SA','ST','NTE'])
    task_ids = list(tasks.values_list('task_ref_id', flat=True))

    task_value = ['picklist', 'st_picklist', 'jo_picklist']
    if task_sub_type == 'pick_and_pass':
        task_value = ['picklist']
    final_data = {}
    for task in task_value:
        taskids = get_tasks_based_on_task_type(task, warehouse, request.user, tasks, task_ids, group_type_str, misc_options_dict, misc_dict)

        pick_count = get_tasks_aggregation(task, taskids, task_sub_type, warehouse, group_type_str)
        final_data[task] = pick_count
    
    final_response = frame_response(final_data)

    return JsonResponse(final_response, status = 200)

def frame_response(data):
    """
    converts the data to lms get api response format
    """
    picklist = {}
    
    picklist['picklist'] = {'open': data.get('picklist', {})}
    picklist['st_picklist'] = {'open': data.get('st_picklist', {"picklist": 0})}
    picklist['jo_picklist'] = {'open': data.get('jo_picklist', {"items": 0, "orders": 0})}
    picklist['open'] = {'Sale Order': data.get('picklist', {}).get('picklist', 0), 'Stock Transfer': data.get('st_picklist', {}).get('picklist', 0), 'Job Order': data.get('jo_picklist', {}).get('orders', 0)}
    final_response = {'picklist': picklist}
    return final_response

def get_tasks_aggregation(task_type, task_ids, task_sub_type, warehouse, group_type):

    if task_type == 'picklist' or task_type=='st_picklist':
        picklist_filters= {'id__in': task_ids, 'status': 'open', 'reserved_quantity__gt': 0}
        aggr_obj = Picklist.objects.filter(**picklist_filters).exclude(Q(order__order_type__in=BACKFLUSH_JO_TYPES) | Q(pick_type='pick_and_pass')).aggregate(picklist=Count('picklist_number', distinct=True))
        
        if task_sub_type == "pick_and_pass":
            lms = lmstasks1()
            lms.admin_user = warehouse
            lms.group_type = group_type
            extra_params = {'picklist_excludes': {'order_type__in': BACKFLUSH_JO_TYPES}}
            total_picklist_numbers = lms.get_pick_and_pass_picker_picklist_count('so_picking', extra_params=extra_params)
            aggr_obj['picklist'] = len(total_picklist_numbers)
        return dict(aggr_obj)

    elif task_type == 'jo_picklist':
        picklist_filters= {'id__in': task_ids, 'order_type__in': JO_TYPES}
        picklist_filters["status"] = "open"
        picklist_filters["reserved_quantity__gt"] = 0
        aggr_obj =  Picklist.objects.filter(**picklist_filters).aggregate(items=Sum('reserved_quantity'), orders=Count('picklist_number', distinct=True))
        return dict(aggr_obj)


def get_tasks_based_on_task_type(task, warehouse, user, tasks_list, taskids, group_type_str, misc_options_dict, misc_dict):

    manual_task_ids, hybrid_picklists = [], []

    manual_assignment_order_types = misc_options_dict.get('manual_assignment', [])
    label_based_picking_order_types = misc_options_dict.get('label_based_picking', [])
    hybrid_assignment_order_types = misc_options_dict.get('hybrid_task_assignment', [])
    zone_wise_manual_assignment = bool(misc_dict.get('zone_wise_manual_assignment', 'false') == 'true')

    task_ids = deepcopy(taskids)
    task_filters = {
        'picklist': {'exclude': {'order_type__in': ['STOCKTRANSFER', 'ST'] + BACKFLUSH_JO_TYPES + label_based_picking_order_types}},
        'st_picklist': {'filter': {'order_type': 'STOCKTRANSFER'}, 'exclude': {'order_type__in': label_based_picking_order_types}},
        'jo_picklist': {'filter': {'order_type__in': JO_TYPES}, 'exclude': {'order_type__in': label_based_picking_order_types}}
    }
    if task not in task_filters:
        return task_ids
    
    filter_dict = task_filters[task].get('filter', {})
    exclude_dict = task_filters[task].get('exclude', {})

    assignment_filters = {
        'warehouse_id': warehouse.id,
        'reference_type': 'picklist',
        'status': 0,
    }
    if zone_wise_manual_assignment and group_type_str:
        assignment_filters['zones__in'] = group_type_str
    if manual_assignment_order_types or hybrid_assignment_order_types:
        manual_picklists = list(ManualAssignment.objects.filter(
            employee__user=user.id, **assignment_filters
        ).values_list('reference_number', flat=True))

        manual_task_ids = tasks_list.filter(
            task_ref_type='Picklist', reference_number__in=manual_picklists, **filter_dict
        ).exclude(**exclude_dict).values_list('task_ref_id', flat=True)
        exclude_dict['order_type__in'].extend(manual_assignment_order_types)
    
    if hybrid_assignment_order_types:
        hybrid_picklists = list(ManualAssignment.objects.filter(
            **assignment_filters
        ).values_list('reference_number', flat=True).distinct())

    task_ids = list(tasks_list.filter(task_ref_type='Picklist', **filter_dict).exclude(**exclude_dict).values_list('task_ref_id', flat=True))
    if not ('all' in manual_assignment_order_types or 'all' in label_based_picking_order_types):
        task_ids = list(tasks_list.filter(task_ref_type='Picklist', **filter_dict).exclude(**exclude_dict).exclude(reference_number__in=hybrid_picklists).values_list('task_ref_id', flat=True))
    task_ids.extend(manual_task_ids)
    
    return task_ids

