#package imports
import math
import datetime
import copy
import pytz
import traceback
import pandas as pd
from dateutil import parser
from json import dumps, loads
from collections import defaultdict

#django imports
from django.db.models import Q, Max, Count
from django.http import HttpResponse, JsonResponse
from django.test.client import RequestFactory
from django.utils import timezone
from django.core.cache import cache
from wms.celery import app as celery_app

#inventory imports
from inventory.models import (
    CycleCount, StockDetail, CycleCountSchedule,
    InventoryAdjustment, BatchDetail, LocationMaster
)

#serial number functions
from inventory.views.serial_numbers.serial_number import SerialNumberMixin
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#constants imports
from inventory.models.locator import INVENTORY_CHOICES
status_choices = dict(INVENTORY_CHOICES)

#core imports
from core.models import (
    SKUMaster, PurchaseApprovalConfig,
    MasterEmailMapping
)

#wms base imports
from wms_base.wms_utils import (
    init_logger, CYCLE_COUNT_EXCEL_HEADERS_KEYMAP, rename_dict_keys,
    reverse_stock_choice_mapping
)
from wms_base.models import User

#inbound imports
from inbound.models import PurchaseApprovals

#core common functions
from core_operations.views.common.main import (
    get_misc_value, get_user_ip, get_multiple_misc_values,
    get_decimal_value, truncate_float,
    WMSListView, get_user_time_zone,
    scroll_data, get_local_date_known_timezone, get_sku_ean_numbers,
    get_warehouse, get_incremental
)
from inventory.views.locator.stock_detail import (
    validate_batch_details, get_or_create_batch, format_date,
    get_available_stock_details
)
from inbound.views.grn.grn_reversal import get_current_stock_details


log = init_logger('logs/cycle_count.log')

short_pick_const = 'short pick'

def get_payload_to_generate_cycle_count_for_location(warehouse, request_data):
    """
    Function to check if the request data contains short pick cycle type
    and return the modified request data along with location zone mapping.
    """
    short_pick = any(data.get('cycle_type') for data in request_data)
    misc_types = ['all_batch_cycle_count_on_short_pick']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    all_batch_cycle_count = misc_dict.get('all_batch_cycle_count_on_short_pick', 'false')
    location_ids = [data.get('location_id') for data in request_data]
    location_zone_mapping = dict(LocationMaster.objects.filter(zone__user=warehouse.id, id__in=location_ids).values_list('id', 'zone_id'))
    if short_pick and all_batch_cycle_count == 'true':
        sku_ids,batch_details_list = [], []
        short_pick_sources = {}
        for data in request_data:
            sku_id, location_id = data.get('sku_id'), data.get('location_id')
            sku_ids.append(sku_id)
            sku_location_key = (sku_id, location_id)
            short_pick_sources[sku_location_key] = data.get('source')
            batch_details_list.append((sku_id, location_id, data.get('batch_detail_id', ''), data.get('stock_status')))

        stock_details = get_available_stock_details(warehouse, sku_ids=sku_ids, location_ids=location_ids)
        final_payload = []
        for stock in stock_details:
            sku_location_key = (stock.get('sku_id'), stock.get('location_id'))
            sku_id, location_id, batch_detail_id, stock_status = (
                stock.get('sku_id'), stock.get('location_id'), stock.get('batch_detail_id'), 
                status_choices.get(stock.get('stock_status'))
            )
            payload = {
                'sku_code' : stock.get('sku_code'),
                'zone': stock.get('zone'),
                'location' : stock.get('location'),
                'quantity' : stock.get('quantity'),
                'sku_id': sku_id,
                'location_id': location_id,
                'stock_status': stock_status,
                'batch_detail_id': batch_detail_id,
                'lpn_number' : stock.get('lpn_number',''),
                'source': short_pick_sources.get(sku_location_key, '')
            }
            if (sku_id, location_id, batch_detail_id, stock_status) in batch_details_list:
                payload['cycle_type'] = short_pick_const

            final_payload.append(payload)
        return final_payload, location_zone_mapping

    return request_data, location_zone_mapping

def group_cycle_objs_based_on_sku_and_location(warehouse, bulk_cycle_objs):
    """
    Groups cycle count objects based on SKU and location, assigning unique cycle IDs.
    Args:
        warehouse (User): The warehouse object.
        bulk_cycle_objs (list): List of CycleCount objects to be processed.
    Returns:
        list: List of CycleCount objects with assigned cycle IDs.
    """
    # Dictionary to store unique cycle IDs based on (sku_id, location_id) key
    unique_cycle_ids = {}
    num_cycles_needed = len({(obj.sku_id, obj.location_id, obj.run_type) for obj in bulk_cycle_objs})

    # Generate incremental cycle IDs
    if num_cycles_needed > 0:
        cycle_id = get_incremental(warehouse, 'cycle_id', '', False, num_cycles_needed)

        for obj in bulk_cycle_objs:
            unique_key = (obj.sku_id, obj.location_id, obj.run_type)
            if unique_key not in unique_cycle_ids:
                unique_cycle_ids[unique_key] = cycle_id
                cycle_id += 1
            obj.cycle = unique_cycle_ids[unique_key]

    return bulk_cycle_objs

@get_warehouse
def generate_cycle_count(request, warehouse:User):
    '''
    Function to Generate Cycle Count on Batch Status Level
    '''
    from core_operations.views.integration.integration import webhook_integration_3p
    request_data = loads(request.body)
    log.info((
        "Request Generate Cycle Count for Username: %s,IPAddress: %s and request params are: %s ") % (
        str(warehouse.username), str(get_user_ip(request)), str(request_data)
    ))
    update_cycle_type, bulk_cycle_objs, response_data, sku_codes = [], [], [], []
    sku_zones_dict = defaultdict(list)
    # List to store cache keys for later deletion
    cache_keys = []

    #Check if needs to Generate Unscheduled Cycle Count based on cofig on short pick cycle count
    request_data, location_zone_mapping = get_payload_to_generate_cycle_count_for_location(warehouse, request_data)

    for data in request_data:
        sku_id = data.get('sku_id')
        location_id = data.get('location_id')
        batch_detail_id = data.get('batch_detail_id')
        stock_status = data.get('stock_status')

        # Check if this combination is already in the cache and add it if it doesn't exist
        error_message, cache_keys = check_sku_location_batch_cache(sku_id, location_id, batch_detail_id, stock_status, cache_keys=cache_keys)

        # If the combination is in the cache, skip this item
        if error_message:
            log.info(f"Skipping item with sku_id: {sku_id}, location_id: {location_id}, batch_detail_id: {batch_detail_id}, stock_status: {stock_status} - {error_message}")
            continue

        cycle_data = {
            'sku_id': sku_id,
            'location_id': location_id,
            'stock_status': reverse_stock_choice_mapping.get(data.get('stock_status')),
            'status': 1,
            'batch_detail_id': batch_detail_id,
        }
        if data.get('lpn_number'):
            cycle_data.update({'lpn_number':data.get('lpn_number')})
        sku_codes.append(data.get('sku_code'))
        zone_id = location_zone_mapping.get(data.get('location_id'))
        sku_zones_dict[zone_id].append(data.get('sku_code'))
        source = data.get('source', 'User Generated')
        json_data = {'generated_user': request.user.username, 'generation_source': source}

        short_pick = data.get('cycle_type', False)
        short_pick_reason = data.get('short_pick_reason', '')

        if short_pick_reason:
            json_data.update({'upload_reason': short_pick_reason})
        elif short_pick:
            json_data.update({'upload_reason': 'Short Cycle Count'})

        #Check whether the cycle already exists, and changing the status if already exists
        exist_cycle_obj = CycleCount.objects.filter(**cycle_data).first()
        json_data.update({'priority': 1})
        cycle_data['run_type'] = 'unscheduled'
        if short_pick:
            cycle_data['run_type'] = short_pick_const
            json_data.update({'priority': 2})

        cycle_data['json_data'] = json_data
        cycle_data['quantity'] = data.get('quantity', 0)

        if exist_cycle_obj:
            exist_cycle_type = exist_cycle_obj.run_type

            #Updation of Runtype
            if cycle_data['run_type'] == short_pick_const and exist_cycle_type in ['unscheduled','scheduled']:
                exist_cycle_obj.run_type, exist_cycle_obj.json_data = cycle_data['run_type'], {'priority':2}
            elif cycle_data['run_type'] == 'unscheduled' and exist_cycle_type in ['scheduled']:
                exist_cycle_obj.run_type, exist_cycle_obj.json_data = cycle_data['run_type'], {'priority':1}
            else:
                response_data.append(
                    f"Entry Already Exists for sku_code: {data.get('sku_code')}, location: {data.get('location')}, "
                    f"stock_status: {data.get('stock_status')}, batch_no: {data.get('batch_no')}, run_type: {exist_cycle_type}"
                )

            #Bulk Updation of Runtype
            update_cycle_type.append(exist_cycle_obj)
        else:
            #Bulk Creation of Cycle Count
            cycle_data.update({'account_id': warehouse.userprofile.id})
            bulk_cycle_objs.append(CycleCount(**cycle_data))

    #Bulk Updation of Cycle Count runtype
    if update_cycle_type:
        CycleCount.objects.bulk_update_with_rounding(update_cycle_type, ['run_type'])

    #Grouping Cycle ID based on sku and location
    bulk_cycle_objs = group_cycle_objs_based_on_sku_and_location(warehouse, bulk_cycle_objs)

    #Bulk Cycle Count Creation
    if bulk_cycle_objs:
        CycleCount.objects.bulk_create_with_rounding(bulk_cycle_objs)
    
    #Inventory Callback
    filters = {
        'sku_codes': sku_codes,
        "zones_data": sku_zones_dict
    }
    webhook_integration_3p(warehouse.id, "cycle_count", filters)

    # Remove cache entries using the list of cache keys
    cache.delete_many(cache_keys)

    #If Entry Already Exists
    if response_data:
        return JsonResponse({'message': response_data}, status = 400)

    return JsonResponse({'message': 'Created/Updated Successfully'}, status = 200)

def get_sku_location_batch_cache_key(sku_id, location_id, batch_detail_id, stock_status=None):
    """
    Generate a unique cache key for a specific combination of sku_id, location_id, batch_detail_id, and stock_status

    Args:
        sku_id: The SKU ID
        location_id: The location ID
        batch_detail_id: The batch detail ID (can be None)
        stock_status: The stock status

    Returns:
        str: The cache key
    """
    batch_detail_id = batch_detail_id or 'none'  # Handle None values
    return f"cycle_count:{sku_id}:{location_id}:{batch_detail_id}:{stock_status}"

def check_sku_location_batch_cache(sku_id, location_id, batch_detail_id, stock_status=None, cache_keys=None):
    """
    Check if a specific combination of sku_id, location_id, batch_detail_id, and stock_status exists in cache

    Args:
        sku_id: The SKU ID
        location_id: The location ID
        batch_detail_id: The batch detail ID (can be None)
        stock_status: The stock status
        cache_keys: List to store cache keys for later deletion (optional)

    Returns:
        tuple: (str, list) where:
            - str: Error message if any
            - list: Updated list of cache keys (if cache_keys was provided)
    """
    error_message = ''
    # Generate a unique cache key for this combination
    cache_key = get_sku_location_batch_cache_key(sku_id, location_id, batch_detail_id, stock_status)

    # Attempt to add the key to the cache atomically with a timeout of 10 minutes
    added = cache.add(cache_key, 'True', timeout=60*10)
    if not added:
        # If the key already exists in cache, skip this item
        return "This item is already in the cache, skipping", cache_keys
    
    # Add to the list of cache keys for later deletion
    if cache_key is not None:
        cache_keys.append(cache_key)
    
    return error_message, cache_keys

def get_current_stock_quantity(data, available_stock_data):
    """
    Function to get the current stock quantity based on the provided data and available stock data.
    """
    stock_filter_dict = {
        'location_id': data.get('location_id'),'sku_id':data.get('sku_id')
    }
    if data.get('stock_status') not in ['', None]:
        stock_filter_dict.update({'status': data.get('stock_status')})
    if data.get('batch_detail_id'):
        stock_filter_dict.update({'batch_detail_id': data.get('batch_detail_id')})
    if data.get('lpn_number'):
        stock_filter_dict.update({'lpn_number': data.get('lpn_number')})
    filtered_df = available_stock_data
    if not filtered_df.empty:
        for key, value in stock_filter_dict.items():
            filtered_df = filtered_df[filtered_df[key] == value]
    
        # Sum the quantity and handle zero stock case
        stock_quantity = filtered_df['quantity'].sum()
    return stock_quantity if not filtered_df.empty else 0

def cycle_count_details_from_request(request):
    """
    Function to extract cycle count details from the request.
    It processes the POST data to gather cycle IDs and their corresponding counts,
    and checks for existing cycle IDs in the cache to prevent duplicate processing.
    Returns:
        tuple: A tuple containing:
            - cycle_ids: List of cycle IDs extracted from the request.
            - count_dict: Dictionary mapping cycle IDs to their counts.
            - reasons: Dictionary of reasons for the cycle counts, if provided.
            - error: Error message if any cycle ID is already being processed.
            - cycle_cache_keys: List of cache keys used for cycle counts.
    """
    cycle_ids, count_dict = [], {}
    cycle_cache_keys = []

    for data_id, count in request.POST.items():
        if not str(count) or data_id in ['source', 'custom_fields', '', None, 'serial_dict', 'reasons', 'convert_to_queryset','skip_callback','skip_inventory_adjustment']:
            continue
        
        # Attempt to add the key to the cache atomically with a timeout of 10 minutes
        cache_key = f"submit_cycle_count:{data_id}"
        added = cache.add(cache_key, 'True', timeout=60*10)
        if not added:
            # Return error information if a cycle ID is already being processed
            return cycle_ids, count_dict, {}, f"Cycle count ID {data_id} is already being processed. Please wait", cycle_cache_keys
        
        # Add to the list of cache keys for later deletion
        cycle_cache_keys.append(cache_key)

        cycle_ids.append(data_id)
        count_dict[str(data_id)] = count

    # fetching reasons from request
    reasons = request.POST.get('reasons', {})

    return cycle_ids, count_dict, reasons, None, cycle_cache_keys

def get_required_misc_values(warehouse):
    """
    Function to retrieve miscellaneous configuration values for cycle count.
    """
    #misc details
    misc_types = ['cycle_count_allowance_value','enable_inventory_approval']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    allowance_value = misc_dict.get('cycle_count_allowance_value', 'false')
    inventory_adjustment_approval = misc_dict.get('enable_inventory_approval','false')
    return allowance_value, inventory_adjustment_approval

def task_release_check(request, cycle_ids, cycle_obj):
    """
    Function to check if the task has been released by someone else.
    It filters the cycle objects based on the user and cycle IDs, and checks their status.
    If the task is released, it returns a message indicating that the task has been released by someone.
    Args:
        request: The HTTP request object.
        cycle_ids: List of cycle IDs to check.
        cycle_obj: QuerySet of CycleCount objects to filter.
    Returns:
        dict: An empty dictionary if the task is not released, or a message if it is released.
    """
    if request.POST.get('source') == 'APP':
        employee_check = cycle_obj.filter(employee__user = request.user, id__in = cycle_ids, status = 1)
        if employee_check.exists():
            return {'message': 'Task Released by someone'}
    return {}

def get_adjusted_value(data, adjusted_qty):
    """
    Function to calculate the adjusted value based on the adjusted quantity and SKU price.
    """
    sku_cost_price = int(data.get('sku__cost_price', 0))
    sku_landed_cost =  data.get('batch_detail__json_data__sku_landed_cost', 0)
    if data.get('sku__batch_based'):
        sku_price = float(sku_landed_cost) if sku_landed_cost else sku_cost_price
    else:
        sku_price = data.get('cycle__sku__skuwac__average_price_rt', 0) if data.get('cycle__sku__skuwac__average_price_rt', 0) else sku_cost_price

    return adjusted_qty * sku_price

def get_done_by(request, data):
    """
    Function to determine who performed the cycle count action."""
    done_by = request.POST.get('source','WEB') or 'APP'
    if data.get('json_data') and data.get('json_data').get('source'):
        done_by = data.get('json_data').get('source')
    return done_by

def get_available_stock_data_for_cycle_count(warehouse, cycle_obj_values):
    """
    Function to retrieve available stock data for cycle count based on the cycle object values.
    It filters the StockDetail objects based on SKU and location IDs from the cycle object values.
    Args:
        warehouse (User): The warehouse object.
        cycle_obj_values (list): List of cycle count objects containing SKU and location information.
    Returns:
        pd.DataFrame: A DataFrame containing stock details filtered by SKU and location IDs.
    """
    # Extract SKU and location IDs from cycle_obj_values
    sku_code_list = [data.get('sku_id') for data in cycle_obj_values]
    location_list = [data.get('location_id') for data in cycle_obj_values]
    stock_data_df = pd.DataFrame(StockDetail.objects.filter(sku__user = warehouse.id, sku__in = sku_code_list,
         location__in = location_list).values(
        'id','sku_id','location_id','status','batch_detail_id','quantity','lpn_number'))
    
    return stock_data_df

def get_details_of_cycle_count_for_updation(request, warehouse, cycle_obj_values, count_dict, value_decimal):
    """
    Function to prepare the details of cycle count for updation.
    It calculates the adjusted quantity, seen quantity, and current stock quantity for each cycle count object.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
        cycle_obj_values (list): List of cycle count objects to be processed.
        count_dict (dict): Dictionary mapping cycle IDs to their seen quantities.
        value_decimal (int): Decimal places to truncate the float values.
    Returns:
        tuple: A tuple containing:
            - cycle_count_update_dict: Dictionary with cycle count details for updation.
            - error_status: Error status message if any serial numbers are missing.
    """
    cycle_count_update_dict = {}
    available_stock_data = get_available_stock_data_for_cycle_count(warehouse, cycle_obj_values)
    serial_dict = request.POST.get('serial_dict', {})
    error_status = ''
    for data in cycle_obj_values:
        adjusted_value, seen_quantity, quantity = 0, 0, 0
        #Current Stock Quantity
        stock_quantity = get_current_stock_quantity(data, available_stock_data)

        #Seen Qty,System Quantity, Adjusted Quantity, Adjusted Value
        seen_quantity = count_dict.get(str(data.get('id')))
        seen_quantity, quantity = truncate_float(float(seen_quantity), value_decimal), stock_quantity

        adjusted_qty= float(seen_quantity) - quantity
        adjusted_value = get_adjusted_value(data, adjusted_qty)

        done_by = get_done_by(request, data)
        status, remarks = ('3', 'matched') if float(seen_quantity) == quantity else ('2', '')
        serial_numbers = serial_dict.get(str(data.get('id')), [])
        if data['sku__enable_serial_based'] and not serial_numbers and not status == "3":
            error_status = 'Serial Numbers are Mandatory'

        cycle_count_update_dict[data.get('id')] = {
            'seen_quantity': seen_quantity,
            'quantity': quantity, 'adjusted_qty':adjusted_qty,
            'adjusted_value':adjusted_value, 'done_by':done_by,
            'remarks':remarks,'status':status,
            'serial_numbers': serial_numbers
        }
    return cycle_count_update_dict, error_status

def prepare_serial_data(data, update_data, serial_data):
    """
    Function to prepare serial data for cycle count transactions.
    It extracts serial numbers and prepares a dictionary for each transaction.
    Args:
        data: CycleCount object containing SKU and location information.
        update_data: Dictionary containing update details for the cycle count.
        serial_data: List to append prepared serial data.
    Returns:
        list: Updated serial_data list with prepared serial transaction details.
    """
    # Prepare serial data for transaction
    serial_status = 2
    serial_numbers = update_data.get('serial_numbers', [])
    transact_type = 'add' if update_data.get('adjusted_qty',0) > 0 else 'remove'
    if serial_numbers and transact_type == "add":
        serial_status = 3
    serial_data.append({
        'transact_id': data.id,
        'transact_type': transact_type,
        'serial_numbers': serial_numbers,
        'sku_code': data.sku.sku_code,
        'sku_id': data.sku.id,
        'batch_number': data.batch_detail.batch_no if data.batch_detail else '',
        'batch_detail_id': data.batch_detail.id if data.batch_detail else None,
        'lpn_number': data.lpn_number,
        'location_id': data.location.id,
        'zone': data.location.zone,
        'status': 1,
        'serial_status': serial_status
    })
    return serial_data


def update_cycle_count_details(request, cycle_count_update_dict, cycle_objs, reason_dict={}):
    '''
    Updates Cycle Count Status and Quantity Details

    '''
    cycle_count_update_list, serial_data = [], []
    for data in cycle_objs:
        update_data = cycle_count_update_dict.get(data.id,{})
        data.seen_quantity = update_data.get('seen_quantity', 0)
        data.quantity = update_data.get('quantity', 0)
        data.adjusted_value = update_data.get('adjusted_value', 0)
        data.status = update_data.get('status')
        data.remarks = update_data.get('remarks','')

        json_data_update = {
            'adjusted_qty': update_data.get('adjusted_qty',0), 'adjusted_value': update_data.get('adjusted_value',0),
            'requested_user' :  request.user.username, 'done_by': update_data.get('done_by','')
        }

        if reason_dict:
            json_data_update['upload_reason'] = reason_dict.get(str(data.id), '')
        
        data.json_data = json_data_update if data.json_data is None else {**data.json_data, **json_data_update}
        cycle_count_update_list.append(data)
        if update_data.get('serial_numbers'):
            serial_data = prepare_serial_data(data, update_data, serial_data)

    CycleCount.objects.bulk_update_with_rounding(
        cycle_count_update_list, ['seen_quantity','quantity','adjusted_value','status','remarks','json_data'],
        batch_size=100
    )

    # Handle serial number transactions if serial data is present
    if serial_data:
        serial_dict = {'reference_number': data.id, 'reference_type': 'cycle_count', 'items': serial_data, 'extra_params': {'serial_creation': True}}
        SerialNumberTransactionMixin(request.user, request.warehouse, serial_dict).create_update_sn_transaction()

def fetch_inventory_adjustment_data(warehouse_id):
    """
    Function to fetch inventory adjustment data for a given warehouse.
    It retrieves InventoryAdjustment objects related to CycleCount with status 2 (completed).
    Args:
        warehouse_id (int): The ID of the warehouse to filter the inventory adjustments.
    Returns:
        pd.DataFrame: A DataFrame containing the inventory adjustments with relevant fields.
    """
    inventory_adjustments = InventoryAdjustment.objects.filter(
        cycle__sku__user=warehouse_id, cycle__status=2
    ).values(
        'cycle_id', 'cycle__sku__id', 'cycle__location__id', 'cycle__stock_status', 'cycle__batch_detail_id'
    )
    return pd.DataFrame(inventory_adjustments)

def update_or_create_inventory_adjustment(request, warehouse, cycle_ids):
    """
    Function to update or create inventory adjustments based on cycle count data.
    It processes cycle count objects, checks for existing inventory adjustments,
    and creates new adjustments if necessary.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
        cycle_ids (list): List of cycle IDs to process for inventory adjustments.
    Returns:
        tuple: A tuple containing:
            - inv_creation_list: List of InventoryAdjustment objects to be created.
            - cycle_objs: List of CycleCount objects processed.
            - sku_codes: List of SKU codes involved in the inventory adjustments.
            - sku_zones_dict: Dictionary mapping zone IDs to SKU codes.
    """
    #Update or Create Inventory Adjustment
    try:
        inv_update_ids, inv_creation_list, sku_codes = [], [], []
        sku_zones_dict = defaultdict(list)
        values_list = ['id','sku_id','sku__sku_code', 'location_id', 'stock_status', 'batch_detail_id', 'seen_quantity',
                        'location__location','adjusted_value','location__zone_id']

        cycle_objs = CycleCount.objects.filter(id__in = cycle_ids, sku__user = warehouse.id).values(*values_list)
        pending_inventory_adjustment_data = fetch_inventory_adjustment_data(warehouse.id)
        for data in cycle_objs:
            inv_filter_dict = {
                'cycle__sku__id':data.get('sku_id'), 'cycle__location__id':data.get('location_id'),
            }
            sku_codes.append(data.get('sku__sku_code',''))
            sku_zones_dict[data.get('location__zone_id')].append(data.get('sku__sku_code',''))
            if data.get('stock_status'):
                inv_filter_dict.update({'cycle__stock_status': data.get('stock_status')})
            if data.get('batch_detail_id'):
                inv_filter_dict.update({'cycle__batch_detail_id': data.get('batch_detail_id')})

            filtered_df = pending_inventory_adjustment_data
            inv_data = []
            if not filtered_df.empty:
                for key, value in inv_filter_dict.items():
                    filtered_df = filtered_df[filtered_df[key] == value]
            
                # Collect the `cycle_id` from filtered data
                inv_data = filtered_df['cycle_id'].tolist()
            if inv_data:
                inv_update_ids.extend(inv_data)

            inv_creation_data_dict = {
                'cycle_id': data.get('id'),
                'adjusted_location': data.get('location__location'),
                'adjusted_quantity': float(data.get('seen_quantity')),
                'reason': '', 'creation_date': datetime.datetime.now(),
                'updation_date': datetime.datetime.now(),
                'account_id': warehouse.userprofile.id
            }
            if request.POST.get('custom_fields', {}):
                inv_creation_data_dict['json_data'] = {'custom_fields': request.POST.get('custom_fields', {})}

            inv_creation_list.append(InventoryAdjustment(**inv_creation_data_dict))

        #if record already exists in inventory adjustment updating the status
        if inv_update_ids:
            CycleCount.objects.filter(sku__user=warehouse.id, id__in = inv_update_ids).\
                update(status='4',remarks='Updated')

        #bulk creation of inventory adjustment records
        if inv_creation_list:
            log.info(("Inventory Adjustment Creation for Username %s, IPAddress %s, data %s") % (
                str(request.user.username), str(get_user_ip(request)), str(inv_creation_list)))

            InventoryAdjustment.objects.bulk_create_with_rounding(inv_creation_list)

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(((
            "Inventory Adjustment Creation Failed for Username %s, IPAddress %s, data %s and error message %s") % (
            str(request.user.username), str(get_user_ip(request)), str(inv_creation_list), str(e)
        )))
    return inv_creation_list, cycle_objs, sku_codes, sku_zones_dict

def auto_adjustment_for_allowance_satisfied(request, warehouse, cycle_objs, allowance_value):
    """
    Function to perform automatic inventory adjustment for cycle counts that satisfy the allowance check.
    It checks if the adjusted value of each cycle count is within the specified allowance value,
    and if so, updates the cycle count status and remarks accordingly.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
        cycle_objs (QuerySet): QuerySet of CycleCount objects to process.
        allowance_value (str): The allowance value to check against.
    Returns:
        None
    """
    confirm_inv_adj, update_remarks_list = {}, []
    skip_callback = request.POST.get('skip_callback', False) or False
    skip_inventory_adjustment = request.POST.get('skip_inventory_adjustment', False) or False
    for data in cycle_objs:
    #Allowance check checking
        check = data.adjusted_value
        if skip_inventory_adjustment == True:
            if data.status != "3":
                confirm_inv_adj.update({data.id: {'reason': "Inventory Adjustment", 'internal_adjustment': "true"}})
                data.remarks, data.status = 'inventory_adjustment', 3
                update_remarks_list.append(data)
        elif allowance_value not in ['false','', '0', 0]:
            if abs(check) <= int(allowance_value):
                confirm_inv_adj.update({data.id: {'reason': "Allowance Check Satisfied", 'internal_adjustment': "true"}})
                data.remarks, data.status = 'allowance_check_satisfied', 3
                update_remarks_list.append(data) 
    if confirm_inv_adj:
        create_auto_inventory_adjustment(request, warehouse, confirm_inv_adj, skip_callback)
    if update_remarks_list:
        CycleCount.objects.bulk_update_with_rounding(update_remarks_list, ['remarks'])

def create_auto_inventory_adjustment(request, warehouse, confirm_inv_adj, skip_callback=False):
    """
    Function to create an inventory adjustment for cycle counts that satisfy the allowance check.
    It prepares the request data and calls the confirm_inventory_adjustment function to process the adjustment.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
        confirm_inv_adj (dict): Dictionary containing cycle IDs and their corresponding adjustment details.
        skip_callback (bool): Flag to indicate whether to skip the callback after adjustment.
    Returns:
        None
    """
    from inventory.views.adjustment.inventory_adjustment import confirm_inventory_adjustment
    inv_request = RequestFactory
    inv_request.method= 'POST'
    inv_request.warehouse = warehouse
    inv_request.body = dumps({
        "data": confirm_inv_adj,
        "skip_callback" : skip_callback,
    })
    inv_request.user = request.user
    confirm_inventory_adjustment(inv_request)


@get_warehouse
def submit_cycle_count(request, warehouse:User):
    """
    Function to submit cycle count details.
    It processes the request data, updates cycle count quantities, and creates or updates inventory adjustments.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
    Returns:
        JsonResponse: A response indicating the success or failure of the operation.
    """
    from core_operations.views.integration.integration import webhook_integration_3p
    log.info(("Request Confirm Cycle Count for Username: %s, IPAddress: %s, and request params are: %s") % (
        str(warehouse.username), str(get_user_ip(request)), str(request.POST)
    ))
    #Misc Values for below
    allowance_value, inventory_adjustment_approval = get_required_misc_values(warehouse)
    reasons_dict = {}
    body_data = {}
    try:
        body_data = loads(request.body)
        if body_data.get('serial_dict'):
            request.POST.update({'serial_dict': body_data.get('serial_dict')})
        if body_data.get('reasons'):
            if body_data.get('source') == 'APP':
                reasons_dict = body_data.get('reasons')
                request.POST = body_data
            else:
                request.POST.update({'reasons': body_data.get('reasons')})
                body_data = request.POST
        else:
            body_data = request.POST
    except Exception as e:
        log.info(e)

    #Cycle Count Id and Seen Quantity Details from request
    cycle_ids, count_dict, reasons_dict, error_message, cycle_cache_keys = cycle_count_details_from_request(request)

    # If there was an error in processing cycle IDs, return the error
    if error_message:
        return JsonResponse({
            'message': error_message,
            'status': 400
        }, status=400)


    #Cycle Objects
    cycle_objs = CycleCount.objects.filter(sku__user = warehouse.id)

    values_list =  [
        'cycle', 'id','sku__user' ,'sku__sku_code', 'sku_id','location_id','status','sku__cost_price','quantity',
        'json_data','sku__average_price_rt','sku__batch_based','batch_detail__json_data__sku_landed_cost',
        'stock_status', 'batch_detail_id', 'sku__enable_serial_based', 'lpn_number'
    ]
    #cycle obj values
    cycle_obj_values = cycle_objs.filter(id__in = cycle_ids).values(*values_list)
    value_decimal = get_decimal_value(warehouse.id)
    #GET Updated Cycle Count Values
    cycle_count_update_dict, error_status = get_details_of_cycle_count_for_updation(
        request, warehouse, cycle_obj_values, count_dict, value_decimal
    )
    if error_status:
        # Remove cache entries for cycle IDs
        cache.delete_many(cycle_cache_keys)
        return JsonResponse({'message' : error_status, 'status':400}, status=400)

    #Update Cycle Count Quantity Details
    update_cycle_count_details(request, cycle_count_update_dict, cycle_objs.filter(id__in = cycle_ids), reason_dict=reasons_dict)

    log.info(("Response Confirm Cycle Count for Username: %s, IPAddress: %s, and request params are: %s") % (
        str(warehouse.username), str(get_user_ip(request)), str(request.POST)
    ))

    #Update or Create Inventory Adjustment
    inv_creation_list, return_cycle_objs, sku_codes, sku_zones_dict = update_or_create_inventory_adjustment(request, warehouse, cycle_ids)

    #Allowance Check and Auto Adjustment
    auto_adjustment_for_allowance_satisfied(request, warehouse, cycle_objs.filter(id__in = cycle_ids), allowance_value)
    if inventory_adjustment_approval == 'true' and request.POST.get('skip_inventory_adjustment', False) == False:
        url = request.META.get('HTTP_REFERER', '')
        create_new_purchase_approval_status(warehouse, inv_creation_list, return_cycle_objs, url)
    
    #Inventory Callback
    filters = {
        'sku_codes': sku_codes,
        "zones_data": sku_zones_dict
    }
    webhook_integration_3p(warehouse.id, "cycle_count", filters)

    # Remove cache entries for cycle IDs
    cache.delete_many(cycle_cache_keys)

    return JsonResponse({'message' :'Updated Successfully', 'status':200})

def create_new_purchase_approval_status(warehouse, inv_creation_list, cycle_objs, url):
    """
    Function to create new purchase approval status for inventory adjustments.
    It checks the adjusted values of cycle counts and creates purchase approvals based on the approval configuration.
    Args:
        warehouse (User): The warehouse object.
        inv_creation_list (list): List of InventoryAdjustment objects to process.
        cycle_objs (QuerySet): QuerySet of CycleCount objects to process.
        url (str): The URL to include in the approval remarks.
    Returns:
        None
    """
    email_map_master_id, master_ids_list, inv_email_mapping = {}, [], {}
    inv_adjusted_value_map = dict(cycle_objs.values_list('id','json_data__adjusted_value'))
    purchase_approval_obj = PurchaseApprovalConfig.objects.filter(
        user_id=warehouse.id, approval_type='INV', purchase_type='INV', level='level1'
    )
    pa_config = purchase_approval_obj.values('id','name','min_Amt','level','max_Amt')
    max_pa_range = purchase_approval_obj.aggregate(Max('max_Amt'))['max_Amt__max']
    if pa_config:
        name = pa_config[0]['name']
        for cycleid, adj_val in inv_adjusted_value_map.items():
            for each_config in pa_config.iterator():
                if math.ceil(abs(adj_val)) >= each_config.get('min_Amt') and math.ceil(abs(adj_val)) <= each_config.get('max_Amt'):
                    email_map_master_id[cycleid] = each_config.get('id')
                    master_ids_list.append(each_config.get('id'))
                    break

        masteremail_map = MasterEmailMapping.objects.filter(user=warehouse.id,master_id__in=master_ids_list,
                master_type='inventory_approvals_conf').values('master_id','email_id')

        for cycleid, masterid in email_map_master_id.items():
            inv_email_mapping.setdefault(cycleid,[])
            for emails in masteremail_map.iterator():
                if emails.get('master_id') == str(masterid):
                    inv_email_mapping[cycleid].append(emails.get('email_id'))

        for each_inv in inv_creation_list:
            if each_inv.cycle.status == '3':
                continue
            approvals_dict = {
                'purchase_number': each_inv.cycle_id,
                'configName': name,
                'approval_type': 'INV',
                'pr_user_id': warehouse.id,
                'level': 'level1',
                'status': '',
                'validated_by': ','.join(inv_email_mapping.get(each_inv.cycle_id,[])),
                'remarks': url,
                'account_id': warehouse.userprofile.id
            }
            if inv_adjusted_value_map.get(each_inv.cycle_id,0) > max_pa_range:
                approvals_dict['validated_by'] = "Adjustment value is not defined in Approval master"
            purchase_approvals = PurchaseApprovals(**approvals_dict)
            purchase_approvals.save()

def get_max_receipt_number(warehouse):
    """
    Function to get the maximum receipt number for stock details in a warehouse.
    It retrieves the highest receipt number from StockDetail objects associated with the warehouse.
    Args:
        warehouse (User): The warehouse object.
    Returns:
        int: The maximum receipt number found, or 0 if no receipt numbers exist.
    """
    receipt_number = StockDetail.objects.filter(sku__user=warehouse.id).values_list(
        'receipt_number', flat=True
    ).order_by('-receipt_number').first() or 0
    return receipt_number

def get_misc_details(warehouse):
    """
    Function to retrieve miscellaneous configuration values for batch details in a warehouse.
    It fetches values related to stock creation restrictions, additional batch attributes,
    and non-mandatory batch attributes.
    Args:
        warehouse (User): The warehouse object.
    Returns:
        tuple: A tuple containing:
            - restrict_stock_creation (str): Restriction on stock creation ('true' or 'false').
            - configured_batch_attributes (list): List of additional batch attributes configured.
            - configured_non_batch_attributes (list): List of non-mandatory batch attributes configured.
    """
    misc_types = ['restrict_stock_creation', 'additional_batch_attributes', 'non_mandatory_batch_attributes']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    restrict_stock_creation = misc_dict.get('restrict_stock_creation', "false")

    configured_batch_attributes, configured_non_batch_attributes = [], []
    #Additional batch details
    additional_batch_details, non_mandatory_batch_attributes = (
        misc_dict.get('additional_batch_attributes'),
        misc_dict.get('non_mandatory_batch_attributes'),
    )

    if additional_batch_details not in ['false', '', None, False]:
        configured_batch_attributes = additional_batch_details.split(',')

    if non_mandatory_batch_attributes not in ['false', '', None, False]:
        configured_non_batch_attributes = non_mandatory_batch_attributes.split(',')

    return restrict_stock_creation, configured_batch_attributes, configured_non_batch_attributes


def get_stock_detail_search_params(final_dict):
    """
    Function to prepare search parameters for StockDetail based on the final_dict.
    It extracts SKU ID, location ID, and stock status from the final_dict
    """
    sku_id = final_dict.get('sku')[0]
    stock_search_params = {
        'sku_id': sku_id, 'location_id': final_dict.get('location')[0]
    }
    if final_dict.get('stock_status'):
        stock_search_params['status'] = final_dict.get('stock_status')

def get_batch_detail_attributes(final_dict):
    """
    Function to retrieve batch detail attributes based on the final_dict.
    It constructs a dictionary with batch details such as batch number, batch reference,
    vendor batch number, expiry date, manufactured date, MRP, best before date,
    retest date, reevaluation date, and inspection lot number.
    Args:
        final_dict (dict): Dictionary containing SKU and batch details.
    Returns:
        dict: A dictionary containing batch detail attributes.
    """
    batch_filters = {'sku_id': final_dict.get('sku_id')}
    if final_dict.get('batch_no'):
        batch_filters.update({'batch_no': final_dict.get('batch_no')})
    if final_dict.get('batch_reference'):
        batch_filters.update({'batch_reference': final_dict.get('batch_reference')})
    if final_dict.get('vendor_batch_number'):
        batch_filters.update({'vendor_batch_no': final_dict.get('vendor_batch_number')})

    batch_obj = BatchDetail.objects.filter(**batch_filters).values(
        'batch_no', 'batch_reference', 'vendor_batch_no',
        'expiry_date', 'manufactured_date', 'mrp', 'best_before_date',
        'retest_date', 'reevaluation_date', 'inspection_lot_number'
    ).first()

    if batch_obj:
        final_dict['batch_no'] = batch_obj.get('batch_no')
        final_dict['batch_reference'] = batch_obj.get('batch_reference')
        final_dict['vendor_batch_number'] = batch_obj.get('vendor_batch_no')

    batch_details_dict = {
        "transact_type": "cycle_count",
        "batch_no": final_dict.get("batch_no"),
        "batch_reference" : final_dict.get("batch_reference", ''),
        "vendor_batch_no" : final_dict.get("vendor_batch_number", ''),
        "sku_id": final_dict.get("sku_id")
    }

    update_attributes = [
        'expiry_date', 'manufactured_date', 'mrp', 'best_before_date',
        'retest_date', 'reevaluation_date', 'inspection_lot_number', 'weight'
    ]
    for attr in update_attributes:
        if attr == 'mrp':
            batch_details_dict['mrp'] = float(final_dict.get(attr))
        elif final_dict.get(attr):
            batch_details_dict[attr] = final_dict.get(attr)

    return batch_details_dict

def validate_configured_batch_attributes(
        error_status, batch_detail_dict, configured_batch_attributes,
        configured_non_batch_attributes
    ):
    """
    Validate Configured Batch Attributes
    This function checks if the batch details provided in batch_detail_dict
    match the configured batch attributes and non-mandatory attributes.
    Args:
        error_status (list): List to store error messages.
        batch_detail_dict (dict): Dictionary containing batch details.
        configured_batch_attributes (list): List of configured batch attributes.
        configured_non_batch_attributes (list): List of non-mandatory batch attributes.
    Returns:
        list: Updated error_status with validation messages.
    """
    required_batch_attributes, not_required_batch_attributes = [], []
    for batch_attribute in configured_batch_attributes:
        if batch_attribute == 'batch_number':
            batch_attribute = 'batch_no'
        if batch_attribute == 'vendor_batch_number':
            batch_attribute = 'vendor_batch_no'

        batch_attr_value = str(batch_detail_dict.get(batch_attribute))

        if batch_attribute in ['mrp']:
            batch_attr_value = float(batch_attr_value)

        if batch_attr_value in ['', None, 'None', '0', 0] and batch_attribute not in configured_non_batch_attributes:
            required_batch_attributes.append(batch_attribute.replace('_', ' ').title())

    if required_batch_attributes:
        error_status.append('{} are Mandatory'.format(', '.join(required_batch_attributes)))

    batch_attributes = [
        'batch_number', 'batch_reference', 'best_before_date',
        'expiry_date', 'inspection_lot_number', 'mrp', 'manufactured_date',
        'reevaluation_date', 'retest_date', 'vendor_batch_number', 'weight'
    ]
    for key, value in batch_detail_dict.items():
        if key == 'batch_no':
            key = 'batch_number'

        if (key in batch_attributes) and (str(value)) and (str(value) not in ['', None, 'None','0.0']) \
            and (key not in configured_batch_attributes):

            not_required_batch_attributes.append(key.replace('_', ' ').title())

    if not_required_batch_attributes:
        error_status.append(
            '{} are Non- Mandatory batch details which can not be updated'. format(
                ', '.join(not_required_batch_attributes)
            )
        )

    return error_status

def get_and_validate_batch_details(
        warehouse, final_dict, restrict_stock_creation, error_status,
        configured_batch_attributes, configured_non_batch_attributes, extra_params
    ):
    """
    Get and Validate Batch Details
    This function retrieves and validates batch details based on the provided final_dict.
    It checks if batch creation is required, validates the batch details,
    and returns the batch object if successful.
    Args:
        warehouse (User): The warehouse object.
        final_dict (dict): Dictionary containing SKU and batch details.
        restrict_stock_creation (bool): Flag to restrict stock creation.
        error_status (bool): Flag to indicate if there are errors.
        configured_batch_attributes (list): List of configured batch attributes.
        configured_non_batch_attributes (list): List of non-mandatory batch attributes.
        extra_params (dict): Additional parameters for batch creation.
    Returns:
        tuple: A tuple containing:
            - error_status (bool): Updated error status after validation.
            - batch_obj (BatchDetail): The batch object if created or retrieved successfully.
            - final_dict (dict): Updated final dictionary with batch details.
    """
    error_list, batch_obj = [], None
    final_dict['Status'] = []
    final_dict['sku_id'] = final_dict.get('sku')[0]
    batch_detail_dict = get_batch_detail_attributes(final_dict)

    if extra_params.get('batch_creation') == 'true':

        batch_dict_for_validations = copy.deepcopy(batch_detail_dict)
        if batch_dict_for_validations.get('mrp', 0) == 0:
            del batch_dict_for_validations['mrp']

        #Validate Batch Details
        error_list, batch_obj = validate_batch_details(warehouse, batch_dict_for_validations, cycle_count=True)
        if error_list:
            final_dict['Status'] = error_list
            error_status = True

        if not batch_obj:
            final_dict['Status'] = validate_configured_batch_attributes(
                final_dict['Status'], batch_detail_dict, configured_batch_attributes,
                configured_non_batch_attributes
            )
            if final_dict['Status']:
                error_status = True

    #Get or Create Batch Details
    if not final_dict.get('Status') and not restrict_stock_creation:
        error_list, batch_obj = get_or_create_batch(warehouse, batch_detail_dict)
        if error_list:
            final_dict['Status'], error_status = error_list, True
    return error_status, batch_obj, final_dict

def get_stock_detail_search_params(final_dict):
    """
    Function to prepare search parameters for StockDetail based on the final_dict.
    It extracts SKU ID, location ID, batch detail ID, stock status, and LPN number from the final_dict.
    Args:
        final_dict (dict): Dictionary containing SKU and stock details.
    Returns:
        dict: A dictionary containing search parameters for StockDetail.
    """
    stock_filters = {'sku_id': final_dict['sku_id'], 'location_id': final_dict.get('location')[0]}
    if final_dict.get('batch_detail_id'):
        stock_filters.update({'batch_detail_id': final_dict['batch_detail_id']})
    if final_dict.get('stock_status'):
        stock_filters.update({'status': final_dict['stock_status']})
    if final_dict.get('lpn_number'):
        stock_filters.update({'lpn_number': final_dict['lpn_number']})

    return stock_filters

def check_and_create_new_stock(warehouse, receipt_number, final_dict, restrict_stock_creation, 
                               error_status, available_stock_data):
    """
    Function to check and create new stock details based on the provided final_dict.
    It validates existing stock details, prepares stock creation parameters,
    and creates a new StockDetail object if necessary.
    Args:
        warehouse (User): The warehouse object.
        receipt_number (int): The current receipt number for stock details.
        final_dict (dict): Dictionary containing SKU and stock details.
        restrict_stock_creation (bool): Flag to restrict stock creation.
        error_status (bool): Flag to indicate if there are errors.
        available_stock_data (pd.DataFrame): DataFrame containing available stock data.
    Returns:
        tuple: A tuple containing:
            - stock_creation_obj (StockDetail): The StockDetail object if created, else None.
            - receipt_number (int): Updated receipt number after stock creation.
            - error_status (bool): Updated error status after stock creation.
            - final_dict (dict): Updated final dictionary with stock details.
    """
    stock_creation_obj = None
    data_frame_filter = {}
    #Get Stock Filter Params
    stock_detail_params = get_stock_detail_search_params(final_dict)
    for field_name, field_value in stock_detail_params.items():
        if field_name == 'lpn_number':
            data_frame_filter[field_name] = f"== '{field_value}'"
        else:
            data_frame_filter[field_name] = f"== {field_value}"

    stock_details = False
    stock_validation_df = pd.DataFrame()
    validation_conditions = [
        f"({column} {condition})" for column, condition in data_frame_filter.items()
    ]
    if validation_conditions:
        query = " & ".join(validation_conditions)
        try:
            if not available_stock_data.empty:
                stock_validation_df = available_stock_data.query(query)
            else:
                stock_validation_df = pd.DataFrame()
        except Exception as e:
            log.info(f"Error in validating duplicate records: {str(e)}")
            stock_validation_df = pd.DataFrame()

    #Check Stocks
    if not stock_validation_df.empty:
        stock_details = True

    if not stock_details:
        if restrict_stock_creation:
            final_dict['Status'], error_status = ("Cannot Create New Stock", True)

        if not final_dict.get('Status'):

            #Preparting Stock Creation Params
            stock_creation_dict = {
                'receipt_type': 'inv_adjustment', 'receipt_date': datetime.datetime.now(),
                'receipt_number': receipt_number + 1, 'account_id': warehouse.userprofile.id,
                'status': 1, 'sku_id': final_dict.get('sku_id'),
                'location_id': final_dict['location'][0],
                'original_quantity': float(final_dict.get('quantity', 0)),
                'unit_price': float(final_dict.get('unit_price', 0)) if final_dict.get('unit_price') else 0,
            }
            if final_dict.get('stock_status'):
                stock_creation_dict['status'] = final_dict.get('stock_status')

            if final_dict.get('batch_detail_id'):
                stock_creation_dict['batch_detail_id'] = final_dict.get('batch_detail_id')
            if final_dict.get('lpn_number'):
                stock_creation_dict['lpn_number'] = final_dict.get('lpn_number')
            stock_creation_obj = StockDetail(**stock_creation_dict)
            receipt_number += 1

    return stock_creation_obj, receipt_number, error_status, final_dict

def get_available_cycle_count_data(warehouse, query_data_dict):
    """
    Function to retrieve available cycle count data for a given warehouse.
    It filters CycleCount objects based on the warehouse ID, status, and additional query parameters.
    Args:
        warehouse (User): The warehouse object.
        query_data_dict (dict): Dictionary containing additional query parameters for filtering.
    Returns:
        pd.DataFrame: A DataFrame containing available cycle count data with relevant fields.
    """
    available_cycle_data_df = pd.DataFrame(CycleCount.objects.filter(
        sku__user = warehouse.id, status = 1, **query_data_dict
    ).values('id','sku_id','location_id','batch_detail_id','stock_status','price', 'lpn_number'))
    return available_cycle_data_df


def get_cycle_count_bulk_creation_objs(warehouse, data_list, cycle_type, query_data_dict):
    """
    Function to prepare bulk creation objects for CycleCount based on the provided data list.
    It constructs CycleCount objects with relevant fields and checks for existing cycle counts.
    Args:
        warehouse (User): The warehouse object.
        data_list (list): List of dictionaries containing cycle count data.
        cycle_type (str): The type of cycle count (e.g., 'Uploads', 'Adhoc').
        query_data_dict (dict): Dictionary containing additional query parameters for filtering.
    Returns:
        tuple: A tuple containing:
            - bulk_cycle_objs (list): List of CycleCount objects to be created.
            - custom_fields (dict): Dictionary of custom fields from the data list.
            - serial_dict (dict): Dictionary mapping cycle IDs to their serial numbers.
    """
    bulk_cycle_objs, custom_fields, existing_cycle_ids, serial_dict = [], {}, [], {}
    available_cycle_data = get_available_cycle_count_data(warehouse, query_data_dict)

    #Cycle Count Creation
    cycle_id = get_incremental(warehouse, 'cycle_id', '', False, len(data_list))
    for final_dict in data_list:
        data_frame_filter = {}
        cycle_data = {
            'sku_id': final_dict.get('sku')[0],
            'location_id': final_dict.get('location')[0],
            'price': final_dict.get('unit_price', 0) or 0
        }
        if final_dict.get('stock_status') not in ['', None]:
            cycle_data['stock_status'] = str(final_dict.get('stock_status'))

        if final_dict.get('batch_detail_id'):
            cycle_data['batch_detail_id'] = final_dict.get('batch_detail_id')
        if final_dict.get('lpn_number'):
            cycle_data['lpn_number'] = final_dict.get('lpn_number')

        #Check whether the cycle already exists, and changing the status if already exists
        for field_name, field_value in cycle_data.items():
            if field_name == 'lpn_number':
                data_frame_filter[field_name] = f"== '{field_value}'"
            else:
                data_frame_filter[field_name] = f"== {field_value}"

        cycle_validation_df = pd.DataFrame()

        validation_conditions = [
        f"({column} {condition})" for column, condition in data_frame_filter.items()
        ]
        if validation_conditions:
            query = " & ".join(validation_conditions)
            try:
                if not available_cycle_data.empty:
                    cycle_validation_df = available_cycle_data.query(query)
                else:
                    cycle_validation_df = pd.DataFrame()
            except Exception as e:
                log.info(f"Error in validating duplicate records: {str(e)}")
                cycle_validation_df = pd.DataFrame()

        if not cycle_validation_df.empty:
            existing_cycle_ids.append(cycle_validation_df['id'].values[0])

        run_type = 'adhoc' if cycle_type == 'Adhoc' else final_dict.get('adjustment_type', 'unscheduled')
        cycle_data.update({
            'account_id': warehouse.userprofile.id,
            'cycle': cycle_id,
            'run_type': run_type, 'json_data': {
                'seen_qty': final_dict.get('quantity'),
                'upload_reason': final_dict.get('reason'),
                'source': 'User Generated'
            }
        })
        bulk_cycle_objs.append(CycleCount(**cycle_data))
        custom_fields = final_dict.get('custom_fields')
        if final_dict.get('serial_numbers'):
            serial_dict.update({cycle_id: final_dict.get('serial_numbers')})
        cycle_id += 1
    if existing_cycle_ids:
        CycleCount.objects.filter(id__in = existing_cycle_ids).update(status = '4', remarks = 'System Updated')

    return bulk_cycle_objs, custom_fields, serial_dict

def return_error_status(error_data_list):
    """
    Function to return error status for cycle count uploads.
    It processes the error data list, updates the fields based on specific conditions,
    and renames the dictionary keys according to a predefined keymap.
    Args:
        error_data_list (list): List of dictionaries containing error data for cycle counts.
    Returns:
        list: A list of dictionaries with updated fields and renamed keys.
    """
    updated_list = [
    {k: ('' if v is False else (v[1] if isinstance(v, list) and k in ['sku', 'location'] else v)) for k, v in d.items()}
    for d in error_data_list
    ]
    status = rename_dict_keys(CYCLE_COUNT_EXCEL_HEADERS_KEYMAP, updated_list,reverse=True)
    return status

def confirm_cycle_count_uploads(request, warehouse, cycle_objs, custom_fields, cycle_type, extra_params, serial_dict, request_data):
    """
    Function to confirm cycle count uploads.
    It processes the cycle count objects, prepares the cycle quantity details,
    and submits the cycle count for processing.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
        cycle_objs (QuerySet): QuerySet of CycleCount objects to process.
        custom_fields (dict): Dictionary of custom fields for cycle counts.
        cycle_type (str): The type of cycle count (e.g., 'Uploads', 'Adhoc').
        extra_params (dict): Additional parameters for processing.
        serial_dict (dict): Dictionary mapping cycle IDs to their serial numbers.
        request_data (dict): Dictionary containing request data.
    Returns:
        list: A list of cycle IDs that were processed.
    """
    return_cycle_ids = []
    new_serial_dict = {}
    skip_callback = request_data.get('skip_callback', False) or False
    skip_inventory_adjustment = request_data.get('skip_inventory_adjustment', False) or False
    cycle_quantity_details = {'custom_fields': custom_fields, 'source': cycle_type}
    for cycle in cycle_objs:
        new_serial_dict.update({str(cycle.id): serial_dict.get(cycle.cycle, [])})
        cycle_id = cycle.id
        return_cycle_ids.append(cycle_id)
        cycle_quantity_details.update({cycle_id: str(cycle.json_data.get('seen_qty'))})
    
    cycle_quantity_details.update({'serial_dict': new_serial_dict, 'skip_callback': skip_callback, 'skip_inventory_adjustment': skip_inventory_adjustment})

    if cycle_quantity_details:
        if cycle_type == 'Uploads':
            request_user_id = request.id
            request_META = {'HTTP_REFERER': extra_params.get('base_url')}
        else:
            request_user_id = request.user.id
            request_META = {'HTTP_REFERER': request.META.get('HTTP_REFERER', '')}
        # async_submit_cycle_count.apply_async(
        #     (warehouse.id, request_user_id, cycle_quantity_details, request_META)
        # )
        async_submit_cycle_count(warehouse.id, request_user_id, cycle_quantity_details, request_META)
    return return_cycle_ids

# @celery_app.task
def async_submit_cycle_count(warehouse_id, request_user_id, cycle_quantity_details, request_META):
    """
    Asynchronous function to submit cycle count details.
    It prepares the request object and calls the submit_cycle_count function.
    Args:
        warehouse_id (int): The ID of the warehouse.
        request_user_id (int): The ID of the user making the request.
        cycle_quantity_details (dict): Dictionary containing cycle quantity details.
        request_META (dict): Dictionary containing request metadata.
    Returns:
        None
    """
    inv_request = RequestFactory()
    inv_request.method= 'POST'
    inv_request.POST = cycle_quantity_details
    inv_request.warehouse = User.objects.get(id=warehouse_id)
    inv_request.user = User.objects.get(id=request_user_id)
    inv_request.META = request_META
    submit_cycle_count(inv_request)

def validate_batch_based_on_batch_flag(final_dict):
    """
    Function to validate if batch attributes are provided for non-batch based SKUs.
    It checks if any batch attributes are present in the final_dict when the 'batch_based' flag is not set.
    Args:
        final_dict (dict): Dictionary containing SKU and batch details.
    Returns:
        bool: True if batch attributes are present for non-batch based SKUs, False otherwise.
    """
    non_batch_based_data = False
    batch_attributes = ['manufactured_date', 'expiry_date', 'mrp', 'weight', 'batch_reference', 'vendor_batch_number',
                            'batch_no', 'best_before_date', 'inspection_lot_number', 'reevaluation_date']

    if not final_dict.get('batch_based'):
        for attr in batch_attributes:
            if final_dict.get(attr) and final_dict.get(attr) not in ['0', '0.0']:
                non_batch_based_data = True
    return non_batch_based_data

def get_available_stock_data(warehouse, data_list):
    """
    Function to retrieve available stock data for a given warehouse and data list.
    It filters StockDetail objects based on SKU codes and location names from the data list.
    Args:
        warehouse (User): The warehouse object.
        data_list (list): List of dictionaries containing SKU and location details.
    Returns:
        tuple: A tuple containing:
            - stock_data_df (pd.DataFrame): DataFrame containing available stock data.
            - sku_code_list (list): List of SKU codes from the data list.
            - location_list (list): List of location names from the data list.
    """
    sku_code_list = [data.get('sku_code') for data in data_list]
    location_list = [data.get('location')[1] for data in data_list]
    stock_data_df = pd.DataFrame(StockDetail.objects.select_related('sku', 'location').filter(
        sku__user = warehouse.id, sku__sku_code__in = sku_code_list, location__location__in = location_list
        ).values('sku_id','location_id','batch_detail_id','status','lpn_number').distinct())

    return stock_data_df, sku_code_list, location_list

def validate_serial_number_data(warehouse, data_list):
    """
    Validates serial number data for cycle count uploads.
    It checks if the serial numbers are provided for SKUs that require them,
    and validates the serial numbers against the configured serial number attributes.
    Args:
        warehouse (User): The warehouse object.
        data_list (list): List of dictionaries containing cycle count data.
    Returns:
        tuple: A tuple containing:
            - data_list (list): Updated list of dictionaries with validation results.
            - error (bool): True if there are validation errors, False otherwise.
    """
    serial_mixin = SerialNumberMixin(warehouse, warehouse, data_list)
    data_list = serial_mixin.validate_create_data()
    error = any([data.get('Status') for data in data_list])
    return data_list, error

def generate_and_submit_cycle_count(request, warehouse, data_list, cycle_type = 'Uploads', extra_params = None):
    """
    Function to generate and submit cycle count based on the provided data list.
    It processes the data list, validates batch details, checks and creates stock details,
    and prepares cycle count objects for bulk creation.
    Args:
        request: The HTTP request object.
        warehouse (User): The warehouse object.
        data_list (list): List of dictionaries containing cycle count data.
        cycle_type (str): The type of cycle count (e.g., 'Uploads', 'Adhoc').
        extra_params (dict): Additional parameters for processing.
    Returns:
        dict: A dictionary containing cycle IDs that were processed.
    """
    try:
        if cycle_type == "Adhoc":
            request_data = request.body
            if request_data:
                request_data = loads(request_data)
        else:
            request_data = {}
        return_cycle_ids = []
        extra_params = extra_params or {}
        log.info(("Generate and Submit Cycle Count for Username: %s, and data params are: %s") % (str(warehouse.username), str(data_list)))

        #Misc Details
        restrict_stock_creation, configured_batch_attributes, configured_non_batch_attributes = get_misc_details(warehouse)
        restrict_stock_creation = True if restrict_stock_creation and restrict_stock_creation not in ['false'] else False

        #Max Receipt Number
        receipt_number = get_incremental(warehouse, 'inv_adj')

        stock_creation_list, error_data_list, error_status = [], [], False
        available_stock_data, sku_code_list, location_list  = get_available_stock_data(warehouse, data_list)
        query_data_dict = {'sku__sku_code__in': sku_code_list, 'location__location__in': location_list}
        for final_dict in data_list:
            #Batch Detail Validation Attributes
            if final_dict.get('batch_based') and not final_dict.get('batch_detail_id'):
                error_status, batch_obj, final_dict = get_and_validate_batch_details(
                    warehouse, final_dict, restrict_stock_creation, error_status,
                    configured_batch_attributes, configured_non_batch_attributes,
                    extra_params
                )
                if not error_status:
                    final_dict['batch_detail_id'] = batch_obj.id if batch_obj else None

            error = validate_batch_based_on_batch_flag(final_dict)
            if error:
                error_status = True
                final_dict.update({'Status':["Batch attribute can not be passed for non batch based SKUs"]})

            if not error_status:
                #Check Stock and Creates New Stock if not Exists with 0 quantity
                stock_creation_obj, receipt_number, error_status, final_dict = check_and_create_new_stock(
                    warehouse, receipt_number, final_dict, restrict_stock_creation, error_status, available_stock_data
                )
                stock_creation_list.append(stock_creation_obj) if stock_creation_obj else None
            error_data_list.append(final_dict)

        #Returns If any error
        if error_status:
            if cycle_type == "Adhoc":
                return error_data_list

            status = return_error_status(error_data_list)
            return status
        
        data_list, error = validate_serial_number_data(warehouse, data_list)
        if error:
            if cycle_type == "Adhoc":
                return data_list

            status = return_error_status(data_list)
            return status

        #Bulk Stock Creation
        StockDetail.objects.bulk_create_with_rounding(stock_creation_list)

        #Bulk Cycle Count Creation
        bulk_cycle_objs, custom_fields, serial_dict = get_cycle_count_bulk_creation_objs(warehouse, data_list, cycle_type, query_data_dict)
        if bulk_cycle_objs:
            cycle_objs = CycleCount.objects.bulk_create_with_rounding(bulk_cycle_objs)

            #Call Submit Cycle Count
            return_cycle_ids = confirm_cycle_count_uploads(request, warehouse, cycle_objs, custom_fields, cycle_type, extra_params, serial_dict, request_data)
        return {'cycle_ids': return_cycle_ids}

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(f"Generate and Submit Cycle Count Failed for Username: {warehouse.username}, data params are: {data_list} and exception is {e}")
        return 'Failed'

class ScheduledCycleCountSet(WMSListView):
    """
    View to handle the creation and updating of cycle count schedules.
    This view allows users to create new cycle count schedules or update existing ones.
    It supports both POST and PUT methods for creating and updating schedules respectively.
    """
    def post(self, *args, **kwargs):
        """
        Create Cycle Count Schedule.
        This method creates a new cycle count schedule for the given warehouse based on the provided JSON payload.
        Request Payload:
        A JSON array of objects, where each object represents a cycle count schedule to create. Each object can have the following
        keys:
        - `type` (str, required): The type of cycle count (e.g., 'Category', 'Sub Category', 'Zone', 'Past Picking', 'Past Putaway').
        - `value` (str, optional): The value associated with the cycle count type (e.g., category name, sub-category name, zone name).
        - `frequency` (int, required): The frequency in days for the cycle count schedule. Must be a positive integer.
        Response:
        - On success: Returns a JSON object with a message indicating the schedule was created successfully and HTTP status 200.
        - On invalid input (e.g., missing or invalid `type`, `value`, or `frequency`): Returns a JSON object with an error message and HTTP status 400.
        - On server error: Returns a JSON object with a message "Internal Server Error" and HTTP status 500.
        """
        request = self.request
        status = ''
        try:
            self.set_user_credientials()
            warehouse = self.warehouse
            cycle_count_schedulers = loads(request.body)
            log_message = (("Request Create Cycle Count Schedule Username %s, IPAddress %s and params are %s") % (
                str(warehouse.username),str(get_user_ip(request)), str(cycle_count_schedulers)
            ))
            log.info(log_message)
            for cycle_count in cycle_count_schedulers:
                type_ = cycle_count.get('type','')
                value = cycle_count.get('value','')
                frequency = cycle_count.get('frequency','')
                if type_ in ('Category', 'Sub Category', 'Zone') and not (type_ and frequency and value):
                    return JsonResponse({'message': 'Type, Value and Frequency are mandatory'}, status = 400)

                if type_ in ('Past Picking', 'Past Putaway') and not (type_ and frequency):
                    return JsonResponse({'message': 'Type and Frequency are mandatory'}, status = 400)

                try:
                    frequency = int(frequency)
                except ValueError:
                    return JsonResponse({'message': 'Invalid Frequency'}, status = 400)

                if not frequency or frequency <= 0:
                    return JsonResponse({'message': 'Frequency is mandatory'}, status = 400)

                cycle_count_schedule_obj = CycleCountSchedule.objects.filter(
                    user = warehouse, cycle_type = type_, cycle_value = value, status = 0
                ).exists()

                if cycle_count_schedule_obj:
                    error_msg = f"Record already exists for Zone {value}"
                    return JsonResponse({'message': error_msg}, status = 400)
                else:
                    cycle_count_obj = CycleCountSchedule.objects.create(
                        user = warehouse, cycle_type = type_, cycle_value = value,frequency_days = frequency,
                        account_id = warehouse.userprofile.id
                    )
                    cycle_count_obj.save()
                    status = "created successfully"
            return JsonResponse({'message': status}, status = 200)

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log_message = (("Cycle Count Schedule failed for Username %s, IPAddress %s and error statement is %s") %
            (str(warehouse.username), str(get_user_ip(request)), str(e)))
            status =  "Internal Server Error"
        return JsonResponse({'message': status}, status = 400)

    def put(self, *args, **kwargs):
        """
        Update Cycle Count Schedule.

        This method updates the cycle count schedule for the given warehouse based on the provided JSON payload.

        Request Payload:
        A JSON array of objects, where each object represents a cycle count schedule to update. Each object can have the following keys:
        - `id` (int, required): The ID of the cycle count schedule to update.
        - `delete` (bool, optional): If present and true, marks the schedule as deleted (status = 1).
        - `frequency` (int, optional): The frequency in days for the cycle count schedule. Must be a positive integer.

        Response:
        - On success: Returns a JSON object with a message "Updated Successfully" and HTTP status 200.
        - On invalid input (e.g., missing or invalid `frequency`): Returns a JSON object with an error message and HTTP status 400.
        - On server error: Returns a JSON object with a message "Internal Server Error" and HTTP status 500.
        """
        self.set_user_credientials()
        warehouse = self.warehouse
        request = self.request
        try:
            cycle_count_schedule_ids = loads(request.body)
            log_message = (("Request Update Cycle Count Schedule Username %s, IPAddress %s and params are %s ") % (
                str(warehouse.username), str(get_user_ip(request)), str(cycle_count_schedule_ids)
            ))
            log.info(log_message)

            for schedule_entry in cycle_count_schedule_ids:
                if 'delete' in schedule_entry.keys():
                    CycleCountSchedule.objects.filter(user = warehouse, id = schedule_entry['id']).update(status = 1)
                else:
                    frequency_days = schedule_entry.get('frequency', 0) or 0
                    try:
                        frequency_days = int(frequency_days)

                    except ValueError:
                        return JsonResponse({'message': 'Invalid Frequency'}, status = 400)

                    if not frequency_days or frequency_days <= 0:
                        return JsonResponse({'message': 'Frequency is mandatory'}, status = 400)

                    CycleCountSchedule.objects.filter(user = warehouse, id = schedule_entry['id']).\
                            update(frequency_days = frequency_days)

            return JsonResponse({'message': "Updated Successfully"}, status = 200)

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log_message = (("Cycle Count Schedule failed for Username %s, IPAddress %s and error statement is %s") %
            (str(warehouse.username), str(get_user_ip(request)), str(e)))
            return JsonResponse({'message': "Internal Server Error"}, status = 500)

@get_warehouse
def get_category_list(request, warehouse:User):
    """
    Function to get the list of categories and sub-categories for SKUs in a warehouse.
    """
    sku_obj = list(SKUMaster.objects.filter(
        user=warehouse.id, sku_category__isnull = False, sub_category__isnull = False
    ).values('sku_category', 'sub_category').distinct())

    categories, sub_categories = [], []
    for sku in sku_obj:
        categories.append(sku.get('sku_category'))
        sub_categories.append(sku.get('sub_category'))
    return HttpResponse(dumps({'categories' : categories, 'sub_categories': sub_categories}))


class CycleCountSet(WMSListView):

    def get_queryset(self, args, kwargs, warehouse=None):
        return None

    def frame_date_filters(self, request_data, search_params):
        """
        Frame date filters based on the request data.

        Args:
            request_data (dict): The request data containing the date filter values.

        Returns:
            dict: The date filters dictionary.

        """
        try:
            # Parsing the date filters to convert them to the required format
            date_keys = {'from_date': 'creation_date__gte', 'to_date': 'creation_date__lte', 'updation_date': 'updation_date__gte'}
            for key, filter_key in date_keys.items():
                if request_data.get(key):
                    parsed_date = parser.parse(request_data[key])
                    localized_date = pytz.timezone(self.timezone).localize(parsed_date)
                    search_params[filter_key] = localized_date.astimezone(pytz.UTC)
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('GET Cycle Count API Date Filters Failed for {} and params are {} and error statement is {}'.format(
                self.request.user.username, request_data, e))

        return search_params

    def prepare_search_dict(self, request_data, search_params):
        """
        Prepare search parameters for Cycle Count based on the request data.
        """
        status_dict = {'open': 2, 'completed': 3, 'cancelled': 'inventory_delete'}
        skus ,limit = [], 10
        if request_data.get('limit'):
            limit = request_data['limit']

        if request_data.get('sku_code'):
            search_params['sku__sku_code'] = request_data['sku_code']

        skus = request_data.get('sku_list', [])
        if skus:
            search_params['sku__sku_code__in'] = skus

        search_params = self.frame_date_filters(request_data, search_params)

        status = request_data.get('status')
        if status:
            search_params['status'] = status_dict.get(status, status)

        return search_params, limit

    def get_adjustment_status(self, cycle):
        """
        Get the adjustment status based on the cycle count status and remarks.
        """
        adjustment_status = ''
        if cycle.get('status') == '3':
            if cycle.get('remarks'):
                adjustment_status = cycle.get('remarks').capitalize()
            else:
                adjustment_status = 'Approved'
        elif cycle.get('status') == '4':
            adjustment_status = cycle.get('remarks','').capitalize()
        elif cycle.get('status') == '2':
            adjustment_status = 'Not yet Approved'
        if cycle.get('status') == 'inventory_delete':
            adjustment_status = 'Rejected'

        return adjustment_status

    def get(self, *args, **kwargs):
        """
        Get Cycle Count Details.
        This method retrieves cycle count details based on the provided search parameters.
        It supports pagination and filtering based on SKU codes, status, and date ranges.
        Request Parameters:
        - `sku_code` (str, optional): SKU code to filter cycle counts.
        - `sku_list` (list, optional): List of SKU codes to filter cycle counts.
        - `status` (str, optional): Status of the cycle count (e.g., 'open', 'completed', 'cancelled').
        - `from_date` (str, optional): Start date to filter cycle counts (format: 'YYYY-MM-DD').
        - `to_date` (str, optional): End date to filter cycle counts (format: 'YYYY-MM-DD').
        - `limit` (int, optional): Number of records to return per page (default: 10).
        Response:
        - On success: Returns a JSON object with cycle count details, including warehouse name, cycle ID, SKU details,
          location details, quantity, seen quantity, run type, difference, schedule ID, batch number, and status.
        - On error: Returns a JSON object with an error message and HTTP status 400.
        """
        self.set_user_credientials()
        request = self.request
        data_list = []
        warehouse = self.warehouse
        limit = 10
        total_count = 0
        error_status = []
        request_data = request.GET
        self.timezone = get_user_time_zone(self.warehouse)
        search_params = {'sku__user__in': [warehouse.id]}

        if request_data:
            search_params, limit =  self.prepare_search_dict(request_data, search_params)

        #Exlcudes
        excludes = {'status': 'inventory_delete'}
        if search_params.get('status') and search_params.get('status') == 'inventory_delete':
            excludes = {}

        cycle_count_objs = CycleCount.objects.exclude(**excludes).filter(**search_params)

        total_count = cycle_count_objs.count()

        page_info = scroll_data(request, cycle_count_objs, limit=limit, request_type='GET')
        cycle_count_objs = page_info['data']

        if cycle_count_objs:
            values_list = [
                'cycle', 'sku__sku_code', 'sku__sku_desc', 'sku__sku_brand',
                'quantity', 'seen_quantity', 'batch_detail__batch_no', 'batch_detail__manufactured_date',
                'batch_detail__expiry_date', 'batch_detail__mrp',
                'location__location','location__zone__zone', 'status', 'json_data', 'sku__sku_size',
                'master_zone__master_id', 'creation_date', 'updation_date','run_type', 'remarks',
                'location__sub_zone__zone'
            ]
            cycle_count_objs = list(cycle_count_objs.values(*values_list))

            for cycle in cycle_count_objs:
                cycle_dict = {}
                cycle_dict.update({
                    'warehouse': warehouse.username,
                    'cycle_id': cycle.get('cycle'),
                    'sku_code': cycle.get('sku__sku_code'),
                    'sku_description': cycle.get('sku__sku_desc'),
                    'sku_brand': cycle.get('sku__sku_brand'),
                    'sku_size': cycle.get('sku__sku_size'),
                    'location': cycle.get('location__location'),
                    'sub_zone': cycle.get('location__sub_zone__zone'),
                    'zone': cycle.get('location__zone__zone'),
                    'quantity': float(cycle.get('quantity', 0)),
                    'seen_quantity': float(cycle.get('seen_quantity', 0)),
                    'run_type': cycle.get('run_type','').capitalize(),
                    'difference': cycle.get('seen_quantity',0) - cycle.get('quantity',0),
                    'schedule_id': cycle.get('master_zone__master_id',''),
                    'batch_number': cycle.get('batch_detail__batch_no', ''),
                })
                if cycle.get('batch_detail'):
                    cycle_dict.update(
                        {
                            'batch_number': cycle.get('batch_detail__batch_no', ''),
                            'manufacture_date': get_local_date_known_timezone(self.timezone, cycle.get('batch_detail__manufactured_date'), send_date=True).strftime('%Y-%m-%d') if cycle.get('batch_detail__manufactured_date') else '',
                            'expiry_date': get_local_date_known_timezone(self.timezone, cycle.get('batch_detail__expiry_date'), send_date=True).strftime('%Y-%m-%d') if cycle.get('batch_detail__expiry_date') else ''
                        }
                    )
                status, cc_confirm_date, updated_user_name, cc_creation_date =  '','','',''

                #Get Adjustment Status
                adjustment_status = self.get_adjustment_status(cycle)

                if cycle.get('status') != '1':
                    status = 'Checked'
                    cc_confirm_date = get_local_date_known_timezone(self.timezone, cycle.get('updation_date'))
                cc_creation_date = get_local_date_known_timezone(self.timezone, cycle.get('creation_date'))
                if cycle.get('json_data'):
                    updated_user_name = cycle.get('json_data').get('requested_user', '')

                cycle_dict.update(
                    {
                        'cycle_status': status,
                        'adjustment_status': adjustment_status,
                        'updated_user_name': updated_user_name,
                        'generation_date': cc_creation_date,
                        'confirmation_date': cc_confirm_date
                    }
                )
                data_list.append(cycle_dict)

        page_info['data'] = data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = total_count
        page_info['error'] = [{'message': error_status}]
        return page_info

    def get_cycle_count_get_details(self):
        """
        Get Cycle Count Details for GET request.
        This method retrieves cycle count records based on the provided filters such as date range, SKU code, and cycle type.
        It prepares the data for each cycle count record, including warehouse name, SKU details, location, quantity,
        seen quantity, run type, difference, schedule ID, batch number, status, and adjustment status.
        Returns:
            tuple: A tuple containing:
                - final_data (list): List of dictionaries with cycle count details.
                - total_count (int): Total number of cycle count records.
        """
        cycle_objs, final_data, total_count = [],[],0
        cycle_objs, total_count = self.get_cycle_count_records()
        if cycle_objs:
            final_data = self.prepare_cycle_count_data(cycle_objs)
        return final_data, total_count

    def get_cycle_count_records(self):
        """
        Get Cycle Count Records based on the provided filters.
        This method retrieves cycle count records from the database based on the specified filters such as date range,
        SKU code, and cycle type. It prepares the search parameters and retrieves the cycle count records.
        Returns:
            tuple: A tuple containing:
                - cycle_objs (list): List of dictionaries with cycle count details.
                - total_count (int): Total number of cycle count records.
        """
        search_parameters,search_params = {},{}
        if self.from_date:
            search_params['from_date'] = parser.parse(self.from_date)
            search_params['from_date'] = datetime.datetime.combine(search_params['from_date'], datetime.time())
            search_parameters['creation_date__gt'] = search_params['from_date']
        if self.to_date:
            search_params['to_date'] = parser.parse(self.to_date)
            search_params['to_date'] = datetime.datetime.combine(search_params['to_date'], datetime.time())
            search_parameters['creation_date__lt'] = search_params['to_date']
        if self.sku_code:
            search_parameters['sku__sku_code'] = self.sku_code
        if self.cycle:
            search_parameters['cycle'] = self.cycle
        user_id = self.warehouse.id
        values_list = ['cycle','quantity', 'seen_quantity', 'batch_no', 'sku__sku_code', 'sku__sku_desc', 'sku__sku_brand',
                        'location__location','location__zone__zone', 'status', 'json_data', 'sku__sku_size',
                        'master_zone__master_id', 'creation_date', 'updation_date','run_type', 'remarks']
        cycle_count_records = CycleCount.objects.exclude(status = 0).filter(sku__user = user_id,**search_parameters)
        cycle_objs = list(cycle_count_records.values(*values_list))
        return cycle_objs[self.offset: self.limit + self.offset], cycle_count_records.count()

    def prepare_cycle_count_data(self, cycle_objs):
        """
        Prepare Cycle Count Data for GET request.
        This method processes the cycle count records and prepares the data for each cycle count record.
        It extracts relevant information such as warehouse name, SKU details, location, quantity,
        seen quantity, run type, difference, schedule ID, batch number, status, and adjustment status.
        Returns:
            list: List of dictionaries with cycle count details.
        """
        dept_name = self.warehouse.username
        timezone = get_user_time_zone(self.warehouse)
        final_data = []
        for cycle in cycle_objs:
            cycle_dict = {}
            cycle_dict.update({'warehouse': dept_name,'cycle_id': cycle.get('cycle'), 'sku_code': cycle.get('sku__sku_code'),
                    'sku_description': cycle.get('sku__sku_desc'), 'sku_brand': cycle.get('sku__sku_brand'),
                    'sku_size': cycle.get('sku__sku_size'), 'quantity': float(cycle.get('quantity', 0)),
                    'seen_quantity': float(cycle.get('seen_quantity', 0)), 'run_type': cycle.get('run_type','').capitalize(),
                    'location': cycle.get('location__location'),'zone': cycle.get('location__zone__zone'),
                    'difference': cycle.get('seen_quantity',0) - cycle.get('quantity',0)})

            adjustment_status, status, cc_confirm_date, updated_user_name, cc_creation_date, schedule_id, batch_no, serial_numbers = '', '','','','','','',[]
            if cycle.get('master_zone__master_id'):
                schedule_id = cycle.get('master_zone__master_id','')
            if cycle.get('batch_no') != 'None':
                batch_no = cycle.get('batch_no')
            if cycle.get('status') == '3':
                if cycle.get('remarks'):
                    adjustment_status = cycle.get('remarks').capitalize()
                else:
                    adjustment_status = 'Approved'
            elif cycle.get('status') == '4':
                adjustment_status = cycle.get('remarks','').capitalize()
            elif cycle.get('status') == '2':
                adjustment_status = 'Not yet Approved'
            if cycle.get('status') == 'inventory_delete':
                adjustment_status = 'Rejected'
            if cycle.get('status') != '1':
                status = 'Checked'
                cc_confirm_date = get_local_date_known_timezone(timezone,cycle.get('updation_date'),True).strftime('%Y-%m-%d %I:%M %p')
            cc_creation_date = get_local_date_known_timezone(timezone,cycle.get('creation_date'),True).strftime('%Y-%m-%d %I:%M %p')
            if cycle.get('json_data'):
                updated_user_name = cycle.get('json_data').get('requested_user', '')
                serial_numbers = cycle.get('json_data').get('serial_details',[])
            cycle_dict.update({'batch_no': batch_no, 'schedule_id': schedule_id, 'cycle_status': status, 'adjustment_status': adjustment_status, 'updated_user_name': updated_user_name,
                'generation_date': cc_creation_date, 'confirmation_date': cc_confirm_date, 'serial_numbers': serial_numbers})
            final_data.append(cycle_dict)
        return final_data

#Cycle Count GET for Mobile
def get_cyclecount_data(request, task_objs, employee_id='', warehouse_id='', offset=0, limit=50):
    """
    Function to get cycle count data for mobile applications.
    It retrieves cycle count tasks based on the provided filters and formats the data for mobile display.
    Args:
        request: The HTTP request object.
        task_objs (QuerySet): QuerySet of CycleCountTask objects to filter and format.
        employee_id (str): Employee ID to filter tasks for a specific employee.
        warehouse_id (str): Warehouse ID to filter tasks for a specific warehouse.
        offset (int): Offset for pagination.
        limit (int): Limit for pagination.
    Returns:
        list: A list of formatted cycle count tasks with relevant details.
    """
    final_result =[]
    if employee_id:
        task_objs = task_objs.order_by('-json_data__priority', 'location__pick_sequence', 'cycle')
        task_objs = task_objs[offset:limit+offset]

    values_list = [
        'id', 'cycle', 'master_zone__master_id' ,'sku__sku_code',
        'sku__sku_desc', 'sku__sku_size', 'sku__batch_based', 'location__zone__zone',
        'location__location', 'sku__image_url', 'seen_quantity', 'quantity',
        'run_type', 'start_time', 'json_data__end_time','supplier__name',
        'supplier__supplier_id', 'batch_detail__batch_no', 'sku__user', 'stock_status',
        'batch_detail__batch_reference', 'sku__batch_based', 'sku__scan_picking',
        'batch_detail__manufactured_date', 'batch_detail__expiry_date', 'batch_detail__mrp',
        'location__check_digit',
    ]
    all_tasks_values = list(task_objs.values(*values_list))
    task_dict = {}
    supplier_inventory = get_misc_value('stock_supplier_id', warehouse_id)
    users_list = [request.warehouse.id]
    unique_sku_codes = {each_row.get('sku__sku_code') for each_row in all_tasks_values}

    ean_numbers_dict = get_sku_ean_numbers(unique_sku_codes, users_list)


    #GET Available Stocks
    location_quantity_details = []
    for each_row in all_tasks_values:
        details_dict = {
            'sku_code': each_row.get('sku__sku_code'),
            'location': each_row.get('location__location'),
        }
        if each_row.get('sku__batch_based'):
            details_dict.update({'batch_no': each_row.get('batch_detail__batch_no')})
        location_quantity_details.append(details_dict)

    stock_df = get_current_stock_details(request.warehouse, location_quantity_details)
    stock_df = get_current_available_stock_details(stock_df)

    for each_row in all_tasks_values:
        sku_code = each_row.get('sku__sku_code')
        cycle_id = each_row.get('cycle')
        master_id = each_row.get('master_zone__master_id')
        location = each_row.get('location__location')
        supplier_id = each_row.get('supplier__supplier_id','')
        supplier_name = each_row.get('supplier__name','')
        user = each_row.get('sku__user')
        stock_status = each_row.get('stock_status','')
        system_quantity = 0
        stock_key = (sku_code, location, None)
        if stock_key in stock_df:
            system_quantity = stock_df[stock_key]
        batch_details = get_batch_attributes(each_row, stock_df)
        if supplier_inventory == 'true':
            batch_details.update({
                'supplier_id' : supplier_id, 'supplier_name' : supplier_name,
                'id' : each_row.get('id','')
            })

        #Fetching My Tasks Data
        if employee_id:
            batch_details.update({
                'start_time' : each_row.get('start_time'),
                'end_time' : each_row.get('end_time'),
                'seen_quantity' : each_row.get('seen_quantity'),
                'quantity' : each_row.get('quantity'),
            })

        cycle_key = (cycle_id, sku_code, location, user)
        if cycle_key not in task_dict:
            image_url = each_row.get('sku__image_url')
            if image_url and "http" not in image_url:
                image_url = request.build_absolute_uri('/') + image_url
            image_url = image_url.replace("https://udaan.azureedge.net/products", "https://ud-img.azureedge.net/w_120/u/products")
            task_dict[cycle_key] = {
                    'cycle_id' : cycle_id,
                    'master_id' : master_id,
                    'id' : each_row.get('id'),
                    'sku_code' : sku_code,
                    'location' : location,
                    'check_digit': each_row.get('location__check_digit'),
                    'stock_status': status_choices.get(stock_status),
                    'zone' : each_row.get('location__zone__zone'),
                    'user' : user,
                    'image_url' : image_url,
                    'sku_desc' : each_row.get('sku__sku_desc'),
                    'system_quantity': system_quantity,
                    'items': [batch_details] if batch_details else [],
                    'ean_numbers': ean_numbers_dict.get(sku_code,[]),
                    'batch_based': each_row.get('sku__batch_based'),
                    'cycle_type': each_row.get('run_type', '').lower(),
                    'scannable': each_row.get('sku__scan_picking')}
            if employee_id:
                task_dict[cycle_key].update(
                    {
                    'cycle_type' : each_row.get('run_type'),
                    'employee_id' :each_row.get('employee_id'),
                    'warehouse_id':each_row.get('warehouse_id')
                    }
                )
        else:
            if batch_details:
                task_dict[cycle_key]['items'].append(batch_details)

    for key, value in task_dict.items():
        final_result.append(value)
    return final_result

def get_batch_attributes(each_row, stock_df):
    """
    Function to get batch details for a cycle count task.
    Args:
        each_row (dict): Dictionary containing cycle count task details.
        stock_df (dict): Dictionary containing stock details for batch-based SKUs.
    Returns:
        dict: A dictionary containing batch details such as batch number, reference, system quantity, and dates.
    """
    batch_details = {}
    if each_row.get('sku__batch_based'):
        batch_no = each_row.get('batch_detail__batch_no','')
        batch_reference = each_row.get('batch_detail__batch_reference','')
        system_quantity = 0
        stock_key = (each_row.get('sku__sku_code'), each_row.get('location__location'), batch_no)
        if stock_key in stock_df:
            system_quantity = stock_df[stock_key]
        batch_details.update({
            'id': each_row.get('id',''),
            'system_quantity': system_quantity,
            'batch_number': batch_no,
            'batch_reference': batch_reference,
            'batch_display_key': batch_reference or batch_no,
            'manufactured_date': format_date(timezone.get_current_timezone().zone, each_row.get('batch_detail__manufactured_date')) if each_row.get('batch_detail__manufactured_date') else None,
            'expiry_date': format_date(timezone.get_current_timezone().zone, each_row.get('batch_detail__expiry_date')) if each_row.get('batch_detail__expiry_date') else None,
            'mrp': each_row.get('batch_detail__mrp')
        })
    return batch_details

def get_current_available_stock_details(stock_df):
    """
    Function to convert stock DataFrame to a dictionary with available quantities.
    Args:
        stock_df (DataFrame): DataFrame containing stock details with columns 'sku_code', 'location_name', 'batch_number', and 'available_quantity'.
    Returns:
        dict: A dictionary where keys are tuples of (sku_code, location_name, batch_number) and values are available quantities.
    """
    stock_dict = {}
    for each_row in stock_df:
        sku_code = each_row.get('sku_code')
        location = each_row.get('location_name')
        batch_no = each_row.get('batch_number')
        stock_dict[(sku_code, location, batch_no)] = each_row.get('available_quantity', 0)

    return stock_dict


@get_warehouse
def aggregated_cycle_count(request, warehouse: User):
    """
    Handles the aggregation of cycle count data for a given warehouse.

    This function processes the request to filter and aggregate cycle count data based on the provided
    warehouse and request parameters. It supports filtering by zone and subzone, and aggregates the
    count of items and distinct cycle tasks.

    Args:
        request (HttpRequest): The HTTP request object containing GET parameters.
        warehouse (User): The warehouse user object to filter the cycle count data.

    Returns:
        JsonResponse: A JSON response containing the aggregated cycle count data with the number of open items and tasks.
    """

    request_data = request.GET
    subzone_mapping = get_misc_value('subzone_mapping', warehouse.id)

    filters = {
        'sku__user': warehouse.id,
        'status': 1,
    }
    if request_data.get('zone'):
        zones = request_data.get('zone').split(',')
        if subzone_mapping == 'true':
            filters.update({'location__sub_zone__zone__in': zones})
        else:
            filters.update({'location__zone__zone__in': zones})

    employee_id = request.user.employee.id
    # Fetch Cycle Count aggregated data
    cycle_dict = dict(CycleCount.objects
        .filter(Q(employee__isnull=True) | Q(employee=employee_id), **filters)
        .aggregate(items=Count('id'), tasks=Count('cycle', distinct=True))
    )

    return JsonResponse({'cyclecount': {'open': cycle_dict}})
