#package imports
from functools import reduce
from json import loads
from collections import OrderedDict, defaultdict
import json
import operator

#django imports
from django.db.models import Q, Sum, Max
from django.db.models.functions import Cast
from django.http import JsonResponse
from django.db.models.fields import DateField
from django.core import management
from django.core.cache import cache

#inventory imports
from inventory.models import (
    CycleCount, StockDetail, CycleCountSchedule
)
from inventory.views.locator.stock_detail import (
    choice_mapping, get_restricted_stock_status
)

#wms base imports
from wms_base.wms_utils import init_logger

from wms_base.models import User

#core common functions
from core_operations.views.common.main import (
    get_decimal_value, get_user_time_zone, get_local_date_known_timezone,
    frame_datatable_header_filter, frame_datatable_column_filter,
    get_warehouse, truncate_float, get_company_id, get_uom_decimals,
)
from core_operations.views.integration.integration import webhook_integration_3p

log = init_logger('logs/cycle_count.log')

GenerateCycleCountHeaders = {'wms_code': 'SKU Code', 'sku_desc': 'SKU Description',
    'sku_size': 'SKU Size', 'shelf_life': 'Shelf Life',
    'zone': 'Zone', 'location': 'Location', 'quantity': 'Quantity'
}

CycleConfirmedHeaders = {
    'Schedule ID': 'Schedule ID', 'Cycle Count ID': 'Cycle Count ID',
    'cycle_type': 'Cycle Count Type', 'wms_code': 'SKU Code',
    'sku_desc': 'SKU Description', 'sku_size': 'SKU Size',
    'zone': 'Zone', 'location': 'Location',
    'Date': 'Date', 'Assigned To': 'Assigned To'
}


def get_cycle_count_filters(column_filters, custom_filters, or_filters, modified_column_filters):
    '''
    Function to get cycle count filters based on column headers and custom filters.
    '''
    choice_mapping_new = {value.lower(): key for key, value in choice_mapping.items()}
    for key, value in column_filters.items():
        if key in custom_filters:
            if isinstance(custom_filters[key], list):
                or_filters += [Q(**{custom_filters[key][i]: value}) for i in range(len(custom_filters[key]))]
            else:
                input_values = value.split(',')
                if len(input_values) > 1:
                    or_filters = [Q(**{custom_filters[key]: input_value}) for input_value in input_values]
                else:
                    modified_column_filters[custom_filters[key]] = value

        elif key == 'stock_status__icontains' and value in choice_mapping_new:
            modified_column_filters['status'] = choice_mapping_new.get(value)
        else:
            modified_column_filters[key] = value

    return modified_column_filters, or_filters


def get_cycle_count(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters):
    '''
        Returns Generate Cycle Count Records
    '''
    decimal_limit = get_decimal_value(warehouse.id)
    company_id = get_company_id(warehouse)
    restricted_stock_status = get_restricted_stock_status(warehouse)
    #search terms
    request_data = request.GET
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-sku__sku_code"
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    sort_type = request_data.get('sort_type', 0)
    order_data_dict = {'sku_code': 'sku__sku_code', 'sku_desc': 'sku__sku_desc', 'sku_size': 'sku__sku_size',
                       'zone': 'location__zone__zone', 'location': 'location__location', 'quantity': 'quantity'}
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    else:
        order_data = sort_by_column

    if sort_type == '1':
        order_data = '-%s' % order_data
    
    modified_column_filters = {'sku__user': warehouse.id, 'quantity__gt': 0}
    #Header Search
    column_filters = {}
    or_filters = []
    if column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filters = {
            "sku_code__icontains": "sku__sku_code__icontains",
            "batch_display_key__icontains": ["batch_detail__batch_no__icontains", "batch_detail__batch_reference__icontains"],
            "sku_desc__icontains": "sku__sku_desc__icontains",
            "location__icontains": "location__location__icontains",
            "sub_zone__icontains": "location__sub_zone__zone__icontains",
            "zone__icontains": "location__zone__zone__icontains"
        }

        modified_column_filters, or_filters = get_cycle_count_filters(
            column_filters, custom_filters, or_filters, modified_column_filters
        )

    filter_query = reduce(operator.or_, or_filters) if or_filters else Q()
    
    values = [
        'sku__sku_code', 'sku__sku_desc', 'location__location', 
        'location__zone__zone', 'batch_detail_id', 'batch_detail__batch_no',
        'status', 'location_id', 'sku_id', 'batch_detail__batch_reference',
        'sku__measurement_type', 'location__sub_zone__zone','lpn_number'
    ]

    exclude_dict = {}
    if restricted_stock_status:
        exclude_dict['status__in'] = restricted_stock_status

    #Current Stocks
    cycle_data = StockDetail.objects.select_related('location', 'location__zone', 'sku', 'batch_detail').exclude(**exclude_dict).exclude(
        location__zone__segregation__in=['inbound_staging', 'outbound_staging']
    ).exclude(
        location__zone__storage_type__in =  ['wip_area', 'replenishment_staging_area', 'nte_staging_area']
    ).filter(filter_query, **modified_column_filters).order_by(order_data)


    # Grouped query with annotation for sum of quantity
    grouped_cycle_data = cycle_data.values(*values).annotate(total=Sum('quantity'))

    # Get the count of unique groups without fetching all rows
    if request_data.get('count', '') == 'true':
        temp_data['count'] = grouped_cycle_data.count()  # This fetches only the count of groups
        return

    # Apply slicing after counting
    return_cycle_data = list(grouped_cycle_data[start_index:stop_index])

    temp_data['headers'] = GenerateCycleCountHeaders
    index = 1
    sku_uoms = [details.get('sku__measurement_type') for details in return_cycle_data]
    uom_decimals = get_uom_decimals(company_id, uom_codes = sku_uoms)
    for data in return_cycle_data:
 
        #Round off Based on UOM / Deciaml Limit Config
        uom_decimal_limit = uom_decimals.get(data.get('sku__measurement_type'))
        round_off = uom_decimal_limit or decimal_limit
        quantity = truncate_float(data['total'], round_off)
        batch_no, batch_reference = data.get('batch_detail__batch_no'), data.get('batch_detail__batch_reference')
        temp_data['aaData'].append({
            'sku_id': data.get('sku_id'),
            'sku_code': data.get('sku__sku_code'),
            'sku_desc': data.get('sku__sku_desc'),
            'zone': data.get('location__zone__zone'),
            'location': data.get('location__location'),
            'sub_zone': data.get('location__sub_zone__zone'),
            'stock_status': choice_mapping.get(data.get('status')),
            'batch_no': batch_no,
            'batch_display_key': batch_reference or batch_no,
            'quantity': quantity,
            'id': index,
            'lpn_number' : data.get('lpn_number'),
            'batch_detail_id': data.get('batch_detail_id'),
            'location_id': data.get('location_id')
        })
        index = index + 1

def get_cycle_confirmed(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse,
                        filters=''):
    '''
    Returns Confirmed Cycle Count at sku<>location<>cycle id level
    '''
    filtered_dict = {
        'cycle_count_id': 'cycle', 'wms_code': 'sku__sku_code', 'sku_desc': 'sku__sku_desc',
        'sku_size': 'sku__sku_size', 'zone': 'location__zone__zone', 
        'location': 'location__location', 'sub_zone': 'location__sub_zone__zone',
        'shelf_life': 'sku__shelf_life', 'creation_date': 'creation_date', 
        'cycle_type': 'run_type', 'schedule_id': 'master_zone__master_id',
        'assigned_to': 'employee__user__username'
    }
    #search terms
    sort_by_column, sort_type = 'creation_date', '1'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = filtered_dict.get(request.GET.get('sort_by_column'))

    #Header Search
    column_filters, exclude_filters = {}, {}
    column_headers = request.GET.get('columnFilter', {})
    if column_headers:
        column_filters = frame_datatable_header_filter(loads(column_headers))
    
    modified_column_filters = {'status': 1, 'location__zone__user': warehouse.id}
    batch_key = ''
    for key, value in column_filters.items():
        if key == 'batch_display_key':
            batch_key = Q(Q(batch_detail__batch_no__icontains=value) | Q(batch_detail__batch_reference__icontains=value))
        elif key != "empty_location":
            modified_column_filters.update({filtered_dict.get(key)+"__icontains" : value})
    
    # Handle 'empty_location' case
    if column_filters.get("empty_location") == "true":
        modified_column_filters['run_type'] = 'empty_location'
    else:
        exclude_filters['run_type'] = 'empty_location'

    if not batch_key:
        batch_key = Q()
    
    sort_type = request.GET.get('sort_type')
    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column

    timezone = get_user_time_zone(warehouse)
    values_list = [
        'cycle','sku__sku_code', 'sku__sku_desc', 'sku__sku_size',
        'sku__shelf_life', 'location__zone__zone', 'location__location',
        'run_type','master_zone__master_id','employee__user__username',
        'location__sub_zone__zone', 'json_data'
    ]

    if sort_by_column:
        cycle_data = CycleCount.objects.filter(**modified_column_filters).filter(batch_key).exclude(**exclude_filters).values(*values_list).\
            annotate(date_only=Cast('creation_date', DateField())).order_by(sort_by_column)
    else:
        cycle_data = CycleCount.objects.filter(**modified_column_filters).filter(batch_key).exclude(**exclude_filters).values(*values_list).distinct()

    if request.GET.get('count', '') == 'true':
        temp_data['count'] = cycle_data.count()
        return

    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    temp_data['headers'] = CycleConfirmedHeaders
    cycle_data = cycle_data[start_index:stop_index]
    cycle_id_list = [item['cycle'] for item in cycle_data]
    all_cycle_counts = CycleCount.objects.select_related('sku').filter(location__zone__user=warehouse.id, status=1,cycle__in = cycle_id_list).values('cycle', 'creation_date')
    cycle_counts = all_cycle_counts.values('cycle').annotate(max_creation_date=Max('creation_date'))
    cycle_data_dict = {item['cycle']: item['max_creation_date'] for item in cycle_counts}
    unique_data = {}
    for item in cycle_data:
        master_id = item.get('master_zone__master_id') if item['master_zone__master_id'] else ''
        cycle_type_ = item.get('run_type') if item['run_type'] else 'UnScheduled'
        employee = item.get('employee__user__username') if item['employee__user__username'] else ''
        creation_date = cycle_data_dict.get(item['cycle'])
        json_data = item.get('json_data') or {}
        generation_source = json_data.get('generation_source', '')
        generated_user = json_data.get('requested_user', '')
        key = (item['cycle'], item['sku__sku_code'], item['location__location'])
        if key in unique_data:
            continue
        unique_data[key] = item
        temp_data['aaData'].append(
            OrderedDict((('cycle_count_id', item['cycle']), 
                         ('wms_code', item['sku__sku_code']),
                         ('sku_desc', item['sku__sku_desc']),
                         ('sku_size', item['sku__sku_size']),
                         ('zone', item['location__zone__zone']),
                         ('sub_zone', item['location__sub_zone__zone']),
                         ('location', item['location__location']),
                         ('shelf_life', item['sku__shelf_life']),
                         ('creation_date',  get_local_date_known_timezone(timezone,creation_date)),
                         ('cycle_type', cycle_type_.capitalize()),
                         ('schedule_id',master_id),
                         ('assigned_to',employee),
                         ('stock_status', choice_mapping.get(item.get('stock_status'))),
                         ('generation_source', generation_source),
                         ('generated_user', generated_user),
                         ('DT_RowClass', 'results'), ('DT_RowId', item['cycle']))))
    
def get_scheduled_cycle_count(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters=''):
    '''
    Returns Scheduled Cycle Count
    '''
    
    #search terms
    search_term = request.GET.get('global_search', '')
    sort_by_column, sort_type = 'creation_date', '1'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = request.GET.get('sort_by_column')
    
    #Header Search
    column_filters = {}
    column_headers = request.GET.get('columnFilter', {})
    if column_headers:
        column_filters = frame_datatable_column_filter(loads(column_headers))
    column_filters.update({'status': 0, 'user':warehouse.id})

    sort_type = request.GET.get('sort_type')
    timezone = get_user_time_zone(warehouse)

    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column

    if search_term:
        scheduled_cycle_data = CycleCountSchedule.objects.filter(
            **column_filters
        ).filter(Q(cycle_type__icontains=search_term) | Q(cycle_value__icontains=search_term) |
                 Q(frequency_days__icontains=search_term)).order_by(sort_by_column).\
        values('id','cycle_type','cycle_value','frequency_days')

    elif sort_by_column:
        scheduled_cycle_data = CycleCountSchedule.objects.filter(**column_filters).values(
            'id','cycle_type','cycle_value','frequency_days','creation_date'
        ).distinct().annotate(date_only=Cast('creation_date', DateField())).order_by(sort_by_column)

    else:
        scheduled_cycle_data = CycleCountSchedule.objects.filter(**column_filters).values(
            'id','cycle_type','cycle_value','frequency_days'
        )
    all_cycle_counts = CycleCountSchedule.objects.filter(**column_filters).values('id','creation_date')
    temp_data['recordsTotal'] = scheduled_cycle_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    for item in scheduled_cycle_data[start_index:stop_index]:
        creation_date = all_cycle_counts.filter(id=item['id'])[0]['creation_date']
        temp_data['aaData'].append(
            OrderedDict((('id', item['id']),
			('cycle_type', item['cycle_type']),
            ('cycle_value', item['cycle_value']),
			('frequency', item['frequency_days']),
			('creation_date', get_local_date_known_timezone(timezone,creation_date))
        )))
@get_warehouse
def get_confirmed_cycle(request, warehouse:User):
    '''
    Returns Pop up data for Generated Cycle Count
    '''
    cycle_id = request.GET.get('data_id','')
    if not cycle_id or cycle_id in ["none", "false"]:
        return JsonResponse({'message': "Record Not Found"}, status = 400)
    cycle_data = list(CycleCount.objects.filter(cycle=cycle_id, sku__user=warehouse.id, status=1).values(
        'sku_id', 'location_id', 'batch_detail_id', 'sku__sku_code', 'sku__sku_desc', 'location__location', 'batch_detail__batch_no', 
        'batch_detail_id', 'batch_detail__batch_reference', 'seen_quantity', 'sku__enable_serial_based',
        'location__zone__zone', 'id', 'cycle', 'quantity', 'location__check_digit','lpn_number'
    ))

    sku_ids, location_ids, batch_detail_ids = set(), set(), set()
    for data in cycle_data:
        sku_ids.add(data.get('sku_id'))
        location_ids.add(data.get('location_id'))
        if data.get('batch_detail_id'):
            batch_detail_ids.add(data.get('batch_detail_id'))

    stock_filters = {'sku_id__in': sku_ids, 'location_id__in': location_ids}
    if batch_detail_ids:
        stock_filters['batch_detail_id__in'] = batch_detail_ids

    stock_data_list = list(
        StockDetail.objects.exclude(status = 2).filter(status = 1, **stock_filters).values(
            'sku__sku_code', 'location__location', 'batch_detail__batch_no'
        ).annotate(Sum('quantity'))
    )

    stock_dict = {}
    for stock in stock_data_list:
        key = (stock.get('sku__sku_code'), stock.get('location__location'), stock.get('batch_detail__batch_no'))
        stock_dict[key] = stock.get('quantity__sum')

    total_data, cycle = [], ''
    for dat in cycle_data:
        system_quantity = 0
        cycle = dat.get('cycle')
        batch_no, batch_reference = dat.get('batch_detail__batch_no'), dat.get('batch_detail__batch_reference')
        sku_code, location = dat.get('sku__sku_code'), dat.get('location__location')
        key = (sku_code, location, batch_no)
        if key in stock_dict:
            system_quantity = stock_dict[key]

        total_data.append({
            'id': dat.get('id'), 'wms_code': dat.get('sku__sku_code'), 'sku_desc': dat.get('sku__sku_desc'),
            'zone': dat.get('location__zone__zone'),'location': dat.get('location__location'), 
            'seen_quantity': None, 'batch': batch_no, 'supplier_id': '', 'enable_serial_based': dat.get('sku__enable_serial_based',0),
            'supplier_name': '', 'batch_display_key': batch_reference or batch_no, 'system_quantity': system_quantity,
            'check_digit': dat.get('location__check_digit'),'lpn_number' : dat.get('lpn_number','')
        })
    return JsonResponse({'data': total_data, 'cycle_id': cycle}, status = 200)

@get_warehouse
def multi_select_and_delete_cycle_count(request, warehouse: User):
    """
    Multi-select and delete cycle count records based on provided IDs or parameters.
    """
    try:
        # Parse the incoming request data
        request_data = loads(request.body)
        cycle_ids = request_data.get('cycle_count_ids', [])
        delete_param = request_data.get('delete_param', '')
        column_filters = request_data.get('column_filters', {})

        # Validate input
        if not cycle_ids and not delete_param:
            return JsonResponse({'message': "Invalid request. Provide either 'cycle_count_ids' or 'delete_param'"}, status_code=400)

        # Build initial filters
        cycle_filters = {'sku__user': warehouse.id, 'status': 1}
        if cycle_ids:
            cycle_filters['cycle__in'] = cycle_ids


        #Apply Filters
        filtered_dict = {
            'cycle_count_id': 'cycle', 'wms_code': 'sku__sku_code', 'sku_desc': 'sku__sku_desc',
            'sku_size': 'sku__sku_size', 'zone': 'location__zone__zone',
            'location': 'location__location', 'sub_zone': 'location__sub_zone__zone',
            'shelf_life': 'sku__shelf_life', 'creation_date': 'creation_date',
            'cycle_type': 'run_type', 'schedule_id': 'master_zone__master_id',
            'assigned_to': 'employee__user__username'
        }
        batch_key = ''
        for key, value in column_filters.items():
            if key == 'batch_display_key':
                batch_key = Q(Q(batch_detail__batch_no__icontains=value) | Q(batch_detail__batch_reference__icontains=value))
            else:
                cycle_filters.update({filtered_dict.get(key)+"__icontains" : value})
        if not batch_key:
            batch_key = Q()


        # Query cycle count objects matching the filters
        cycle_objs = CycleCount.objects.filter(**cycle_filters).filter(batch_key).select_related('sku', 'location', 'location__zone')

        # Check for assigned employees
        if cycle_objs.exclude(employee_id=None).exists():
            assigned_users = cycle_objs.exclude(employee_id=None).values_list('employee__user__username', flat=True)
            usernames = ', '.join(assigned_users)
            return JsonResponse(
                {'message': f"Please unassign the following users before deletion: {usernames}"},
            status=400
            )

        # Perform bulk update to mark objects as deleted
        update_cycle_objs = []
        sku_codes, sku_zones_dict = [], defaultdict(list)
        for obj in cycle_objs:
            obj.status = 'cancelled'
            sku_codes.append(obj.sku.sku_code)
            zone_id = obj.location.zone.id
            sku_zones_dict[zone_id].append(obj.sku.sku_code)
            update_cycle_objs.append(obj)

        CycleCount.objects.bulk_update(update_cycle_objs, ['status'], batch_size=100)

        #Inventory Callback
        filters = {
            'sku_codes': sku_codes,
            "zones_data": sku_zones_dict
        }
        webhook_integration_3p(warehouse.id, "cycle_count", filters)

        return JsonResponse({'message': 'Deleted successfully'}, status=200)

    except Exception as e:
        # Log unexpected errors
        import traceback
        log.error(f"Cycle count delete failed for IDs {cycle_ids}. Error: {str(e)}")
        log.debug(traceback.format_exc())
        return JsonResponse({'message': 'Deletion failed', 'error': str(e)}, status=500)


@get_warehouse
def run_scheduled_cycle_count(request, warehouse:User):
    """
    Function to run scheduled cycle count.
    It checks if a cycle count is already in progress and if not, it triggers the scheduled cycle count command.
    Args:
        request: The HTTP request object.
        warehouse: The warehouse object associated with the request.
    Returns:
        JsonResponse: A response indicating the success or failure of the scheduled cycle count.
    """
    response, status = 'Success', 200
    try:
        cache_key = (warehouse.username, "scheduled_cycle_count")
        cache_status = cache.add(cache_key, "True", timeout=20)
        if not cache_status:
            response, status = 'In Progress, Please try again', 400
        else:
            management.call_command("scheduled_cycle_count", user=warehouse.username)
    except Exception:
        response, status = 'Scheduled Cycle Count Failed', 400
    return JsonResponse({'message': response}, status = status)

@get_warehouse
def update_cycle_count_picker(request, warehouse=User):
    """
    Function to update the picker for cycle counts.
    It checks if the picker is provided, retrieves cycle counts for the specified picker,
    and updates the employee and start time for those cycle counts.
    Args:
        request: The HTTP request object.
        warehouse: The warehouse object associated with the request.
    Returns:
        JsonResponse: A response indicating the success or failure of the update operation.
    """
    picker = request.GET.get('employee','')
    if picker:
        cycle_objs = CycleCount.objects.filter(
            sku__user = warehouse.id, status = 1, employee__user__username = picker
        )
        if cycle_objs:
            cycle_objs.update(employee_id = None, start_time = None)
            return JsonResponse({'message': 'Success'}, status = 200)
        else:
            return JsonResponse({'message': 'No Cycle Counts With the Given Sub User'}, status = 400)
    else:
        return JsonResponse({'message': 'Picker is null'}, status = 400)