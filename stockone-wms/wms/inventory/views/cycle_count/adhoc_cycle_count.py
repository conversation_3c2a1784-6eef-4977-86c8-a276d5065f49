#package imports
from json import loads

#django imports
from django.http import JsonResponse

#wms base imports
from wms_base.models import User
from wms_base.wms_utils import (
    init_logger
)

#core common functions
from core_operations.views.common.main import get_warehouse

#inventory imports
from inventory.views.cycle_count.cycle_count import generate_and_submit_cycle_count
from inventory.views.cycle_count.uploads import validate_cycle_count_form


log = init_logger('logs/cycle_count.log')

@get_warehouse
def adhoc_cycle_count(request, warehouse: User):
    """
    Function to handle adhoc cycle count requests.
    It validates the request data, generates a cycle count, and submits it.
    Args:
        request: The HTTP request object containing the cycle count data.
        warehouse: The warehouse object associated with the request.
    Returns:
        JsonResponse: A response indicating the success or failure of the cycle count generation.
    """
    request_data = request.body
    if request_data:
        request_data = loads(request_data)
    data_list = request_data.get('data_list', [])
    request_details = {
                'request_headers': request.headers,
                'request_meta': request.META,
            }
    extra_params = {
                    "headers": {
                        "Warehouse": request_details.get('request_headers', {}).get("Warehouse"),
                        "Authorization": request_details.get('request_headers', {}).get('Authorization', ''),
                    },
                    "request_meta": request_details.get('request_meta', {})
                }
    status, data_list = validate_cycle_count_form(request.user, warehouse, data_list, adhoc=True, extra_params=extra_params)
    if status != 'Success':
        return JsonResponse({'message': status}, status = 400)
    try:
        log_message = (("Request Adhoc Cycle Count Generation Username %s, data %s ") % (
            str(request.user.username), str(data_list)
        ))
        log.info(log_message)
        response = generate_and_submit_cycle_count(request, warehouse, data_list, cycle_type='Adhoc')
        if isinstance(response, list):
            return JsonResponse({'message': response}, status = 400)

        return JsonResponse({'message': 'Success'}, status = 200)

    except Exception as e:
        log_message = (("Adhoc Cycle Count Failed for Username %s, data %s and error message %s") % (
            str(request.user.username), str(data_list), str(e)
        ))
        log.info(log_message)
        return JsonResponse({'message': 'Failed'}, status = 400)