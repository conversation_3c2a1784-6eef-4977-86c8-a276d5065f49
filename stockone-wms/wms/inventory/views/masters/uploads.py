#package immports
from copy import deepcopy
from collections import defaultdict
import traceback

#django imports
from django.http import HttpResponse

#core operation functions
from core_operations.views.common.main import (
    get_misc_value, validate_special_characters, get_multiple_misc_values
)

#inventory functions
from inventory.views.masters.hierarchy_master import (
    validate_hierarchy_data_upload, create_hierarchies
)
#wms utils
from wms_base.wms_utils import (
    LOC_DATA, ZONE_DATA, LOCATION_HEADERS_MAPPING, LOCATION_HEADERS,
    ZONE_HEADERS, ZONE_HEADER_MAPPING, SUBZONE_HEADERS, SUBZONE_HEADER_MAPPING,
    STAGING_ROUTE_HEADERS, STAGING_ROUTE_HEADER_MAPPING,
    rename_dict_keys
)

#inventory models
from inventory.models import (
    LocationMaster, StockDetail, ZoneMaster,
    CycleCount
)
from inventory.views.masters.zone_master import (
    validate_zones_data, create_or_update_zones
)

#core models
from core.models import (
SKUMaster, MiscDetailOptions, MiscDetail
)

#inbound models
from inbound.models import (
    StagingRoute
)

#import wms base
from wms_base.wms_utils import init_logger

#constants
ZONE_LITERAL = 'Zone*'
SUB_ZONE_LITERAL = 'Sub Zone'
CHECK_DIGIT_LITERAL = 'Check Digit*'

log = init_logger('logs/location_upload.log')

def location_form(user, extra_params={}):
    log.info("Request GET Location Form: %s, Extra Params: %s" % (user.username, extra_params))
    headers = deepcopy(LOCATION_HEADERS)

    misc_types = ['subzone_mapping', 'check_digit_limit', 'enable_check_digit']
    misc_dict = get_multiple_misc_values(misc_types, user.id)
    subzone_mapping = misc_dict.get('subzone_mapping')
    check_digit_limit = misc_dict.get('check_digit_limit')

    if subzone_mapping == 'true':
        headers.append(SUB_ZONE_LITERAL)
    if misc_dict.get('enable_check_digit', 'false') == 'true' and check_digit_limit:
        headers.append(CHECK_DIGIT_LITERAL)
    hierarchy_headers = list(MiscDetailOptions.objects.filter(misc_detail__misc_type="hierarchy", status=1, misc_detail__user=user.id).values_list('misc_key',flat=True))
    for header in hierarchy_headers:
        headers.append(header + "*")
    return headers

def location_upload(request, user, data_list, extra_params={}):
    try:
        log.info("Request GET Location Form: %s, Data List %s, Extra Params: %s" % (user.username, str(data_list), extra_params))
        subzone_mapping = list(MiscDetail.objects.filter(misc_type='subzone_mapping', user=user.id).values_list('misc_value', flat=True)) or ['false']
        hierarchy_order = list(MiscDetailOptions.objects.filter(misc_detail__misc_type='hierarchy', misc_detail__user=user.id, status = 1).values_list('misc_key', flat=True).order_by('misc_value'))
        status, extra_data = validate_location_form(user.id, data_list, hierarchy_order, subzone_mapping)
        if not status:
            return extra_data
        if hierarchy_order:
            error, status = validate_hierarchy_data_upload(user, extra_data)
            if error:
                for i in range(len(data_list)):
                    data_list[i]['Status'] = status[i]
                return data_list

        zone_list = check_and_create_zone(user, data_list)
        subzones_list = []
        if subzone_mapping[0] == 'true':
            subzones_list = check_and_create_subzone(user, data_list)
               
        data_list = create_hierarchies(request.username, user, extra_data, data_list)

        created_locations = process_location(request.username, user, data_list, zone_list, subzones_list)

        if not created_locations:
            return HttpResponse('Location Master Creation Failed')
        return HttpResponse('Success')
    except Exception as e:
        log.error("Error in Location Upload: %s" % str(e), exc_info=True)
        return HttpResponse('Failed')

def validate_zone(cell_data, status, zones_list, sub_zone_list, sub_zone=False):
    if cell_data:
        special_char_status = validate_special_characters(cell_data,'^[a-zA-Z0-9-_/|.]*$')
        if special_char_status != "Success":
            zone_str = SUB_ZONE_LITERAL if sub_zone else "Zone"
            status.append(zone_str + ' Cannot have Special Characters')

        if not sub_zone and cell_data not in zones_list:
            status.append('Zone Not Found')

        if sub_zone and cell_data not in sub_zone_list:
            status.append('Sub Zone Not Found')
    else:
        if not sub_zone:
            status.append('Zone is Mandatory')

    return status

def validate_location(cell_data, location_data, status):
    location = ""
    
    if cell_data in location_data:
        status.append('Duplicate Location')
    
    if cell_data:
        if cell_data != cell_data.strip():
            status.append('Location Cannot have spaces at the start or end')
            return status, ''
        
        cell_data = cell_data.strip()
        special_char_status = validate_special_characters(cell_data,'^[a-zA-Z0-9-_/|. ]*$')
        if special_char_status != "Success":
            status.append('Location Cannot have Special Characters')
        else:
            location_data.append(cell_data)
            location = cell_data
    else:
        status.append('Location is Mandatory')

    return status, location

def validate_check_digit_limit(cell_data, limit, status):
    limit = int(limit)
    if cell_data:
         if len(cell_data) != limit:
            status.append(f"check digit should be {limit} characters")
    else:
        status.append(f"Check digit is mandatory")
    return status

def validate_integer_fields(status, key, cell_data):
    if not cell_data:
        status.append('Missing required fields %s'% str(key))
    else:
        try:
            cell_data = float(cell_data)
            if cell_data and (not isinstance(cell_data, (int, float)) or int(cell_data) < 0):
                status.append('Invalid Quantity')
        except ValueError:
            status.append('Invalid Quantity')

    return status

def validate_location_status(cell_data, location, stock_locations, status_list, status, open_cycle_count):
    if cell_data:
        loc_status = cell_data.lower()
        if loc_status not in status_list:
            status.append('select a valid status %s' % ('Active/Inactive'))
        if loc_status == "inactive" and location in stock_locations:
            status.append('Stock Found in Location %s Please Move before making Inactive' % location)
        if loc_status == "inactive" and location in open_cycle_count:
            status.append('Inventory adjustments are pending for this location- %s, Please clear them before deactivating the location' % location)
    return status

def validate_carton_capacity(cell_data, status):
    try:
        if cell_data:
            cell_data = float(cell_data)
        if cell_data and not isinstance(cell_data, (int, float)):
            status.append('Invalid Carton Capacity')
    except ValueError:
        status.append('Invalid Carton Capacity')

    return status

def validate_hierarchy_fields(row_data, hierarchy_order, status):
    data_dict = { 'zone' : row_data[ZONE_LITERAL] }
    for key in hierarchy_order:
        access_key = key + "*"
        if not row_data.get(access_key):
            status.append('Missing required fields %s'% str(key))
        data_dict[key] = row_data.get(access_key)
    return data_dict


def validate_subzone_mapping(subzone_mapping_dict, input_subzone_mapping_dict, row_data, status):
    subzone = row_data.get(SUB_ZONE_LITERAL).upper() if row_data.get(SUB_ZONE_LITERAL) else ''
    input_zone = row_data.get(ZONE_LITERAL).upper() if row_data.get(ZONE_LITERAL) else ''

    mapped_subzone = subzone_mapping_dict.get(subzone)
    if subzone:
        if subzone in subzone_mapping_dict and mapped_subzone != input_zone:
            status.append("Subzone is already mapped to "+ mapped_subzone)

        if subzone in input_subzone_mapping_dict and len(input_subzone_mapping_dict.get(subzone)) > 1:
            status.append("Invalid Subzone Mapping, Please Rectify")

    return status

def get_zone_details(user):
    zone_objs = ZoneMaster.objects.filter(user=user).values('zone', 'zone_type')
    zones, subzones = [], []
    for zone in zone_objs:
        if zone['zone_type'] == 'zone':
            zones.append(zone['zone'])
        else:
            subzones.append(zone['zone'])
    return zones, subzones

def get_subzone_mapping_details(warehouse_id, data_to_integrate):
    input_subzone_mapping_dict, subzones, subzone_mapping_dict = defaultdict(list), [], {}
    for data in data_to_integrate:
        subzone = data.get(SUB_ZONE_LITERAL).upper() if data.get(SUB_ZONE_LITERAL) else ''
        zone = data.get(ZONE_LITERAL).upper() if data.get(ZONE_LITERAL) else ''
        if subzone:
            if subzone not in subzones:
                subzones.append(subzone)
            if zone and zone not in input_subzone_mapping_dict[subzone]:
                input_subzone_mapping_dict[subzone].append(zone)

    if subzones:
        subzone_mapping_dict = dict(LocationMaster.objects.filter(
            status = 1, zone__user = warehouse_id, sub_zone__zone__in = subzones
        ).values_list('sub_zone__zone', 'zone__zone'))

    return subzone_mapping_dict, input_subzone_mapping_dict


def validate_location_form(user, data_to_integrate, hierarchy_order, subzone_mapping):
    location_header_mapping = deepcopy(LOCATION_HEADERS_MAPPING)
    location_data, stock_locations, hierarchy_data = [], [], []
    error_data_list, error_status = [], False
    status_list = ['active','inactive']

    zones_list, subzones_list = get_zone_details(user)

    stock_locations = list(
        StockDetail.objects.exclude(receipt_number=0).\
        filter(sku__user= user,quantity__gt = 0).\
        values_list('location__location',flat=True).distinct())
    
    open_cycle_count = list(CycleCount.objects.filter(
            sku__user = user, status = 2
        ).values_list('location__location',flat=True).distinct())

    subzone_mapping_dict, input_subzone_mapping_dict = get_subzone_mapping_details(user, data_to_integrate)
 
    if subzone_mapping[0] == 'true':
        location_header_mapping[SUB_ZONE_LITERAL] = 'sub_zone'
    misc_types = ['check_digit_limit', 'enable_check_digit']
    misc_dict = get_multiple_misc_values(misc_types, user)
    enable_check_digit = misc_dict.get('enable_check_digit', 'false')
    check_digit_limit = misc_dict.get('check_digit_limit')
    if enable_check_digit == 'true' and check_digit_limit:
        location_header_mapping[CHECK_DIGIT_LITERAL] = 'check_digit'

    for row_data in data_to_integrate:
        status, location = [], ''
        for key, value in row_data.items():
            cell_data = value
            key = location_header_mapping.get(key)

            if key == 'zone':
                status = validate_zone(cell_data, status, zones_list, subzones_list)

            elif key == 'location':
                status, location = validate_location(cell_data, location_data, status)

            elif key in ['capacity', 'put_sequence', 'get_sequence']:
                status = validate_integer_fields(status, key, cell_data)

            elif key == 'status':
                status = validate_location_status(cell_data, location, stock_locations, status_list, status, open_cycle_count)

            elif key == 'carton_capacity':
                status = validate_carton_capacity(cell_data, status)
                
            elif key == 'sub_zone':
                status = validate_zone(cell_data, status, zones_list, subzones_list, True)
            
            elif key == 'check_digit':
                status = validate_check_digit_limit(cell_data, check_digit_limit, status)
        
        hierarchy_dict = {}
        if hierarchy_order:
            hierarchy_dict = validate_hierarchy_fields(row_data, hierarchy_order, status)

        #Validate Subzone Mapping
        status = validate_subzone_mapping(subzone_mapping_dict, input_subzone_mapping_dict, row_data, status)

        if status:
            error_status = True
            row_data['Status'] = status
        error_data_list.append(row_data)
        if hierarchy_dict:
            hierarchy_data.append(hierarchy_dict)
            row_data['hierarchy_data'] = hierarchy_dict

    if error_status:
        return "", error_data_list
    else:
        return 'Success', hierarchy_data

def process_location(request_user, user, data_to_integrate, zone_list, subzones_list):
    zone_ids = dict(ZoneMaster.objects.filter(zone__in = zone_list, user = user.id, zone_type = 'zone').values_list('zone', 'id'))
    subzone_ids = dict()
    if subzones_list:
        subzone_ids = dict(ZoneMaster.objects.filter(zone__in = subzones_list, user = user.id, zone_type = 'sub_zone').values_list('zone', 'id'))
    created_locations = []
    for row_data in data_to_integrate:
        max_capacity = row_data.get('Capacity*')
        get_sequence = row_data.get('Get sequence*')
        put_sequence = row_data.get('Put sequence*')
        status = row_data.get('Status(Active/Inactive)')
        location_ = row_data.get('Location*')
        zone = row_data.get(ZONE_LITERAL)
        zone = zone.upper()
        zone_id = zone_ids.get(zone)
        sub_zone = row_data.get(SUB_ZONE_LITERAL)
        sub_zone = sub_zone.upper() if sub_zone else ''
        sub_zone_id = subzone_ids.get(sub_zone)
        last_level = row_data.get('last_level')
        hierarchy_data = row_data.get('hierarchy_data',{})
        check_digit = row_data.get(CHECK_DIGIT_LITERAL)
        check_digit = check_digit if check_digit else ''

        carton_capacity = ''
        if get_misc_value('inbound_packing', user.id) == 'true':
            carton_capacity = row_data.get('Carton Capacity')
        location_ = create_location(request_user, user, location_, zone_id, sub_zone_id, max_capacity, get_sequence, put_sequence, status, carton_capacity, last_level, hierarchy_data, check_digit)
        created_locations.append(location_)
    return created_locations

def create_location(request_user, user, location_, zone_id, sub_zone_id, max_capacity, get_sequence, put_sequence, status, carton_capacity, last_level, hierarchy_data, check_digit):
    location = LocationMaster.objects.filter(location__iexact=location_, zone__user=user.id)
    location_status = location[0].status if location else 1
    if status:
        location_status = int(status == 'Active')

    if not location:
        location_data = deepcopy(LOC_DATA)
        location_data['zone_id'] = zone_id
        location_data['sub_zone_id'] = sub_zone_id
        location_data['max_capacity'] = max_capacity
        location_data['pick_sequence'] = get_sequence
        location_data['fill_sequence'] = put_sequence
        json_data = {'created_by': request_user, 'updated_by': request_user, 
                     'hierarchy_data': hierarchy_data, 'created_from': 'Upload'}
        location_data['json_data'] = json_data
        location_data['status'] = location_status
        location_data['account_id'] = user.userprofile.id
        location_data['location'] = location_
        if check_digit:
            location_data['check_digit'] = check_digit
        if carton_capacity:
            location_data['carton_capacity'] = carton_capacity
        if last_level:
            location_data['lower_hierarchy_level_id'] = last_level
        location = LocationMaster(**location_data)
        location.save()
    else:
        location = location[0]
        location.fill_sequence = put_sequence
        location.pick_sequence = get_sequence
        location.max_capacity = max_capacity
        location.status = location_status
        if last_level:
            location.lower_hierarchy_level_id = last_level
        if sub_zone_id:
            location.sub_zone_id = sub_zone_id
        if carton_capacity:
            location.carton_capacity = carton_capacity
        if location.json_data and location.json_data.get('updated_by'):
            location.json_data.update({'updated_by': request_user})
        else:
            location.json_data = {'updated_by': request_user}
        if hierarchy_data:
            location.json_data.update({'hierarchy_data': hierarchy_data})
        if check_digit:
            location.check_digit = check_digit
        location.save()
    return location_

def check_and_create_zone(user, data_to_integrate):
    zone_list = {row_data[ZONE_LITERAL].upper() for row_data in data_to_integrate}
    zone_obj_list = []
    zones = set(ZoneMaster.objects.filter(user = user.id, zone__in = zone_list, zone_type="zone").values_list('zone', flat=True))
    for zone in zone_list:
        if zone not in zones:
            zone_data = deepcopy(ZONE_DATA)
            zone_data['user'] = user.id
            zone_data['account_id'] = user.userprofile.id
            zone_data['zone'] = zone
            if zone == 'WIPZONE':
                zone_data['segregation'] = 'non_sellable'
                zone_data['storage_type'] = 'wip_area'
            zone_obj_list.append(ZoneMaster(**zone_data))
    if zone_obj_list:
        ZoneMaster.objects.bulk_create_with_rounding(zone_obj_list)

    return zone_list

def check_and_create_subzone(user, data_to_integrate):
    subzones_list = {row_data[SUB_ZONE_LITERAL].upper() for row_data in data_to_integrate if row_data.get(SUB_ZONE_LITERAL)}
    subzone_obj_list = []
    subzones = set(ZoneMaster.objects.filter(user = user.id, zone__in = subzones_list, zone_type="sub_zone").values_list('zone', flat=True))
    for zone in subzones_list:
        if zone not in subzones:
            zone_data = deepcopy(ZONE_DATA)
            zone_data['user'] = user.id
            zone_data['account_id'] = user.userprofile.id
            zone_data['zone'] = zone
            zone_data['zone_type'] = 'sub_zone'
            subzone_obj_list.append(ZoneMaster(**zone_data))
    if subzone_obj_list:
        ZoneMaster.objects.bulk_create_with_rounding(subzone_obj_list)

    return subzones_list


def zone_form(warehouse, extra_params={}):
    log.info("Request Zone Master form %s, %s" % (warehouse, extra_params))
    return ZONE_HEADERS

def zone_upload(request, warehouse, data_list, extra_params={}):
    log.info(f"Zone Master Creation: User {request.username}, Data: {data_list}, Params: {extra_params}")
    try:
        data_list = rename_dict_keys(ZONE_HEADER_MAPPING, data_list)
        status = validate_zones_data(warehouse, data_list)

        if isinstance(status, str):
            return create_or_update_zones(warehouse, data_list)

        return rename_dict_keys(ZONE_HEADER_MAPPING, status, reverse=True)
    except Exception as e:
        log.exception(f"Zone Upload failed for {warehouse.username}: {e}")
        return "Zone Upload Failed"

def subzone_form(warehouse, extra_params={}):
    log.info("Request SubZone form %s, %s" % (warehouse, extra_params))
    return SUBZONE_HEADERS

def subzone_upload(request, warehouse, data_list, extra_params={}):
    log.info(f"Sub Zone Master Creation: User {request.username}, Data: {data_list}, Params: {extra_params}")
    try:
        data_list = rename_dict_keys(SUBZONE_HEADER_MAPPING, data_list)
        status = validate_zones_data(warehouse, data_list, zone_type='sub_zone')

        if isinstance(status, str):
            return create_or_update_zones(warehouse, data_list, zone_type='sub_zone')

        return rename_dict_keys(SUBZONE_HEADER_MAPPING, status, reverse=True)
    except Exception as e:
        log.exception(f"Sub Zone Upload failed for {warehouse.username}: {e}")
        return "Sub Zone Upload Failed"

def staging_route_form(warehouse, extra_params={}):
    log.info("Request Staging Route form %s, %s" % (warehouse, extra_params))
    return STAGING_ROUTE_HEADERS

def staging_route_upload(request, warehouse, data_list, extra_params={}):
    log.info(f"Staging Route Master Creation: User {request.username}, Data: {data_list}, Params: {extra_params}")
    try:
        data_list = rename_dict_keys(STAGING_ROUTE_HEADER_MAPPING, data_list)
        status, zone_dict, location_dict, put_zones, staging_locations = validate_staging_route_data(warehouse, data_list)

        if isinstance(status, str):
            return create_or_update_staging_route(warehouse, data_list, zone_dict, location_dict, put_zones, staging_locations)

        return rename_dict_keys(STAGING_ROUTE_HEADER_MAPPING, status, reverse=True)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.exception(f"Staging Route Upload failed for {warehouse.username}: {e}")
        return "Staging Route Upload Failed"

def get_zones_and_locations(staging_route_data):
    put_zones, staging_locations = set(), set()
    for data in staging_route_data:
        data['attribute_name'] = data['attribute_name'].lower()
        data['attribute_value'] = data['attribute_value'].lower()
        data['stage'] = data['stage'].upper()
        data['put_zone'] = data['put_zone'].upper()
        data['staging_location'] = data['staging_location'].upper()
        put_zones.add(data['put_zone'])
        staging_locations.add(data['staging_location'])
    return put_zones, staging_locations

def get_zones_and_staging_data(warehouse, put_zones, staging_locations):
    zone_dict, location_dict = {}, {}
    zone_dict = dict(ZoneMaster.objects.filter(user=warehouse.id, zone__in=put_zones).values_list('zone', 'id'))
    locations = LocationMaster.objects.filter(zone__user=warehouse.id, location__in=staging_locations).values('location', 'zone__storage_type', 'id')
    for location in locations:
        location_dict[(location['location'], location['zone__storage_type'])] = location['id']
    return zone_dict, location_dict

def get_existing_staging_route_data(warehouse, put_zones, staging_locations):
    existing_staging_route_data = StagingRoute.objects.filter(
        warehouse=warehouse, put_zone__zone__in=put_zones, staging_location__location__in=staging_locations
    )
    existing_staging_route_dict = {}
    for data in existing_staging_route_data:
        key = (data.transaction, data.attribute_name, data.attribute_value, data.stage, data.put_zone.zone)
        existing_staging_route_dict[key] = data
    return existing_staging_route_dict

def validate_staging_routes(data, zone_dict, location_dict):
    status = []
    if not data['stage']:
        status.append('Staging Type is Mandatory')
    if data['stage'] not in ['REC', 'REPACK', 'PUT', 'INSP']:
        status.append('Staging Type is Invalid')
    if data['transaction'] not in ['PO', 'SR']:
        status.append('Transaction Type is Invalid')
    if data['put_zone'] and not zone_dict.get(data['put_zone']):
        status.append('Invalid Put Zone')
    if not location_dict.get((data['staging_location'], data['stage'])):
        status.append('Invalid Staging Location')
    
    return ', '.join(status) if status else ''

def validate_staging_route_data(warehouse, staging_route_data):
    put_zones, staging_locations = get_zones_and_locations(staging_route_data)
    zone_dict, location_dict = get_zones_and_staging_data(warehouse, put_zones, staging_locations)

    for data in staging_route_data:
        status = validate_staging_routes(
            data, zone_dict, location_dict
        )
        if status:
            data['Status'] = status
    error_entries = 'Success'
    if any('Status' in entry for entry in staging_route_data):
        error_entries = staging_route_data
    return error_entries, zone_dict, location_dict, put_zones, staging_locations

def create_or_update_staging_route(warehouse, staging_route_data, zone_dict, location_dict, put_zones, staging_locations):
    existing_staging_route_data = get_existing_staging_route_data(warehouse, put_zones, staging_locations)
    staging_route_objs, update_route_objs = [], []
    for data in staging_route_data:
        put_zone_id = zone_dict.get(data['put_zone'])
        staging_location_id = location_dict.get((data['staging_location'], data['stage']))
        key = (data['transaction'], data['attribute_name'], data['attribute_value'], data['stage'], data['put_zone'])
        existing_data = existing_staging_route_data.get(key)
        if existing_data:
            existing_data.staging_location_id = staging_location_id
            update_route_objs.append(existing_data)
        else:
            staging_route_data = {
                'warehouse': warehouse,
                'account_id': warehouse.userprofile.id,
                'transaction': data['transaction'],
                'attribute_name': data['attribute_name'],
                'attribute_value': data['attribute_value'],
                'stage': data['stage'],
                'put_zone_id': put_zone_id,
                'staging_location_id': staging_location_id
            }
            staging_route_objs.append(StagingRoute(**staging_route_data))
    if staging_route_objs:
        StagingRoute.objects.bulk_create_with_rounding(staging_route_objs, batch_size = 100)
    if update_route_objs:
        StagingRoute.objects.bulk_update_with_rounding(update_route_objs, ['staging_location_id'])
    return 'Success'

    
