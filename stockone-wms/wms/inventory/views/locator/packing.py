from django.test.client import RequestFactory
from django.db.models import Sum
from wms.celery import app as celery_app

from wms_base.models import User
from inventory.models import StockDetail

from core_operations.views.services.packing_service import PackingService
from core_operations.views.common.main import generate_log_message


from wms_base.wms_utils import init_logger
log = init_logger('logs/lpn_stockdetail.log')


def empty_carton_check(warehouse_id, lpn_numbers=[]):
    filters = {'sku__user': warehouse_id, 'lpn_number__in': lpn_numbers}
    empty_carton_list = list(
        StockDetail.objects.filter(
            **filters
        ).values_list('lpn_number', flat=True
        ).annotate(lpn_quantity=Sum('quantity')
        ).filter(lpn_quantity__lte=0)
    )
    return empty_carton_list

def lpn_transaction_completion_check(warehouse_id, reference_numbers, transaction_type):
    '''
    Check if all the LPNs are completed for the given reference number
    '''
    receipt_type_dict = {
        'batosa': 'batosa',
        'nte_packing': 'nte',
        'BA_TO_SA': 'batosa',
    }
    filters = {
        'sku__user' : warehouse_id,
        'grn_number__in' : reference_numbers,
        'lpn_number__isnull' : False,
        'receipt_type': receipt_type_dict.get(transaction_type, 'grn_packing')
        }
    group_by_values = ['lpn_number', 'grn_number']
    lpn_details = list(StockDetail.objects.filter(**filters
                    ).values(*group_by_values
                    ).annotate(lpn_quantity=Sum('quantity')
                    ).filter(lpn_quantity__lte=0))
    
    transaction_wise_lpns = {}
    for lpn in lpn_details:
        if lpn.get('grn_number') not in transaction_wise_lpns:
            transaction_wise_lpns[lpn.get('grn_number')] = [lpn.get('lpn_number')]
        else:
            transaction_wise_lpns[lpn.get('grn_number')].append(lpn.get('lpn_number'))
    return lpn_details, transaction_wise_lpns


@celery_app.task
def update_packing_status_to_complete(username, warehouse_id, transaction_number, transaction_type, lpn_numbers, extra_params):
    warehouse = User.objects.get(id=warehouse_id)
    user = User.objects.get(username=username)

    request_dict = {
        "request_headers": extra_params.get('headers', {}),
        "request_meta": extra_params.get('request_meta', {}),
        "request_scheme": extra_params.get('request_scheme', ''),
    }
    params = {
            "warehouse": warehouse.username,
            "transaction_type": transaction_type,
            "lpn_numbers": lpn_numbers
        }
    status = "completed"
    packing_service_instance = PackingService(request_dict, user, warehouse)
    packing_details, packing_service_errors = packing_service_instance.update_packing_status(params, status)
    log.info(generate_log_message(
        "PackingStatusUpdateDetails",
        reference_number=transaction_number,
        transaction_type=transaction_type,
        packing_details=packing_details
        ))
    if packing_service_errors:
        log.info(generate_log_message(
            "PackingStatusUpdateFailure",
            reference_number=transaction_number,
            transaction_type=transaction_type,
            error=packing_service_errors
            ))

@celery_app.task
def update_packing_transactional_status(username, warehouse_id, reference_numbers, transaction_type="grn_packing", loaded_lpns=[], extra_params={}):
    '''
    Update packing status as completed after Putaway
    '''
    warehouse = User.objects.get(id=warehouse_id)
    user = User.objects.get(username=username)

    
    lpn_details, transaction_wise_lpns \
        = lpn_transaction_completion_check(warehouse_id, reference_numbers, transaction_type)
    empty_cartons = empty_carton_check(warehouse_id, loaded_lpns)

    log.info(generate_log_message(
        "LPNTransactionDetailsAfterPutaway",
        transaction_number=reference_numbers,
        lpn_details=lpn_details,
        loaded_lpns = loaded_lpns,
        empty_cartons = empty_cartons
        ))
    
    request_dict = {
        "request_headers": extra_params.get('headers', {}),
        "request_meta": extra_params.get('request_meta', {}),
        "request_scheme": extra_params.get('request_scheme', ''),
    }

    packing_service_instance = PackingService(request_dict, user, warehouse)
    for transaction_number, lpn_numbers in transaction_wise_lpns.items():
        params = {
            "warehouse": warehouse.username,
            "transaction_number": str(transaction_number),
            "transaction_type": transaction_type,
            "lpn_numbers": lpn_numbers
        }
        status = "completed"
        packing_details, packing_service_errors = packing_service_instance.update_packing_status(params, status)
        log.info(generate_log_message(
            "PackingStatusUpdateDetails",
            reference_number=transaction_number,
            packing_details=packing_details
            ))
        if packing_service_errors:
            log.info(generate_log_message(
                "PackingStatusUpdateFailure",
                reference_number=transaction_number,
                error=packing_service_errors
                ))
        
    if empty_cartons:
        #Changing the LPN Status
        lpn_numbers_str = ",".join(empty_cartons)
        params = {
            "warehouse": warehouse.username,
            "lpn_number": lpn_numbers_str,
            "status": True,
            "usable": True,
            "dropped": False,
            "blocked": False,
            "qc_flag": False,
        }
        lpn_details, packing_service_errors = packing_service_instance.update_lpn(params)
        log.info(generate_log_message(
            "LPNStatusUpdateDetails",
            empty_cartons=empty_cartons,
            params=params,
            lpn_details=lpn_details,
            ))
        if packing_service_errors:
            log.info(generate_log_message(
                "LPNStatusUpdateFailure",
                empty_cartons=empty_cartons,
                params=params,
                error=packing_service_errors)
                )