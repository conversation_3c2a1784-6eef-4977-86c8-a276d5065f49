import datetime
import pytz
from xlrd import xldate_as_tuple
import pandas as pd
## core models import
from core.models import SKUMaster
from itertools import chain

def get_sku_codes(data_to_integrate, fetch_serials=False):
    sku_codes, lpn_numbers, locations = [], [], []
    for data in data_to_integrate:
        sku_code = data.get('sku_code')
        location = data.get('location')
        lpn_number = data.get('lpn_number','') or ''
        if sku_code:
            if isinstance(sku_code, (int, float)):
                sku_code = str(int(sku_code))
            sku_codes.append(sku_code)
        if location:
            if isinstance(location, (int, float)):
                location = str(int(location))
            locations.append(location)
        if lpn_number:
            if isinstance(lpn_number, (int, float)):
                lpn_number = str(int(lpn_number))
            lpn_numbers.append(lpn_number)

    if fetch_serials:
        data_to_integrate, serial_dict = process_serial_data(data_to_integrate)
        return list(set(sku_codes)), list(set(locations)), list(set(lpn_numbers)), serial_dict, data_to_integrate

    return sku_codes, locations

def normalize_serial(s):
    """
    Normalize serial numbers to a list of strings.
    Handles lists, NaN, numeric values, and comma-separated strings.
    """
    # If the input is already a list, return it as is
    if isinstance(s, list):
        return s
    
    # If the input is NaN, return an empty list
    if pd.isna(s):
        return []
    
    # If the input is a numeric value, convert it to a string in a list
    if isinstance(s, (int, float)):
        return [str(int(s))]
    
    # Handle comma-separated strings: split by comma, strip whitespace
    if isinstance(s, str) and ',' in s:
        return [item.strip() for item in s.split(',') if item.strip()]
    
    # Fallback: just a single string in a list
    return [str(s.strip())]

def ensure_column_str(df, col):
    """
    Ensure that the specified column exists in the DataFrame and is of type string.
    If the column does not exist, it will be created with empty strings.
    If it exists, NaN values will be replaced with empty strings.
    """
    if col not in df.columns:
        df[col] = ''
    else:
        df[col] = df[col].fillna('')

def process_serial_data(data_list):
    """
    Process serial data from the input list of dictionaries.
    This function normalizes the serial numbers and groups the data by SKU code, location, batch number, and LPN number.
    """
    df = pd.DataFrame(data_list)

    ensure_column_str(df, 'batch_no')
    ensure_column_str(df, 'lpn_number')

    if 'serial_number' in df.columns:
        # Normalize serials if the column exists
        df['serial_number'] = df['serial_number'].apply(normalize_serial)
        df['serial_number'] = df['serial_number'].apply(lambda serials: [s for s in serials if s.strip()])
    else:
        # If not present, use empty lists for grouping
        df['serial_number'] = [[] for _ in range(len(df))]

    df['quantity'] = df['quantity'].apply(lambda x: float(str(x)))
    group_cols = ['sku_code', 'location', 'batch_no', 'lpn_number']

    # --- Vectorized filtering ---
    # Filter rows for additions
    add_serials_df = df[(df['quantity'] > 0) & df['serial_number'].apply(bool)]

    # Group by the key columns and aggregate serial_number into lists
    add_serials = (
        add_serials_df
        .groupby(group_cols)['serial_number']
        .agg(lambda x: [item for sublist in x for item in (sublist if isinstance(sublist, list) else [sublist])])
        .to_dict()
    )

    # Similarly for removals
    remove_serials_df = df[(df['quantity'] == 0) & df['serial_number'].apply(bool)]

    remove_serials = (
        remove_serials_df
        .groupby(group_cols)['serial_number']
        .agg(lambda x: [item for sublist in x for item in (sublist if isinstance(sublist, list) else [sublist])])
        .to_dict()
    )

    # Fixed aggregations:
    agg_dict = {
        'quantity': 'sum',
        'serial_number': lambda x: list(chain.from_iterable(x))
    }

    # Detect other columns dynamically:
    other_cols = [col for col in df.columns if col not in group_cols and col not in agg_dict]

    # Add default aggregation for these other columns (choose 'first')
    for col in other_cols:
        agg_dict[col] = 'first'

    df_grouped = df.groupby(group_cols).agg(agg_dict).reset_index()

    # --- Merge back add/remove serials ---
    def get_serials_from_dict(row, source_dict):
        key = tuple(row[col] for col in group_cols)
        return source_dict.get(key, [])

    df_grouped['add_serials'] = df_grouped.apply(lambda row: get_serials_from_dict(row, add_serials), axis=1)
    df_grouped['remove_serials'] = df_grouped.apply(lambda row: get_serials_from_dict(row, remove_serials), axis=1)

    keys = list(zip(df_grouped['sku_code'], df_grouped['location'], df_grouped['batch_no'], df_grouped['lpn_number']))
    values = df_grouped['serial_number'].tolist()
    serial_dict = dict(zip(keys, values))

    return df_grouped.to_dict(orient='records'), serial_dict

def get_sku_master_dict(warehouse, sku_codes, fetch_serials=False):
    sku_master_dict = SKUMaster.objects.filter(
        user = warehouse.id, sku_code__in = sku_codes
    ).values('id','sku_code','batch_based','enable_serial_based').distinct()
    batch_based_sku_list, serial_skus = [], []
    sku_details_dict = {}
    for sku in sku_master_dict:
        sku_details_dict[sku.get('sku_code')] = {
            'sku_id': sku.get('id'),
            'batch_based': sku.get('batch_based'),
            'enable_serial_based': sku.get('enable_serial_based')
        }
        if sku.get('batch_based'):
            batch_based_sku_list.append(sku.get('sku_code'))
        if sku.get('enable_serial_based'):
            serial_skus.append(sku.get('sku_code'))
    
    if fetch_serials:
        return sku_details_dict, batch_based_sku_list, serial_skus

    return sku_details_dict, batch_based_sku_list

def validate_dates(
        manufactured_date, expiry_date, retest_date, reevaluation_date, best_before_date, 
        timezone, status, data_dict
    ):

    date_keys = {
        'manufactured_date': manufactured_date, 'expiry_date': expiry_date,
        'retest_date': retest_date, 'reevaluation_date': reevaluation_date,
        'best_before_date': best_before_date
    }
    mfg_date, expiry_date, retest_date, reevaluation_date, best_before_date = '', '', '', '', ''
    for key, cell_data in date_keys.items():
        data_dict[key] = False

        if cell_data:
            try:
                ist_timezone = pytz.timezone(timezone)
                if isinstance(cell_data, float) or '-' in str(cell_data):
                    if '-' in str(cell_data):
                        year, month, day, hour, minute, second, wday, yday,isdst = datetime.datetime.strptime(cell_data, '%Y-%m-%d').timetuple()
                    else:
                        year, month, day, hour, minute, second = xldate_as_tuple(cell_data, 0)

                    if key == 'manufactured_date':
                        mfg_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))
                        status = validate_manufactured_date(mfg_date, status)

                    elif key == 'retest_date':
                        hour, minute = 23, 59
                        retest_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))

                    elif key == 'reevaluation_date':
                        reevaluation_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))

                    elif key == 'best_before_date':
                        hour, minute = 23, 59
                        best_before_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))

                    elif key == 'expiry_date':
                        hour, minute = 23, 59
                        expiry_date = ist_timezone.localize(datetime.datetime(year, month, day, hour, minute, second))
                        status = validate_expiry_date(mfg_date, expiry_date, status)

                    data_dict = update_dates(mfg_date, expiry_date, retest_date, reevaluation_date, best_before_date, data_dict)

                else:
                    status.append('Invalid %s format' % (key))
                    data_dict[key] = False

            except ValueError:
                status.append('Invalid %s format' % (key))
                data_dict[key] = False
    
    return data_dict, status

def validate_manufactured_date(mfg_date, status):
    if mfg_date and mfg_date > datetime.datetime.now(datetime.timezone.utc):
        status.append('Invalid Manufactured Date %s format' % ('manufactured_date'))
    
    return status

def validate_expiry_date(mfg_date, expiry_date, status):
    if mfg_date and mfg_date > expiry_date:
        status.append('Invalid Expiry Date %s format' % ('expiry_date'))
    return status


def update_dates(mfg_date, expiry_date, retest_date, reevaluation_date, best_before_date, data_dict):
    #Date Formats
    date_fields = {
        'mfg_date': mfg_date, 'expiry_date': expiry_date,
        'retest_date': retest_date, 'reevaluation_date': reevaluation_date,
        'best_before_date': best_before_date
    }
    for date, value in date_fields.items():
        if value:
            if date in ['mfg_date']:
                data_dict['manufactured_date'] = value
            else:
                data_dict[date] = value
    return data_dict

